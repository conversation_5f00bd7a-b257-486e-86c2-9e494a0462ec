# 关系路径过滤功能

## 功能概述

该功能用于过滤企业联系关系的路径数据，根据边的类型（当前关系 vs 历史关系）进行优先级筛选。

## 实现位置

- **工具函数**: `src/domain/utils/relation-path.utils.ts`
- **使用位置**: `src/modules/relationship-investigation/mobile-bidding/service/mobile.graph.service.ts`
- **单元测试**: `src/domain/utils/relation-path.utils.spec.ts`

## 数据结构

### 路径结构

路径采用点边点的结构：
- **点**（偶数下标）：Company、Person、Phone、Email、Address、WebSite，通过 `NodeType` 标识
- **边**（奇数下标）：Legal、Employ、Invest、HisLegal、HisEmploy、HisInvest、Branch、ActualController、FinalBenefit、HasPhone、HasEmail、HasWebsite、HasAddress，通过 `type` 值标识

示例：
```typescript
[
  { NodeType: 'Company', 'Company.keyno': 'key1' },      // 点（下标0）
  { type: 'HasEmail', role: '相同邮箱' },                // 边（下标1）
  { NodeType: 'Email', 'Email.keyno': 'em1' },           // 点（下标2）
  { type: 'HasEmail', role: '相同邮箱' },                // 边（下标3）
  { NodeType: 'Company', 'Company.keyno': 'key2' }       // 点（下标4）
]
```

## 过滤逻辑

### 优先级规则

对于每组 `startCompany` 和 `endCompany` 的 `relationPaths`，按以下优先级筛选：

1. **第一优先级（最高）**：所有边的 `type` 都是当前关系（type 值字符串中不包含"His"）的路径
   - 例如：`HasEmail`、`Branch`、`Invest`、`Legal`、`Employ` 等

2. **第二优先级**：路径中既包含当前关系又包含历史关系的路径
   - 例如：一条路径中同时存在 `Invest` 和 `HisEmploy`

3. **第三优先级（最低）**：所有边的 `type` 都是历史关系（type 值字符串中包含"His"）的路径
   - 例如：`HisInvest`、`HisLegal`、`HisEmploy` 等

### 实现函数

#### 1. `filterRelationPathsByPriority(relationPaths: any[][]): any[][]`

对单个关系对象的路径数组进行过滤，返回符合最高优先级的路径集合。

**参数**：
- `relationPaths`: 路径数组（二维数组）

**返回值**：
- 过滤后的路径数组

**示例**：
```typescript
const paths = [
  [ /* 当前关系路径 */ ],
  [ /* 历史关系路径 */ ],
  [ /* 混合关系路径 */ ]
];

const filtered = filterRelationPathsByPriority(paths);
// 返回: [ /* 当前关系路径 */ ] (第一优先级)
```

#### 2. `filterEnterpriseContactRelations(enterpriseContactRelations: any[]): any[]`

对整个企业联系关系数组进行处理，为每个关系对象的 `relationPaths` 应用过滤逻辑。

**参数**：
- `enterpriseContactRelations`: 企业联系关系数组

**返回值**：
- 过滤后的企业联系关系数组

**示例**：
```typescript
const relations = [
  {
    startCompanyKeyno: 'key1',
    startCompanyName: 'Company A',
    endCompanyKeyno: 'key2',
    endCompanyName: 'Company B',
    relationPaths: [
      [ /* 路径1 */ ],
      [ /* 路径2 */ ]
    ]
  }
];

const filtered = filterEnterpriseContactRelations(relations);
// 每个关系对象的 relationPaths 都会被按优先级过滤
```

## 使用方式

在 `MobileGraphService.getEnterpriseContactRelation()` 方法中，返回数据前自动应用过滤：

```typescript
async getEnterpriseContactRelation(keyNos: string[], relationTypes: string[]) {
  // ... 调用API获取数据 ...
  const result = response?.data;
  return filterEnterpriseContactRelations(result);  // 自动过滤
}
```

## 测试覆盖

单元测试文件 `relation-path.utils.spec.ts` 包含以下测试场景：

1. ✅ 第一优先级：所有边都是当前关系
2. ✅ 第二优先级：混合当前和历史关系
3. ✅ 第三优先级：所有边都是历史关系
4. ✅ 边界情况：空数组、null、undefined
5. ✅ 真实数据格式测试

测试运行命令：
```bash
npm test -- src/domain/utils/relation-path.utils.spec.ts
```

## 注意事项

1. 过滤是基于边的 `type` 字段进行判断
2. 历史关系的判断标准是 `type` 字符串中包含 "His"
3. 如果所有路径都不满足任何优先级，返回空数组
4. 函数对输入数据进行了防御性处理，可以安全处理 null 和 undefined

## 性能考虑

- 时间复杂度：O(n * m)，其中 n 是关系对象数量，m 是每个对象的平均路径数量
- 空间复杂度：O(n * m)，需要创建新的数组存储过滤结果
- 对于大量数据，过滤操作是高效的，不会产生性能瓶颈

---

## 管理层投资关系过滤功能

### 数据结构

管理层投资关系的数据结构：

```typescript
[
  {
    startCompanyKeyNo: "公司1的KeyNo",
    startCompanyName: "公司1名称",
    endCompanyKeyNo: "公司2的KeyNo",
    endCompanyName: "公司2名称",
    relationCount: 5,  // 关系数量
    relations: [
      {
        midCompanyKeyNo: "中间公司KeyNo",
        midCompanyName: "中间公司名称",
        person1KeyNo: "人员1 KeyNo",
        person1Name: "人员1姓名",
        person2KeyNo: "人员2 KeyNo",
        person2Name: "人员2姓名",
        path: {
          r1: { relationType: "INVEST", role: "投资", ... },
          r2: { relationType: "EMPLOY", role: "任职", ... },
          r3: { relationType: "HISINVEST", role: "曾投资", ... },
          r4: { relationType: "HISEMPLOY", role: "曾任职", ... }
        }
      }
    ]
  }
]
```

### 过滤逻辑

对于每组公司关系的 `relations` 数组，按以下优先级筛选：

1. **第一优先级（最高）**：path 中 r1, r2, r3, r4 的 `relationType` 全为当前关系类型（不包含"HIS"）
   - 例如：`INVEST`、`EMPLOY`、`LEGAL` 等

2. **第二优先级**：path 中既包含当前关系类型又包含历史关系类型
   - 例如：r1 是 `INVEST`，r2 是 `HISINVEST`

3. **第三优先级（最低）**：path 中的 relationType 全部为历史关系类型（包含"HIS"）
   - 例如：`HISINVEST`、`HISEMPLOY`、`HISLEGAL` 等

### 实现函数

#### 1. `filterManagementRelationsByPriority(relations: any[]): any[]`

对单个结果对象的 relations 数组进行过滤，返回符合最高优先级的 relation 集合。

**参数**：
- `relations`: 关系数组

**返回值**：
- 过滤后的关系数组

**示例**：
```typescript
const relations = [
  { path: { r1: {relationType: 'INVEST'}, r2: {relationType: 'EMPLOY'}, ... } },  // 当前关系
  { path: { r1: {relationType: 'HISINVEST'}, r2: {relationType: 'HISEMPLOY'}, ... } },  // 历史关系
];

const filtered = filterManagementRelationsByPriority(relations);
// 返回: [ 当前关系 ] (第一优先级)
```

#### 2. `filterManagementInvestmentRelations(managementInvestmentResults: any[]): any[]`

对整个管理层投资关系数组进行处理，为每个结果对象的 `relations` 应用过滤逻辑，并更新 `relationCount`。

**参数**：
- `managementInvestmentResults`: 管理层投资关系数组

**返回值**：
- 过滤后的管理层投资关系数组（包含更新后的 relationCount）

**示例**：
```typescript
const results = [
  {
    startCompanyKeyNo: 'key1',
    endCompanyKeyNo: 'key2',
    relationCount: 5,
    relations: [ /* 5个关系 */ ]
  }
];

const filtered = filterManagementInvestmentRelations(results);
// 每个结果对象的 relations 都会被按优先级过滤
// relationCount 会被更新为过滤后的数量
```

### 使用方式

在 `MobileGraphService.getManagementInvestment()` 方法中，返回数据前自动应用过滤：

```typescript
async getManagementInvestment(keyNos: string[], relationTypes: string[]) {
  // ... 调用API获取数据 ...
  return filterManagementInvestmentRelations(response.data.results);  // 自动过滤
}
```

### 测试覆盖

单元测试文件包含管理层投资关系的测试场景：

1. ✅ 第一优先级：所有 relationType 都是当前关系
2. ✅ 第二优先级：混合当前和历史关系
3. ✅ 第三优先级：所有 relationType 都是历史关系
4. ✅ 边界情况：空数组、null、undefined
5. ✅ relationCount 更新测试
6. ✅ 真实数据格式测试

总共测试用例：20 个（10 个企业联系关系 + 10 个管理层投资关系）

测试运行命令：
```bash
npm test -- src/domain/utils/relation-path.utils.spec.ts
```
