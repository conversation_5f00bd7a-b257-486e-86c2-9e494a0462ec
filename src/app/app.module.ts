import { Module } from '@nestjs/common';
import { App<PERSON>ontroller } from './app.controller';
import { RedisModule } from '@kezhaozhao/nestjs-redis';
import { KzzHealthIndicator } from './KzzHealthIndicator';
import { HealthController } from './health.controller';
import { GracefulShutdown } from './GracefulShutdown';
import { HealthModule } from '@kezhaozhao/nest-sentinel';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SaasService } from './saas.service';
import { CallbackController } from './callback.controller';
import { AccountModule } from '@modules/user-access-management/account/account.module';
import { CustomerModule } from '@modules/company-management/customer/customer.module';
import { ElementModule } from '@modules/system-management/element/element.module';
import { UserModule } from '@modules/user-access-management/user/user.module';
import { BlacklistModule } from '@modules/company-management/blacklist/blacklist.module';
import { SchemaModule } from '@modules/system-management/schema/schema.module';
import { DiligenceModule } from '@modules/risk-assessment/diligence/diligence.module';
import { SettingsModule } from '@modules/system-management/settings/settings.module';
import { PersonModule } from '@modules/company-management/person/person.module';
import { BatchModule } from '@modules/batch/batch.module';
import { MessageModule } from '@modules/system-management/message/message.module';
import { ChartsModule } from '@modules/risk-assessment/diligence/charts/charts.module';
import { CompanySearchModule } from '@modules/company-management/company/company-search.module';
import { Product, QCCBundleModule } from '@kezhaozhao/saas-bundle-service';
import { QccAuthModule } from '@kezhaozhao/saas-auth';
import { TerminusModule } from '@nestjs/terminus';
import { ConfigService } from '@core/config/config.service';
import { ConfigModule } from '@core/config/config.module';
import { UserEntity } from '@domain/entities/UserEntity';
import { RoverSessionGuard } from '@core/guards/RoverSession.guard';
import { InternalModule } from '@modules/integration-api/internal/internal.module';
import { RoverScheduleModule } from '@modules/data-processing/schedule/schedule.module';
import { DataModule } from '@modules/data-processing/data/data.module';
import { MonitorModule } from '@modules/risk-assessment/monitor/monitor.module';
import { UdeskModule } from '@modules/integration-api/udesk/udesk.module';
import { BiddingModule } from '@modules/relationship-investigation/bidding/bidding.module';
import { join } from 'path';
import { HandlebarsAdapter, MailerModule } from '@kezhaozhao/nest-mailer';
import { OpenApiModule } from '@modules/integration-api/openapi/openapi.module';
import { OpenApiJwtGuard } from '@core/guards/openapi.jwt.guard';
import { OpenApiResourceEntity } from '@domain/entities/OpenApiResourceEntity';
import { OpLogModule } from '@modules/system-management/oplog/oplog.module';
import { TenderAlertModule } from '@modules/relationship-investigation/tenderAlert/tenderAlert.module';
import { BenchModule } from '@modules/risk-assessment/bench/bench.module';
import { DevapiModule } from '@modules/integration-api/devapi/devapi.module';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { BasicModule } from '@core/cache/basic/basic.module';
import { CanalStreamModule } from '@modules/data-processing/canal_stream/CanalStreamModule';
import { RoverSocketModule } from '@modules/integration-api/socket/rover.socket.module';
import { UserConfigurationModule } from '@modules/user-access-management/user_configuration/user.configuration.module';
import { SpecificModule } from '@modules/relationship-investigation/specific/specific.module';
import { AiAnalyzerModule } from '@modules/risk-assessment/ai_analyzer/ai.analyzer.module';
import { PotentialModule } from '@modules/relationship-investigation/potential/potential.module';
import { PackageUsageModule } from '@modules/system-management/usage/package.usage.module';
import { ThrottlerModule } from '@nestjs/throttler';
import { ThrottlerStorageRedisService } from 'nestjs-throttler-storage-redis';
import { QichachaModule } from '@kezhaozhao/qichacha-service-utils';
import { AliyunModule } from '@kezhaozhao/qcc-aliyun-utils';
import { SmsModule } from '@kezhaozhao/qcc-sms-utils';
import { SettingsDistributeModule } from '@modules/system-management/settings/distribute/settngs.distribute.module';
import { MobileUserSessionGuard } from '@core/guards/MobileUserSession.guard';
import { AnyGuard } from '@core/guards/AnyGuard';
import { MobileBiddingModule } from '@modules/relationship-investigation/mobile-bidding/mobile.bidding.module';

@Module({
  imports: [
    ConfigModule,
    TerminusModule,
    ThrottlerModule.forRootAsync({
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        storage: new ThrottlerStorageRedisService(configService.redis),
        throttlers: [
          {
            ttl: 300000, // 300 seconds in milliseconds
            limit: 1000000,
          },
        ],
      }),
    }),
    HealthModule.registerAsync({
      useFactory: () => {
        return {
          terminationGracePeriodSeconds: 60,
          canary: false,
          preStopPauseSeconds: 5,
          gracefulShutdown: {
            pauseSecond: 3,
          },
        };
      },
    }),
    TypeOrmModule.forRootAsync({
      useFactory: async (configService: ConfigService) => configService.typeorm,
      inject: [ConfigService],
    }),
    AliyunModule.registerAsync({
      useFactory: (configService: ConfigService) => {
        return {
          oss: configService.server.oss,
        };
      },
      inject: [ConfigService],
    }),
    RedisModule.forRootAsync({
      useFactory: (configService: ConfigService) => configService.redis, // or use async method
      inject: [ConfigService],
    }),
    MailerModule.forRootAsync({
      useFactory: (configService: ConfigService) => ({
        transport: configService.server.mailerService,
        defaults: {
          from: '"企查查" <<EMAIL>>',
        },
        template: {
          dir: join(process.cwd(), 'templates'),
          adapter: new HandlebarsAdapter(),
          options: {
            strict: true,
          },
        },
      }),
      inject: [ConfigService],
    }),
    QccAuthModule.registerAsync({
      useFactory: async (configService: ConfigService) => {
        return {
          jwt: configService.jwt,
          server: {
            comDomainService: configService.server.comDomainService,
            ssoService: configService.server.ssoService,
            appService: configService.server.appService,
            wxAdminService: configService.server.wxAdminService,
            saasService: configService.server.saasService,
            entService: configService.kzzServer.enterpriseService,
            bundleService: configService.kzzServer.bundleService,
            authService: configService.kzzServer.authService,
          },
          sessionName: 'QCCSESSID',
          mobileSessionName: 'MQCCSESSID',
          redis: configService.redis,
          product: Product.Rover,
          validBundle: true,
        };
      },
      inject: [ConfigService],
    }),
    QichachaModule.registerAsync({
      useFactory: async (configService: ConfigService) => {
        return {
          comDomainService: configService.server.comDomainService,
          extDomainService: configService.server.extDomainService,
          appService: configService.server.appService,
          saasService: configService.server.saasService,
          bossService: configService.server.bossService,
          wxQccDomainService: configService.server.wxQccDomainService,
        };
      },
      inject: [ConfigService],
    }),
    SmsModule.registerAsync({
      useFactory: (configService: ConfigService) => {
        return configService.server.smsService;
      },
      inject: [ConfigService],
    }),
    QCCBundleModule.register({
      useFactory: async (configService: ConfigService) => {
        return {
          serviceURL: configService.kzzServer.bundleService,
          product: Product.Rover,
        };
      },
      inject: [ConfigService],
    }) as any,
    TypeOrmModule.forFeature([UserEntity, OpenApiResourceEntity]),
    DataModule,
    CompanySearchModule,
    AccountModule,
    CustomerModule,
    ElementModule,
    UserModule,
    BlacklistModule,
    SchemaModule,
    DiligenceModule,
    SettingsModule,
    UserConfigurationModule,
    PersonModule,
    BatchModule,
    MessageModule,
    ChartsModule,
    InternalModule,
    RoverScheduleModule,
    MonitorModule,
    UdeskModule,
    BiddingModule,
    OpenApiModule,
    OpLogModule,
    TenderAlertModule,
    BenchModule,
    BasicModule,
    DevapiModule.register(),
    EventEmitterModule.forRoot(),
    CanalStreamModule,
    RoverSocketModule,
    SpecificModule,
    AiAnalyzerModule,
    PotentialModule,
    PackageUsageModule,
    SettingsDistributeModule,
    MobileBiddingModule,
  ],
  controllers: [AppController, HealthController, CallbackController],
  providers: [ConfigService, KzzHealthIndicator, GracefulShutdown, SaasService, RoverSessionGuard, OpenApiJwtGuard, MobileUserSessionGuard, AnyGuard],
  exports: [KzzHealthIndicator, AnyGuard, RoverSessionGuard, MobileUserSessionGuard],
})
export class AppModule {}
