# 📱 应用层 (App Layer)

> 生成时间：2026-01-19 12:00:00

## 层级概述

应用层是整个 QCC Rover Service 的最顶层，负责应用的启动、模块组装和全局配置管理。这一层主要关注应用的入口点、健康检查、模块整合等应用级别的关注点。

## 核心职责

### 🚀 应用启动管理

- 应用程序入口点配置
- NestJS 应用实例化
- 全局中间件和管道配置
- 应用启动生命周期管理

### 🔧 模块组装

- 各业务域模块的整合
- 依赖注入容器配置
- 模块间依赖关系管理
- 功能模块的启用/禁用控制

### 🏥 健康检查

- 应用健康状态监控
- 数据库连接状态检查
- 外部服务可用性检测
- 系统资源状态监控

### ⚙️ 全局配置

- 环境变量管理
- 应用级别的配置设置
- 跨模块共享配置
- 运行时配置调整

## 目录结构

```
app/
├── app.module.ts                    # 主应用模块，整合所有业务域
├── app.controller.ts                # 应用级别的控制器
├── health/                          # 健康检查模块
│   ├── health.controller.ts         # 健康检查API接口
│   ├── health.service.ts            # 健康检查业务逻辑
│   └── health.module.ts             # 健康检查模块定义
└── README.md                        # 应用层说明文档
```

## 设计原则

### 1. 单一职责

- 应用层只关注应用级别的关注点
- 不包含具体的业务逻辑
- 专注于模块组装和配置管理

### 2. 依赖管理

- 依赖业务模块层(modules)
- 可以依赖框架核心层(core)
- 不能被其他层依赖

### 3. 配置分离

- 环境相关的配置与代码分离
- 支持多环境配置管理
- 配置的热更新支持

## 核心文件说明

### app.module.ts

主应用模块，负责：

- 导入所有业务域模块
- 配置全局守卫、过滤器、拦截器
- 设置数据库连接
- 配置缓存、队列等基础设施

### app.controller.ts

应用级别控制器，提供：

- 根路径响应
- 应用信息查询
- 全局状态检查

### health/ 目录

健康检查模块，包含：

- `/health` 健康检查端点
- 数据库连接检查
- Redis 连接检查
- 外部 API 可用性检查

## 与其他层的关系

### 🔗 依赖关系

```
app → modules (业务模块层)
app → core (框架核心层)
```

### 📋 业务域集成

应用层集成以下业务域：

- 🔍 risk-assessment (风险评估)
- 🏢 company-management (企业管理)
- 🔗 relationship-investigation (关系排查)
- 👥 user-access-management (用户权限管理)
- 🔧 system-management (系统管理)
- 🛠 data-processing (数据处理)
- 🔌 integration-api (集成 API)

## 开发规范

### 🏷 命名约定

- 模块文件：`*.module.ts`
- 控制器文件：`*.controller.ts`
- 服务文件：`*.service.ts`
- 配置文件：`*.config.ts`

### 📋 模块结构

```typescript
@Module({
  imports: [
    // 业务域模块导入
    RiskAssessmentModule,
    CompanyManagementModule,
    // 核心模块导入
    CoreModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
```

### 🧪 测试要求

- 应用启动测试
- 健康检查端点测试
- 模块集成测试
- 配置加载测试

## 最佳实践

### 1. 环境配置

```typescript
// 使用ConfigModule管理环境变量
ConfigModule.forRoot({
  isGlobal: true,
  envFilePath: ['.env.local', '.env'],
  validationSchema: Joi.object({
    NODE_ENV: Joi.string().valid('development', 'production', 'test'),
    PORT: Joi.number().default(3000),
  }),
});
```

### 2. 全局配置

```typescript
// 全局管道配置
app.useGlobalPipes(
  new ValidationPipe({
    whitelist: true,
    transform: true,
    forbidNonWhitelisted: true,
  }),
);
```

### 3. 健康检查

```typescript
// 健康检查配置
@Controller('health')
export class HealthController {
  @Get()
  @HealthCheck()
  check() {
    return this.health.check([() => this.db.pingCheck('database'), () => this.redis.pingCheck('redis')]);
  }
}
```

## 注意事项

### ⚠️ 避免的反模式

- 在应用层编写业务逻辑
- 直接操作数据库或外部服务
- 硬编码配置值
- 创建循环依赖

### ✅ 推荐做法

- 使用依赖注入管理组件
- 通过配置模块管理设置
- 实现优雅的关闭处理
- 提供详细的健康检查信息

---

> 📚 更多架构信息请参考 [六层分层架构设计](../README.md)
