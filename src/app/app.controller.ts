import { Controller, Get, Post, Query, Req, Request, Res, UploadedFile, UseFilters, UseGuards, UseInterceptors } from '@nestjs/common';
import { ApiCookieAuth, ApiOkResponse, ApiOperation, ApiParam, ApiQuery, ApiTags } from '@nestjs/swagger';
import { LogUtils, QccLogger } from '@kezhaozhao/qcc-logger';
import { RoverSessionGuard } from '@core/guards/RoverSession.guard';
import { Response } from 'express';
import { GuardExceptionFilter } from '@core/filters/guard-exception.filter';
import { BundleReachedLimitationException, Product, RoverBundleAvailability, RoverBundleService, RoverBundleUsage } from '@kezhaozhao/saas-bundle-service';
import { SessionGuard } from '@kezhaozhao/saas-auth';
import { SettingsService } from '@modules/system-management/settings/service/settings.service';
import { HttpService } from '@nestjs/axios';
import { Cacheable } from '@type-cacheable/core';
import { ConfigService } from '@core/config/config.service';
import { Logger } from 'log4js';
import { FileInterceptor } from '@nestjs/platform-express';
import { commonFileUploadOptions } from '@commons/file/config';
import { createReadStream } from 'fs';
import { v1 as uuidv1 } from 'uuid';
import { SettingTypeEnums } from '@domain/model/settings/SettingTypeEnums';
import { getBundleStart } from '@domain/utils/diligence/diligence.utils';
import { RoverUserModel } from '@domain/model/RoverUserModel';
import { OssService } from '@kezhaozhao/qcc-aliyun-utils';
import { AnyGuard } from '@core/guards/AnyGuard';
import { BundleTypeEnum } from '@kezhaozhao/saas-bundle-service/dist_client/model/counter/BundleTypeEnum';

class BetterRoverBundleUsage extends RoverBundleUsage {
  bundleUsage: any;
  standardBundleStartDate: any;
}

@Controller()
@ApiTags('Home')
export class AppController {
  private readonly logger: Logger = QccLogger.getLogger(AppController.name);

  constructor(
    private readonly bundleService: RoverBundleService,
    private readonly settingService: SettingsService,
    protected readonly configService: ConfigService,
    protected readonly httpService: HttpService,
    private readonly ossService: OssService,
  ) {}

  @Get('ping')
  public ping() {
    return `Pong at ${new Date()}`;
  }

  @ApiCookieAuth()
  @UseGuards(SessionGuard)
  @Get('qccProfile')
  @ApiOperation({ summary: '获取用户信息' })
  @ApiOkResponse({ type: RoverUserModel, description: '用户信息' })
  public qccProfile(@Request() req, @Res() res: Response) {
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', 0);
    res.setHeader('Surrogate-Control', 'no-store');
    req.user.ip = LogUtils.ip(req);
    res.json(req.user);
  }

  @ApiCookieAuth()
  @UseGuards(AnyGuard)
  @Get('profile')
  @ApiOperation({ summary: '获取用户信息' })
  @ApiOkResponse({ type: RoverUserModel, description: '用户信息' })
  public async getProfile(@Request() req, @Res() res: Response) {
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', 0);
    res.setHeader('Surrogate-Control', 'no-store');
    req.user.ip = LogUtils.ip(req);
    const userAgent = req.headers['user-agent'];
    if (req.user?.currentOrg) {
      const { userId, currentOrg: orgId } = req.user;
      // 记录活跃日志
      this.httpService.axiosRef
        .post(`${this.configService.kzzServer.enterpriseService}/internal/integration/user/loginTime`, {
          userId,
          orgId,
          ip: req.user.ip,
          userAgent,
          serviceCode: Product.Rover,
        })
        .catch((err) => this.logger.error(err));

      const settings = (await this.settingService.getOrgLatestSettings(req.user.currentOrg, SettingTypeEnums.diligence_risk)).content;
      req.user.groupVersion = settings?.version;
      delete req.user?.resources;
    }
    res.json(req.user);
  }

  @ApiCookieAuth()
  @UseGuards(RoverSessionGuard)
  @Get('bundle/usage')
  @ApiOperation({ summary: '获取当前登录用户套餐使用量' })
  @ApiQuery({ name: 'depId', required: false, description: '获取指定部门的套餐使用情况' })
  @ApiOkResponse({ type: BetterRoverBundleUsage, description: '获取当前登录用户套餐使用量' })
  async getBundleUsage(@Request() req, @Query('depId') depId?: number): Promise<BetterRoverBundleUsage> {
    const currentUser: RoverUserModel = req.user;
    const { currentOrg: orgId, userId, bundle, departments } = currentUser;
    const usage = (await this.bundleService.getBundleUsage(currentUser)) as BetterRoverBundleUsage;
    try {
      // 从counter中取usage 有延迟问题, 所以改为直接通过接口查es获取套餐详情
      // const bundleUsage = usage['totalUsage'];
      const response = await this.httpService.axiosRef.post(`${this.configService.kzzServer.bundleService}/usage`, {
        orgId,
        onlyActualUsage: true,
        productCode: Product.Rover,
        userId,
        depId,
      });
      const totalUsage = response?.data?.['totalUsage'];
      const bundleUsageList = response?.data?.['bundleUsage'];
      if (departments.length > 0) {
        // const depBundleUsage = bundleUsageList?.find((x) => x.depId == departments[0]);
        // 下面代码 usage?.['bundleUsage'] 是个object 没有find方法
        // const depBundleUsage = usage?.['bundleUsage'].find((x) => x.depId == departments[0]);

        const depBundleUsage = bundleUsageList?.find((x) => x.bundleType == BundleTypeEnum.Dep);
        if (depBundleUsage?.usage) {
          Object.assign(usage, { depBundleUsage: depBundleUsage.usage });
        }
      }

      if (totalUsage) {
        const memberLimit = req.user?.bundle?.parameters?.memberLimit;
        const orgBundleId = req.user?.bundle?.mainBundleId;
        if (memberLimit && orgBundleId) {
          const memberUsed = await this.getOrgBundleUserCount(orgBundleId);
          if (typeof memberUsed === 'number') {
            totalUsage['memberLimit'] = { limitation: memberLimit, stock: memberLimit - memberUsed };
          }
        }
        usage.bundleUsage = totalUsage;
      }
      usage.standardBundleStartDate = getBundleStart(bundle.startDate);
      // 获取套餐剩余可分配额度，用于没有分配额度人员的可使用额度
      if (bundleUsageList?.length > 0) {
        const orgBundleId = bundleUsageList[0].orgBundleId;
        const detailResponse = await this.httpService.axiosRef.post(`${this.configService.kzzServer.enterpriseService}/internal/depBundles/detail`, {
          orgBundleId,
          orgId,
          userId,
        });
        usage.allocableParams = detailResponse?.data?.['allocableParams'];
      }
    } catch (err) {
      this.logger.error(`get bundle usage  orgId: ${orgId}, userId: ${userId} error: ${err.message}`);
      this.logger.error(err);
    }
    return usage;
  }

  @Cacheable({ ttlSeconds: 60, cacheKey: (args: any[]) => `cache:getOrgBundleUserCount:${args[0]}` })
  private async getOrgBundleUserCount(orgBundleId: number) {
    return this.httpService.axiosRef
      .post(`${this.configService.kzzServer.enterpriseService}/internal/integration/orgBundle/${orgBundleId}/usersCount`)
      .then((res) => res.data);
  }

  // @Get('getStock/:clearCache/:bundleFiled')
  // @ApiCookieAuth()
  // @UseGuards(RoverSessionGuard)
  // @ApiOperation({ summary: '查看指定套餐项维度' })
  // async getStock(@Request() req, @Param('clearCache') clearCache: boolean, @Param('bundleFiled') bundleFiled: string) {
  //   const currentUser: RoverUser = req.user;

  //   const counter: ICounter = await this.bundleService.getBundleCounter(currentUser, bundleFiled as RoverBundleCounterType);
  //   if (clearCache == true) {
  //     await counter.clear(bundleFiled as RoverBundleCounterType);
  //   }

  //   return counter.getStock(clearCache);
  // }

  @ApiCookieAuth()
  @UseGuards(RoverSessionGuard)
  @Get('bundle')
  @ApiOperation({ summary: '获取当前登录用户套餐内的可用项目' })
  @ApiOkResponse({ type: RoverBundleAvailability, description: '获取当前登录用户套餐内的可用项目' })
  getBundle(@Request() req): Promise<RoverBundleAvailability> {
    const currentUser: RoverUserModel = req.user;
    return this.bundleService.getBundleAvailability(currentUser);
  }

  @ApiCookieAuth()
  @UseGuards(RoverSessionGuard)
  @UseFilters(GuardExceptionFilter)
  @Get('/transit')
  @ApiOperation({ summary: '登录中转页' })
  public async transit(@Request() req, @Res() res: Response) {
    try {
      if (req.query.target && req.query.target.startsWith('/')) {
        if (req.user.kzzApps && req.user.kzzApps.length > 0) {
          res.redirect(req.query.target);
        } else {
          res.redirect('/');
        }
      } else if (req.user?.currentOrg) {
        res.redirect('/supplier/investigation');
      } else {
        res.redirect('/');
      }
    } catch (err) {
      res.redirect('/');
    }
  }

  @ApiCookieAuth()
  @UseGuards(RoverSessionGuard)
  @Post('file/upload')
  @UseInterceptors(FileInterceptor('file', { ...commonFileUploadOptions }))
  @ApiParam({ name: 'file', type: 'file', description: '文件上传' })
  @ApiOperation({ summary: '上传文件，返回oss链接' })
  async fileUpload(@UploadedFile() file, @Request() req, @Query('fileName') fileName: string) {
    try {
      const finalFileName = fileName || file.originalname;
      let extension = '';
      if (finalFileName.lastIndexOf('.') > 0) {
        extension = finalFileName.substring(finalFileName.lastIndexOf('.'), finalFileName.length);
      }
      const ossObject = this.configService.getOssObject(`upload/${req.user.currentOrg}`, uuidv1() + extension);
      const options: any = {};
      if (finalFileName) {
        const downloadName = encodeURIComponent(finalFileName);
        options.headers = {
          'Content-Disposition': `attachment;filename=${downloadName};filename*=UTF-8''${downloadName}`,
        };
      }
      const result = await this.ossService.putStream(ossObject, createReadStream(file.path), options);
      // await this.ossService.putACL(result['name'], 'private');
      return result['name'];
    } catch (e) {
      this.logger.error('oss error', e);
    }
  }

  @Get('error')
  error() {
    try {
      throw new BundleReachedLimitationException('counterType', 'product' as Product, 100, 1);
    } catch (e) {
      return e;
    }
  }

  // // @Post('callbacktest')
  // public callbacktest() {
  //   return 'success';
  // }

  @Get('headers')
  headers(@Req() req) {
    return {
      headers: req.headers,
      ip1: LogUtils.ip(req),
      ip2: req.ip,
    };
  }
}
