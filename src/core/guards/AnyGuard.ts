import { Injectable, ExecutionContext } from '@nestjs/common';
import { MobileUserSessionGuard } from './MobileUserSession.guard';
import { SessionGuard } from '@kezhaozhao/saas-auth';
import { RoverSessionGuard } from './RoverSession.guard';

@Injectable()
export class AnyGuard extends SessionGuard {
  guards: SessionGuard[] = [];
  constructor(private readonly mobileUserSessionGuard: MobileUserSessionGuard, private readonly roverSessionGuard: RoverSessionGuard) {
    super();
    this.guards.push(this.mobileUserSessionGuard);
    this.guards.push(this.roverSessionGuard);
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    for (const guard of this.guards) {
      try {
        const result = await guard.canActivate(context);
        if (result) {
          return true; // 任意一个守卫通过，立即返回true
        }
      } catch (error) {
        // 忽略当前守卫的错误，继续尝试下一个
        continue;
      }
    }
    return false; // 所有守卫都未通过
  }
}
