import {
  ConflictException,
  ExecutionContext,
  ForbiddenException,
  HttpException,
  Injectable,
  InternalServerErrorException,
  UnauthorizedException,
} from '@nestjs/common';
import { SessionGuardV2 } from '@kezhaozhao/saas-auth';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Logger } from 'log4js';
import { RoverExceptions } from '@commons/constants/exceptionConstants';
import { Observable } from 'rxjs';
import isPromise from 'is-promise';
import { Product, ProductAccessDeniedException } from '@kezhaozhao/saas-bundle-service';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@core/config/config.service';
import { Cacheable } from '@type-cacheable/core';
import { API_BASE } from '@domain/constants/common';
import * as moment from 'moment';
import { UserService } from '@modules/user-access-management/user/user.service';
import { ThrottleAxiosRequest } from '@kezhaozhao/qcc-common-utils';

@Injectable()
export class RoverSessionGuard extends SessionGuardV2 {
  readonly throttleInstance: ThrottleAxiosRequest;
  private readonly logger1: Logger = QccLogger.getLogger(RoverSessionGuard.name);

  constructor(protected readonly httpService: HttpService, protected readonly configService: ConfigService, protected readonly userService: UserService) {
    super();
    this.throttleInstance = ThrottleAxiosRequest.getInstance(httpService.axiosRef);
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const httpContext = context.switchToHttp();
    const request = httpContext.getRequest();
    const from = request.headers['x-kzz-request-from'];
    if (from == 'qcc-rover-mobile') {
      // 移动端请求，不进行权限校验
      return false;
    }
    try {
      let result: any = super.canActivate(context);

      if (result instanceof Observable) {
        result = result.toPromise();
      }
      if (isPromise(result)) {
        result = await result;
      }
      if (!result) {
        return false;
      }

      let bundleError;
      if (!request.user.currentOrg) {
        bundleError = RoverExceptions.UserRelated.Auth.OrgNotCreated;
        // 用户无当前组织
        if (request.url == `${API_BASE}/profile`) {
          Object.assign(request.user, { bundleError });
          return true;
        }
        // 未创建组织
        throw new ForbiddenException(bundleError);
      } else {
        const requestOrg = request.headers['x-kzz-orgid'];
        if (requestOrg && requestOrg != request.user.currentOrg) {
          throw new ConflictException(RoverExceptions.UserRelated.Auth.OrgNotMatch);
        }

        const { currentOrg: orgId, userId, isOwner, departments } = request.user;
        const bundle = await this.userService.getUserBundle(userId, orgId, departments?.[0], 0);
        if (!bundle?.mainBundleId) {
          // 未开通套餐
          if (isOwner) {
            // 当前是组织拥有者
            bundleError = RoverExceptions.UserRelated.Auth.ApplicationNotFound;
          } else {
            // 当前是组织成员
            bundleError = RoverExceptions.UserRelated.User.ApplicationForbidden;
          }
        } else if (moment().isAfter(moment(bundle.endDate).endOf('day'))) {
          // 有套餐，套餐已过期
          bundleError = RoverExceptions.UserRelated.User.ApplicationExpired;
        } else {
          const [permissions, depUsers] = await Promise.all([
            this.userService.fetchUserPermissionsV2(orgId, userId),
            this.userService.fetchDepUsers(orgId, departments[0]),
          ]);
          if (bundle.bundles[0]?.paramsSetting?.modules && bundle.bundles[0]?.bundle?.paramsSetting) {
            bundle.bundles[0].bundle.paramsSetting.modules = bundle.bundles[0].paramsSetting.modules;
          }
          // 套餐正常
          Object.assign(request.user, {
            bundle,
            permissions: permissions.map((x) => x.permissionId),
            permissionScopes: permissions,
            depUserIds: depUsers.map((x) => x.userId), // 部门及子部门的用户id
          });
          // const [resources, permissions] = await this.fetchUserPermissions(orgId, userId);
          // request.user.resources = resources;
        }

        if (bundleError) {
          if ([`${API_BASE}/profile`, `${API_BASE}/account/org/list`, `${API_BASE}/account/changeCurrentOrg`].includes(request.url)) {
            Object.assign(request.user, { bundleError });
          } else {
            throw new ForbiddenException(bundleError);
          }
        }
      }

      return true;
    } catch (err) {
      if (err instanceof HttpException) {
        if (err instanceof ProductAccessDeniedException) {
          if (request?.user?.userId) {
            //获取到userId，说明用户已经存在，可能是没有被赋予产品的使用权限
            throw new ForbiddenException(RoverExceptions.UserRelated.User.ApplicationForbidden);
          } else {
            //用户不在某个组织中
            throw new ForbiddenException(RoverExceptions.UserRelated.User.OrphanUser);
          }
        }
        if (err.getResponse()?.['internalMessage'] == 'Unauthorized') {
          // saas-auth 与 rover nest 版本不同步导致的问题兼容
          throw new UnauthorizedException(RoverExceptions.UserRelated.User.GetUserBundleInfoFailed);
        }
        throw err;
      } else {
        let message = err.message;
        if (err?.response?.data) {
          message = JSON.stringify(err.response.data);
        }
        this.logger1.error('RoverSessionGuard error ', err);
        this.logger1.error(err);
        throw new InternalServerErrorException({
          ...RoverExceptions.UserRelated.User.GetUserOrgInfoFailed,
          internalMessage: message,
        });
      }
    }
  }

  async formatUser(reqUser) {
    const phone = reqUser.phone || '';
    if (reqUser.guid) {
      const user = await this.httpService.axiosRef
        .get(`${this.configService.kzzServer.authService}getUserByGuid/guid/${reqUser.guid}/phone/${phone}`)
        .then((resp) => resp.data);
      return Object.assign(reqUser, user);
    }
    return reqUser;
  }

  async exchangeUser(reqUser) {
    // const phone = reqUser.phone || '';
    const user = await this.httpService.axiosRef
      .post(`${this.configService.kzzServer.enterpriseService}/internal/auth/user/info`, {
        guid: reqUser.guid,
        phone: reqUser.phone,
        productCode: Product.Rover,
      })
      .then((resp) => resp.data);
    if (null == user) {
      throw new ForbiddenException(RoverExceptions.UserRelated.User.OrphanUser);
    }
    Object.assign(reqUser, user);
    return reqUser;
  }

  @Cacheable({ ttlSeconds: 15, cacheKey: (args: any[]) => `cache:fetchUserPermissions:${args[0]}:${args[1]}` })
  async fetchUserPermissions(orgId: number, userId: number): Promise<number[]> {
    // const phone = reqUser.phone || '';
    return await this.httpService.axiosRef
      .post(`${this.configService.kzzServer.enterpriseService}/internal/auth/user/permissions`, {
        orgId,
        userId,
        // productCode: Product.Rover,
      })
      .then((resp) => resp.data);
  }

  /**
   *
   * @param userId
   * @param orgId
   * @param isOwner
   */
}
