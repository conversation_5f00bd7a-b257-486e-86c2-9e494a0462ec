# 安全守卫模块 (Guards)

## 📝 模块概述

安全守卫模块提供了系统级的认证、授权和流量控制守卫。基于 NestJS Guards 机制，实现了 JWT 身份验证、用户会话管理、API 资源权限校验以及针对特定场景（如 Rover 系统、移动端）的访问控制。该模块是系统安全防线的重要组成部分，确保请求的合法性和安全性。

生成时间：2026-01-19 10:00:00

## 📖 文档导航

- **当前文档**: 模块概览 - 快速了解模块功能

## 🎯 核心功能

- **OpenApiJwtGuard**: 核心 JWT 认证守卫。负责解析 Token、验证用户身份、检查用户套餐（Bundle）有效性以及校验 API 资源权限。
- **MobileUserSessionGuard**: 专用于移动端用户的会话认证守卫。
- **RoverSessionGuard**: Rover 系统的会话守卫，处理特定的会话逻辑。
- **ThrottlerGuards**: 包含 `IpThrottlerGuard`, `UserThrottlerGuard`, `OpenApiUserThrottlerGuard`，配合限流模块进行请求频率控制。
- **AnyGuard**: 通用守卫，用于灵活的组合验证策略。

## 📁 模块结构

- `guard.module.ts`: 模块定义，导出各类守卫。
- `openapi.jwt.guard.ts`: OpenAPI JWT 认证核心逻辑，集成 UserService 和 ConfigService。
- `App.guard.ts`: 应用级守卫。
- `*.guard.ts`: 其他特定场景的守卫实现。

## 🔗 核心依赖

- **JwtService**: 用于 JWT Token 的验证和解析。
- **UserService**: 获取用户详细信息和套餐状态。
- **ConfigService**: 获取系统配置（如 JWT 密钥）。
- **HttpService**: 用于记录登录日志等副作用操作。
- **TypeORM**: 访问 `OpenApiResourceEntity` 等权限资源数据。

## 🚀 快速示例

```typescript
// 在 Controller 或 Method 上使用守卫
import { UseGuards } from '@nestjs/common';
import { OpenApiJwtGuard } from '@core/guards/openapi.jwt.guard';

@Controller('api/v1/company')
export class CompanyController {

  @UseGuards(OpenApiJwtGuard)
  @Get('info')
  async getInfo() {
    // 只有通过 JWT 认证且有权限的用户才能访问
    return 'Company Info';
  }
}
```

## 📚 全局文档导航

### ⬆️ 上级导航

- **[核心模块层总览](../README.md)** - 返回核心模块层概览
- **[技术架构详解](../../README.md)** - 查看系统整体架构设计

### ➡️ 同级子模块

- **[Rate Limiter 模块](../rate-limiter/README.md)** - 限流服务
- **[Cache 模块](../cache/README.md)** - 缓存服务
- **[Config 模块](../config/README.md)** - 配置服务

---

> 为系统提供全方位的身份认证与访问控制能力。
