import { ExecutionContext, Injectable, Inject, UnauthorizedException } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { SessionGuard } from '@kezhaozhao/saas-auth';
import { ThrottleAxiosRequest } from '@kezhaozhao/qcc-common-utils';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { ConfigService } from '@core/config/config.service';

@Injectable()
export class MobileUserSessionGuard extends SessionGuard {
  readonly throttleInstance: ThrottleAxiosRequest;
  protected readonly logger: Logger = QccLogger.getLogger(MobileUserSessionGuard.name);

  @Inject()
  protected readonly configService: ConfigService;

  @InjectEntityManager()
  protected readonly entityManager: EntityManager;

  @Inject()
  protected readonly redisService: RedisService;

  constructor(private readonly httpService: HttpService) {
    super();
    this.throttleInstance = ThrottleAxiosRequest.getInstance(this.httpService.axiosRef);
  }

  async canActivate(context: ExecutionContext) {
    const httpContext = context.switchToHttp();
    const request = httpContext.getRequest();
    const from = request.headers['x-kzz-request-from'];
    try {
      const result: any = await super.canActivate(context); // 父类已经验证了token，获取到了 guid,下面进行账号信息转换
      if (result) {
        if (from !== 'qcc-rover-mobile' || request.user.currentOrg) {
          return false;
        }
        if (!request?.user?.guid) return false;
        const refreshCache = request?.path?.startsWith('/rover/profile') ? 1 : 0;
        const user = await this.formatOrgUser(request.user, refreshCache);
        request.user = user;
      }
      return result;
    } catch (error) {
      const guid = request.headers['guid'];
      if (!guid) return false;
      if (
        from == 'qcc-rover-mobile-qa-test' &&
        ['5e8e6102f7260e8b0a1bccbbc27122a0', 'c1fc6162958705a674ff1f63c6607cb6', '3f55e35c4671ef24d48ff8e911b94830'].includes(guid)
      ) {
        const refreshCache = request?.path?.startsWith('/rover/profile') ? 1 : 0;
        const user = await this.formatOrgUser({ guid }, refreshCache);
        request.user = user;
        return true;
      }
    }
    return false;
  }

  /**
   * profile专用
   * @param reqUser
   * @param refreshCache
   */
  async formatOrgUser(reqUser, refreshCache?: number) {
    if (reqUser.guid) {
      // 登陆用户
      if (reqUser?.isSeoSpider) {
        reqUser.isLogin = 0;
      } else {
        reqUser.isLogin = 1;
      }
      const cacheKey = `mobileUser:${reqUser.guid}`;
      let cacheString = await this.redisService.getClient().get(cacheKey);
      if (cacheString) {
        // 有缓存
        if (refreshCache) {
          this.throttleInstance
            .request({
              options: {
                method: 'GET',
                url: `${this.configService.kzzServer.authService}rover/getUser/guid/${reqUser.guid}`,
              },
              cacheTTL: 5000, // 缓存5秒
            })
            .then((resp) => {
              const responseData = resp.data;
              if (!responseData?.currentOrg) {
                throw new UnauthorizedException({
                  code: 401,
                  error: 'Unauthorized',
                });
              }
              this.redisService
                .getClient()
                .set(cacheKey, JSON.stringify(responseData), 'EX', 3600)
                .catch((err1) => {
                  this.logger.error(err1);
                });
            })
            .catch((err) => {
              this.logger.error(err);
              throw new UnauthorizedException({
                code: 401,
                error: 'Unauthorized',
              });
            });
          cacheString = await this.redisService.getClient().get(cacheKey);
        }
        const user = JSON.parse(cacheString);
        reqUser = Object.assign(reqUser, user);
      } else {
        const user = await this.throttleInstance
          .request({
            options: {
              method: 'GET',
              url: `${this.configService.kzzServer.authService}rover/getUser/guid/${reqUser.guid}`,
            },
            cacheTTL: 5000, // 缓存5秒
          })
          .then((resp) => resp.data);
        this.redisService
          .getClient()
          .set(cacheKey, JSON.stringify(user), 'EX', 3600)
          .catch((err1) => {
            this.logger.error(err1);
            throw new UnauthorizedException({
              code: 401,
              error: 'Unauthorized',
            });
          });
        reqUser = Object.assign(reqUser, user);
      }
    } else {
      return null;
    }
    return reqUser;
  }

  /**
   * 获取用户的uidHash
   * @param reqUser
   */
  async getUidHash(reqUser) {
    if (reqUser.guid && !reqUser.uidHash && !reqUser?.isSeoSpider) {
      const guid = reqUser.guid;
      const cacheKey = `uidHash:${guid}`;
      const uidHash = await this.redisService.getClient().get(cacheKey);
      if (uidHash) {
        // 有缓存
        reqUser.uidHash = uidHash;
      } else {
        // 没有缓存
        try {
          const uidInfo = await this.throttleInstance
            .request({
              options: {
                method: 'POST',
                url: `${this.configService.server.saasService}/user/getUserInfoByUserIdLite`,
                data: { userId: guid },
              },
              cacheTTL: 5000, // 缓存5秒
            })
            .then((resp) => resp.data);
          if (uidInfo?.uidHash) {
            reqUser.uidHash = uidInfo.uidHash;
            this.redisService
              .getClient()
              .set(cacheKey, uidInfo.uidHash, 'EX', 3600)
              .catch((err) => {
                this.logger.error(err);
              });
          }
        } catch (uiderror) {
          this.logger.error(uiderror);
        }
      }
    }
  }
}
