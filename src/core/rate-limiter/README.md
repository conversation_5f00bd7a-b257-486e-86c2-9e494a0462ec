# 速率限制模块 (Rate Limiter)

## 📝 模块概述

核心限流服务模块，基于 Redis 和数据库配置实现分布式限流。采用令牌桶算法（Token Bucket），支持多维度（组织、限流类型）的流量控制。该模块不仅支持基础的限流检查，还支持基于时间段的动态限流配置、请求耗时预估以及限流监控数据统计，能够有效保护系统资源，防止过载。

生成时间：2026-01-19 10:00:00

## 📖 文档导航

- **当前文档**: 模块概览 - 快速了解模块功能
- **[架构设计](doc/rate-limit-design.md)** - 理解技术实现与设计思路
- **[架构生成](doc/rate-limit-architechture-generation.md)** - 架构生成相关文档

## 🎯 核心功能

- **分布式限流**: 基于 Redis 实现的令牌桶算法，支持集群环境下的精准限流。
- **动态限流配置**: 支持分时段配置不同的限流阈值（如工作日/非工作日、闲时/忙时）。
- **请求耗时预估**: `estimateCostTime` 方法可根据当前令牌数和生成速率，精准预估请求所需的等待时间，支持跨天、跨时间段计算。
- **限流监控**: `getMonitoringData` 提供实时的限流器状态监控，包括当前令牌数、消耗速率、超限次数等。
- **多级策略**: 支持全局限制、突发容量（Burst Capacity）和预热机制（Initial Fill Ratio）。

## 📁 模块结构

- `rate-limiter.service.ts`: 核心服务类，封装限流逻辑、配置加载和监控统计。
- `model/RateLimiter.ts`: 限流器实体类，实现令牌桶算法的核心逻辑。
- `model/RateLimiterTypeEnums.ts`: 定义各类限流场景枚举（如 OpenApi 批量调用、尽调队列等）。
- `doc/`: 包含架构设计和生成相关文档。

## 🔗 核心依赖

- **RedisService**: 用于存储令牌桶状态，保证分布式环境下的数据一致性。
- **TypeORM**: 用于加载 `RateLimitConfigEntity` (配置) 和 `RateLimitExceededHistoryEntity` (超限记录)。
- **RateLimiterException**: 模块定义的特定异常类。

## 🚀 快速示例

```typescript
// 注入 RateLimiterService
constructor(private readonly rateLimiterService: RateLimiterService) {}

// 获取限流器实例并消费令牌
async checkLimit(orgId: number) {
  const limiter = await this.rateLimiterService.getLimiter(orgId, RateLimiterTypeEnums.Openapi_Sync_Limit);
  // 尝试消费 1 个令牌
  const allowed = await limiter.tryAcquire(1);
  if (!allowed) {
    throw new Error('Rate limit exceeded');
  }
}

// 预估耗时
async estimate(orgId: number, tokens: number) {
  const waitSeconds = await this.rateLimiterService.estimateCostTime(orgId, RateLimiterTypeEnums.Diligence_Queue_L1, tokens);
  console.log(`Need to wait ${waitSeconds} seconds`);
}
```

## 📚 全局文档导航

### ⬆️ 上级导航

- **[核心模块层总览](../README.md)** - 返回核心模块层概览
- **[技术架构详解](../../README.md)** - 查看系统整体架构设计

### ➡️ 同级子模块

- **[Cache 模块](../cache/README.md)** - 缓存服务
- **[Config 模块](../config/README.md)** - 配置服务
- **[Guards 模块](../guards/README.md)** - 安全守卫
- **[Monitor 模块](../monitor/README.md)** - 监控服务

---

> 提供高性能、可配置的分布式限流能力，保障系统稳定性。
