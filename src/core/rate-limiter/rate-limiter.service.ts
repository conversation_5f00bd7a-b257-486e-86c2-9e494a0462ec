import { RedisService } from '@kezhaozhao/nestjs-redis';
import { RateLimiter } from './model/RateLimiter';
import { Injectable } from '@nestjs/common';
import { RateLimiterTypeEnums } from './model/RateLimiterTypeEnums';
import Redis from 'ioredis';
import { In, MoreThanOrEqual, Repository } from 'typeorm';
import { RateLimitConfigEntity } from '@domain/entities/RateLimitConfigEntity';
import { InjectRepository } from '@nestjs/typeorm';
import { RateLimitExceededHistoryEntity } from '@domain/entities/RateLimitExceededHistoryEntity';
import { RateLimiterException } from './model/RateLimiterException';

/**
 * 监控数据接口
 */
export interface RateLimiterMonitoringData {
  orgId: number;
  limiters: {
    limiterType: RateLimiterTypeEnums;
    currentTokens: number;
    limit: number;
    ttlSeconds: number;
    tokenRate: number;
    exceededCount: number;
  }[];
}

@Injectable()
export class RateLimiterService {
  private readonly redisClient: Redis;
  private readonly defaultLimiterConfigs: RateLimitConfigEntity[] = [
    {
      orgId: -1,
      id: -1,
      orgName: 'default',
      limiterType: RateLimiterTypeEnums.Openapi_Batch_Limit,
      defaultTokenPerRequest: 1,
      globalLimit: 40,
      ttlSeconds: 300,
      burstCapacity: 10,
      maxTokensPerRequest: 5,
      warningThreshold: 2,
      initialFillRatio: 0.2,
      dynamicLimits: [
        {
          hourStart: 0,
          hourEnd: 8,
          limit: 80,
        },
        {
          hourStart: 8,
          hourEnd: 18,
          limit: 40,
        },
        {
          hourStart: 18,
          hourEnd: 24,
          limit: 60,
        },
      ],
      description: '批量调用限流桶，用于控制 openapi 提交的批量调用频次',
    },
    {
      orgId: -1,
      id: -1,
      orgName: 'default',
      limiterType: RateLimiterTypeEnums.Openapi_Sync_Limit,
      defaultTokenPerRequest: 1,
      globalLimit: 100,
      ttlSeconds: 300,
      burstCapacity: 30,
      maxTokensPerRequest: 10,
      warningThreshold: 5,
      initialFillRatio: 0.2,
      dynamicLimits: [
        {
          hourStart: 0,
          hourEnd: 8,
          limit: 150,
        },
        {
          hourStart: 8,
          hourEnd: 18,
          limit: 100,
        },
        {
          hourStart: 18,
          hourEnd: 24,
          limit: 120,
        },
      ],
      description: '同步调用限流桶，用于控制 openapi 提交的同步调用频次',
    },
    {
      orgId: -1,
      id: -1,
      orgName: 'default',
      limiterType: RateLimiterTypeEnums.Openapi_Common_Sync_Limit,
      defaultTokenPerRequest: 1,
      globalLimit: 1000,
      dynamicLimits: [],
      ttlSeconds: 300,
      burstCapacity: 30,
      maxTokensPerRequest: 10,
      warningThreshold: 5,
      initialFillRatio: 0.2,
      description: 'openapi 提交的普通数据库变更同步调用频次控制',
    },
    {
      orgId: -1,
      id: -1,
      orgName: 'default',
      limiterType: RateLimiterTypeEnums.Diligence_Queue_L0,
      defaultTokenPerRequest: 1,
      globalLimit: 150,
      dynamicLimits: [],
      ttlSeconds: 300,
      burstCapacity: 30,
      maxTokensPerRequest: 10,
      warningThreshold: 5,
      initialFillRatio: 0.2,
      description: '优先级最高的队列，一般可以用作 OpenApi 提交的异步的尽调',
    },
    {
      orgId: -1,
      id: -1,
      orgName: 'default',
      limiterType: RateLimiterTypeEnums.Diligence_Queue_L1,
      defaultTokenPerRequest: 1,
      globalLimit: 100,
      ttlSeconds: 300,
      burstCapacity: 20,
      maxTokensPerRequest: 5,
      warningThreshold: 3,
      initialFillRatio: 0.5,
      dynamicLimits: [
        {
          hourStart: 0,
          hourEnd: 8,
          limit: 150,
        },
        {
          hourStart: 8,
          hourEnd: 18,
          limit: 50,
        },
        {
          hourStart: 18,
          hourEnd: 24,
          limit: 120,
        },
      ],
      description: '优先级第二的队列，可以用作风险年检',
    },
    {
      orgId: -1,
      id: -1,
      orgName: 'default',
      limiterType: RateLimiterTypeEnums.Diligence_Queue_L2,
      defaultTokenPerRequest: 1,
      globalLimit: 50,
      ttlSeconds: 300,
      burstCapacity: 10,
      maxTokensPerRequest: 3,
      warningThreshold: 2,
      initialFillRatio: 0.5,
      dynamicLimits: [
        {
          hourStart: 0,
          hourEnd: 8,
          limit: 50,
        },
        {
          hourStart: 8,
          hourEnd: 18,
          limit: 10,
        },
        {
          hourStart: 18,
          hourEnd: 24,
          limit: 30,
        },
      ],
      description: '优先级第三，普通批量排查，主要是区分闲时和忙时设置动态的并发',
    },
  ];
  constructor(
    private readonly redisService: RedisService,
    @InjectRepository(RateLimitConfigEntity) private readonly rateLimitConfigRepository: Repository<RateLimitConfigEntity>,
    @InjectRepository(RateLimitExceededHistoryEntity) private readonly rateLimitExceededHistoryRepository: Repository<RateLimitExceededHistoryEntity>,
  ) {
    this.redisClient = this.redisService.getClient();
  }

  async getLimitConfig(orgId: number, limiterType: RateLimiterTypeEnums): Promise<RateLimitConfigEntity> {
    const configs: RateLimitConfigEntity[] = await this.rateLimitConfigRepository.find({
      where: {
        orgId: In([orgId, -1]),
        limiterType,
      },
    });
    let config = configs.find((c) => c.orgId === orgId);
    if (!config) {
      config = configs.find((c) => c.orgId === -1);
    }
    if (!config) {
      config = this.defaultLimiterConfigs.find((c) => c.limiterType === limiterType);
    }
    if (!config) {
      throw new RateLimiterException(`orgId=${orgId}, limiterType=${limiterType} 未找到限流配置`);
    }
    return config;
  }

  async getLimiter(orgId: number, limiterType: RateLimiterTypeEnums): Promise<RateLimiter> {
    const key = `limiter:${orgId}:${limiterType}`;

    const catchLimiter = await this.redisClient.get(key);
    if (catchLimiter) {
      const options = JSON.parse(catchLimiter);
      return new RateLimiter(options, this.redisClient);
    }
    // 创建新实例
    const config: RateLimitConfigEntity = await this.getLimitConfig(orgId, limiterType);
    const options = {
      orgId,
      limiterType,
      globalLimit: config.globalLimit,
      dynamicLimits: config.dynamicLimits,
      tokensPerRequest: config.defaultTokenPerRequest,
      ttlSeconds: config.ttlSeconds || 300,
      // burstCapacity: config.burstCapacity,
      maxTokensPerRequest: config.maxTokensPerRequest,
      warningThreshold: config.warningThreshold,
      initialFillRatio: config.initialFillRatio,
    };
    const limiter = new RateLimiter(options, this.redisClient);
    // 缓存实例
    await this.redisClient.setex(key, 300, JSON.stringify(options));
    return limiter;
  }

  /**
   * 估计请求的耗时
   * 需要根据 org 和 rateLimiterType 获取到限流器在不同时间段的限制，结合 dynamicLimits 和 globalLimit 预测指定的token在未来多长时间能产生出来
   * @param orgId 组织ID
   * @param rateLimiterType 限流器类型
   * @param tokens 请求的令牌数
   * @returns 估计的耗时（秒）
   */
  async estimateCostTime(orgId: number, rateLimiterType: RateLimiterTypeEnums, tokens: number): Promise<number> {
    // 如果令牌数为0，直接返回0
    if (tokens <= 0) {
      return 0;
    }

    // 获取限流配置
    const config = await this.getLimitConfig(orgId, rateLimiterType);
    const ttlSeconds = config.ttlSeconds || 300;

    // 当前时间
    const now = new Date();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    const currentSecond = now.getSeconds();

    // 计算当前的令牌生产率（每秒产生的令牌数）
    let currentLimit = config.globalLimit;

    // 如果存在动态限制，则根据当前时间获取对应的限制
    if (config.dynamicLimits && config.dynamicLimits.length > 0) {
      const dynamicLimit = config.dynamicLimits.find((limit) => {
        // 处理跨天的情况
        if (limit.hourEnd <= limit.hourStart) {
          // 跨天时间段，如 22-2
          return currentHour >= limit.hourStart || currentHour < limit.hourEnd;
        } else {
          // 普通时间段，如 9-18
          return currentHour >= limit.hourStart && currentHour < limit.hourEnd;
        }
      });

      if (dynamicLimit) {
        currentLimit = dynamicLimit.limit;
      }
    }

    // 计算每秒生产的令牌数
    const tokensPerSecond = currentLimit / ttlSeconds;

    // 预估需要消耗的令牌
    let remainingTokens = tokens;
    let estimatedSeconds = 0;

    // 如果不存在动态限制，或者只有一个时间段，则直接计算
    if (!config.dynamicLimits || config.dynamicLimits.length <= 1) {
      return Math.ceil(remainingTokens / tokensPerSecond);
    }

    // 对于多个时间段的情况，需要计算跨时间段的生产时间
    // 首先计算当前时间段剩余时间内能产生的令牌数
    const sortedLimits = [...config.dynamicLimits].sort((a, b) => {
      // 处理跨天的情况
      const aStart = a.hourStart;
      const bStart = b.hourStart;
      return aStart - bStart;
    });

    // 找到当前所处的时间段
    const currentTimeSlot = sortedLimits.find((limit) => {
      // 处理跨天的情况
      if (limit.hourEnd <= limit.hourStart) {
        // 跨天时间段，如 22-2
        return currentHour >= limit.hourStart || currentHour < limit.hourEnd;
      } else {
        // 普通时间段，如 9-18
        return currentHour >= limit.hourStart && currentHour < limit.hourEnd;
      }
    });

    if (currentTimeSlot) {
      // 计算当前时间段的结束时间（秒数）
      let currentSlotEndSeconds;
      if (currentTimeSlot.hourEnd <= currentTimeSlot.hourStart) {
        // 跨天时间段
        if (currentHour >= currentTimeSlot.hourStart) {
          // 当前时间在开始时间之后（如 23点）
          currentSlotEndSeconds = (currentTimeSlot.hourEnd + 24 - currentHour) * 3600 - currentMinute * 60 - currentSecond;
        } else {
          // 当前时间在结束时间之前（如 1点）
          currentSlotEndSeconds = (currentTimeSlot.hourEnd - currentHour) * 3600 - currentMinute * 60 - currentSecond;
        }
      } else {
        // 普通时间段
        currentSlotEndSeconds = (currentTimeSlot.hourEnd - currentHour) * 3600 - currentMinute * 60 - currentSecond;
      }

      // 当前时间段内能产生的令牌数
      const tokensInCurrentSlot = currentSlotEndSeconds * tokensPerSecond;

      if (tokensInCurrentSlot >= remainingTokens) {
        // 如果当前时间段内能产生足够的令牌，直接返回所需时间
        return Math.ceil(remainingTokens / tokensPerSecond);
      }

      // 减去当前时间段能产生的令牌
      remainingTokens -= tokensInCurrentSlot;
      estimatedSeconds += currentSlotEndSeconds;
    }

    // 循环计算后续时间段内的令牌生产，直到满足需求
    let nextHour = currentTimeSlot ? currentTimeSlot.hourEnd : currentHour;
    let loopCount = 0; // 防止无限循环

    while (remainingTokens > 0 && loopCount < 48) {
      // 最多计算两天
      loopCount++;

      // 获取下一个时间段
      const nextTimeSlot = sortedLimits.find((limit) => {
        // 处理跨天的情况
        if (limit.hourEnd <= limit.hourStart) {
          // 跨天时间段
          return nextHour >= limit.hourStart || nextHour < limit.hourEnd;
        } else {
          // 普通时间段
          return nextHour >= limit.hourStart && nextHour < limit.hourEnd;
        }
      });

      if (!nextTimeSlot) {
        // 如果找不到下一个时间段，使用全局限制
        const globalTokensPerSecond = config.globalLimit / ttlSeconds;
        return estimatedSeconds + Math.ceil(remainingTokens / globalTokensPerSecond);
      }

      // 计算该时间段的令牌生产率
      const slotLimit = nextTimeSlot.limit;
      const slotTokensPerSecond = slotLimit / ttlSeconds;

      // 计算该时间段的持续时间（秒）
      let slotDurationHours;
      if (nextTimeSlot.hourEnd <= nextTimeSlot.hourStart) {
        // 跨天时间段
        slotDurationHours = nextTimeSlot.hourEnd + 24 - nextTimeSlot.hourStart;
      } else {
        // 普通时间段
        slotDurationHours = nextTimeSlot.hourEnd - nextTimeSlot.hourStart;
      }
      const slotDurationSeconds = slotDurationHours * 3600;

      // 计算该时间段内能产生的令牌数
      const tokensInSlot = slotDurationSeconds * slotTokensPerSecond;

      if (tokensInSlot >= remainingTokens) {
        // 如果该时间段内能产生足够的令牌，计算所需时间并返回
        return estimatedSeconds + Math.ceil(remainingTokens / slotTokensPerSecond);
      }

      // 减去该时间段能产生的令牌
      remainingTokens -= tokensInSlot;
      estimatedSeconds += slotDurationSeconds;

      // 更新下一个小时
      nextHour = nextTimeSlot.hourEnd;
    }

    // 如果计算了两天还不够，使用全局限制来估算剩余时间
    const globalTokensPerSecond = config.globalLimit / ttlSeconds;
    return estimatedSeconds + Math.ceil(remainingTokens / globalTokensPerSecond);
  }

  /**
   * 获取监控数据
   * @param orgId 组织ID
   * @param limiterType 限流器类型（可选）
   * @returns 监控数据
   */
  async getMonitoringData(orgId: number, limiterType?: RateLimiterTypeEnums): Promise<RateLimiterMonitoringData> {
    const result: RateLimiterMonitoringData = {
      orgId,
      limiters: [],
    };

    // 确定要监控的限流器类型
    const limiterTypes = limiterType ? [limiterType] : Object.values(RateLimiterTypeEnums);

    for (const type of limiterTypes) {
      const limiter = await this.getLimiter(orgId, type);
      const config = await this.getLimitConfig(orgId, type);

      // 获取当前令牌数
      const { currentTokens, limit } = await limiter.getCurrentTokens();

      result.limiters.push({
        limiterType: type,
        currentTokens,
        limit,
        ttlSeconds: config.ttlSeconds,
        tokenRate: limit / config.ttlSeconds,
        exceededCount: await this.getExceededCount(orgId, type),
      });
    }

    return result;
  }

  /**
   * 获取超限次数
   * @param orgId 组织ID
   * @param limiterType 限流器类型
   * @returns 超限次数
   */
  private async getExceededCount(orgId: number, limiterType: RateLimiterTypeEnums): Promise<number> {
    // 统计最近24小时的超限记录
    const count = await this.rateLimitExceededHistoryRepository.count({
      where: {
        orgId,
        limiterType,
        createDate: MoreThanOrEqual(new Date(Date.now() - 24 * 300 * 300 * 1000)),
      },
    });

    return count;
  }
}
