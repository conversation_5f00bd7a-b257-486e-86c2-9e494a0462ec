import { Injectable } from '@nestjs/common';
import { getUrlPath } from '@commons/utils/string.utils';
import { ConfigService } from '@core/config/config.service';
import OSS from 'ali-oss';
import { createReadStream } from 'fs';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { OssService } from '@kezhaozhao/qcc-aliyun-utils';

@Injectable()
export default class MyOssService {
  private logger: Logger = QccLogger.getLogger(MyOssService.name);
  constructor(private readonly configService: ConfigService, private readonly ossService: OssService) {}

  signSingleUrl(p: string, preview = false): string {
    if (!p) return '';
    let pathname;
    if (p.startsWith('https')) {
      // 首先剥离正确的path
      pathname = getUrlPath(p);
    } else {
      pathname = p;
    }
    const options: OSS.SignatureUrlOptions = { expires: this.configService.server.oss.urlExpires };
    if (preview) {
      options.response = {
        'content-disposition': 'inline',
      };
    }
    return this.ossService.signatureUrl(pathname, options).replace(/-internal/gi, '');
  }

  signSingleUrlWithFileName(p: string, preview = false, fileName: string): string {
    if (!p) return '';
    let pathname;
    if (p.startsWith('https')) {
      // 首先剥离正确的path
      pathname = getUrlPath(p);
    } else {
      pathname = p;
    }
    const options: OSS.SignatureUrlOptions = { expires: this.configService.server.oss.urlExpires };
    if (preview) {
      options.response = {
        'content-disposition': 'inline',
      };
    }
    if (fileName) {
      options.response = {
        'content-disposition': `attachment; filename="${fileName}"; filename*=UTF-8''${fileName}`,
      };
    }
    return this.ossService.signatureUrl(pathname, options).replace(/-internal/gi, '');
  }

  async putSteam(ossObject: string, filepath: string, options?: any): Promise<string> {
    const result = await this.ossService.putStream(ossObject, createReadStream(filepath), options);
    // await this.ossService.putACL(result['name'], 'private');
    return result['name'];
  }

  async getObject(ossObject: string) {
    try {
      const res = await this.ossService.get(ossObject);
      return res?.content ? JSON.parse(res.content) : null;
    } catch (e) {
      this.logger.error(`oss 未找到对应的文件(可能还在生成中):${ossObject}`);
      this.logger.error(e);
      return null;
    }
  }
}
