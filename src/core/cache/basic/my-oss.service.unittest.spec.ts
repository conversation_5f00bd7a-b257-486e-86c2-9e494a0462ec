import { AppTestModule } from '@app/app.test.module';
import { ConfigService } from '@core/config/config.service';
import { OssService } from '@kezhaozhao/qcc-aliyun-utils';
import { BatchModule } from '@modules/batch/batch.module';
import { Test, TestingModule } from '@nestjs/testing';
import MyOssService from './my-oss.service';

describe('MyOssService 单元测试', () => {
  let service: MyOssService;
  let configService: ConfigService;
  let ossService: OssService;
  let module: TestingModule;

  beforeEach(async () => {
    module = module = await Test.createTestingModule({
      imports: [AppTestModule, BatchModule],
    }).compile();

    service = module.get<MyOssService>(MyOssService);
    configService = module.get<ConfigService>(ConfigService);
    ossService = module.get<OssService>(OssService);
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
  });

  describe('signSingleUrlWithFileName 方法', () => {
    it('当参数 p 为空字符串时，应该返回空字符串', () => {
      // Arrange
      const p = '';
      const preview = false;
      const fileName = 'test.txt';

      // Act
      const result = service.signSingleUrlWithFileName(p, preview, fileName);

      // Assert
      expect(result).toBe('');
    });

    it('当参数 p 为 null 时，应该返回空字符串', () => {
      // Arrange
      const p = null as any;
      const preview = false;
      const fileName = 'test.txt';

      // Act
      const result = service.signSingleUrlWithFileName(p, preview, fileName);

      // Assert
      expect(result).toBe('');
    });

    it('当参数 p 为 undefined 时，应该返回空字符串', () => {
      // Arrange
      const p = undefined as any;
      const preview = false;
      const fileName = 'test.txt';

      // Act
      const result = service.signSingleUrlWithFileName(p, preview, fileName);

      // Assert
      expect(result).toBe('');
    });

    it('当参数 p 为普通路径（不以https开头）时，应该正确处理', () => {
      // Arrange
      const p =
        'https://kezhaozhao-data.oss-cn-hangzhou.aliyuncs.com/pdf_generated/test/rover/2025-11-03/9c1d5440-b88e-11f0-abc1-c1ad34b34fde-1762158318.pdf?OSSAccessKeyId=LTAI5tDyoRfiuAXJAbk8qFVY&Expires=1762763140&Signature=h5LNCW9s7CX%2FIUTB35IikdpEX5s%3D';
      const preview = false;
      const fileName = '风险排查报告-北京车和家信息技术有限公司';

      // Act
      const result = service.signSingleUrlWithFileName(p, preview, fileName);

      // Assert
      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
      expect(result).not.toBe('');
      expect(result).not.toContain('-internal');
    });

    it('当参数 p 为完整的 https URL 时，应该正确提取路径并生成签名URL', () => {
      // Arrange
      const p = 'https://example.com/path/to/file.pdf';
      const preview = false;
      const fileName = 'test.pdf';

      // Act
      const result = service.signSingleUrlWithFileName(p, preview, fileName);

      // Assert
      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
      expect(result).not.toBe('');
      expect(result).not.toContain('-internal');
    });

    it('当 preview 参数为 true 时，应该设置 content-disposition 为 inline', () => {
      // Arrange
      const p = 'path/to/file.pdf';
      const preview = true;
      const fileName = 'test.pdf';

      // Act
      const result = service.signSingleUrlWithFileName(p, preview, fileName);

      // Assert
      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
      expect(result).not.toBe('');
    });

    it('当 preview 参数为 false 且提供了 fileName 时，应该设置 content-disposition 为 attachment', () => {
      // Arrange
      const p = 'path/to/file.pdf';
      const preview = false;
      const fileName = 'test.pdf';

      // Act
      const result = service.signSingleUrlWithFileName(p, preview, fileName);

      // Assert
      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
      expect(result).not.toBe('');
    });

    it('当同时提供 preview=true 和 fileName 时，fileName 应该覆盖 preview 的设置', () => {
      // Arrange
      const p = 'path/to/file.pdf';
      const preview = true;
      const fileName = 'test.pdf';

      // Act
      const result = service.signSingleUrlWithFileName(p, preview, fileName);

      // Assert
      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
      expect(result).not.toBe('');
    });

    it('当 fileName 为空字符串时，应该正常工作', () => {
      // Arrange
      const p = 'path/to/file.pdf';
      const preview = false;
      const fileName = '';

      // Act
      const result = service.signSingleUrlWithFileName(p, preview, fileName);

      // Assert
      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
    });

    it('当 fileName 为 null 时，应该正常工作', () => {
      // Arrange
      const p = 'path/to/file.pdf';
      const preview = false;
      const fileName = null as any;

      // Act
      const result = service.signSingleUrlWithFileName(p, preview, fileName);

      // Assert
      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
    });

    it('当 fileName 为 undefined 时，应该正常工作', () => {
      // Arrange
      const p = 'path/to/file.pdf';
      const preview = false;
      const fileName = undefined as any;

      // Act
      const result = service.signSingleUrlWithFileName(p, preview, fileName);

      // Assert
      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
    });

    it('当 fileName 包含中文字符时，应该正确处理 UTF-8 编码', () => {
      // Arrange
      const p = 'path/to/file.pdf';
      const preview = false;
      const fileName = '测试文件.pdf';

      // Act
      const result = service.signSingleUrlWithFileName(p, preview, fileName);

      // Assert
      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
      expect(result).not.toBe('');
    });

    it('当 fileName 包含特殊字符时，应该正确处理', () => {
      // Arrange
      const p = 'path/to/file.pdf';
      const preview = false;
      const fileName = 'test-file (1).pdf';

      // Act
      const result = service.signSingleUrlWithFileName(p, preview, fileName);

      // Assert
      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
      expect(result).not.toBe('');
    });

    it('当参数 p 为包含 internal 的 URL 时，应该移除 -internal 字符串', () => {
      // Arrange
      const p = 'path/to/file.pdf';
      const preview = false;
      const fileName = 'test.pdf';

      // Act
      const result = service.signSingleUrlWithFileName(p, preview, fileName);

      // Assert
      expect(result).toBeDefined();
      expect(result).not.toContain('-internal');
      expect(result).not.toContain('-INTERNAL');
    });

    it('当参数 p 为包含查询参数的 https URL 时，应该正确提取路径部分', () => {
      // Arrange
      const p = 'https://example.com/path/to/file.pdf?param=value&other=test';
      const preview = false;
      const fileName = 'test.pdf';

      // Act
      const result = service.signSingleUrlWithFileName(p, preview, fileName);

      // Assert
      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
      expect(result).not.toBe('');
    });

    it('当使用默认 preview 参数（false）时，应该正常工作', () => {
      // Arrange
      const p = 'path/to/file.pdf';
      const fileName = 'test.pdf';

      // Act
      const result = service.signSingleUrlWithFileName(p, undefined as any, fileName);

      // Assert
      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
    });
  });
});
