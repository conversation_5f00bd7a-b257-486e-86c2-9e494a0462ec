# ⚙️ 框架核心层业务域

> **生成时间：2026-01-19 12:00:00**

## 📝 概述

框架核心层是 QCC Rover Service 的基础设施业务域，专注于提供 NestJS 框架级别的通用组件和配置管理。该业务域封装了框架层面的复杂性，为上层业务模块提供统一的技术基础设施，包括认证授权、缓存管理、限流控制、配置服务等核心能力。

作为六层分层架构中的核心层，该业务域承担着技术框架抽象的重要职责，通过依赖注入和模块化设计，为整个系统提供稳定、高性能的基础服务支撑。所有业务模块都依赖于此层提供的核心服务，形成了清晰的技术分层和依赖关系。

## 🎯 核心功能

- **认证授权管理**：JWT 认证、角色权限控制、API 密钥验证、会话管理
- **限流与安全控制**：基于令牌桶的多级限流、IP 限流、用户请求限流、安全防护
- **配置与服务管理**：统一配置中心、HTTP 工具服务、队列服务、安全服务
- **缓存基础设施**：OSS 对象存储、尽调历史缓存、批量缓存处理
- **数据库基础设施**：数据迁移管理、数据种子初始化
- **框架组件增强**：装饰器、过滤器、自定义守卫组件

## 📁 子模块结构

```
core/
├── rate-limiter/              # 限流控制模块 - 基于令牌桶的多级限流系统
├── config/                    # 配置管理模块 - 统一配置服务和工具服务
├── guards/                    # 守卫组件模块 - 认证授权和安全控制
├── cache/basic/               # 基础缓存模块 - OSS服务和缓存助手
├── decorators/                # 装饰器模块 - 应用和权限装饰器
├── filters/                   # 过滤器模块 - 异常处理过滤器
├── database/                  # 数据库模块 - 迁移和种子数据管理
├── constants/                 # 常量模块 - 框架常量定义
├── interceptors/              # 拦截器模块 - 请求响应拦截处理
├── queue/                     # 队列模块 - 异步任务队列管理
└── search/                    # 搜索模块 - 搜索引擎集成
```

## 🔗 子模块介绍

### 🚦 [限流控制模块](./rate-limiter/README.md)

**核心职责**：基于令牌桶算法的分布式限流控制系统

- **多级限流队列**：支持 L0/L1/L2 优先级队列，适应不同业务场景
- **动态限流配置**：支持基于时间段的动态限流规则调整
- **组织级别隔离**：每个组织独立的限流配置和令牌桶管理
- **Redis 分布式支持**：基于 Redis 实现集群环境下的分布式限流

### ⚙️ [配置管理模块](./config/)

**核心职责**：统一配置管理和基础服务提供

- **统一配置服务**：环境配置、阿里云服务配置、第三方服务配置管理
- **HTTP 工具服务**：封装 HTTP 请求、响应处理、错误重试机制
- **批量缓存助手**：提供批量缓存操作、缓存策略管理
- **安全服务**：提供加密解密、签名验证等安全功能
- **队列服务**：异步任务队列的统一管理和调度

### 🛡️ [守卫组件模块](./guards/README.md)

**核心职责**：系统安全认证和访问控制

- **API 认证守卫**：Rover API 接口的统一认证验证
- **JWT 认证管理**：OpenAPI JWT 令牌验证和用户身份认证
- **角色权限控制**：基于角色的细粒度权限验证机制
- **限流守卫组件**：IP 限流、用户请求频率限制
- **会话管理守卫**：用户会话状态验证和管理

### 💾 [基础缓存模块](./cache/basic/)

**核心职责**：对象存储和缓存数据管理

- **OSS 对象存储服务**：阿里云 OSS 文件上传、下载、管理
- **尽调历史缓存助手**：企业尽调数据的缓存策略和管理

### 🎨 [装饰器模块](./decorators/)

**核心职责**：框架增强装饰器组件

- **应用装饰器**：应用级别的元数据装饰和标注
- **套餐权限装饰器**：基于套餐的权限控制装饰器

### 🔧 [过滤器模块](./filters/)

**核心职责**：异常处理和错误过滤

- **守卫异常过滤器**：统一处理守卫组件抛出的异常

### 💿 [数据库模块](./database/)

**核心职责**：数据库基础设施管理

- **数据迁移管理**：数据库结构变更的版本控制
- **数据种子初始化**：系统初始化数据和测试数据管理

## 🔌 对外接口

### 服务导出

- **ConfigService**：全局配置服务，提供环境配置和服务配置访问
- **RateLimiterService**：限流服务，提供令牌桶限流控制能力
- **HttpUtilsService**：HTTP 工具服务，封装常用的 HTTP 请求操作
- **BatchCacheHelper**：批量缓存助手，提供批量缓存管理功能
- **SecurityService**：安全服务，提供加密解密和安全验证功能
- **QueueService**：队列服务，提供异步任务队列管理
- **MyOssService**：OSS 对象存储服务，提供文件存储操作
- **DiligenceHistoryCacheHelper**：尽调历史缓存助手

### 守卫组件

- **RoverApiGuard**：Rover API 认证守卫
- **OpenApiJwtGuard**：OpenAPI JWT 认证守卫
- **RoverRolesGuard**：角色权限守卫
- **RoverSessionGuard**：会话管理守卫
- **IpThrottlerGuard**：IP 限流守卫
- **UserThrottlerGuard**：用户限流守卫

## 📊 数据流程

```
业务请求 → 守卫验证 → 限流检查 → 配置加载 → 缓存查询 → 业务处理 → 响应过滤 → 返回结果
    ↓           ↓          ↓          ↓          ↓          ↓          ↓
认证授权     令牌桶     配置服务    缓存服务    数据处理    异常过滤    响应格式化
```

## 🔧 技术特点

- **模块化架构设计**：每个功能模块独立封装，支持按需加载和组合使用
- **分布式限流支持**：基于 Redis 的令牌桶算法，支持集群环境下的精确限流
- **统一配置管理**：类型安全的配置服务，支持环境变量和动态配置
- **安全防护机制**：多层次的安全验证，包括认证、授权、限流和异常处理
- **缓存策略优化**：提供多种缓存策略和批量操作，提升系统性能

## 📚 文档导航

### ⬆️ 上级导航

- **[技术架构详解](../../README.md)** - 查看六层分层架构设计

### ⬇️ 子模块导航

- **[限流控制模块](./rate-limiter/README.md)** - 基于令牌桶算法的分布式限流系统
- **[配置管理模块](./config/)** - 统一配置服务和基础工具服务集合
- **[守卫组件模块](./guards/)** - 认证授权和安全控制守卫组件
- **[基础缓存模块](./cache/basic/)** - OSS 对象存储和缓存数据管理服务
- **[装饰器模块](./decorators/)** - 应用和权限控制装饰器组件
- **[过滤器模块](./filters/)** - 异常处理和错误过滤组件
- **[数据库模块](./database/)** - 数据迁移和种子数据管理

### ➡️ 同级业务域

- **[业务模块层总览](../modules/README.md)** - 查看业务逻辑实现层
- **[共享服务层](../commons/README.md)** - 查看通用工具和基础类型定义
- **[领域实体层](../domains/README.md)** - 查看数据实体和业务对象定义

---

> 框架核心层：为整个系统提供稳定、高性能的基础设施支撑，确保业务模块专注于业务逻辑实现
