import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { AxiosRequestConfig } from 'axios';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from './config.service';
import * as crypto from 'node:crypto';
import { axiosErrorExtraData, BadParamsException, KzzErrorMessage } from '@kezhaozhao/qcc-exception-handler';
import { CommonExceptions } from '@kezhaozhao/search-utils';
import { retryWithDelay } from '@commons/utils/utils';
import { HttpQueryException } from '@commons/exceptions/HttpQueryException';

@Injectable()
export class HttpUtilsService {
  private readonly logger: Logger = QccLogger.getLogger(HttpUtilsService.name);

  constructor(private readonly configService: ConfigService, private readonly httpService: HttpService) {}

  public async postRequest<T>(url: string, data: T) {
    const requestParams: AxiosRequestConfig = {
      method: 'POST',
      url: url,
      data: data,
      headers: {
        'x-request-from-head-app-name': 'kzz-qcc-rover-service',
      },
    };
    // this.logger.info('url=' + url + '; request=' + JSON.stringify(data));
    // console.log('result=' + JSON.stringify(response));

    return await retryWithDelay(() => this.sendRequest(requestParams), 3, Math.floor(Math.random() * 5) + 1);
  }

  public async getRequest<T>(url: string, data: T) {
    const requestParams: AxiosRequestConfig = {
      method: 'GET',
      url: url,
      params: data,
      headers: {
        'x-request-from-head-app-name': 'kzz-qcc-rover-service',
      },
    };
    // console.log('url=' + url + '; request=' + JSON.stringify(data));
    // console.log('result=' + JSON.stringify(response));
    return await retryWithDelay(() => this.sendRequest(requestParams), 3, Math.floor(Math.random() * 5) + 1);
  }

  public sendRequest(requestParams: AxiosRequestConfig) {
    const url = requestParams.url;
    const dataServiceUrl = this.configService.proxyServer.dataService;
    if (url.startsWith(dataServiceUrl)) {
      // 请求数据服务添加token
      const timestamp = (new Date().getTime() / 1000).toString().split('.')[0];
      const tokenKey = 'cabae57f-d28e-4e5f-abf5-e0a88800ebd4';
      const fromApp = 'qcc-rover-service';
      const header = requestParams.headers;
      header['x-request-from-app-name'] = fromApp;
      header['x-request-timestamp'] = timestamp;
      header['x-request-access-token'] = crypto.createHash('md5').update(`${fromApp}${timestamp}${tokenKey}`).digest('hex');
    }
    return this.httpService.axiosRef
      .request(requestParams)
      .then((res) => {
        return res?.data;
      })
      .catch((error) => {
        // 不需要抛异常的接口处理，返回null或[]
        if (error.response?.data) {
          const data = error.response.data;
          if (data?.status == 400 && data?.message == '未找到风险详情') {
            // 获取风险监控动态详情接口未找到对应风险，会返回400，message为未找到风险详情
            return null;
          }
          if (data?.statusCode == 404 && data?.code == 900103) {
            // 招投标号码查询接口 api/search/company/${companyId}/contacts 未查到公司会返回404 ，data code 900103
            return [];
          }
        }
        const errorMessage = `url=${requestParams.url}, message=${error.message}`;
        this.logger.error(`errorInfo  requestParams: ${JSON.stringify(requestParams)}`);
        this.logger.error(error);
        if (error.response?.data) {
          const data = error.response.data;
          const kzzErrorMessage = Object.assign(new KzzErrorMessage(), {
            code: data.code,
            error: errorMessage,
            message: errorMessage,
          });
          throw new HttpQueryException(kzzErrorMessage);
        }
        throw new HttpQueryException({
          ...CommonExceptions.Common.QccService.Error,
          message: errorMessage,
          internalMessage: error.message,
          errorExtraData: axiosErrorExtraData(error),
        });
      });
  }
}
