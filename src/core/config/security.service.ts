import { ForbiddenException, Injectable } from '@nestjs/common';
import { InjectEntityManager } from '@nestjs/typeorm';
import { BaseEntity, EntityManager, FindOptionsWhere, In } from 'typeorm';
import { EntityTarget } from 'typeorm/common/EntityTarget';
import { AccessResourceDeniedException } from '@commons/exceptions/AccessResourceDeniedException';
import { RoverExceptions } from '@commons/constants/exceptionConstants';
import { PermissionByEnum, PermissionScopeEnum } from '@domain/enums/PermissionScopeEnum';
import { compact } from 'lodash';
import { BatchBusinessTypeEnums } from '@domain/enums/batch/BatchBusinessTypeEnums';
import { RoverUserModel } from '@domain/model/RoverUserModel';

@Injectable()
export class SecurityService {
  constructor(@InjectEntityManager() private readonly entityManager: EntityManager) {}

  /**
   * 资源必须属于同一个组织才能访问
   * @param entityClass
   * @param user
   * @param pkValues
   * @param pkName
   * @param permissionId
   * @param orgIdName
   * @param creatorName
   */
  async allowWithinOrg<Entity>(
    entityClass: EntityTarget<Entity>,
    user: RoverUserModel,
    pkValues: number[],
    pkName = 'id',
    permissionIds?: number[],
    creatorName = 'createBy',
    orgIdName = 'orgId',
  ) {
    if (compact(pkValues).length <= 0) {
      throw new AccessResourceDeniedException();
    }
    const condition = {
      [pkName]: In(pkValues),
      [orgIdName]: user.currentOrg,
    } as FindOptionsWhere<Entity>;
    if (permissionIds?.length > 0) {
      const { by, userIds } = this.checkScopeByPermissionIds(user, permissionIds);
      if (by == PermissionByEnum.USER) {
        condition[creatorName] = In(userIds);
      }
    }

    const n = await this.entityManager.count(entityClass, { where: condition });
    if (n != pkValues.length) {
      throw new AccessResourceDeniedException();
    }
  }

  public checkScope(user: RoverUserModel, permissionId: number) {
    const permissionScope = user.permissionScopes?.find((x) => x.permissionId == permissionId);
    if (!permissionScope) {
      throw new ForbiddenException(RoverExceptions.UserRelated.User.RoleForbidden);
    }
    if (permissionScope.scope == PermissionScopeEnum.ORG) {
      return { by: PermissionByEnum.ORG };
    } else if (permissionScope.scope == PermissionScopeEnum.DEPARTMENT) {
      return { by: PermissionByEnum.USER, userIds: user.depUserIds };
    } else {
      return { by: PermissionByEnum.USER, userIds: [user.userId] };
    }
  }

  public checkScopeByPermissionIds(user: RoverUserModel, permissionIds: number[]) {
    const permissionScope = user.permissionScopes?.find((x) => permissionIds.includes(x.permissionId));
    if (!permissionScope) {
      throw new ForbiddenException(RoverExceptions.UserRelated.User.RoleForbidden);
    }
    if (permissionScope.scope == PermissionScopeEnum.ORG) {
      return { by: PermissionByEnum.ORG };
    } else if (permissionScope.scope == PermissionScopeEnum.DEPARTMENT) {
      return { by: PermissionByEnum.USER, userIds: user.depUserIds };
    } else {
      return { by: PermissionByEnum.USER, userIds: [user.userId] };
    }
  }

  /**
   * 资源必须属于同一个用户才能访问
   * @param entityClass
   * @param user
   * @param pkValues
   * @param pkName
   * @param orgIdName
   */
  async allowWithinUser<Entity extends BaseEntity>(
    entityClass: EntityTarget<Entity>,
    user: RoverUserModel,
    pkValues: number[],
    pkName = 'id',
    userIdName = 'userId',
  ) {
    const n = await this.entityManager.count(entityClass, {
      where: {
        [pkName]: In(pkValues),
        [userIdName]: user.userId,
      } as FindOptionsWhere<Entity>,
    });
    if (n < 1) {
      throw new AccessResourceDeniedException();
    }
  }

  async getUserIds(db: string, user: RoverUserModel, userIdName = 'create_by', condition?: { column: string; value: string }): Promise<number[]> {
    const orgIdName = 'org_id';
    let querySql = `select distinct ${userIdName}
                    from ${db}
                    where ${orgIdName} = ${user.currentOrg} `;
    if (user.depUserIds?.length) {
      const userIds = user.depUserIds.map((d) => `${userIdName} = ${d}`).join(' or ');
      querySql += `and (${userIds})`;
    }
    if (condition) {
      querySql += ` and ${condition.column} = ${condition.value}`;
    }
    querySql += ` group by ${userIdName}`;
    const entities = await this.entityManager.query(querySql);
    if (entities?.length) {
      return entities?.map((e) => e?.[userIdName]) || [];
    }
    return [];
  }

  public getBatchPermissionSql(businessType: BatchBusinessTypeEnums, user: RoverUserModel) {
    let permissionIds = [];
    switch (businessType) {
      case BatchBusinessTypeEnums.Monitor_Company_Export:
        permissionIds = [2108];
        break;
      case BatchBusinessTypeEnums.Potential_Batch_Data:
      case BatchBusinessTypeEnums.Potential_Batch_Excel:
      case BatchBusinessTypeEnums.Potentail_Batch_Customer:
        permissionIds = [20074];
        break;
      case BatchBusinessTypeEnums.Specific_Diligence_File:
        permissionIds = [10194];
        break;
      case BatchBusinessTypeEnums.Specific_Record_List_Export:
      case BatchBusinessTypeEnums.Specific_Report_Export:
      case BatchBusinessTypeEnums.Specific_Batch_Export:
        permissionIds = [10191, 10195];
        break;
      case BatchBusinessTypeEnums.Diligence_File:
      case BatchBusinessTypeEnums.Diligence_ID:
        permissionIds = [2011];
        break;
      case BatchBusinessTypeEnums.Diligence_Customer:
      case BatchBusinessTypeEnums.Diligence_Customer_Analyze:
        permissionIds = [2014, 2071]; //风险巡检查看权限, 巡检分析查看权限
        break;
      case BatchBusinessTypeEnums.Customer_File:
      case BatchBusinessTypeEnums.Customer_ID:
        permissionIds = [2032];
        break;
      case BatchBusinessTypeEnums.Person_File:
        permissionIds = [2062];
        break;
      case BatchBusinessTypeEnums.Monitor_File:
        //合作监控添加权限
        permissionIds = [2102];
        break;
      case BatchBusinessTypeEnums.InnerBlacklist_File:
        permissionIds = [2042];
        break;
      case BatchBusinessTypeEnums.Diligence_Batch_Detail:
      case BatchBusinessTypeEnums.Dimension_Detail_Export:
        permissionIds = [2011];
        break;
      case BatchBusinessTypeEnums.Diligence_Record:
        permissionIds = [2022];
        break;
      case BatchBusinessTypeEnums.Person_Export:
        permissionIds = [2061];
        break;
      case BatchBusinessTypeEnums.InnerBlacklist_Export:
        permissionIds = [2041];
        break;
      case BatchBusinessTypeEnums.Diligence_Report_Export:
      case BatchBusinessTypeEnums.Diligence_Report_Batch_Export:
        permissionIds = [2002];
        break;
      case BatchBusinessTypeEnums.Customer_Export:
        permissionIds = [2031];
        break;
      case BatchBusinessTypeEnums.Tender_Detail_Export:
      case BatchBusinessTypeEnums.Tender_Export:
        permissionIds = [2136];
        break;
      case BatchBusinessTypeEnums.Bidding_Diligence_File:
        permissionIds = [2114];
        break;
      case BatchBusinessTypeEnums.Risk_Export:
        permissionIds = [2093];
        break;
      case BatchBusinessTypeEnums.Sentiment_Export:
        permissionIds = [2094];
        break;
      case BatchBusinessTypeEnums.Analyze_Record_Export:
      case BatchBusinessTypeEnums.Analyze_Dimension_Detail:
        permissionIds = [2015];
        break;
      case BatchBusinessTypeEnums.Tender_Report_Export:
      case BatchBusinessTypeEnums.Bundle_Bidding_Consume_detail_Export:
        //招标排查导出报告 2115
        permissionIds = [2115];
        break;
      case BatchBusinessTypeEnums.Tender_Diligence_Record_Export:
      case BatchBusinessTypeEnums.Tender_Dimension_Detail_Export:
      case BatchBusinessTypeEnums.Tender_All_Dimension_Detail_Export:
        permissionIds = [10134];
        break;
      case BatchBusinessTypeEnums.Tender_Diligence_History_Export:
        permissionIds = [2116];
        break;
      case BatchBusinessTypeEnums.Bundle_Diligence_Consume_detail_Export:
        permissionIds = [2021];
        break;
      case BatchBusinessTypeEnums.Bundle_Analyze_Record_Consume_detail_Export:
        permissionIds = [2014];
        break;
      case BatchBusinessTypeEnums.Bundle_Special_Consume_detail_Export:
        permissionIds = [10196];
        break;
    }
    try {
      const { by, userIds } = this.checkScopeByPermissionIds(user, permissionIds);
      if (by == PermissionByEnum.USER) {
        return `(batch.businessType = ${businessType} and batch.creatorId in (${userIds.join(',')}))`;
      } else {
        return `(batch.businessType = ${businessType})`;
      }
    } catch (error) {
      // 当前用户没有对应的 BatchBusiness权限时， 不抛异常，直接返回空
      return '';
    }
  }
}
