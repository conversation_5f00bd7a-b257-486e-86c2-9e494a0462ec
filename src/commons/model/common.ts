import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { ArrayNotEmpty, IsArray, IsNumber, IsOptional, <PERSON>, <PERSON> } from 'class-validator';

// import { UserBundleResponse } from '@kezhaozhao/saas-bundle-service/dist_client/model/response/UserBundleResponse';

export class AggsResponse {
  @ApiProperty({ description: '聚合字段' })
  fieldLabel: string;
  @ApiProperty({ description: '聚合字段值' })
  fieldValue: string | number;
  @ApiProperty({ description: '聚合字段数量' })
  count: number;
}

export class PaginationResponse {
  @ApiProperty({ type: Number, description: '当前多少页' })
  pageIndex: number;
  @ApiProperty({ type: Number, description: '每页多少条' })
  pageSize: number;
  @ApiProperty({ type: Number, description: '总共多少条数据' })
  total: number;
  @ApiProperty({ description: '列表' })
  data: any[];
  @ApiPropertyOptional({ description: 'es聚合返回的bucket' })
  aggs?: any;
}

export class PaginationQueryParams {
  @ApiProperty({
    required: false,
    type: Number,
    default: 10,
    example: 10,
    maximum: 100,
    description: '每页显示的记录数',
  })
  @IsNumber()
  @Min(1)
  @Max(100)
  @Type(() => Number)
  @IsOptional()
  pageSize = 10;

  @ApiProperty({ required: false, type: Number, default: 1, example: 1, maximum: 100, description: '页码，从1开始计数' })
  @IsNumber()
  @Min(1)
  @Max(100000)
  @Type(() => Number)
  @IsOptional()
  pageIndex = 1;
}

export class PaginationParams {
  @ApiProperty({
    required: false,
    type: Number,
    default: 5,
    example: 5,
    maximum: 100,
    description: '每页显示的记录数',
  })
  @IsNumber()
  @Min(1)
  @Max(100)
  @Type(() => Number)
  @IsOptional()
  pageSize? = 5;

  @ApiProperty({ required: false, type: Number, default: 1, example: 1, maximum: 100, description: '页码，从1开始计数' })
  @IsNumber()
  @Min(1)
  @Max(100000)
  @Type(() => Number)
  @IsOptional()
  pageIndex? = 1;
}

export class AffectedResponse {
  @ApiProperty({ description: '影响条数' })
  affected: number;
}

export class IdsParams {
  @ApiPropertyOptional({ description: 'ids', type: Number, isArray: true })
  @ArrayNotEmpty()
  @IsArray()
  @IsNumber({ allowNaN: false }, { each: true })
  ids: number[];
}

export class BaseType {}
