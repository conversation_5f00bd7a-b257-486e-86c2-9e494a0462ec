# 🛠️ 公共基础层 (Commons Layer)

生成时间：2026-01-19 12:00:00

## 📝 概述

公共基础层是 QCC Rover Service 的技术基础设施层，提供纯工具函数、基础类型定义、异常处理、分布式锁等通用技术组件。该层的特点是无业务语义、无副作用，为所有上层业务模块提供稳定可靠的技术支撑。

作为六层架构的底层基础，Commons 层专注于技术性功能的实现，包括加密解密、数据格式化、缓存操作、SQL 查询构建、事件发射、分布式锁等核心技术能力，确保系统各层级都能使用统一标准的技术工具。

该层通过清晰的功能分类（工具函数、异常定义、常量管理、模型定义等）和模块化的组织结构，为企业风险评估、尽职调查、监控告警等复杂业务场景提供高效稳定的技术底座。

## 🎯 核心功能

### 🔧 工具函数集合

- 数据加密解密和安全处理
- 日期时间格式化和计算
- 字符串处理和验证
- 数值计算和货币格式化

### 📊 数据处理工具

- 分页数据批量获取
- Redis 缓存操作封装
- SQL 查询构建助手
- 监控数据处理

### 🔒 分布式锁机制

- Redis 进度锁实现
- 快照进度锁管理
- 批处理进度锁控制

### ⚡ 事件管理系统

- 全局事件发射器
- 尽职调查相关事件
- 系统级事件处理

### 🚨 异常处理框架

- 标准化异常定义
- 业务异常分类管理
- 错误码常量统一维护

### 🏗️ 基础模型定义

- 通用数据传输模型
- 文件配置管理
- 类型定义和常量

## 📁 子模块结构

```
commons/
├── utils/                             # 工具函数集合
│   ├── utils.ts                       # 核心工具函数(加密、格式化、分页等)
│   ├── date.utils.ts                  # 日期处理工具
│   ├── string.utils.ts                # 字符串处理工具
│   ├── redis.utils.ts                 # Redis缓存工具
│   ├── cache.utils.ts                 # 缓存操作工具
│   ├── monitor.utils.ts               # 监控工具
│   ├── type.utils.ts                  # 类型工具
│   ├── name-counter.ts                # 名称计数器
│   └── utils.bundle.ts                # 工具集合导出
├── distributed_lock/                  # 分布式锁实现
│   ├── RedisProgressLock.ts           # Redis进度锁
│   ├── SnapshotProgressLock.ts        # 快照进度锁
│   └── BatchProgressLock.ts           # 批处理进度锁
├── exceptions/                        # 异常定义
│   ├── RoverServerErrorException.ts   # 服务器错误异常
│   ├── DiligenceSnapshotNotFoundException.ts # 调查快照未找到异常
│   ├── EntityDuplicatedException.ts   # 实体重复异常
│   └── [其他异常类...]                 # 各类业务异常
├── constants/                         # 常量定义
│   └── exceptionConstants.ts          # 异常常量定义
├── model/                             # 通用模型
│   └── common.ts                      # 通用数据模型
├── file/                              # 文件处理
│   └── config.ts                      # 文件配置管理
├── types/                             # 类型定义
├── RoverEventEmitter.ts               # 事件发射器
├── sql.helper.ts                      # SQL查询助手
├── validatorHelper.ts                 # 验证助手
└── README.md                          # 本文档
```

## 🔗 子模块介绍

### 🔧 [工具函数集合](./utils/)

**核心职责**: 提供通用的数据处理和技术工具函数

- **加密解密**: AES-128-CBC/ECB 算法实现数据安全处理
- **数值处理**: 科学计数法转换、四舍五入、货币格式化
- **日期工具**: 时间戳格式化、日期范围计算、相对时间处理
- **分页工具**: 批量数据获取、全量数据查询的通用实现
- **RSA 加密**: 公私钥生成、加密解密操作
- **哈希计算**: 数据完整性校验和唯一标识生成

### 🔒 [分布式锁机制](./distributed_lock/)

**核心职责**: 基于 Redis 实现的分布式锁和进度控制

- **RedisProgressLock**: 通用 Redis 进度锁，支持批量键值操作和 TTL 管理
- **SnapshotProgressLock**: 快照操作的进度锁，防止并发冲突
- **BatchProgressLock**: 批处理任务的进度锁，确保任务执行顺序

### 🚨 [异常处理框架](./exceptions/)

**核心职责**: 标准化的异常定义和错误处理机制

- **业务异常**: DiligenceNotFoundException、CustomerNoAnalyzedResultException 等
- **技术异常**: RoverServerErrorException、InvalidJWTException 等
- **验证异常**: EntityDuplicatedException、FileSizeLimitExceptionFilter 等
- **访问异常**: AccessResourceDeniedException、CallbackException 等

### ⚡ [事件管理系统](./RoverEventEmitter.ts)

**核心职责**: 全局事件发射和订阅机制

- **系统事件**: ProcessExit 等系统级事件处理
- **尽职调查事件**: RefreshDiligenceAnalyze、UpdateSpecificDiligence 等业务事件
- **Socket 事件**: 与前端实时通信的事件管理
- **事件分发**: 基于 EventEmitter 的类型安全事件处理

### 🗃️ [SQL 查询助手](./sql.helper.ts)

**核心职责**: TypeORM 查询构建的辅助工具

- **日期范围查询**: 支持复杂日期范围条件的动态构建
- **过滤模型查询**: 注册资本、实缴资本等范围查询的标准化处理
- **查询条件组合**: 基于 Brackets 的复杂条件组合逻辑

### 📊 [常量定义](./constants/)

**核心职责**: 系统级常量和配置的统一管理

- **异常常量**: 错误码、错误信息的标准化定义
- **系统配置**: 全局配置常量的集中管理

## 🔌 对外接口

### 工具函数

```typescript
// 核心工具函数
export { encrypt, decrypt, formatDate, formatMoney, getDateRange } from './utils/utils';
export { RedisUtils } from './utils/redis.utils';
export { QueryBuilderHelper } from './sql.helper';
```

### 分布式锁

```typescript
// 分布式锁服务
export { RedisProgressLock } from './distributed_lock/RedisProgressLock';
export { SnapshotProgressLock } from './distributed_lock/SnapshotProgressLock';
```

### 事件系统

```typescript
// 事件发射器
export { RoverEventEmitter, SystemEvents, DiligenceRelatedEventEnums } from './RoverEventEmitter';
```

### 异常处理

```typescript
// 异常类定义
export { RoverServerErrorException } from './exceptions/RoverServerErrorException';
export { DiligenceNotFoundException } from './exceptions/DiligenceNotFoundException';
```

## 📊 数据流程

Commons 层作为技术基础设施，为上层业务模块提供底层技术支撑：

1. **工具函数调用**: 业务模块直接导入使用各类工具函数
2. **事件通知机制**: 通过 RoverEventEmitter 实现模块间解耦通信
3. **分布式锁协调**: 保证分布式环境下的数据一致性和任务协调
4. **异常统一处理**: 提供标准化的错误处理和异常抛出机制

## 🔧 技术特点

### 高性能数据处理

- 支持科学计数法和浮点数精确计算
- 优化的批量数据获取算法
- 基于 Redis 的高效缓存操作

### 安全加密机制

- AES-128 多模式加密支持
- RSA 公私钥加密体系
- 安全的哈希计算和随机数生成

### 分布式协调能力

- Redis 分布式锁实现
- 支持 TTL 的进度控制机制
- 批量键值操作的原子性保证

### 类型安全事件系统

- TypeScript 类型安全的事件定义
- 单例模式的全局事件管理
- 支持业务事件和系统事件分离

### 灵活的 SQL 构建

- TypeORM 查询构建器封装
- 支持复杂条件组合
- 日期范围和数值范围查询优化

## 📚 文档导航

### ⬆️ 上级导航

- **[源码目录总览](../README.md)** - 返回源码目录概览
- **[技术架构详解](../../README.md)** - 查看六层分层架构设计

### ⬇️ 子模块导航

- **[工具函数集合](./utils/)** - 数据处理和技术工具函数
- **[分布式锁机制](./distributed_lock/)** - Redis 分布式锁实现
- **[异常处理框架](./exceptions/)** - 标准化异常定义和处理
- **[常量定义](./constants/)** - 系统级常量和配置管理
- **[通用模型](./model/)** - 基础数据传输模型
- **[文件处理](./file/)** - 文件配置和处理工具
- **[类型定义](./types/)** - 通用类型定义

### ➡️ 同级技术层

- **[领域概念层](../domain/README.md)** - 业务概念和领域模型
- **[核心服务层](../core/README.md)** - 核心技术服务
- **[业务模块层](../modules/README.md)** - 业务功能模块

### 🔗 相关业务域

- **[基础设施层](../infrastructure/README.md)** - 外部服务集成
- **[应用程序层](../app/README.md)** - 应用程序入口

---

> 💡 公共基础层是系统的技术底座，通过提供标准化的工具函数、安全机制、分布式协调和事件管理能力，为整个 QCC Rover Service 提供稳定可靠的技术基础设施支撑。
