/**
 * 数据处理相关的工具函数
 */

import { QueryParamsEnums } from '@domain/model/diligence/pojo/dimension/dimension.filter.params';

/**
 * 处理关联制裁路径信息
 * @param pathStr 路径字符串，格式为JSON数组
 * @returns 处理后的路径数组
 */
export const processRelatedPath = (pathStr: string): any => {
  try {
    // 解析路径字符串为对象
    const paths = JSON.parse(pathStr);

    // 处理每条路径
    return paths.map((pathGroup: any[]) => {
      const processedPath: any[] = [];

      // 递归处理路径中的每个节点
      const processPathNode = (nodes: any[]) => {
        nodes.forEach((node) => {
          // 添加当前节点到路径中
          processedPath.push({
            KeyNo: node.KeyNo,
            Name: node.Name,
            Percent: parseFloat(node.Percent).toFixed(2) + '%', // 格式化百分比
          });

          // 递归处理子路径
          if (node.Paths && Array.isArray(node.Paths) && node.Paths.length > 0) {
            node.Paths.forEach((subPath: any[]) => {
              if (subPath && subPath.length > 0) {
                processPathNode(subPath);
              }
            });
          }
        });
      };

      // 开始处理路径组
      pathGroup.forEach((pathNode) => {
        processPathNode([pathNode]);
      });

      // 反转路径，使其从主制裁指向关联制裁
      return processedPath.reverse();
    });
  } catch (error) {
    return [];
  }
};

// 获取黑名单类型的类型
export const getBlackListTypes = (dimension: any) => {
  // 优先使用 newBlackListType 的 activeCodes
  const newBlackListTypeParam = dimension.params?.find((p) => p.field === QueryParamsEnums.newBlackListType);
  if (newBlackListTypeParam && newBlackListTypeParam.activeCodes) {
    // 直接返回 activeCodes 数组
    return newBlackListTypeParam.activeCodes;
  }

  // 回退到旧类型
  const oldBlackListTypeParam = dimension.params?.find((p) => p.field === QueryParamsEnums.blackListType);
  if (oldBlackListTypeParam && oldBlackListTypeParam.fieldVal) {
    // 返回旧类型中状态为 1 的 key 值
    return oldBlackListTypeParam.fieldVal.filter((val) => val.status === 1).map((val) => Number(val.key));
  }

  return [];
};
