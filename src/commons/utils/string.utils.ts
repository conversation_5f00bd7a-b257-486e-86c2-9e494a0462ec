export function toFullWidth<T>(str: T) {
  if (!str || 'string' !== typeof str) return str;
  if (containsChinese(str)) {
    return str.replace('(', '（').replace(')', '）').trim();
  }
  return str.trim();
}

export function getUrlPath(url: string): string {
  const urlObj = new URL(url);
  let path = urlObj.pathname;

  if (path.startsWith('/')) {
    path = path.substring(1);
  }
  return path;
}

/**
 * 安全地将 string 转换为数字类型
 * 处理 undefined、null、字符串数字等边界情况
 * @param string 原始值
 * @returns 转换后的数字，如果转换失败返回 undefined
 */
export function parseToNumber(value: any): number | undefined {
  // 如果已经是数字类型，直接返回
  if (typeof value === 'number') {
    return value;
  }

  // 如果是 null 或 undefined，返回 undefined
  if (value == null) {
    return undefined;
  }

  // 尝试转换为数字
  const numericValue = Number(value);

  // 检查转换是否成功（Number() 不会抛出异常，但会返回 NaN）
  if (isNaN(numericValue)) {
    return undefined;
  }

  return numericValue;
}

/**
 * 判断字符串是否包含中文
 * @param str
 * @returns
 */
export function containsChinese(str: string) {
  const regex = /[u4e00-u9fa5]/;
  return regex.test(str);
}
