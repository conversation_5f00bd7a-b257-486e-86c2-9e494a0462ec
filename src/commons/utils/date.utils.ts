import { DATE_FORMAT } from '@domain/constants/common';
import * as moment from 'moment/moment';
import { DateRange, DateRangeRelative, NumberRange } from '@kezhaozhao/qcc-model';

export const dateRangeRelativeToDateRange = (d: DateRangeRelative, format = 'YYYY-MM-DD'): DateRange => {
  const d1: DateRange = new DateRange();
  const currently: boolean = d.currently;
  const quantity: number = d.number;
  let startDate: moment.Moment;
  let endDate: moment.Moment | void = moment();
  let baseDate = moment().endOf('day');
  let unit: moment.unitOfTime.StartOf;

  switch (d.unit) {
    case 'day':
      unit = 'day';
      break;
    case 'week':
      unit = 'week';
      break;
    case 'month':
      unit = 'month';
      break;
    case 'quarter':
      unit = 'quarter';
      break;
    case 'year':
      unit = 'year';
      break;
    default: {
      unit = 'day';
      break;
    }
  }

  if (!currently) {
    // 实时查询操作
    baseDate = moment().subtract(1, unit).endOf(unit);
  }
  switch (d.flag) {
    case 0:
      // 本日，本周，本月，本年
      startDate = moment(baseDate).startOf(unit).startOf('day');
      endDate = moment(baseDate).endOf('day');
      break;
    case 1:
      // 近几日，近几周，近几月，近几年
      startDate = moment(baseDate)
        .subtract(quantity - 1, unit)
        .startOf(unit);
      endDate = moment(baseDate).endOf('day');
      break;
    case 2:
      // 昨日，前天 等之前的某一日
      startDate = moment(baseDate).subtract(quantity, 'day').startOf('day');
      endDate = moment(startDate).endOf('day');
      break;
    case 3:
      // xx 日，周，月，年 以上 成立日期 < endDate
      startDate = undefined;
      endDate = moment(baseDate).subtract(quantity, unit).endOf('day');
      break;
    case 4:
      // N ~ M 日，周，月，年 之间
      // 例如 3到5年  N = 3, M = 5
      startDate = moment(baseDate).subtract(Number(d.max), unit).startOf('day');
      endDate = moment(baseDate).subtract(Number(d.min), unit).endOf('day');
      break;
    case 5:
      // 固定日期之间
      // 例如 2018-08-08到2019-09-09
      startDate = moment(d.min);
      endDate = moment(d.max);
      break;
    case 6:
      // xxxx年xx月 或 xxxx年第x季度 起止时间
      if (d.year != null) {
        startDate = moment()
          .set('year', d.year)
          .set(unit, unit == 'month' ? quantity - 1 : quantity)
          .startOf(unit);
      }
      if (d.year != null) {
        endDate = moment()
          .set('year', d.year)
          .set(unit, unit == 'month' ? quantity - 1 : quantity)
          .endOf(unit);
      }
      break;
    case 7:
      // 昨天到期，n 天前到期，不需要开始时间
      if (quantity < 0) {
        startDate = undefined;
      }
      // 当天到期，未来时间到期，n 天内到期，开始时间为当天开始
      else {
        startDate = moment().startOf('day');
      }
      endDate = moment().add(quantity, unit).endOf('day');
      break;
    default:
      break;
  }

  d1.start = startDate?.isValid() ? startDate.format(format) : (undefined as any);
  d1.end = endDate?.isValid() ? endDate.format(format) : (undefined as any);
  return d1;
};

export const dateRangeRelativeToTimestamp = (dateRangeRelative: DateRangeRelative): NumberRange => {
  const dateRange = dateRangeRelativeToDateRange(dateRangeRelative);
  return Object.assign(new NumberRange(), {
    min: moment(dateRange.start).startOf('day').unix(),
    max: moment(dateRange.end).endOf('day').unix(),
  });
};

export const dateTransform = (date: any, dateFormat = DATE_FORMAT) => {
  if (!date || date === '-') return '-';

  // 处理数字型时间戳
  if (typeof date === 'number') {
    return moment(date * 1000).format(dateFormat);
  }

  // 处理字符串型数字时间戳
  if (typeof date === 'string' && /^\d+$/.test(date)) {
    // 如果是10位数字，视为秒级时间戳
    if (date.length === 10) {
      return moment(parseInt(date) * 1000).format(dateFormat);
    }
    // 如果是13位数字，视为毫秒级时间戳
    if (date.length === 13) {
      return moment(parseInt(date)).format(dateFormat);
    }
    // 处理如 "20240101" 格式
    if (date.length === 8) {
      return moment(date, 'YYYYMMDD').format(dateFormat);
    }
  }

  // 处理 Date 对象和 ISO 8601 格式的字符串
  if (date instanceof Date || typeof date === 'string') {
    return moment(date, moment.ISO_8601).format(dateFormat);
  }

  return '-';
};

/**
 * 将时间戳或日期字符串统一转换为指定格式
 * 支持: 秒级时间戳(number/string)、日期字符串(如 "2004-07-01", "2004-07", "1994")
 * @param date 时间戳或日期字符串
 * @param dateFormat 输出格式，默认 DATE_FORMAT
 * @returns 格式化后的日期字符串，无效时返回 null
 */
export const formatDateOrTimestamp = (date: string | number, dateFormat = DATE_FORMAT): string | null => {
  if (!date) return null;
  // 处理数字型时间戳（秒级）
  if (typeof date === 'number') {
    const parsed = moment(date * 1000);
    return parsed.isValid() ? parsed.format(dateFormat) : null;
  }
  // 处理字符串
  if (typeof date === 'string') {
    // 4位纯数字视为年份（如 "1994"），10位以上纯数字视为秒级时间戳
    if (/^\d+$/.test(date)) {
      if (date.length === 4) {
        const parsed = moment(date, 'YYYY', true);
        return parsed.isValid() ? parsed.format(dateFormat) : null;
      }
      const parsed = moment(parseInt(date) * 1000);
      return parsed.isValid() ? parsed.format(dateFormat) : null;
    }
    // 尝试多种常见日期格式
    const formats = ['YYYY-MM-DD', 'YYYY-MM', 'YYYY/MM/DD', 'YYYY/MM', 'YYYY.MM.DD', 'YYYY.MM'];
    const parsed = moment(date, formats, true);
    return parsed.isValid() ? parsed.format(dateFormat) : null;
  }
  return null;
};

/**
 * 将格式化的日期字符串转换为 Date 对象
 * @param dateStr 格式化的日期字符串（如 "2004-07-01"）
 * @param dateFormat 输入格式，默认 DATE_FORMAT
 * @returns Date 对象，无效时返回 null
 */
export const parseStringToDate = (dateStr: string | null, dateFormat = DATE_FORMAT): Date | null => {
  if (!dateStr) return null;
  const parsed = moment(dateStr, dateFormat, true);
  return parsed.isValid() ? parsed.toDate() : null;
};

/**
 * 获取未来近多少时间
 * @param dateType
 */
export const getExpirationDate = (dateType = 2) => {
  switch (dateType) {
    case 1:
      // 近 7 天
      return moment().add('7', 'days').startOf('day').unix();
    case 3:
      // 近 3 个月
      return moment().add('3', 'months').startOf('day').unix();
    default:
      // 近 1 个月
      return moment().add('1', 'months').startOf('day').unix();
  }
};

/**
 * 获取过去近多少时间
 * @param dateType
 */
export const getPastExpirationDate = (dateType = 2) => {
  switch (dateType) {
    case 1:
      // 近 7 天
      return moment().add('-7', 'days').startOf('day').unix();
    case 3:
      // 近 3 个月
      return moment().add('-3', 'months').startOf('day').unix();
    default:
      // 近 1 个月
      return moment().add('-1', 'months').startOf('day').unix();
  }
};

/**
 *  20210501  -> 2021-05-01
 * @param dateString
 * @private
 */
export const convertDateFormat = (dateString: string): string => {
  const year = dateString.substring(0, 4);
  const month = dateString.substring(4, 6);
  const day = dateString.substring(6, 8);
  return `${year}-${month}-${day}`;
};

export const getNextDayStartSeconds = () => {
  const now = new Date();
  const nextDay = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
  return Math.floor((nextDay.getTime() - now.getTime()) / 1000);
};
