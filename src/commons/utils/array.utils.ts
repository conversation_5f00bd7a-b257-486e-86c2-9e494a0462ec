/**
 * 数组处理相关的工具函数
 */

/**
 * 计算数字的二进制表示中 1 的个数
 * @param n 数字
 * @returns 二进制中 1 的个数
 */
function bitCount(n: number): number {
  let count = 0;
  while (n > 0) {
    count += n & 1;
    n >>>= 1;
  }
  return count;
}

/**
 * 生成数组中所有长度为 k 的组合
 * 使用位掩码算法生成所有可能的组合，时间复杂度为 O(2^n * n)
 * @param elements 元素数组
 * @param k 组合长度
 * @returns 所有长度为 k 的组合数组
 * @example
 * generateCombinations([1, 2, 3], 2)
 * // 返回 [[1, 2], [1, 3], [2, 3]]
 */
export function generateCombinations<T>(elements: T[], k: number): T[][] {
  if (k < 0 || k > elements.length) {
    return [];
  }
  if (k === 0) {
    return [[]];
  }
  const result: T[][] = [];
  const total = 1 << elements.length;
  for (let mask = 0; mask < total; mask++) {
    if (bitCount(mask) === k) {
      const combination: T[] = [];
      for (let i = 0; i < elements.length; i++) {
        if ((mask & (1 << i)) !== 0) {
          combination.push(elements[i]);
        }
      }
      result.push(combination);
    }
  }
  return result;
}
