# 🏢 领域概念层 (Domain Layer)

> 生成时间：2026-01-19 12:00:00

## 📝 概述

领域概念层是 QCC Rover Service 的核心业务逻辑承载层，定义了系统中所有业务概念、规则和领域模型。该层保持业务逻辑的纯净性，不依赖具体的技术实现细节，专注于业务规则的表达和业务概念的建模。

作为六层架构中的核心层，Domain 层为上层业务模块提供稳定的业务概念定义，包括数据库实体映射、业务模型传递、枚举定义、值对象封装、验证策略等，确保业务逻辑在整个系统中的一致性和可维护性。

领域层通过清晰的概念分离（Entity、DTO、Model、VO）和策略模式的验证框架，为复杂的企业风险评估、尽职调查、监控告警等业务场景提供强有力的技术支撑。

## 🎯 核心功能

### 🏗️ 业务实体管理

- 数据库实体定义与 ORM 映射
- 企业、人员、用户等核心业务实体
- 实体关系和约束定义

### 📊 业务模型传递

- 跨服务间的数据传输载体
- 复杂业务流程的参数组合
- 业务操作的状态承载

### 🔄 数据传输对象

- API 层的请求响应数据结构
- 输入验证和文档规范
- 前后端数据协议定义

### 💎 值对象封装

- 具有业务含义的不可变概念
- 业务规则的自包含验证
- 可复用的原子业务概念

### ✅ 业务验证策略

- 基于策略模式的验证框架
- 可扩展的业务规则验证
- 批处理场景的数据校验

### 📈 领域工具服务

- 包含业务逻辑的工具函数
- 风险计算和评估算法
- 企业匹配和分析工具

## 📁 子模块结构

```
domain/
├── entities/                   # 数据库实体层
├── dto/                       # 数据传输对象层
├── model/                     # 业务模型层
│   ├── diligence/            # 尽职调查业务模型
│   ├── company/              # 企业管理业务模型
│   ├── monitor/              # 监控告警业务模型
│   ├── customer/             # 客户管理业务模型
│   └── [20+ 业务域...]       # 其他业务域模型
├── enums/                     # 业务枚举层
│   ├── diligence/            # 尽职调查枚举
│   ├── company/              # 企业相关枚举
│   ├── monitor/              # 监控相关枚举
│   └── [15+ 业务域...]       # 其他业务域枚举
├── value-objects/            # 值对象层
├── validation/               # 业务验证层
│   ├── strategies/           # 验证策略实现
│   └── ValidationModule.ts  # 验证模块
├── utils/                    # 领域工具层
├── constants/               # 业务常量层
└── types/                   # 类型定义层
```

## 🔗 子模块介绍

### 🗃️ [数据库实体层](./entities/)

**核心职责**: 定义系统中所有业务实体的数据库映射关系

- **企业实体管理**: CompanyEntity、CompanyRelationEntity 等企业核心数据
- **用户体系实体**: UserEntity、CustomerEntity、DepartmentEntity 等用户相关
- **业务流程实体**: BatchEntity、DiligenceHistoryEntity 等流程数据
- **监控告警实体**: MonitorRiskDynamicsV2Entity、TenderAlertSettingEntity 等
- **系统配置实体**: SystemSettingsEntity、OrgConfigurationEntity 等配置数据

### 📦 [数据传输对象层](./dto/)

**核心职责**: 定义 API 层的数据传输规范和验证规则

- **请求参数定义**: 包含完整的验证装饰器和文档注解
- **响应数据结构**: 标准化的 API 响应格式
- **查询条件封装**: 复杂查询条件的结构化定义
- **批量操作支持**: 批处理场景的数据传输格式

### 🧩 [业务模型层](./model/)

**核心职责**: 提供业务流程中的数据传递载体和操作封装

#### 📊 [尽职调查模型](./model/diligence/)

**核心职责**: 尽职调查业务流程的数据模型和操作逻辑

- **调查参数模型**: 调查深度、维度配置、过滤条件的参数组合
- **报告生成模型**: 调查报告的结构化数据和生成逻辑
- **风险评估模型**: 多维度风险评估的计算模型和结果封装

#### 🏢 [企业管理模型](./model/company/)

**核心职责**: 企业信息管理和关系分析的业务模型

- **企业信息模型**: 企业基础信息的结构化表示和验证
- **关系分析模型**: 企业间关系的图谱分析和路径计算
- **风险识别模型**: 企业风险特征的识别和评估逻辑

#### 🔔 [监控告警模型](./model/monitor/)

**核心职责**: 实时监控和告警处理的业务模型

- **监控配置模型**: 监控规则的配置和管理逻辑
- **告警事件模型**: 告警事件的生成、处理和状态管理
- **风险动态模型**: 风险变化的追踪和分析模型

#### 👥 [客户管理模型](./model/customer/)

**核心职责**: 客户信息管理和业务关系维护

- **客户档案模型**: 客户基础信息和业务属性管理
- **分组管理模型**: 客户分组策略和权限控制
- **业务关系模型**: 客户业务关系的建模和维护

### 📋 [业务枚举层](./enums/)

**核心职责**: 提供具有业务语义的枚举定义和状态管理

- **状态枚举**: 业务对象的生命周期状态定义
- **类型枚举**: 业务分类和类型体系定义
- **配置枚举**: 业务配置选项和参数定义
- **权限枚举**: 权限范围和级别的标准化定义

### 💎 [值对象层](./value-objects/)

**核心职责**: 封装具有特定业务含义的不可变概念

- **风险分数**: 标准化的风险评分计算和等级判定
- **企业信息**: 企业基础信息的值对象封装和验证
- **评估结果**: 评估操作结果的标准化表示
- **业务规则**: 核心业务规则的概念化封装

### ✅ [业务验证层](./validation/)

**核心职责**: 提供可扩展的业务规则验证框架

- **验证策略**: 基于策略模式的可插拔验证机制
- **批处理验证**: 大批量数据的高效验证处理
- **业务规则引擎**: 复杂业务规则的组合和执行
- **标志判定器**: 业务标志位的智能判定逻辑

### 🛠️ [领域工具层](./utils/)

**核心职责**: 提供包含业务逻辑的工具函数和算法

- **风险计算工具**: 多维度风险评估的算法实现
- **企业匹配工具**: 企业名称和信息的智能匹配算法
- **评估分析工具**: 业务数据的统计分析和趋势计算
- **批处理工具**: 批量操作的优化和性能工具

## 🔌 对外接口

### 📚 实体定义接口

```typescript
// 为业务模块提供标准化的数据库实体
export { CompanyEntity, UserEntity, CustomerEntity } from './entities';
```

### 🔄 数据传输接口

```typescript
// 为API层提供请求响应的数据结构
export { CompanySearchRequest, DiligenceReportResponse } from './dto';
```

### 🧩 业务模型接口

```typescript
// 为业务服务提供数据传递载体
export { CompanyInvestigationModel, RiskAssessmentModel } from './model';
```

### 💎 值对象接口

```typescript
// 为业务逻辑提供核心概念封装
export { RiskScore, CompanyInfo, PercentageVO } from './value-objects';
```

### ✅ 验证服务接口

```typescript
// 为业务模块提供验证策略服务
export { BatchValidationService, ValidationStrategyFactory } from './validation';
```

### 🛠️ 工具函数接口

```typescript
// 为业务模块提供算法和工具支持
export { RiskCalculatorUtil, CompanyMatcherUtil } from './utils';
```

## 📊 数据建模规范

### 🏗️ Entity、DTO、Model、VO 使用指南

#### **Entity (实体) - 数据库映射层**

**位置**: `src/domain/entities/`  
**命名**: `XxxEntity.ts`  
**用途**: 数据库实体映射

**特点**:

- 与数据库表一一对应
- 包含 ORM 映射注解
- 有持久化标识（ID）
- 可变的（mutable）

```typescript
@Entity('companies')
export class CompanyEntity {
  @PrimaryGeneratedColumn()
  id: string;

  @Column()
  name: string;

  @Column()
  registeredCapital: number;
}
```

#### **DTO (数据传输对象) - API 数据传输层**

**位置**: `src/domain/dto/`  
**命名**: `XxxRequest.ts` / `XxxResponse.ts`  
**用途**: API 层数据传输

**特点**:

- 包含验证装饰器 (`@IsString`, `@IsOptional`)
- 包含 API 文档装饰器 (`@ApiProperty`)
- 扁平化的数据结构
- 面向接口设计

```typescript
export class CompanySearchRequest {
  @ApiProperty({ description: '公司名称', required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ description: '注册资本最小值', required: false })
  @IsOptional()
  @IsNumber()
  minCapital?: number;
}
```

#### **Model (业务模型) - 业务流程载体**

**位置**: `src/domain/model/`  
**命名**: `XxxModel.ts`  
**用途**: 业务流程中的数据传递载体

**特点**:

- 用于参数传递，主要在 Service 层方法间传递数据
- 业务流程载体，承载业务操作所需的数据组合
- 包含操作性方法，主要是操作和查询类型
- 不可变设计，使用 readonly 属性
- 关注"做什么"，侧重于业务流程和操作

```typescript
export class CompanyInvestigationModel {
  readonly orgId: number;
  readonly companyId: string;
  readonly depth: number;
  readonly types?: string[];

  constructor(params: {...}) {
    // 赋值逻辑
  }

  hasType(type: string): boolean {
    return this.types?.includes(type) ?? false;
  }

  getEffectiveTypes(): string[] {
    // 获取有效类型的逻辑
  }
}
```

#### **VO (值对象) - 业务概念封装**

**位置**: `src/domain/value-objects/`  
**命名**: `XxxVO.ts` 或 `Xxx.ts`  
**用途**: 具有特定业务含义的不可变概念

**特点**:

- 表示业务概念，代表具有特定含义的业务概念
- 完全不可变，一旦创建就不能修改
- 值相等性，通过值比较，而非引用比较
- 自包含规则，包含该概念的所有业务规则和验证
- 原子性，通常表示单一概念或紧密相关的属性组合
- 关注"是什么"，侧重于概念的定义和特征

```typescript
export class PercentageVO {
  private readonly _value: number;

  constructor(value: number) {
    if (value < 0 || value > 100) {
      throw new Error('持股比例必须在0-100之间');
    }
    this._value = Number(value.toFixed(2));
  }

  get value(): number {
    return this._value;
  }

  isControlling(): boolean {
    return this._value > 50;
  }

  equals(other: PercentageVO): boolean {
    return this._value === other._value;
  }
}
```

### 🎯 选择判断标准

#### **使用 Model 的情况**:

- ✅ **参数传递**: 在 Service 方法间传递复杂参数组合
- ✅ **流程载体**: 承载业务流程中的数据状态
- ✅ **操作集合**: 需要对多个相关属性进行操作
- ✅ **临时组合**: 为特定业务场景组合的数据

#### **使用 VO 的情况**:

- ✅ **具体概念**: 表示具有明确业务含义的概念
- ✅ **独立验证**: 概念本身需要业务规则验证
- ✅ **可复用性**: 可以在多个场景中复用
- ✅ **原子性**: 表示不可分割的业务概念

### 📝 判断口诀

> **Model**: "我需要传递这些数据去做某事" (关注做什么)
>
> **VO**: "这是一个具有特定含义的东西" (关注是什么)

### 🗂️ 推荐的文件组织结构

```
src/domain/
├── entities/                    # 数据库实体
│   ├── company/
│   │   ├── company.entity.ts
│   │   └── company-relation.entity.ts
│   └── user/
│       └── user.entity.ts
├── dto/                        # 数据传输对象
│   ├── company/
│   │   ├── requests/
│   │   │   ├── company-search.request.ts
│   │   │   └── company-create.request.ts
│   │   └── responses/
│   │       └── company-detail.response.ts
│   └── user/
│       ├── requests/
│       └── responses/
├── model/                      # 业务模型
│   ├── company/
│   │   ├── company-investigation.model.ts
│   │   └── data-range-condition.model.ts
│   └── user/
├── value-objects/              # 值对象
│   ├── percentage.vo.ts
│   ├── risk-score.vo.ts
│   └── money.vo.ts
└── enums/                      # 枚举
    ├── company/
    └── user/
```

## 📚 文档导航

### 📂 上级导航

- [🏠 项目根目录](../../README.md)
- [📁 源码目录](../README.md)

### 📊 领域层文档

- [📋 当前文档 - 领域概念层概览](./README.md)
- [🔄 数据建模迁移指南](./model-dto-vo-migration-guide.md)

### 🔗 相关业务域

- [📊 业务模块层](../modules/README.md)
- [🛠️ 核心服务层](../core/README.md)
- [🔧 公共基础层](../commons/README.md)

### 📖 子模块导航

- [🗃️ 数据库实体层](./entities/)
- [📦 数据传输对象层](./dto/)
- [🧩 业务模型层](./model/)
- [📋 业务枚举层](./enums/)
- [💎 值对象层](./value-objects/)
- [✅ 业务验证层](./validation/)
- [🛠️ 领域工具层](./utils/)
- [📊 常量定义层](./constants/)
- [📝 类型定义层](./types/)

---

> 💡 **开发提示**: 领域概念层是业务逻辑的核心，通过清晰的概念分离和统一的建模规范，为整个系统提供稳定可靠的业务基础。在开发过程中，请严格遵循 Entity、DTO、Model、VO 的使用规范，确保代码的可维护性和业务逻辑的清晰性。
