export enum MobileBiddingSettingGroupEnum {
  RelationshipCheck = 'RelationshipCheck', // 关联关系检查
  ComplianceCheck = 'ComplianceCheck', // 合规检查
  BasicQualification = 'BasicQualification', // 资格检查
}

export enum RelationshipCheckGroupEnum {
  ParentSubsidiary = 'ParentSubsidiary', // 母子公司与控制关系
  HeadBranch = 'HeadBranch', // 总分公司关系
  SameControlPerson = 'SameControlPerson', // 有相同实际控制人
  SameParentCompany = 'SameParentCompany', // 同一母公司
  SameGroupCompany = 'SameGroupCompany', // 隶属同一集团
  PartnerInvestment = 'PartnerInvestment', // 股权投资关联
  SameLegal = 'SameLegal', // 法定代表人为同一人
  SameEmployee = 'SameEmployee', // 管理层有相同主要人员
  SameEmployeeInvestor = 'SameEmployeeInvestor', // 管理层共同任职/投资
  SameContactWay = 'SameContactWay', // 联系方式相同
  SameAddress = 'SameAddress', // 地址信息相同或高度相似
  Guarantor = 'Guarantor', // 存在资金担保关系
  EquityPledge = 'EquityPledge', // 股权出质
  ChattelMortgage = 'ChattelMortgage', // 动产抵押
  SameInvestCompany = 'SameInvestCompany', // 共同投资同一公司
  SameLegalInvestor = 'SameLegalInvestor', // 有共同股东
  IndirectComplex = 'IndirectComplex', // 间接复杂或隐蔽关联
}

export enum BasicQualificationCheckItemEnum {
  RegistrationStatus = 'RegistrationStatus', // 登记状态
  IsBranchCompany = 'IsBranchCompany', // 独立法人资格
  TaxpayerType = 'TaxpayerType', // 一般纳税人
  BusinessStatus = 'BusinessStatus', // 经营异常
  FakeStateOwnedEnterprise = 'FakeStateOwnedEnterprise', // 假冒国企
  StopProduction = 'StopProduction', // 被责令停产停业
  Bankruptcy = 'Bankruptcy', // 破产重整
}

export enum ComplianceScreeningCheckItemEnum {
  // 被列入严重违法失信企业名单
  SeriousViolation = 'SeriousViolation', // 被列入严重违法失信企业名单

  FraudulentEnterprise = 'FraudulentEnterprise', // 涉行贿犯罪记录
  ShixinRen = 'ShixinRen', //失信被执行人
  LimitHighConsumption = 'LimitHighConsumption', //限制高消费
  MajorTaxFraud = 'MajorTaxFraud', //重大税收违法
  TaxNonNormal = 'TaxNonNormal', // 被列入税务非正常户
  DebtRecord = 'DebtRecord', // 欠税记录
  BlacklistForUnpaidWorkers = 'BlacklistForUnpaidWorkers', // 被列入拖欠农民工工资黑名单
  StatisticalSerious = 'StatisticalSerious', // 统计领域严重失信企业及其有关人员
  SafetyProduction = 'SafetyProduction', // 安全生产领域失信生产经营单位
  OtherIndustryRegulatoryBlacklist = 'OtherIndustryRegulatoryBlacklist', // 其他行业领域监管黑名单
  GovernmentProcurementSeriousFraud = 'GovernmentProcurementSeriousFraud', // 列入政府采购严重违法失信名单
  // 军队采购失信名单
  MilitaryProcurementFraud = 'MilitaryProcurementFraud', // 军队采购失信名单
  // 军队采购暂停供应商资格名单
  MilitaryProcurementSuspensionSupplier = 'MilitaryProcurementSuspensionSupplier', // 军队采购暂停供应商资格名单
  // 国央企采购黑名单
  StateOwnedEnterprisePurchaseBlacklist = 'StateOwnedEnterprisePurchaseBlacklist', // 国央企采购黑名单
  // 涉采购处罚
  PurchasePunishment = 'PurchasePunishment', // 涉采购处罚
  // 涉诉围串标记录
  BidFraud = 'BidFraud', // 涉诉围串标记录
}

/**
 * 风险等级
 */
export enum RiskLevelEnum {
  High = 2,
  Medium = 1,
  Low = 0,
}

export enum RelationshipCheckItemEnum {
  // 直接持股
  DirectEquity = 'DirectEquity', // 直接持股
  // 间接持股
  IndirectEquity = 'IndirectEquity', // 间接持股
  // 单一控制路径
  SingleControlPath = 'SingleControlPath', // 单一控制路径
  // 多重控制路径
  MultipleControlPath = 'MultipleControlPath', // 多重控制路径
  // 大股东
  MajorShareholder = 'MajorShareholder', // 大股东
  // 总公司与分公司
  HeadBranch = 'HeadBranch', // 总公司与分公司
  // 隶属同一总公司
  SameHeadCompany = 'SameHeadCompany', // 隶属同一总公司
  // 两家公司的最终控制方是同一个自然人
  SameFinalControlPerson = 'SameFinalControlPerson', // 两家公司的最终控制方是同一个自然人
  // 直接控制
  DirectControl = 'DirectControl', // 直接控制
  // 间接控制
  IndirectControl = 'IndirectControl', // 间接控制
  // 同属一个集团
  SameGroupCompany = 'SameGroupCompany', // 同属一个集团
  // 历史持股
  HistoricalEquity = 'HistoricalEquity', // 历史持股
  CurrentSameLegal = 'CurrentSameLegal', // 当前相同法人
  CurrentAndHistoricalLegal = 'CurrentAndHistoricalLegal', // 当前与历史法定代表人为同一人
  HistoricalSameLegal = 'HistoricalSameLegal', // 历史同一法定代表人
  CurrentSameKeyPersonnel = 'CurrentSameKeyPersonnel', // 当前相同主要人员
  CurrentAndHistoricalKeyPersonnel = 'CurrentAndHistoricalKeyPersonnel', // 当前与历史主要人员为同一人
  HistoricalSameKeyPersonnel = 'HistoricalSameKeyPersonnel', // 历史同一主要人员
  // 共同投资
  CommonInvestment = 'CommonInvestment', // 共同投资
  // 联系电话相同
  SamePhoneNumber = 'SamePhoneNumber', // 联系电话相同
  // 邮箱相同
  SameEmail = 'SameEmail', // 邮箱相同
  // 网址相同
  SameWebsite = 'SameWebsite', // 网址相同
  // 地址信息相同或高度相似
  SameAddress = 'SameAddress', // 地址信息相同或高度相似
  // 当前地址完全相同
  CurrentAddressSame = 'CurrentAddressSame', // 当前地址完全相同
  // 当前地址高度相似
  CurrentAddressSimilar = 'CurrentAddressSimilar', // 当前地址高度相似
  // 历史地址相同
  HistoricalAddressSame = 'HistoricalAddressSame', // 历史地址相同
  // 存在资金担保关系
  ExistsGuarantee = 'ExistsGuarantee', // 存在资金担保关系
  // 股权出质
  EquityPledge = 'EquityPledge', // 股权出质
  // 动产抵押
  ChattelMortgage = 'ChattelMortgage', // 动产抵押
  // 共同投资同一公司
  CommonInvestmentCompany = 'CommonInvestmentCompany', // 共同投资同一公司
  CommonHisInvestmentCompany = 'CommonHisInvestmentCompany', // 共同历史投资同一公司
  // 共同法人股东
  CommonLegalShareholder = 'CommonLegalShareholder', // 共同法人股东
  // 共同历史法人股东
  CommonHisLegalShareholder = 'CommonHisLegalShareholder', // 共同历史法人股东
  // 企业间关联关系
  EnterpriseRelationship = 'EnterpriseRelationship', // 企业间关联关系
  // 主要客户或供应商高度重叠
  OverlapCustomerSupplier = 'OverlapCustomerSupplier', // 主要客户或供应商高度重叠
  // 共同申请知识产权
  CommonPatent = 'CommonPatent', // 共同申请知识产权
  // 专利转让
  PatentTransfer = 'PatentTransfer', // 专利转让
  // 曾有围串标
  BidFraud = 'BidFraud', // 曾有围串标
}

export enum DataSourceEnum {
  CompanyDetail = 'CompanyDetail', // 公司详情
  DataInterface = 'DataInterface', // 数据接口
  CreditEs = 'CreditEs', // 信用大数据es
  BlacklistEs = 'BlacklistEs', //外部黑名单es
  // 行政处罚es
  PunishmentEs = 'PunishmentEs', // 行政处罚es
  // 裁判文书 ES
  JudgementEs = 'JudgementEs', // 裁判文书 ES
}

export enum HighlightOrgEnum {
  Company = 0,
  Person = 2,
}

/**
 * 地址匹配模式枚举
 */
export enum AddressMatchingModeEnum {
  Fuzzy = 1, // 模糊匹配（精确到楼栋）
  Exact = 2, // 精确匹配
}
