/* eslint-disable @typescript-eslint/naming-convention */
export enum BatchBusinessTypeEnums {
  /**
   * 任务-批量排查-excel
   */
  Diligence_File = 10, // 批量排查-excel上传 2011
  /**
   * 任务-批量排查-输入框手动填写
   */
  Diligence_ID = 11, // 批量排查-输入框手动填写 2011
  /**
   * 任务-批量排查-从合作伙伴中选择
   */
  Diligence_Customer = 12, // 批量排查-从合作伙伴中选择 2012
  /**
   * 任务-全量风险排查巡检
   */
  Diligence_Customer_Analyze = 13, // 批量排查-全部客商-全量风险排查巡检 2012
  // Diligence_Continuous = 14, // 批量-持续排查 已废弃
  /**
   * 任务-招标排查-单条
   */
  Bidding_Diligence = 15, // 异步招标排查
  /**
   * 导入-第三方列表-excel
   */
  Customer_File = 20, // 合作伙伴-excel上传 2032
  Customer_ID = 21, // 合作伙伴-文本输入 2032
  /**
   * 导入-人员列表-excel
   */
  Person_File = 22, //人员-excel上传 2062
  /**
   * 导入-内部黑名单列表-excel
   */
  InnerBlacklist_File = 23, //内部黑名单-excel上传 2042
  /**
   * 任务-批量招标排查企业-excel
   */
  Bidding_Diligence_File = 24, //批量招标排查-excel上传
  /**
   * 导入-合作监控企业-excel
   */
  Monitor_File = 25, //合作监控-excel上传
  /**
   * 导出-批量排查-公司列表概览
   */
  Diligence_Batch_Detail = 30, //排查结果 导出 2011
  /**
   * 导出-排查记录列表
   */
  Diligence_Record = 31, // 排查记录 导出 2022
  /**
   * 导出-人员列表
   */
  Person_Export = 32, //人员导出 2061
  /**
   * 导出-内部黑名单列表
   */
  InnerBlacklist_Export = 33, //内部黑名单导出 2041
  /**
   * 导出-排查报告pdf-单个
   */
  Diligence_Report_Export = 34, // 排查报告导出 pdf 2002
  /**
   * 导出-第三方列表
   */
  Customer_Export = 35, //第三方列表导出 2031
  /**
   * 导出-批量排查-维度详情
   */
  Dimension_Detail_Export = 36, //维度详情导出
  /**
   * 导出-招投标详情页pdf
   */
  Tender_Detail_Export = 37, // 标讯详情pdf导出
  /**
   * 导出-招投标列表
   */
  Tender_Export = 38, // 标讯导出
  /**
   * 导出-风险动态列表
   */
  Risk_Export = 39, // 风险动态导出
  /**
   * 导出-舆情动态列表
   */
  Sentiment_Export = 40, // 舆情动态导出
  /**
   * 导出-排查报告pdf-批量压缩包
   */
  Diligence_Report_Batch_Export = 41, // 批量排查报告 批量导出
  /**
   * 导出-风险巡检-公司列表概览
   */
  Analyze_Record_Export = 42,
  /**
   * 导出-风险巡检-维度详情
   */
  Analyze_Dimension_Detail = 43,
  /**
   * 导出-招标排查报告-pdf
   */
  Tender_Report_Export = 44, // 招标排查报告导出

  /**
   * 导出-批量招标排查记录列表-excel
   */
  Tender_Diligence_Record_Export = 45,
  /**
   * 导出-批量招标排查历史记录-excel
   */
  Tender_Diligence_History_Export = 46,

  /**
   * 导出-特定利益关系排查记录-excel
   */
  Specific_Record_List_Export = 47,
  /**
   * 导出-特定利益关系排查报告-pdf
   */
  Specific_Report_Export = 48,
  /**
   * 招标的维度详情导出
   */
  Tender_Dimension_Detail_Export = 49,
  Tender_All_Dimension_Detail_Export = 490,
  Specific_Diligence_File = 50, //批量特定利益关系排查-excel上传

  Specific_Batch_Export = 51, //批量特定利益关系排查-批量导出

  Specific_Async_Diligence = 52, //特定利益关系排查异步

  Potential_Batch_Data = 53, // 批量潜在利益风险排查-页面写入

  /**
   * 导出-权益统计-风险排查-消费详情-excel
   */
  Bundle_Diligence_Consume_detail_Export = 54,

  /**
   * 导出-权益统计-风险巡检-消费详情-excel
   */
  Bundle_Analyze_Record_Consume_detail_Export = 55,

  /**
   * 导出-权益统计-招标排查-消费详情-excel
   */
  Bundle_Bidding_Consume_detail_Export = 56,

  /**ß
   * 导出-权益统计-特定利益-消费详情-excel
   */
  Bundle_Special_Consume_detail_Export = 57,

  Potential_Batch_Excel = 58, // 批量潜在利益风险排查-excel

  Potentail_Batch_Customer = 59, // 批量潜在利益风险排查-customer

  /**
   * 导出-监控企业列表
   */
  Monitor_Company_Export = 60,
}

/**
 * 批量任务对象消息标题
 */
export const BatchBusinessMessageTitle = {
  [BatchBusinessTypeEnums.Diligence_File]: '批量风险排查-导入',
  [BatchBusinessTypeEnums.Diligence_ID]: '批量排查-输入文本',
  [BatchBusinessTypeEnums.Diligence_Customer]: '批量排查-从第三方列表中选择',
  [BatchBusinessTypeEnums.Diligence_Customer_Analyze]: '全量风险排查巡检', // 批量排查-全部客商-巡检分析
  [BatchBusinessTypeEnums.Customer_File]: '第三方管理-批量导入',
  [BatchBusinessTypeEnums.Person_File]: '人员管理-批量导入',
  [BatchBusinessTypeEnums.InnerBlacklist_File]: '内部黑名单-批量导入',
  [BatchBusinessTypeEnums.Monitor_File]: '合作监控列表-批量导入',
  [BatchBusinessTypeEnums.Diligence_Batch_Detail]: '批量排查-结果导出',
  [BatchBusinessTypeEnums.Diligence_Record]: '排查记录导出',
  [BatchBusinessTypeEnums.Person_Export]: '人员管理列表导出',
  [BatchBusinessTypeEnums.InnerBlacklist_Export]: '内部黑名单列表导出',
  [BatchBusinessTypeEnums.Diligence_Report_Export]: '风险排查报告',
  [BatchBusinessTypeEnums.Diligence_Report_Batch_Export]: '风险排查-批量生成报告',
  [BatchBusinessTypeEnums.Customer_Export]: '第三方列表导出',
  [BatchBusinessTypeEnums.Dimension_Detail_Export]: '批量排查-维度详情导出',
  [BatchBusinessTypeEnums.Tender_Detail_Export]: '标讯详情报告',
  [BatchBusinessTypeEnums.Tender_Export]: '投标预警导出',
  [BatchBusinessTypeEnums.Bidding_Diligence_File]: '批量招标排查',
  [BatchBusinessTypeEnums.Risk_Export]: '风险动态导出',
  [BatchBusinessTypeEnums.Sentiment_Export]: '舆情动态导出',
  [BatchBusinessTypeEnums.Analyze_Record_Export]: '风险巡检-结果导出',
  [BatchBusinessTypeEnums.Analyze_Dimension_Detail]: '风险巡检-维度详情导出',
  [BatchBusinessTypeEnums.Tender_Report_Export]: '招标排查报告',
  [BatchBusinessTypeEnums.Tender_Diligence_Record_Export]: '批量招标排查报告',
  [BatchBusinessTypeEnums.Tender_Diligence_History_Export]: '招标排查历史记录导出',
  [BatchBusinessTypeEnums.Tender_Dimension_Detail_Export]: '批量招标排查-维度详情导出',
  [BatchBusinessTypeEnums.Tender_All_Dimension_Detail_Export]: '批量招标排查-全部维度详情导出',
  [BatchBusinessTypeEnums.Specific_Record_List_Export]: '特定利益关系排查历史记录导出',
  [BatchBusinessTypeEnums.Specific_Report_Export]: '特定利益关系排查报告',
  [BatchBusinessTypeEnums.Specific_Diligence_File]: '批量特定利益关系排查',
  [BatchBusinessTypeEnums.Specific_Batch_Export]: '批量特定利益关系排查记录导出',
  [BatchBusinessTypeEnums.Potential_Batch_Data]: '批量潜在利益关系排查-页面写入',
  [BatchBusinessTypeEnums.Potential_Batch_Excel]: '批量潜在利益关系排查-excel',
  [BatchBusinessTypeEnums.Bundle_Diligence_Consume_detail_Export]: '风险排查消费明细导出',
  [BatchBusinessTypeEnums.Bundle_Analyze_Record_Consume_detail_Export]: '风险巡检消费明细导出',
  [BatchBusinessTypeEnums.Bundle_Bidding_Consume_detail_Export]: '招标排查消费明细导出',
  [BatchBusinessTypeEnums.Bundle_Special_Consume_detail_Export]: '特定利益关系排查消费明细导出',
  [BatchBusinessTypeEnums.Monitor_Company_Export]: '监控列表导出',
};
