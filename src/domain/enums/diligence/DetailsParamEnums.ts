export enum DetailsParamEnums {
  /** 自然人股东 */
  Invest = 'Invest', //自然人股东
  /** 董监高/法人 */
  LegalAndEmploy = 'LegalAndEmploy', //董监高/法人
  ActualController = 'ActualController', //实际控制人
  FinalBenefit = 'FinalBenefit', //受益所有人
  /** 自然人股东（历史） */
  HisInvest = 'HisInvest', //自然人股东（历史）
  /** 董监高/法人（历史） */
  HisLegalAndEmploy = 'HisLegalAndEmploy', //董监高/法人（历史）
  /** 受益所有人 */
  MainInfoUpdateBeneficiary = 'MainInfoUpdateBeneficiary', //受益所有人
  /** 持股/投资股权比例（含历史） */
  ShareholdingRatio = 'ShareholdingRatio', //持股/投资股权比例（含历史）
  /** 分支机构 */
  Branch = 'Branch', //分支机构
  /** 控制关系 */
  Hold = 'Hold',
  /** 相同姓名 */
  SameName = 'SameName', //相同姓名
  /** 相同联系方式 */
  SameContact = 'SameContact', //相同联系方式
  /** 持股关联 */
  ShareholdingRelationship = 'ShareholdingRelationship', //持股关联
  /** 投资关联 */
  InvestorsRelationship = 'InvestorsRelationship', //投资关联
  /** 持股关联（历史） */
  HisShareholdingRelationship = 'HisShareholdingRelationship', //持股关联（历史）
  /** 投资关联（历史） */
  HisInvestorsRelationship = 'HisInvestorsRelationship', //投资关联（历史）
  /** 对外投资 */
  ForeignInvestment = 'ForeignInvestment', //对外投资
  /** 参股股东 */
  Shareholder = 'Shareholder', //参股股东
  /** 董监高/法人关联 */
  EmploymentRelationship = 'EmploymentRelationship', //董监高/法人关联
  /** 对外投资（历史） */
  HisForeignInvestment = 'HisForeignInvestment', //对外投资（历史）
  /** 参股股东（历史） */
  HisShareholder = 'HisShareholder', //参股股东（历史）
  /** 分组 */
  Groups = 'Groups', //分组
  /** 标签 */
  Labels = 'Labels', //标签
  /** 部门 */
  Departments = 'Departments', //部门
  conditionOperator = 'conditionOperator', //匹配条件，并且 或者
  /** 被列入内部黑名单 */
  HitInnerBlackList = 'HitInnerBlackList', //被列入内部黑名单
  /** 董监高/法人关联 */
  Employ = 'Employ', //董监高/法人关联
  /** 历史董监高/法人关联 */
  HisEmploy = 'HisEmploy', //历史董监高/法人关联
  LegalRepresentative = 'LegalRepresentative', //法定代表人
  MajorShareholder = 'MajorShareholder', //大股东
  ShareHolderInvest = 'ShareHolderInvest', //股东类型为投资机构

  Guarantee = 'Guarantee', //相互担保关系
  EquityPledgeRelation = 'EquityPledgeRelation', //股权质押关系
  HasPhone = 'HasPhone', //相同电话号码关系
  WebsiteRelation = 'WebsiteRelation', //相同网站关系
  HasAddress = 'HasAddress', //相同地址关系
  HasEmail = 'HasEmail', //相同邮箱关系
  HasWebsite = 'HasWebsite', //相同网站关系

  // EquityPledgeRelation 对应图数据库中的两种角色
  HasPledgee = 'HasPledgee', //质押人
  Pledge = 'Pledge', //被质押人
  Benefit = 'Benefit', //受益人
  /** 原告 */
  Plaintiff = 'Plaintiff', // 原告
  /** 被告 */
  Defendant = 'Defendant', // 被告

  /**
   * 其他关联方  目前是本司维度中的其他公司
   */
  otherAssociateObject = 'otherAssociateObject',
}
