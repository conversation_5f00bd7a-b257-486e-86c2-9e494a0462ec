/* eslint-disable @typescript-eslint/naming-convention */
export enum DimensionSourceEnums {
  /**
   * 专业版
   */
  Pro = 'Pro',
  /**
   * 信用用大数据
   */
  Credit = 'Credit',
  /**
   * 企业库
   */
  EnterpriseLib = 'EnterpriseLib',
  /**
   * 企业详情
   */
  CompanyDetail = 'CompanyDetail',

  /**
   * Rover
   */
  Rover = 'Rover',

  /**
   * 标讯
   */
  Tender = 'Tender',
  /**
   * 司法案件
   */
  Case = 'Case',

  /**
   * 负面新闻
   */
  NegativeNews = 'NegativeNews',

  /**
   * 裁判文书
   */
  Judgement = 'Judgement',

  /**
   * 税务公告
   */
  TaxAnnouncement = 'TaxAnnouncement',

  /**
   * 股权出质
   */
  Pledge = 'Pledge',
  /**
   * 风险ES
   */
  RiskChange = 'RiskChange',
  /**
   * 特殊黑名单 仅用来详情搜索
   */
  SpecialBlacklist = 'SpecialBlacklist',
  OuterBlacklist = 'OuterBlacklist',

  /**
   * 行政处罚
   */
  SupervisePunish = 'SupervisePunish',

  /**
   * 动产抵押融合ES
   */
  PledgeMergerES = 'PledgeMergerES',

  /**
   * 不良资产ES
   */
  NonPerformingAssets = 'NonPerformingAssets',
}
