import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { MaxLength, IsNumber } from 'class-validator';
import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, JoinColumn } from 'typeorm';
import { UserEntity } from './UserEntity';
import { MobileBiddingEntity } from './MobileBiddingEntity';

@Entity('mobile_bidding_company')
export class MobileBiddingCompanyEntity {
  @PrimaryGeneratedColumn({
    type: 'bigint',
    name: 'id',
  })
  id: bigint;

  @Column('int', {
    nullable: false,
    name: 'org_id',
  })
  orgId: number;

  @Column('int', {
    nullable: false,
    name: 'diligence_id',
  })
  diligenceId: number;

  @Column('varchar', {
    nullable: false,
    length: 45,
    name: 'company_id',
  })
  companyId: string;

  @Column('varchar', {
    nullable: false,
    length: 500,
    name: 'company_name',
  })
  @MaxLength(500)
  companyName: string;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'operator' })
  @ApiProperty({ type: UserEntity, description: '操作人' })
  editor: UserEntity;

  @Column('int', {
    nullable: false,
    name: 'operator',
  })
  @ApiProperty({ description: '操作人ID' })
  @IsNumber()
  @Type(() => Number)
  operator: number;

  //   @Column('json', {
  //     nullable: true,
  //     name: 'details',
  //   })
  //   @ApiProperty()
  //   @Type(() => DiligenceBiddingCompanyDetailPo)
  //   details: DiligenceBiddingCompanyDetailPo;

  @Column('datetime', {
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date | null;

  @Column('datetime', {
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  createDate: Date | null;

  @ManyToOne(() => MobileBiddingEntity, (entity) => entity.companyList, {
    nullable: false,
    onDelete: 'CASCADE',
    onUpdate: 'RESTRICT',
  })
  @JoinColumn({ name: 'diligence_id' })
  mobileBiddingEntity: MobileBiddingEntity | null;
}
