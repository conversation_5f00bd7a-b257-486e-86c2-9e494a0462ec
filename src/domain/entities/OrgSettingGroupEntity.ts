import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { SettingTypeEnums } from '@domain/model/settings/SettingTypeEnums';
import { UserEntity } from './UserEntity';

@Entity('org_setting_group')
export class OrgSettingGroupEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  id: number;

  @Column('int', {
    nullable: false,
    name: 'org_id',
  })
  orgId: number;

  @Column('varchar', {
    nullable: false,
    length: 64,
    name: 'setting_type',
  })
  @ApiProperty({ description: '设置类型' })
  settingType: SettingTypeEnums;

  @Column('int', {
    nullable: false,
    name: 'setting_version',
  })
  @ApiProperty()
  settingVersion: number;

  @Column('int', {
    nullable: false,
    name: 'group_id',
  })
  @ApiProperty()
  groupId: number;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  @ApiProperty({ type: Date })
  @Type(() => Date)
  createDate: Date;

  @Column('int', {
    nullable: false,
    name: 'create_by',
  })
  createBy: number;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'create_by' })
  @ApiProperty({ type: UserEntity, description: '创建人' })
  creator: UserEntity | null;
}
