import { Column, Entity, ManyToMany, PrimaryGeneratedColumn } from 'typeorm';
import { Matches, MaxLength } from 'class-validator';
import { CustomerEntity } from './CustomerEntity';
import { ApiProperty } from '@nestjs/swagger';

/**
 * 联系人实体
 */
@Entity('contact')
export class ContactEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  contactId: number;

  @Column('varchar', {
    nullable: false,
    length: 30,
    name: 'name',
  })
  @MaxLength(30)
  name: string;

  @Column('varchar', {
    nullable: false,
    length: 100,
    name: 'phone',
  })
  @Matches(/^(0\d{2,3}-\d{7,8}(-\d{1,4})?|1[3456789]\d{9})$/, { message: '手机号码格式不正确' })
  @ApiProperty()
  phone?: string;

  @Column('varchar', {
    nullable: false,
    length: 100,
    name: 'email',
  })
  @Matches(
    /^(?:$|([a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*)(?:,\s*([a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*)){0,4})$/,
    { message: '邮箱格式不正确' },
  )
  @ApiProperty()
  email?: string;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  createDate: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;

  @Column('int', {
    nullable: false,
    name: 'org_id',
  })
  orgId: number;

  @ManyToMany(() => CustomerEntity, (customers) => customers.contacts)
  customers: CustomerEntity[];
}
