import { Column, Entity, Index, JoinColumn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { UserEntity } from './UserEntity';
import { ApiProperty } from '@nestjs/swagger';
import { IsDate, IsIn, IsNotEmpty, IsNumber, IsOptional, Matches, MaxLength } from 'class-validator';
import { Type } from 'class-transformer';
import { GroupsEntity } from './GroupsEntity';

@Entity('person')
@Index('org', ['orgId'])
@Index('dep_person_uniq', ['orgId', 'depId', 'personNo'], { unique: true })
export class PersonEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  id: number;

  @Column('varchar', {
    nullable: false,
    length: 100,
    name: 'name',
  })
  @ApiProperty({ description: '姓名' })
  @MaxLength(100)
  @IsNotEmpty()
  @Matches(/^(?! )[\u4E00-\u9FFFa-zA-Z\s]+(?<! )$/, { message: '姓名应只能输入中英文' })
  name: string;

  @Column('varchar', {
    nullable: false,
    length: 100,
    name: 'person_no',
  })
  @ApiProperty({ description: '编号' })
  @MaxLength(100)
  @IsNotEmpty()
  personNo: string;

  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'key_no',
  })
  @ApiProperty({ type: String, description: '人员keyNo' })
  keyNo?: string | null;

  @Column('varchar', {
    nullable: false,
    length: 100,
    name: 'phone',
  })
  @Matches(/^(?:(?:1[3456789]\d{9}(?:[，,]\s?)?){0,5})$/, { message: '手机号码格式不正确或超过五个' })
  @ApiProperty()
  phone?: string;

  @Column('varchar', {
    nullable: false,
    length: 100,
    name: 'email',
  })
  @Matches(
    /^(?:$|([a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*)(?:,\s*([a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*)){0,4})$/,
    { message: '邮箱格式不正确或超过五个' },
  )
  @ApiProperty()
  email?: string;

  @Column('int', {
    nullable: true,
    name: 'card_type',
  })
  @ApiProperty({ type: Number, description: '证件类型：1-身份证' })
  @IsNumber()
  @Type(() => Number)
  cardType: number;

  @Column('varchar', {
    nullable: true,
    length: 100,
    name: 'card_id',
  })
  @MaxLength(100)
  @ApiProperty({ required: false, description: '身份证号码' })
  cardId: string;

  @Column('varchar', {
    nullable: true,
    length: 100,
    name: 'card_id_show',
  })
  @MaxLength(100)
  @ApiProperty({ required: false, description: '身份证号码脱敏展示' })
  cardIdShow: string;

  @Column('varchar', {
    nullable: true,
    length: 16,
    name: 'birth_day',
  })
  @ApiProperty({ required: false, description: '出生日期' })
  @MaxLength(16)
  birthDay: string;

  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'province',
  })
  @ApiProperty({ type: String, description: '省' })
  @IsOptional()
  province: string | null;

  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'city',
  })
  @ApiProperty({ type: String, description: '市' })
  @IsOptional()
  city: string | null;

  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'district',
  })
  @ApiProperty({ type: String, description: '区' })
  @IsOptional()
  district: string | null;

  @Column('int', {
    nullable: true,
    name: 'group_id',
  })
  @ApiProperty({ type: Number, description: '分组id' })
  @IsNumber()
  @Type(() => Number)
  groupId: number;

  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'relationship',
  })
  @ApiProperty({
    type: String,
    description: '关系类型  spouse-配偶,father-父亲, mother-母亲,children-子女, sibling-兄弟姐妹, other-其他 ',
  })
  @IsOptional()
  relationship: string | null;

  @Column('int', {
    nullable: true,
    name: 'relation_person_id',
  })
  @ApiProperty({ type: Number, description: '关联人员Id, 员工本人为-1' })
  @IsNumber()
  @Type(() => Number)
  relationPersonId: number;

  @ManyToOne(() => GroupsEntity)
  @JoinColumn({ name: 'group_id' })
  group: GroupsEntity | null;

  @Column('varchar', {
    nullable: false,
    length: 100,
    name: 'company_name',
  })
  @ApiProperty({ description: '公司名称' })
  @MaxLength(100)
  companyName: string;

  @Column('varchar', {
    nullable: false,
    length: 45,
    name: 'company_id',
  })
  @ApiProperty({ description: '关联的公司ID' })
  @MaxLength(50)
  companyId: string;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  @ApiProperty({ type: Date })
  @IsDate()
  @Type(() => Date)
  createDate: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;

  @Column('int', {
    nullable: false,
    name: 'create_by',
  })
  createBy: number;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'owner_id' })
  @ApiProperty({ type: UserEntity, description: '负责人' })
  owner: UserEntity | null;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'create_by' })
  @ApiProperty({ type: UserEntity, description: '创建人' })
  creator: UserEntity | null;

  @Column('int', {
    nullable: false,
    name: 'owner_id',
  })
  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  ownerId: number;

  @Column('int', {
    nullable: true,
    name: 'org_id',
  })
  orgId: number | null;

  @Column('int', {
    nullable: false,
    name: 'dep_id',
    default: () => '-1',
  })
  depId: number;

  @Column({
    type: 'int',
    name: 'batch_id',
  })
  batchId?: number;

  @Column({
    type: 'int',
    name: 'status',
  })
  @ApiProperty({ description: '1: 处理中， 2:  处理完成' })
  @IsNumber()
  @Type(() => Number)
  @IsIn([1, 2])
  status?: number;

  @Column({
    type: 'int',
    name: 'active',
    default: () => 1,
  })
  @ApiProperty({ description: '1: 正常 2:  已删除' })
  @IsNumber()
  @Type(() => Number)
  @IsIn([1, 2])
  active?: number;

  @ApiProperty({ description: '套餐额度对应的部门套餐 ID,为null或0时使用的是组织套餐' })
  @Column({
    type: 'int',
    name: 'dep_bundle_id',
    default: () => 0,
  })
  @IsNumber()
  @Type(() => Number)
  depBundleId?: number;
}
