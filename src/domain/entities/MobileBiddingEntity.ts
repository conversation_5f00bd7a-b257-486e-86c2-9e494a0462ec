import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { Column, <PERSON>tity, JoinColumn, ManyToOne, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { IsNumber } from 'class-validator';
import { UserEntity } from './UserEntity';
import { DiligenceBiddingDetailPo } from '@domain/model/bidding/DiligenceBiddingDetailPo';
import { MobileBiddingCompanyEntity } from './MobileBiddingCompanyEntity';

@Entity('mobile_bidding')
export class MobileBiddingEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  id: number;

  @Column('int', {
    nullable: false,
    name: 'org_id',
  })
  orgId: number;

  @Column('tinyint', {
    nullable: false,
    default: () => "'0'",
    name: 'risk_level',
  })
  riskLevel: number;

  @Column('int', {
    nullable: false,
    name: 'operator',
  })
  @ApiProperty({ description: '操作人ID' })
  @IsNumber()
  @Type(() => Number)
  operator: number;

  @Column('json', {
    nullable: true,
    name: 'details',
  })
  @ApiProperty()
  // @Type(() => DiligenceBiddingDetailPo)
  details: object;

  @Column('datetime', {
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date | null;

  @Column('datetime', {
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  createDate: Date | null;

  @Column('tinyint', {
    nullable: false,
    default: () => "'1'",
    name: 'status',
  })
  @ApiProperty({ description: '0-排查中 1-排查成功 2-排查失败' })
  status: number;

  @Column('int', {
    nullable: false,
    default: () => "'0'",
    name: 'company_count',
  })
  @ApiProperty({ description: '排查公司数' })
  companyCount: number;

  @Column('text', {
    nullable: true,
    name: 'message',
  })
  message: string;

  @Column('int', {
    nullable: true,
    name: 'active',
    default: () => 1,
  })
  active: number;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'operator' })
  @ApiProperty({ type: UserEntity, description: '操作人' })
  editor: UserEntity;

  @OneToMany(() => MobileBiddingCompanyEntity, (company) => company.mobileBiddingEntity, {
    cascade: ['insert', 'remove', 'update'],
  })
  companyList: MobileBiddingCompanyEntity[];
}
