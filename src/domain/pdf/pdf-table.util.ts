import { RelationTypeConst } from '@domain/constants/model.constants';
import { diffChars } from 'diff';
import { get, intersection, isArray, isEmpty, isNil, isObject, isString, keyBy, reverse, uniq } from 'lodash';
import * as moment from 'moment';
import { getRolesNames, reverseParse, drawEdge } from './relation-path-diligence.util';
import { isCompany, isPerson } from '@domain/utils/batch/export-path-node.utils';

/**
 * 数字格式化选项
 */
interface NumberFormatterOptions {
  /** 千分位分隔符，默认为 ',' */
  separator?: string;
  /** 小数点精度，默认为 0 */
  precision?: number;
  /** 空值时的默认返回值，默认为 '' */
  emptyValue?: string;
}

const STRONG_LEVEL_RELATION = [
  'biddingcompanyrelationship', // 直接关系
  'BiddingCompanyRelationship', // 直接关系
  'legal',
  'employ',
  'invest',
  'hislegal',
  'hisemploy',
  'hisinvest',
  'branch',
  'finalbenefit',
  'actualcontroller',
  'guarantor',
  'equitypledge',
  'chattelmortgage',
  'controlrelation',
  '法定代表人',
  '董监高',
  '股东',
  '历史股东',
  '持股/投资关联',
  '历史法定代表人',
  '历史董监高',
  '历史持股/投资关联',
  '分支机构',
  '控制关系',
  '受益所有人',
  '实际控制人',
  '相互担保关联',
  '股权出质关联',
  '动产抵押关联',
];
const NORMAL_LEVEL_RELATION = [
  'contactnumber',
  'mail',
  'address',
  'upanddownrelation',
  'bidcollusive',
  'website',
  '相同电话号码',
  '相同邮箱',
  '相同地址',
  '上下游关联',
  '围串标关联',
  '客户',
  '供应商',
  '相同域名信息',
  'contactrelationship',
];
const WEAK_LEVEL_RELATION = [
  'patent',
  'intpatent',
  'softwarecopyright',
  'case',
  'samenameemployee',
  '相同专利信息',
  '相同国际专利信息',
  '相同软件著作权',
  '相同司法案件',
  '疑似同名主要人员',
];

// 只要是联系方式关联的关系都是中等关系
const CONTACT_RELATION = ['hasphone', 'hasemail', 'hasaddress'];
/**
 * 根据风险等级获取标签样式（强弱关系）
 */
export const getLevelStyle = (level: number) => {
  const STYLE_MAP = {
    0: {
      background: '#C4F5E0',
    },
    1: {
      background: '#fec',
    },
    2: {
      background: '#fcc',
    },
  };
  const style = STYLE_MAP[level] || {};
  const styleObject = {
    display: 'inline-block',
    height: '20px',
    borderRadius: '2px',
    'line-height': '18px',
    color: ' #666',
    padding: '0 4px',
    ...style,
  };
  const styleText = `display: ${styleObject.display}; background: ${styleObject.background}; color: ${styleObject.color}; border-radius: ${styleObject.borderRadius}; padding: ${styleObject.padding}`;
  return styleText;
};

/**
 * 资质状态颜色映射
 * @param value
 */
export const getCertificationStyle = (value: string): string => {
  if (['有效'].includes(value)) {
    return 'ntag text-success';
  }
  if (['撤销', '注销', '暂停', '已到期', '缺失'].includes(value)) {
    return 'ntag text-danger';
  }
  if (['近7日到期', '近3个月到期', '近1个月到期', '临近到期'].includes(value)) {
    return 'ntag text-warning';
  }
  return 'ntag text-gray';
};

/**
 * 获取关系类型 风险强弱等级 样式, 图谱的关系可能出现多种
 * @param dimensionLevel
 */
export const getRelationTag = (roleTypes: any[] | string = [], isExcel = false) => {
  const roleData = isArray(roleTypes) ? roleTypes.filter((item) => item).map((d) => d.toLowerCase()) : [roleTypes.toLowerCase()];
  let level = 3;
  let label = '';
  if (intersection(CONTACT_RELATION, roleData).length) {
    // 只要是联系方式关联的关系都是中等关系
    level = 1;
    label = '中等关系';
  } else {
    if (intersection(STRONG_LEVEL_RELATION, roleData).length) {
      level = 2;
      label = '强关系';
    } else if (intersection(NORMAL_LEVEL_RELATION, roleData).length) {
      level = 1;
      label = '中等关系';
    } else if (intersection(WEAK_LEVEL_RELATION, roleData).length) {
      level = 0;
      label = '弱关系';
    } else {
      level = 3;
      label = '';
    }
  }
  if (isExcel && label) {
    label = `【${label}】`;
  }

  let result = '';

  if (label) {
    const styleText = getLevelStyle(level);
    const extraStyle = 'width: 62px;text-align: center;transform: translateY(-1px);';
    result = `<span style="margin-right: 6px; ${styleText};${extraStyle}">${label}</span>`;
  }

  return result;
};

export function isEdge(node: Record<string, any>) {
  // 非联系方式关联类型的边
  return node?.startid !== undefined && node?.endid !== undefined && node?.direction !== undefined;
}

export function isContactEdge(node: Record<string, any>) {
  // 联系方式关联的边，type 在[HasPhone, HasEmail, HasAddress]范围内
  return (
    node?.startid !== undefined && node?.endid !== undefined && node?.direction !== undefined && ['HasPhone', 'HasEmail', 'HasAddress'].includes(node?.type)
  );
}

export function isContactNode(node: Record<string, any>) {
  return ['Email', 'Phone', 'Address'].includes(node?.NodeType);
}

export function createEdge(item, isContactEdge?: boolean) {
  const DIRECTION_MAP = {
    '0': '-',
    '-1': '←',
    '1': '→',
  };

  const direction = DIRECTION_MAP[item.direction];
  const isPlainArrow = item.type === '0' && !item.role;
  const isLeftArrow = item.direction === -1;
  const isRightArrow = item.direction === 1;
  const isDash = item.direction === 0;

  // 纯箭头
  if (isPlainArrow) {
    return `<span> ${direction} </span>`;
  }

  let output = getRolesNames(item);
  if (isContactEdge) {
    output = RelationTypeConst[item.type] || '';
  }
  // 带内容的箭头
  if (isLeftArrow) {
    return `<span> ←${output}- </span>`;
  }
  if (isRightArrow) {
    return `<span> -${output}→ </span>`;
  }
  if (isDash) {
    return `<span> -${output}- </span>`;
  }
  return '';
}

const jobPriorityMap: Record<string, string[]> = {
  top0: ['企业主体'],
  top1: ['法定代表人'],
  top2: ['股东', '大股东', '实际控制人', '受益所有人'],
  top3: ['其他关联方'],
};

export const getJobClass = (job: string) => {
  if (jobPriorityMap.top0.includes(job)) {
    return 'grey';
  }
  if (jobPriorityMap.top1.includes(job)) {
    return 'blue';
  }
  if (jobPriorityMap.top2.includes(job)) {
    return 'gold';
  }
  if (jobPriorityMap.top3.includes(job)) {
    return 'green';
  }
  return 'default';
};

export function getEntities(value) {
  const entities: Array<string | number | { KeyNo: string; Name: string; Job?: string } | { keyNo: string; name: string; job?: string }> = Array.isArray(value)
    ? value
    : [value];
  const result = entities.map((entity) => {
    let name: string | number = '';
    if (typeof entity === 'string' || typeof entity === 'number') {
      name = entity;
    }
    if (typeof entity === 'object') {
      if ('Name' in entity) {
        name = entity.Name;
      } else if ('name' in entity) {
        name = entity.name;
      }

      let jobValue: string | undefined;
      if ('Job' in entity) {
        jobValue = entity.Job;
      } else if ('job' in entity) {
        jobValue = entity.job;
      }

      if (jobValue) {
        const jobArr = jobValue.split(',');
        const order = [...jobPriorityMap.top0, ...jobPriorityMap.top1, ...jobPriorityMap.top2];
        const jobRenderArr = intersection(order, jobArr).concat(jobArr.filter((v) => !order.includes(v)));
        const tags = jobRenderArr.map((t) => `<span class="job-tag ${getJobClass(t)}">${t}</span>`).join('') ?? '';
        name = `<span>${name}</span>`;
        if (tags) {
          name += `<div class="job-tag-wrapper">${tags}</div>`;
        }
      }
    }
    return name;
  });
  return result.every((name) => !!name) ? result.join(', ') : '';
}

/**
 * 从字符串中提取数字并进行千分位格式化
 * @param text - 包含数字的字符串
 * @param options - 格式化选项
 * @returns 格式化后的字符串
 */
function formatNumberInString(text: string, options: Pick<NumberFormatterOptions, 'separator' | 'precision' | 'emptyValue'>): string {
  const { separator = ',', precision } = options;

  // 使用正则表达式替换，直接在回调中处理每个匹配
  const numberRegex = /\d+\.?\d*|\.\d+/g;

  const result = text.replace(numberRegex, (match) => {
    const number = parseFloat(match);
    if (isNaN(number) || !isFinite(number)) {
      return match; // 保持原样
    }

    let formattedNumber: string;

    if (precision !== undefined && precision >= 0) {
      // 如果指定了精度，使用指定精度
      formattedNumber = number.toFixed(precision);
    } else {
      // 如果没有指定精度，保持原有的小数位数
      const originalHasDecimal = match.includes('.');
      if (originalHasDecimal) {
        // 保持原有小数位数
        const decimalPlaces = match.split('.')[1]?.length || 0;
        formattedNumber = number.toFixed(decimalPlaces);
      } else {
        // 原数字没有小数点，格式化为整数
        formattedNumber = Math.round(number).toString();
      }
    }

    const [integerPart, decimalPart] = formattedNumber.split('.');
    const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, separator);
    return decimalPart ? `${formattedInteger}.${decimalPart}` : formattedInteger;
  });

  return result;
}

/**
 * 数字千分位格式化函数
 * 接收一个数字或包含数字的字符串，返回带千分位分隔符的字符串
 *
 * @param value - 要格式化的数字或包含数字的字符串
 * @param options - 格式化选项
 * @returns 格式化后的字符串
 *
 * @example
 * ```typescript
 * // 数字输入
 * numberToHuman(1234567.89, { precision: 2, separator: ',' })
 * // 返回: "1,234,567.89"
 *
 * // 字符串输入（提取数字进行格式化）
 * numberToHuman('共有1000.00美元', { precision: 2 })
 * // 返回: "共有1,000.00美元"
 *
 * numberToHuman('总金额：12345.67元', { precision: 2 })
 * // 返回: "总金额：12,345.67元"
 *
 * // 空值处理
 * numberToHuman(null, { emptyValue: '-' })
 * // 返回: "-"
 * ```
 */
export function numberToHuman(value: number | string | null | undefined, options: Partial<NumberFormatterOptions> = {}): string {
  const { separator = ',', precision, emptyValue = '' } = options;

  // 处理空值情况
  if (isNil(value)) {
    return emptyValue;
  }

  // 如果是字符串，提取数字进行格式化
  if (typeof value === 'string') {
    return formatNumberInString(value, { separator, precision, emptyValue });
  }

  // 处理零值情况（仅当明确设置了 emptyValue 且不为空字符串时）
  if (value === 0 && emptyValue && emptyValue !== '') {
    return emptyValue;
  }

  // 处理非数字情况
  if (typeof value !== 'number' || !isFinite(value)) {
    return emptyValue;
  }

  // 格式化数字
  const fixedNumber = precision !== undefined && precision >= 0 ? value.toFixed(precision) : value.toString();
  const [integerPart, decimalPart] = fixedNumber.split('.');

  // 添加千分位分隔符
  const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, separator);

  return decimalPart ? `${formattedInteger}.${decimalPart}` : formattedInteger;
}

export function dateParser(rowData, dataIndex, options = {}) {
  const settings = {
    format: 'YYYY-MM-DD',
    pattern: undefined,
    ...options,
  };
  const d = rowData[dataIndex];
  // 有效数据: '20180803'
  // 无效数据: 0
  if (String(d).length < 4) {
    return '';
  }
  const m = moment(d, settings.pattern);
  return m.isValid() ? m.format(settings.format) : '';
}

export const dateTrans = (date, needMultiple = true) => {
  if (date && date !== '-') {
    date = needMultiple ? date * 1000 : date;
    return moment(date).format('YYYY-MM-DD');
  }
  return '-';
};
/**
 * 货币解析
 */
export function moneyParser(rowData, dataIndex, options = { reduce: 1, precision: 2 }) {
  const defaults = {
    reduce: 1,
  };
  const value = rowData[dataIndex];
  if ((isString(value) && value.trim() === '') || isNil(value)) {
    return '-';
  }
  if (isNaN(Number(value))) {
    return '-';
  }
  return numberToHuman(value / (options.reduce || defaults.reduce), {
    precision: options.precision || 0,
  });
}

/**
 * 货币(带单位)解析
 */
export function moneyWithUnitParser(rowData, dataIndex, options = { reduce: 1, precision: 2 }) {
  const val = rowData[dataIndex];
  if (isNil(val)) {
    return '-';
  }
  const [match] = val.toString().match(/^\d+/) || [];
  if (!match) {
    return val;
  }
  const defaults = {
    reduce: 1,
  };
  const human = numberToHuman(Number(match) / (options.reduce || defaults.reduce), {
    precision: options.precision || 0,
  });
  return val.toString().replace(match, human);
}

/**
 * 企业、人员列表解析
 * @param entities
 */
export function entitiesParser(rowData, dataIndex) {
  const entity = get(rowData, dataIndex);
  if (!entity) {
    return '';
  }
  return getEntities(entity);
}

/**
 * 状态标签
 */
export function statusParser(rowData, dataIndex, options = {}) {
  const item = get(rowData, dataIndex);
  if (!item) {
    return '-';
  }
  let type = 'success';
  let statusText = item;

  switch (statusText) {
    case '存续（在营、开业、在册）':
      statusText = '存续';
      break;
    case '注销':
    case '吊销':
    case '停业':
    case '撤销':
    case '清算':
    case '无效':
    case '责令关闭':
    case '不合格': // ExecuteStatus
      type = 'danger';
      break;
    case '筹建':
    case '迁入':
    case '迁出':
    case '歇业':
      type = 'warning';
      break;

    default:
      break;
  }
  return `<div class="status-tag ${type}">${statusText}</div>`;
}

/**
 * 风险等级
 */
export function riskLevelParser(rowData) {
  const RISK_LEVEL_MAP = {
    1: {
      type: 'warning',
      label: '关注风险',
    },
    2: {
      type: 'danger',
      label: '警示风险',
    },
  };
  const level = rowData.level;
  if (level === undefined || !RISK_LEVEL_MAP[level]) {
    return '-';
  }
  const schema = RISK_LEVEL_MAP[level];
  return `<div class="risk-level-tag ${schema.type}">${schema.label}</div>`;
}

/**
 * 接收两段文本，输出比对结果，使用 <del> 标签包裹变更后被删除部分，使用 <em> 标签包裹变更后新增的部分
 */
export function diffParser(rowData, dataIndex, options: { before: string; after: string }) {
  if (!options.before || !options.after) {
    return '';
  }
  const before = get(rowData, options.before, '');
  const after = get(rowData, options.after, '');

  const output: string[] = [];

  const diffResult = diffChars(after, before);
  diffResult.forEach((part) => {
    let node = '';
    if (part.added) {
      node = `<em>${part.value}</em>`;
    } else if (part.removed) {
      node = `<del>${part.value}</del>`;
    } else {
      node = `<span>${part.value}</span>`;
    }
    output.push(node);
  });
  const html = output.join('');
  return `<div class="diff-result">${html}</div>`;
}

/**
 * 获取标签信息(司法拍卖)
 */
const getJudicialActionTags = (item: Record<string, any>) => {
  const tagLabelList = [...(item.AuctionType || []), item.AuctionRounds, item.AuctionResult].filter(Boolean);
  if (item.IsBankruptcy === 1) {
    tagLabelList.push('破产拍卖');
  }
  const tags = tagLabelList.map((label) => {
    let type = 'primary';
    if (['拍卖成交'].includes(label)) {
      type = 'success';
    } else if (['未成交', '流拍', '破产拍卖'].includes(label)) {
      type = 'danger';
    }
    return {
      label,
      type,
    };
  });
  return tags;
};

/**
 * 司法拍卖信息
 */
export function judicialAuctionParser(item, dataIndex, options = {}) {
  const title = item.name;
  let node = '';
  if (title) {
    node = `<div class="judicial-auction">${title}</div>`;
  }
  if (item.Caseno) {
    node += `<div style="color: #808080;">${item.Caseno}</div>`;
  }
  const tags = getJudicialActionTags(item);
  if (tags.length) {
    node += `<div>`;
    node += tags
      .map(({ type, label }) => {
        return `<span class="status-tag ${type}">${label}</span>`;
      })
      .join('');
    node += `</div>`;
  }
  return node;
}

interface OptionsObject {
  left: string;
  right: string;
  middle: string;
  leftInfo: string;
  rightInfo: string;
}

/**
 * 关联路径
 */
export function pathParser(rowData, dataIndex, options: OptionsObject) {
  const left = get(rowData, options.left, '');
  const right = get(rowData, options.right, '');
  const middle = get(rowData, options.middle, '');
  const leftInfo = get(rowData, options.leftInfo, '');
  const rightInfo = get(rowData, options.rightInfo, '');

  const leftNode = left ? `<span>${left}</span>` : '';
  const rightNode = right ? `<span>${right}</span>` : '';
  const middleNode = middle ? `<span>${middle}</span>` : '';

  const createLine = (content: string, direction: 'left' | 'right') => {
    let output = content;
    // 特殊字段处理
    if (['stockpercent'].indexOf(options[`${direction}Info`]) > -1) {
      output = `${content}%`;
    }
    output = content ? `（${output}）` : '';

    switch (direction) {
      case 'left':
        return `<span>←${output}</span>`;
      case 'right':
        return `<span>${output}→</span>`;
      default:
        return '';
    }
  };

  return `<div>
    ${leftNode}
    ${leftNode ? createLine(leftInfo, 'left') : ''}
    ${middleNode}
    ${rightNode ? createLine(rightInfo, 'right') : ''}
    ${rightNode}
  </div>`;
}

/**
 * 黑名单有效期
 */
export function blacklistParser(rowData, dataIndex, options = {}) {
  const item = rowData[dataIndex];
  if (item === undefined || item === null) {
    return '';
  }
  const BLACKLIST_DURATION_MAP = {
    '-2': '自定义',
    '0': '3个月',
    '1': '6个月',
    '2': '1年',
    '3': '2年',
    '4': '3年',
    '5': '5年',
    '-1': '不限',
  };
  return BLACKLIST_DURATION_MAP[item];
}

/**
 * 文本模板插值替换
 */
export function templateParser(rowData, dataIndex, options: { template: string }) {
  const item = rowData[dataIndex];
  if (item === undefined || item === null) {
    return '';
  }
  return options.template.replace(/\{value\}/g, item);
}

/**
 * from - to 日期范围
 */
export function rangeParser(rowData, dataIndex, options: { from: string; to: string; pattern?: string; type: string; separator: string }) {
  const PLACEHOLDER = '-';
  const SEPARATOR = ' 至 ';
  let start = get(rowData, options.from, '');
  let end = get(rowData, options.to, '');

  if (options.type === 'date') {
    start = dateParser(rowData, options.from, { pattern: options.pattern }) || PLACEHOLDER;
    end = dateParser(rowData, options.to, { pattern: options.pattern }) || PLACEHOLDER;
  }

  const range = [start, end];
  if (range.some((t) => t !== PLACEHOLDER)) {
    return range.join(options.separator || SEPARATOR);
  }
  return PLACEHOLDER;
}

export function jsonParser(rowData, dataIndex, options: { parser: string }) {
  const item = JSON.parse(get(rowData, dataIndex, '{}'));
  if (options.parser === 'entities') {
    return getEntities(item);
  }
  return '-';
}

export const renderCompanyNameRelated = (item) => {
  const elements = [`<span>${item.companyNameRelated}</span>`];
  if (item.history && item.role) {
    elements.push(`<span class="status-tag default">${item.role}</span>`);
  }
  return elements.join('');
};

// 招标排查: 关联黑名单企业名称
export const relatedCompanyNameWithHistoryRole = (item, options: { key: string }) => {
  const companyName = item.relatedCompanyName || item.companyNameRelated;
  const elements = [`<span>${companyName}</span>`];
  if (item.history) {
    // 历史标签处理，针对黑名单的需要单独做下处理
    let role = item.role;
    switch (options.key) {
      case 'Shareholder':
      case 'ShareholdingRelationship':
        role = '历史股东';
        break;
      case 'ForeignInvestment':
      case 'InvestorsRelationship':
        role = '历史对外投资';
        break;
    }
    if (role) {
      elements.push(`<span class="status-tag default">${role}</span>`);
    }
  }
  return elements.join('');
};

/**
 * 持股比例
 */
export function renderStockPercent(stockpercent?: number) {
  if (stockpercent === undefined) {
    return '-';
  }
  return `${stockpercent}%`;
}

export function blacklistDurationFormatter(duration) {
  if (duration === undefined || duration === null) {
    return '-';
  }

  const BLACKLIST_DURATION_MAP = {
    '0': '3个月',
    '1': '6个月',
    '2': '1年',
    '3': '2年',
    '4': '3年',
    '5': '5年',
    '-1': '不限',
    '-2': '自定义',
  };

  // duration后端返回了两种，一种是时间戳，一种是type，type只有0-8，大于100默认它是时间戳，做处理
  if (duration > 100) {
    return moment
      .duration(duration * 1000)
      .locale('zh-Cn')
      .humanize();
  }
  return BLACKLIST_DURATION_MAP[duration] || '-';
}

/**
 * 相同实际控制人关联关系（控制路径/控制链）
 */
export function controlRelationsPath(_, item, originalData) {
  const paths = item?.details?.path || [];
  if (paths.length === 0) {
    return '';
  }
  return paths
    .map((pList, index) => {
      const description = `控制路径${index + 1}（占比约 ${pList[pList.length - 1]?.PercentTotal} ）`;
      const entities = pList.map((p) => `（${p.Percent}） → ${p.Name}`);
      // 添加实际控制人及其权占比
      const graph = [`${item.name}`, ...entities].join('');
      return `<div>
            <div style="font-weight: bold;">${description}</div>
            <div>${graph}</div>
          </div>`;
    })
    .join('');
}

/**
 * 潜在利益冲突
 */
export function interestConflictStaffColumnRender(_, item) {
  // 关联人的样式
  const getPNO = () => {
    const pNo = item.personNo;
    if (item.relationPersonId && item.relationPersonId !== -1) {
      const name = pNo.split(`_${item.relationship}`)[0];
      return `
            <span>
              <span>${name}</span>_<span>${item.relationPersonName}—</span>
              <span>${item.name}</span>
              <span>（${item.relationship}）</span>
            </span>
          `;
    }
    return `
          <span>
            ${pNo}_${item.name}
          </span>
        `;
  };
  return `
        <span>
          ${getPNO()}
          ${item.status === 1 ? '<span class="status-tag danger">是本人</span>' : ''}
        </span>
      `;
}

/**
 * 涉诉围串标记录-当事人
 */
export function casePartiesColumnRender(_, item) {
  const getStyleByJudgeDescription = (text: string) => {
    if (['支持', '解除财产保全', '对方被驳回', '执行完毕', '申请人被驳回', '部分支持', '同意追加被执行人'].includes(text)) {
      return 'color: #0aad65';
    }
    if (['驳回', '不支持', '驳回上诉', '财产保全', '不支持', '终结本次执行'].includes(text)) {
      return 'color: #F04040';
    }
    return 'color: #999';
  };
  // 获取当事人信息
  const getParties = (_item) => {
    if (_item.caserolegroupbyrolename?.length) {
      return _item.caserolegroupbyrolename
        ?.filter((item2) => item2.LawyerTag === 0 || isNil(item2.LawyerTag))
        ?.flatMap((item2) =>
          item2.DetailList?.map((item3) => ({
            ...item3,
            Role: item2.Role,
          })),
        );
    }

    if (_item.caserole?.length) {
      return _item.caserole.map((item2) => ({
        ...item2,
        Role: item2.R,
        Name: item2.P || item2.ShowName,
        KeyNo: item2.N,
        Org: item2.O,
      }));
    }
    return [];
  };

  const getStyleByJob = (job: string) => {
    if (job === '企业主体') {
      return `color: #666; background: #eee`;
    }
    return `color: #bb833d; background: #f6f0e7`;
  };

  // const parties = [...getParties(item)];

  const parties = [
    ...getParties(item),
    ...(item.involveRole || []).map((role) => ({
      ...role,
      Role: role.Tag,
    })),
  ];

  if (item?.involveRole?.length) {
    parties.push(
      ...item.involveRole.map((role) => ({
        ...role,
        Role: role.Tag,
      })),
    );
  }

  if (!parties?.length) {
    return '-';
  }

  let content = '';

  parties.forEach((item) => {
    content += `<div>`;
    // 角色 - 名称
    content += `<span>${item?.Role} - ${item?.Name}</span>`;

    // 描述
    if (item.JudgeResultDescription) {
      content += ` <span style="${getStyleByJudgeDescription(item.JudgeResultDescription)}">[${item.JudgeResultDescription}]</span>`;
    }

    // 角色
    if (item.Job) {
      content += ` <span style="${getStyleByJob(item.Job)}">${item.Job}</span>`;
    }

    content += `</div>`;
  });

  return content;
}

/**
 * 直接关系-关联详情链
 */
export function relationsPathParser(relations, record: Record<string, any> = {}): string {
  if (!Array.isArray(relations) || relations.length === 0) {
    return '-';
  }

  const nodes: string[] = [];

  relations.forEach((item) => {
    if (isContactEdge(item)) {
      nodes.push(createEdge(item, true));
    } else if (isEdge(item)) {
      nodes.push(createEdge(item));
    } else {
      if (isContactNode(item)) {
        nodes.push(`<span>${item['Address.name'] || item['Email.name'] || item['Phone.name']}</span>`);
      } else {
        const isHighlight = [record.startCompanyKeyno, record.endCompanyKeyno].includes(item['Company.keyno'] || item['Person.keyno']);
        nodes.push(`<span style="color: ${isHighlight ? '#128bed' : '#333'};">${item['Company.name'] || item['Person.name']}</span>`);
      }
    }
  });

  return nodes.join('') || '-';
}

/**
 * 获取全部角色
 */
export const correctRoles = (relations) => {
  let roles: string[] = [];
  relations.forEach((relation) => {
    if (!isEdge(relation)) {
      return;
    }
    if (isContactEdge(relation)) {
      roles = [...roles, relation.type];
      return;
    }

    if (Array.isArray(relation.roles)) {
      // 嵌套数组
      roles = [...roles, ...relation.roles.map((role) => role.type)];
    } else {
      roles = [...roles, relation.type && !['0', '1'].includes(relation.type) ? relation.type : relation.role];
    }
  });
  return uniq(roles);
};

/**
 * 疑似关系-关联详情链
 */
export function relationsPathParser2(record, isExcel = false): string {
  if (record.relations?.length) {
    const html = record.relations
      ?.flatMap((relations, index, source) => {
        // data 包含的额外信息（例如：返回的结果数量，用来展示更多信息、弹窗等）
        const roles = correctRoles(relations);
        const levelTag = getRelationTag(roles, isExcel);

        // // 显示路径
        // const step = source.length > 1 ? `<div style="font-weight: 700;">路径：${index + 1}</div>` : '';
        // return step + relationsPathParser(relations) + levelTag;

        const data = record?.data?.[index] ?? null;
        // 关联路径
        if (data === null) {
          return levelTag + relationsPathParser(relations, record);
        }

        // nodes.push(`<span>${item['Company.name'] || item['Person.name']}</span>`);

        // 从 Edge 中提取关联信息
        const roleInfo = relations.find((v) => v?.role) || {};

        if (Array.isArray(data)) {
          // { key: 'ContactNumber', name: '相同电话号码' },
          // { key: 'Website', name: '相同域名信息' },
          // { key: 'Address', name: '相同地址' },
          // { key: 'Mail', name: '相同邮箱' },
          // { key: 'Patent', name: '相同专利信息' },
          // { key: 'IntPatent', name: '相同国际专利信息' },
          // { key: 'SoftwareCopyright', name: '相同软件著作权' },
          const dataList = data.map((listItem) => {
            // 展示内容：相同邮箱
            if (isString(listItem)) {
              return listItem;
            }
            if (listItem?.t) {
              return listItem.t;
            }
            if (listItem?.Tel) {
              return listItem.Tel;
            }
            if (listItem?.address) {
              return listItem.address;
            }
            // 展示内容：相同电话号码
            return String(listItem);
          });

          const countText = roleInfo.role ? `${roleInfo.role}: ` : null;
          const result = `${countText}${uniq(dataList).join('、')}` || '-';
          return levelTag + result;
        }

        if (isObject(data)) {
          const total = get(data, 'Paging.TotalRecords', 0);
          const countText = `${roleInfo.role || ''}数量: ${total}`;
          return levelTag + countText;
        }

        return '-';
      })
      .map((html: string, index: number, originList: any[]) => {
        // 显示路径
        const step = originList.length > 1 ? `<div style="font-weight: 700;">路径：${index + 1}</div>` : '';
        return `<div>${step}${html}</div>`;
      })
      .join('');
    return `<div style="display: flex; flex-direction: column; gap: 4px;">${html}</div>`;
  }

  return '-';
}

/**
 * 疑似关系
 */
export function relationsTypeParser(record, isExcel = false) {
  // TODO: [关联关系重构] 关联类型抽离为独立函数
  // 遍历关系链上所有角色
  const roles = record.relations.flatMap((relations, index) => {
    const relationsInfo = relations.flatMap((node) => {
      if (!isEdge(node)) {
        return [];
      }
      // 对关联关系进行处理，使`关联类型`与设置中心保持一致
      // record.data 为对象时，仅显示第一层关系
      if (isObject(record?.data?.[index])) {
        const edgeInfo = relations.find((v) => v?.role) || {};
        return edgeInfo.role;
      }
      // FIXME: 临时方案（相同地址）
      if (isArray(node.roles)) {
        const addressRoles = node.roles.map(({ role, type }) => {
          return RelationTypeMap[type.toUpperCase()]?.label ?? role;
        });
        return addressRoles;
      }
      return RelationTypeMap[node?.type?.toUpperCase()]?.label ?? node.role?.split(',');
    });
    return relationsInfo;
  });
  if (isExcel) {
    return uniq(roles.filter(Boolean)).join('、');
  }
  return uniq(roles.filter(Boolean)).join('<br />');
}

/**
 * 直接关系
 */
export function directRelationalTypes(record, isExcel = false) {
  const roles = record.relations.flatMap((relations, index) => {
    const relationsInfo = relations.flatMap((node) => {
      if (!isEdge(node)) {
        return [];
      }

      // 对关联关系进行处理，使`关联类型`与设置中心保持一致
      // record.data 为对象时，仅显示第一层关系
      if (isObject(record?.data?.[index])) {
        const edgeInfo = relations.find((v) => v?.role) || {};
        return edgeInfo.role;
      }

      // FIXME: 临时方案（相同地址）
      if (isArray(node.roles)) {
        const addressRoles = node.roles.map(({ role, type }) => {
          return RelationTypeMap[type.toUpperCase()]?.label ?? role;
        });
        return addressRoles;
      }
      const plainRoles = node.role?.split(',');
      const filteredRoles = plainRoles?.filter(Boolean);
      if (filteredRoles?.length) {
        return uniq(plainRoles.map((role) => RelationTypeMap[role.toUpperCase()]?.label ?? reverseParse(role)));
      }

      return RelationTypeMap[node?.type?.toUpperCase()]?.label ?? [];
    });
    return relationsInfo;
  });
  if (isExcel) {
    return uniq(roles.filter(Boolean)).join('、');
  }
  return uniq(roles.filter(Boolean)).join('<br />');
}

const ContractNameMap = {
  Email: '相同邮箱',
  Phone: '相同电话',
  Address: '相同地址',
};

/**
 * 联系方式关系
 */
export const relationParserContact = (relations: any[]) => {
  const result = relations
    .reduce((arr, node) => {
      const isEdge = !!node.type;
      const NodeType = node.NodeType; // 节点类型
      if (isPerson(node)) {
        arr.push(node['Person.name']);
      } else if (isCompany(node)) {
        arr.push(node['Company.name']);
      } else if (isEdge) {
        const line = drawEdge(node.direction, getRolesNames(node).join());
        arr.push(line);
      } else {
        arr.push(`${ContractNameMap[NodeType]}(${node[`${NodeType}.name`]})`);
      }
      return arr;
    }, [])
    .join('');
  const tag = getRelationTag(['mail']);
  return result ? `<div>${tag}${result}</div>` : '';
};

/**
 * 与内部黑名单列表存在分支机构关联
 * @param item
 * @returns
 */
export function companyBranchPath(item) {
  const role = item.role;

  const path = [item.companyNameDD, item.companyNameRelated];

  const pathArr = item.direction > 0 ? path : reverse(path);

  return pathArr.join(`<span> -（${role}）→ </span>`);
}

export const successStatusTxt = [
  '在业',
  '存续',
  '筹建',
  '新申请用户',
  '已成立事先报批',
  '成立事先报批中',
  '成立中',
  '名称核准发起中',
  '名称核准通过',
  '已成立',
  '正常',
  '仍注册',
  '接管',
  '核准设立',
  '核准认许',
  '核准许可登记',
  '核准许可',
  '核准报备',
  '核准许可报备',
  '核准登记',
  '有效',
  '核准設立',
  '核准認許',
  '核准許可登記',
  '核准許可',
  '核准報備',
  '核准許可報備',
  '核准登記',
  '核準設立',
  '核準認許',
  '核準許可登記',
  '核準許可',
  '核準報備',
  '核準許可報備',
  '核準登記',
  'ACTIVE',
  'CONVERTED',
  'INCORPORATED',
  'MERGED',
  'OTHERS',
  'PERPETUAL',
  'REDEEMED',
  'UNKNOWN',
  'AMALGAMATED',
  'IN BUSINESS',
  'RESERVED',
  'CONVERSION',
  'RE-INSTATEMENT',
  '存续（在营、开业、在册）',
  '迁出',
  '迁入',
];

export const dangerStatusTxt = [
  '清算',
  '撤销',
  '责令关闭',
  '吊销',
  '已撤销',
  '终止破产',
  '涂销破产',
  '清理完结',
  '清理',
  '破产清算完结',
  '破产程序终结',
  '破产',
  '废止清算完结',
  '废止许可完结',
  '废止许可',
  '废止认许',
  '废止认许完结',
  '废止登记完结',
  '废止登记',
  '废止',
  '撤销完结',
  '撤销无需清算',
  '撤销许可',
  '撤销',
  '撤销认许',
  '撤销认许完结',
  '撤回认许',
  '撤回认许完结',
  '无效',
  '終止破產',
  '涂銷破產',
  '清理完結',
  '破產清算完結',
  '破產程序終結',
  '破產',
  '廢止清算完結',
  '廢止許可完結',
  '廢止許可',
  '廢止認許完結',
  '廢止登記完結',
  '廢止登記',
  '廢止',
  '撤銷完結',
  '撤銷無需清算',
  '撤銷許可',
  '撤銷',
  '撤銷認許',
  '撤銷認許完結',
  '撤回認許',
  '撤回認許完結',
  'ABANDONED',
  'CANCELED',
  'CANCELLED',
  'DELINQUENT',
  'DISSOLVED',
  'EXPIRED',
  'FORFEITED',
  'INACTIVE',
  'REMOVED',
  'SUSPENDED',
  'TERMINATED',
  'WITHDRAWN',
  'REVOKED',
  'LIQUIDATION',
  'STRIKE OFF',
  'STRIKING OFF',
  'DEFUNCT',
  'NOT AVAILabel',
  'DORMANT',
  'CAPTURED',
  'DEREGISTRATION',
  'DUPLICATE',
  'DEREGISTERED',
  'NO STATUS',
  'ARCHIVE',
  '撤銷',
  '廢止',
  '除名',
];

export const warningStatusTxt = [
  '注销',
  '停业',
  '歇业',
  '已告解散',
  '已终止注册',
  '已終止註冊',
  '停業',
  '名称核准不通过',
  '注销中',
  '已终止营业地点',
  '不再是独立的实体',
  '休止活动',
  '重整',
  '解散',
  '解散清算完结',
  '设立但已解散',
  '合并解散',
  '分割解散',
  '撤销设立',
  '撤销登记完结',
  '撤销登记',
  '撤回登记',
  '撤回登记完结',
  '解散清算完結',
  '設立但已解散',
  '合併解散',
  '撤銷設立',
  '撤銷登記完結',
  '撤銷登記',
  '撤回登記完結',
  '撤回登記',
  '撤銷設立',
  '撤銷登記完结',
  '撤銷登記',
  '撤回登記完结',
  '撤回登记',
  '設立但已解散',
  '合並解散',
  '解散清算完結',
  '经营异常',
  '裁判文书',
  '严重违法',
  '失信被执行人',
  '税收违法',
  '行政处罚',
  '开庭公告',
];

export enum CompanyStatus {
  Danger = 'danger',
  Warning = 'warning',
  Primary = 'primary',
  Success = 'success',
  Default = 'shade',
}

export const getCompanyStatusColor = (status) => {
  let result = CompanyStatus.Default;
  if (dangerStatusTxt.includes(status)) {
    result = CompanyStatus.Danger;
  } else if (warningStatusTxt.includes(status)) {
    result = CompanyStatus.Warning;
  } else if (successStatusTxt.includes(status)) {
    result = CompanyStatus.Success;
  }
  return result;
};

function generateCompanyStatusTag(status: string) {
  const defaultTagType = getCompanyStatusColor(status);
  return `<span class="status-tag ${defaultTagType}">${status}</span>`;
}

/**
 * 企业登记状态
 */
export function companyStatusParser(record, dataIndex) {
  const statusText = record[dataIndex];
  return generateCompanyStatusTag(statusText);
}

/**
 * 与设置中心的选项保持一致
 */
export const RelationTypeOptions = [
  // 投资任职关联（直接关系）
  { label: '法定代表人', value: 'Legal', type: 0 },
  { label: '历史法定代表人', value: 'HisLegal', type: 0 },
  { label: '持股/投资关联', value: 'Invest', type: 0 },
  { label: '董监高', value: 'Employ', type: 0 },
  { label: '历史董监高', value: 'HisEmploy', type: 0 },
  { label: '历史持股/投资关联', value: 'HisInvest', type: 0 },
  { label: '分支机构', value: 'Branch', type: 0 },
  { label: '实际控制人', value: 'ActualController', type: 0 },
  { label: '控制关系', value: 'Hold', type: 0 },
  { label: '控制关系', value: 'ControlRelation', type: 0 },

  // 潜在关联/利益关联方（疑似关系）
  { label: '相同电话号码', value: 'ContactNumber', type: 1 },
  { label: '相同邮箱', value: 'Mail', type: 1 },
  { label: '相同地址', value: 'Address', type: 1 },
  { label: '相互担保关联', value: 'Guarantor', type: 1 },
  { label: '股权出质关联', value: 'EquityPledge', type: 1 },
  { label: '动产抵押关联', value: 'ChattelMortgage', type: 1 },
  { label: '上下游关联', value: 'UpAndDownRelation', type: 1 },
  { label: '围串标关联', value: 'BidCollusive', type: 1 },
  { label: '相同域名信息', value: 'Website', type: 1 },
  { label: '相同专利信息', value: 'Patent', type: 1 },
  { label: '相同国际专利信息', value: 'IntPatent', type: 1 },
  { label: '相同软件著作权', value: 'SoftwareCopyright', type: 1 },
  { label: '相同司法案件', value: 'Case', type: 1 },
  { label: '疑似同名主要人员', value: 'SameNameEmployee', type: 1 },

  // 联系方式关联
  { label: '相同电话号码', value: 'HasPhone', type: 1 },
  { label: '相同邮箱', value: 'HasEMail', type: 1 },
  { label: '相同地址', value: 'HasAddress', type: 1 },
];

export const RelationTypeMap = keyBy(
  RelationTypeOptions.map((item) => ({ ...item, value: String(item.value).toUpperCase() })),
  'value',
);
enum ColorsEnum {
  Default = '#999',
  Positive = '#0aad65',
  Negative = '#F04040',
}

/**
 * 合同纠纷文本颜色映射
 */
const getColorByDescription = (text: string): { color: ColorsEnum } => {
  const colorMap = new Map<string, ColorsEnum>([
    // 正面
    ['支持', ColorsEnum.Positive],
    ['解除财产保全', ColorsEnum.Positive],
    ['对方被驳回', ColorsEnum.Positive],
    ['执行完毕', ColorsEnum.Positive],
    ['申请人被驳回', ColorsEnum.Positive],
    ['部分支持', ColorsEnum.Positive],
    ['同意追加被执行人', ColorsEnum.Positive],
    // 负面
    ['驳回', ColorsEnum.Negative],
    ['不支持', ColorsEnum.Negative],
    ['驳回上诉', ColorsEnum.Negative],
    ['财产保全', ColorsEnum.Negative],
    ['终结本次执行', ColorsEnum.Negative],
  ]);

  return {
    color: colorMap.get(text) || ColorsEnum.Default,
  };
};
// 裁定文书的状态
export const renderJudgeResultDescription = (listItem) => {
  return listItem?.JudgeResultDescription
    ? `<span style="color: ${getColorByDescription(listItem.JudgeResultDescription)?.color}">[${listItem.JudgeResultDescription}]</span>`
    : '';
};

// 董监高法tag
export const renderJobTags = (listItem) => {
  let jobStr = '';
  listItem?.Job?.split(',').forEach((job) => {
    jobStr += `<span class="job-tag ${getJobClass(job)}" style="margin:2px 1px;">${job}</span>`;
  });

  return jobStr;
};

// 涉案人状态
export const getInvolveTag = (curItem: any, item: any) => {
  const findTag = item?.involveRole?.find((role) => role.KeyNo === curItem.N);
  return findTag?.Tag ? `<span class="status-tag default">${findTag?.Tag}</span>` : '';
};

// 加密名字和全名之间的展示逻辑 如果名字只有一个，就展示链接，如果有2个，就展示加密名字和全名
const renderShowHideName = (nameList: string[], KeyNo?: string) => {
  const uniqNameList = uniq(nameList.filter(Boolean));
  if (uniqNameList.length === 1 || !KeyNo) {
    return `<span>${uniqNameList[0]}</span>`;
  }
  return `<span>${uniqNameList[0]}（${uniqNameList[1]}）</span>`;
};

const renderRoleDes = (listItem, keyMap: { name: string | string[]; no: string }, needNo, index, slotNodeRender) => {
  const { name, no } = keyMap || {};
  const KeyNo = get(listItem, no);
  const NameList = (isArray(name) ? name : [name]).map((nameKey) => get(listItem, nameKey));
  const nameStr = renderShowHideName(NameList, KeyNo);
  const baseComp = nameStr + (slotNodeRender ? slotNodeRender(listItem) : '');
  if (needNo) {
    return `<span>${index + 1}.</span>${baseComp}`;
  }
  return baseComp;
};

/**
 * roleObj: 按身份聚合的数据
 * keyMap：名字取值的字段
 * indexLimit：超过多少展示条数
 */
export const renderNameList = (
  roleObj: Record<string, any[]>,
  keyMap: { name: string | string[]; no: string },
  indexLimit: number,
  slotNodeRender?: (item: any) => string,
) => {
  if (isEmpty(roleObj)) {
    return '-';
  }
  let divText = '<div>';
  // 如果角色list < indexLimit，那么合并展示，否则带序号单独成行
  Object.keys(roleObj).forEach((key: string) => {
    const list = roleObj[key];
    if (list.length > 0) {
      const listLength = list.length;
      const needNo = listLength > indexLimit;
      const nameListArr = list.map((listItem, index) => renderRoleDes(listItem, keyMap, needNo, index, slotNodeRender));
      // 样式处理太麻烦，干脆分开展示
      divText += `<div>${key}：`;
      if (!needNo) {
        nameListArr.forEach((name, index) => {
          divText += `<span>${name}</span>`;
          if (index < listLength - 1) {
            divText += `,`;
          }
        });
      } else {
        nameListArr.forEach((name) => {
          divText += `<div>${name}</div>`;
        });
      }
    }
  });
  divText += '</div>';
  return divText;
};
