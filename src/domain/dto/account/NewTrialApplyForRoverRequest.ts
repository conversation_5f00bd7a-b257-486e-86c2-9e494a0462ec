import { ApiProperty } from '@nestjs/swagger';
import { IsIn, IsNotEmpty, IsOptional } from 'class-validator';

// import { APPLY_SOURCE } from '../order.constant';

export class NewTrialApplyForRoverRequest {
  @ApiProperty({ type: String, required: true, description: '企业名称' })
  @IsNotEmpty()
  companyName: string;

  @ApiProperty({ type: String, required: false, description: '工商Id' })
  @IsOptional()
  refCompanyId?: string;

  @ApiProperty({ type: String, required: true, description: '联系人姓名' })
  @IsNotEmpty()
  name: string;

  @ApiProperty({ type: String, required: true, description: '联系人电话' })
  @IsNotEmpty()
  phone: string;

  @ApiProperty({ type: String, required: false, description: '验证码' })
  @IsOptional()
  code?: string;

  // @ApiProperty({ type: Number, required: true, description: '申请来源： 0-立即使用； 1-联系商务； 3-SEM； 4-风控管家用户邀请; ' })
  // @IsNotEmpty()
  // // @IsIn([APPLY_SOURCE.IMMEDIATE_USE, APPLY_SOURCE.CONTACT_BUSINESS, APPLY_SOURCE.SEM, APPLY_SOURCE.RISK])
  // applySource: number;

  @ApiProperty({ type: String, required: false, description: '备注' })
  @IsOptional()
  remarks: string;

  // @ApiProperty({ type: String, required: true, description: '使用方向：个人使用/企业使用' })
  // @IsNotEmpty()
  // @IsIn(['个人使用', '企业使用'])
  // usage: string;

  @ApiProperty({ type: String, required: true, description: '来源渠道: 第三方风险WEB-申请试用' })
  @IsNotEmpty()
  @IsIn(['第三方风险WEB-申请试用', '第三方风险H5-申请试用', '产品中心-申请试用', 'APP-招标排查', '招标排查'])
  channel: string;

  @ApiProperty({ type: Array, required: false, description: '线索分组' })
  @IsOptional()
  tagIds?: number[];

  @ApiProperty({ type: String, required: false, description: '推广关键字' })
  @IsOptional()
  tracking?: string;

  // @ApiProperty({ type: String, isArray: true, required: true, description: '需求类型：找企业客户/找个人客户' })
  // @IsArray()
  // @ArrayNotEmpty()
  // @IsIn(['找企业客户', '找个人客户'], { each: true })
  // requirements: string[];

  // @ApiProperty({ type: String, required: true, description: '重点关注：拓客/CRM/AI外呼' })
  // @IsNotEmpty()
  // @IsIn(['拓客', 'CRM', 'AI外呼'])
  // focusOn: string;

  /**
   * //测试 线索来源： 2493 平台申请 / 2498 线上自助 / 20508 B端官网 / 23531 企查查 APP
   * //线上 线索来源:  158770 平台申请 / 158775 线上自助 / 730290 B端官网 / 764923 企查查 APP
   */
  @ApiProperty({ type: Number, required: false, description: '来源ID' })
  @IsOptional()
  sourceId?: number;

  @ApiProperty({ type: String, required: false, description: '职位' })
  @IsOptional()
  position?: string;

  @ApiProperty({ type: String, required: false, description: '邮箱' })
  @IsOptional()
  email?: string;
}
