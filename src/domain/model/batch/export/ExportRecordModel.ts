import { ExportEnums } from '@domain/enums/batch/ExportEnums';
import { ApiProperty, IntersectionType, PickType } from '@nestjs/swagger';
import { SearchCustomerResponseItemPO } from '@domain/dto/customer/SearchCustomerResponse';
import { DiligenceHistoryEntity } from '@domain/entities/DiligenceHistoryEntity';
import { AnalyzedResultCompanyPO } from '@domain/model/monitor/AnalyzedResultCompanyPO';
import { InnerBlacklistResponseItem } from '@domain/dto/blacklist/SearchInnerBlacklistResponse';
import { PersonEntity } from '@domain/entities/PersonEntity';
import { MonitorRiskDynamicsV2Entity } from '@domain/entities/MonitorRiskDynamicsV2Entity';
import { MonitorRiskDynamicResponseItemPO } from '@domain/model/monitor/response/NegativeNewsResponse';
import { DiligenceAnalyzeResponseItemPO } from '@domain/model/diligence/pojo/analyze/DiligenceAnalyzeResponseItemPO';
import { PaginationParams } from '@commons/model/common';
import { DiligenceTenderHistoryEntity } from '@domain/entities/DiligenceTenderHistoryEntity';
import { SpecificInterestRecordEntity } from '@domain/entities/SpecificInterestRecordEntity';
import { BatchBusinessTypeEnums } from '@domain/enums/batch/BatchBusinessTypeEnums';
import { MonitorGroupCompany } from '@domain/model/monitor/response/SearchCompanyResponse';

export class ExportConditionBase extends IntersectionType(PaginationParams) {
  userId?: number;
  orgId?: number;
  @ApiProperty({ description: '导出目标batchId' })
  targetId?: number;
}

export class ExportRecordItemPO {}

export class CustomerExportRecordItemPO extends IntersectionType(ExportRecordItemPO, SearchCustomerResponseItemPO) {}

export class ExportDiligenceStatisticsItemPO extends ExportRecordItemPO {
  key: string;
  value: number;
}

export class ExportDiligenceDetailsItemPO extends IntersectionType(ExportRecordItemPO, DiligenceHistoryEntity) {}

export class RiskInterestConflictExportRecordItemPO extends IntersectionType(
  ExportRecordItemPO,
  PickType(DiligenceHistoryEntity, ['snapshotId', 'creditcode'] as const),
) {
  companyName: string;
}

export class SuspectedRiskInterestConflictExportRecordItemPO extends RiskInterestConflictExportRecordItemPO {}

export class DiligenceRecordExportRecordItemPO extends ExportDiligenceDetailsItemPO {}

export class DiligenceAnalysisExportRecordItemPO extends IntersectionType(ExportRecordItemPO, AnalyzedResultCompanyPO) {}

export class DimensionDetailExportRecordItemPO extends ExportRecordItemPO {
  [key: string]: Record<string, any>[];
}

export class InnerBlacklistExportRecordItemPO extends IntersectionType(ExportRecordItemPO, InnerBlacklistResponseItem) {}

export class PersonListExportRecordItemPO extends IntersectionType(ExportRecordItemPO, PersonEntity) {}

export class RiskExportRecordItemPO extends IntersectionType(ExportRecordItemPO, MonitorRiskDynamicsV2Entity) {}

export class BundleConsumeDetailItemPO extends IntersectionType(ExportRecordItemPO) {}

export class SentimentExportRecordItemPO extends IntersectionType(ExportRecordItemPO, MonitorRiskDynamicResponseItemPO) {}

export class TenderListExportRecordItemPO extends ExportRecordItemPO {
  [key: string]: any;
}

export class ExportTenderStatisticsItemPO extends ExportRecordItemPO {
  key: string;
  value: number;
}

export class ExportTenderDetailsItemPO extends IntersectionType(ExportRecordItemPO, DiligenceTenderHistoryEntity) {}

export class ExportSpecificListItemPO extends IntersectionType(ExportRecordItemPO, SpecificInterestRecordEntity) {}

export class ExportTenderDimensionDetailListItemPO extends IntersectionType(ExportRecordItemPO, DiligenceTenderHistoryEntity) {}

export class MonitorCompanyExportRecordItemPO extends IntersectionType(ExportRecordItemPO, MonitorGroupCompany) {}

export class ExportRecordModel {
  [ExportEnums.DiligenceRecord]?: DiligenceRecordExportRecordItemPO[];
  // [ExportEnums.DiligenceAnalyze]?: DiligenceAnalysisExportRecordItemPO[];
  [ExportEnums.DiligenceAnalyze]?: DiligenceAnalyzeResponseItemPO[];
  [ExportEnums.DiligenceStatistics]?: ExportDiligenceStatisticsItemPO[];
  [ExportEnums.RiskInterestConflict]?: RiskInterestConflictExportRecordItemPO[];
  [ExportEnums.SuspectedRiskInterestConflict]?: SuspectedRiskInterestConflictExportRecordItemPO[];
  // [ExportEnums.DiligenceResult]?: ExportDiligenceDetailsItemPO[];
  [ExportEnums.DiligenceResult]?: DiligenceAnalyzeResponseItemPO[];
  [ExportEnums.PersonList]?: PersonListExportRecordItemPO[];
  [ExportEnums.InnerBlacklist]?: InnerBlacklistExportRecordItemPO[];
  [ExportEnums.CustomerList]?: CustomerExportRecordItemPO[];
  [ExportEnums.DimensionDetail]?: DimensionDetailExportRecordItemPO[];
  [ExportEnums.TenderList]?: TenderListExportRecordItemPO[];
  [ExportEnums.Risk_Export]?: RiskExportRecordItemPO[];
  [ExportEnums.Bundle_Diligence_Consume_Detail_Export]?: BundleConsumeDetailItemPO[];
  [ExportEnums.Bundle_Analyze_Record_Consume_Detail_Export]?: BundleConsumeDetailItemPO[];
  [ExportEnums.Bundle_Bidding_Consume_Detail_Export]?: BundleConsumeDetailItemPO[];
  [ExportEnums.Bundle_Special_Consume_Detail_Export]?: BundleConsumeDetailItemPO[];
  [ExportEnums.Sentiment_Export]?: SentimentExportRecordItemPO[];
  [ExportEnums.TenderStatic_Export]?: ExportTenderStatisticsItemPO[];
  [ExportEnums.TenderDetail_Export]?: ExportTenderDetailsItemPO[];
  [ExportEnums.SpecificRecordList]?: ExportSpecificListItemPO[];
  [BatchBusinessTypeEnums.Tender_Dimension_Detail_Export]?: ExportTenderDimensionDetailListItemPO[];
  [BatchBusinessTypeEnums.Tender_All_Dimension_Detail_Export]?: ExportTenderDimensionDetailListItemPO[];
  [ExportEnums.Specific_Batch_Statistics_Export]?: ExportTenderStatisticsItemPO[];
  [ExportEnums.Specific_Batch_Detail_Export]?: ExportSpecificListItemPO[];
  [ExportEnums.MonitorCompany]?: MonitorCompanyExportRecordItemPO[];
}
