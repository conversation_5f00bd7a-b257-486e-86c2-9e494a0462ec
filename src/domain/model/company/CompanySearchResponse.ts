import { ApiProperty } from '@nestjs/swagger';

export interface OriginalName {
  Name: string;
  ChangeDate?: Date;
  StartDate?: Date;
}

export class CompanySearchResponse {
  @ApiProperty({ description: '公司名称' })
  Name: string;

  @ApiProperty({ description: '公司KeyNo' })
  KeyNo: string;

  @ApiProperty({ description: '实际公司名称' })
  ActualName: string;

  @ApiProperty({ description: '公司曾用名列表' })
  OriginalName: OriginalName[];

  @ApiProperty({ description: '标识' })
  Flag: string;

  @ApiProperty({ description: '是否隐藏' })
  IsHide: boolean;

  @ApiProperty({ description: '隐藏原因' })
  HideReason: string;

  @ApiProperty({ description: '公司类型' })
  EntType: string;

  @ApiProperty({ description: '统一社会信用代码' })
  CreditCode: string;

  @ApiProperty({ description: '注册号' })
  RegNo: string;

  @ApiProperty({ description: '标准代码' })
  StandardCode: string[];

  @ApiProperty({ description: '省份代码' })
  ProvinceCode: string;

  @ApiProperty({ description: '公司ID' })
  CompanyId: string;

  @ApiProperty({ description: '公司名称' })
  CompanyName: string;
}
