import { SearchFilter } from '@kezhaozhao/company-search-api';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { ScaleEnums } from '@domain/enums/company/ScaleEnums';
import { NumberRange } from '@kezhaozhao/search-utils';
import { ArrayNotEmpty, IsArray, IsIn, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';
import { StatusCode } from '@modules/batch/common/file.export.template';

export class RoverSearchFilter extends SearchFilter {
  @ApiPropertyOptional({
    description: '实缴资本区间 100万以下、100-200万、200-500万、500-1000万、1000万以上、自定义，区间值为半闭区间，如100-200为[100，200)',
    type: NumberRange,
    isArray: true,
  })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  @Type(() => NumberRange)
  cp?: NumberRange[];

  @ApiPropertyOptional({
    description: '企业规模',
    type: String,
    isArray: true,
    enum: ScaleEnums,
  })
  scale?: string[];

  @ApiPropertyOptional({
    description: '企业登记状态 10: 在业, 20: 存续, 30: 筹建, 40: 清算, 50: 迁入, 60: 迁出, 70: 停业, 80: 撤销, 90: 吊销, 99: 注销',
    type: String,
    isArray: true,
    enum: Object.keys(StatusCode),
  })
  @IsIn(Object.keys(StatusCode), { each: true })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  s?: string[];

  @ApiPropertyOptional({
    description: '资本类型, CNY: 人民币, USD: 美元, OTHER: 其他 ',
    isArray: true,
    type: String,
  })
  @IsIn(['CNY', 'USD', 'OTHER'], { each: true })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  c?: string[];

  @ApiPropertyOptional({
    description:
      '是分支机构/一般纳税人/小微企业/高新企业/纳税信用/进出口信用, ISBR: 是分支机构, N_ISBR: 非分支机构, GT:一般纳税人, N_GT:非一般纳税人 ,SME:是小微企业, N_SME:非小微企业',
    isArray: true,
    type: String,
  })
  @IsIn(['ISBR', 'N_ISBR', 'GT', 'N_GT', 'SME', 'N_SME', 'HT001', 'N_HT001', 'TA', 'N_TA', 'CI', 'N_CI'], { each: true })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  f?: string[];

  @ApiPropertyOptional({
    description: '新兴行业 ',
    isArray: true,
    type: String,
  })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  nil?: string[];

  @ApiPropertyOptional({
    description: '资质证书 ',
    isArray: true,
    type: String,
  })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  cl?: string[];

  @ApiPropertyOptional({
    description: '科技型企业 ',
    isArray: true,
    type: String,
  })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  tec?: string[];

  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  ot?: string[];

  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  toe?: string[];
}
