import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsString } from 'class-validator';

export class CompanySearchRequest {
  @ApiProperty({ description: '多家公司名' })
  @IsArray()
  @IsString({ each: true })
  names: string[];

  @ApiProperty({ description: '是否精确匹配', default: false })
  @IsBoolean()
  isExactlyMatch?: boolean = false;

  @ApiProperty({ description: '省份代码' })
  @IsString()
  province?: string;

  @ApiProperty({ description: '是否重复', default: false })
  @IsBoolean()
  isRepeated?: boolean = false;

  @ApiProperty({ description: '是否仅匹配大陆公司，默认为 false', default: false })
  @IsBoolean()
  isCM?: boolean = false;

  @ApiProperty({ description: '是否多 agg 模式查询', default: false })
  @IsBoolean()
  isNewModule?: boolean = false;

  @ApiProperty({ description: '查询字段', default: ['CreditCode', 'OriginalName', 'StandardCode', 'ProvinceCode'] })
  @IsString()
  selection: string[] = ['CreditCode', 'OriginalName', 'StandardCode', 'ProvinceCode'];

  constructor(names: string[], selection: string[] = ['CreditCode', 'OriginalName', 'StandardCode', 'ProvinceCode'], isCM = false, isRepeated = true) {
    this.names = names;
    this.selection = selection;
    this.isCM = isCM;
    this.isRepeated = isRepeated;
  }
}
