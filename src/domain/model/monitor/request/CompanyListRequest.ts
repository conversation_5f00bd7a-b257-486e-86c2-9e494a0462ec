import { PaginationParams } from '@commons/model/common';
import { ApiProperty } from '@nestjs/swagger';
import { ArrayNotEmpty, IsArray, IsIn, IsString } from 'class-validator';

/**
 * 公司列表查询允许的 selection 字段值
 */
export const ALLOWED_COMPANY_LIST_SELECTION_FIELDS: readonly string[] = [
  'KeyNo',
  'Name',
  'CreditCode',
  'ShortStatus',
  'Scale',
  'Address',
  'InsuredCount',
  'EconKind',
  'RegistCapi',
  'RecCap',
  'StartDate',
  'Area',
  'OperName',
  'OriginalName',
  'CommonList',
  'StandardCode',
  'MultipleOper',
  'No',
  'ProvinceCode',
  'OperKeyNo',
  'Scope',
  'Oper',
  'TaxCredit',
];

export class CompanyListRequest extends PaginationParams {
  @ApiProperty({ type: String, isArray: true, description: 'companyIds' })
  @IsArray()
  @ArrayNotEmpty()
  @IsString({ each: true })
  companyIds: string[];

  @ApiProperty({
    type: String,
    isArray: true,
    description: 'selection',
    enum: ALLOWED_COMPANY_LIST_SELECTION_FIELDS,
  })
  @IsArray()
  @ArrayNotEmpty()
  @IsString({ each: true })
  @IsIn(ALLOWED_COMPANY_LIST_SELECTION_FIELDS, { each: true })
  selection?: string[];
}
