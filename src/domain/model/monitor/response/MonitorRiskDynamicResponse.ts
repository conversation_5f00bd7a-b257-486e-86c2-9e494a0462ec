import { PaginationResponse } from '@commons/model/common';
import { MonitorRiskDynamicsV2Entity } from '@domain/entities/MonitorRiskDynamicsV2Entity';
import { MonitorGroupEntity } from '@domain/entities/MonitorGroupEntity';
import { DescModel } from '@modules/risk-assessment/monitor/risk.copy.from.c/risk.detail.help';
import { ApiProperty } from '@nestjs/swagger';

export class MonitorRiskDynamicResponseItemPO extends MonitorRiskDynamicsV2Entity {
  group?: Partial<MonitorGroupEntity>;
  detail?: DescModel;
  relatedCompanyInfo?: { masterCompany: CompanyInfoPO; relatedCompany: CompanyInfoPO };
}

export class MonitorRiskDynamicResponse extends PaginationResponse {
  @ApiProperty({ type: MonitorRiskDynamicResponseItemPO, isArray: true })
  data: MonitorRiskDynamicResponseItemPO[];
}

export class CompanyInfoPO {
  id: number;
  companyId: string;
  companyName: string;
  creditcode: string;
}
