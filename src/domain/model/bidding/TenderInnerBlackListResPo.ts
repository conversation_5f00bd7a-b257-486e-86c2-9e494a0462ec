export class BlacklistInvestigationsPo {
  companyKeynoRelated: string;
  companyNameRelated: string;
  endCompanyKeyno: string;
  endCompanyName: string;
  entityId: number;
  history: boolean;
  reason: string;
  relationPaths: any[][];
  relationTypes: string[];
  relations: any[][];
  startCompanyKeyno: string;
  startCompanyName: string;
  steps: number;
  version: string;
}

export class DirectConnectionsPo {
  blacklistId: number;
  companyKeynoDD: string;
  companyNameDD: string;
  duration: number;
  durationTs: number;
  history: boolean;
  joinDate: number;
  reason: string;
  expiredDate: Date;
}

export class SuspectedRelationsPo {
  companyKeynoRelated: string;
  companyNameRelated: string;
  endCompanyKeyno: string;
  endCompanyName: string;
  entityId: number;
  history: boolean;
  reason: string;
  relationPaths: any[][];
  relationTypes: string[];
  relations: any[][];
  startCompanyKeyno: string;
  startCompanyName: string;
  steps: number;
  version: string;
}

export class TenderInnerBlackListResPo {
  directConnection: DirectConnectionsPo[];
  blacklistInvestigations: BlacklistInvestigationsPo[];
  suspectedRelations?: SuspectedRelationsPo[];
}

export class ArrByPagePo {
  Result: any[];
  Paging: { PageSize: number; PageIndex: number; TotalRecords: number };
}

export const getArrByPage = (arr: any[], pageIndex: number, pageSize: number): ArrByPagePo => {
  const startIndex = (pageIndex - 1) * pageSize;
  const endIndex = pageIndex * pageSize;
  const totalCount = arr?.length || 0;
  const data = arr?.slice(startIndex, endIndex);
  const result = new ArrByPagePo();
  result.Result = data;
  result.Paging = { PageSize: pageSize, PageIndex: pageIndex, TotalRecords: totalCount };
  return result;
};
