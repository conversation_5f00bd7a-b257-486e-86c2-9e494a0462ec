import { ApiProperty } from '@nestjs/swagger';
import { QueryParamsEnums } from './dimension.filter.params';
import { IsIn, IsNumber, IsOptional } from 'class-validator';

export enum OperatorEnums {
  /**
   * 大于
   */
  gt = 'gt', //大于
  /**
   * 大于等于
   */
  ge = 'ge', //大于等于
  /**
   * 等于
   */
  eq = 'eq', //等于
  /**
   * 小于
   */
  lt = 'lt',
  /**
   * 小于等于
   */
  le = 'le', //小于等于
  /**
   * 包含
   */
  contain = 'contain', //包含
  /**
   * 不包含
   */
  notContain = 'notContain', //不包含
}

export const OperatorName = {
  [OperatorEnums.gt]: '大于', //
  [OperatorEnums.ge]: '大于等于', //大于等于
  [OperatorEnums.eq]: '等于', //等于
  [OperatorEnums.lt]: '小于',
  [OperatorEnums.le]: '小于等于', //小于等于
  [OperatorEnums.contain]: '包含', //包含
  [OperatorEnums.notContain]: '不包含', //
};

export const EsOperator = {
  [OperatorEnums.gt]: 'gt', //
  [OperatorEnums.ge]: 'gte', //大于等于
  [OperatorEnums.lt]: 'lt',
  [OperatorEnums.le]: 'lte', //小于等于
};

export const getCompareResult = (value1: number, vlalue2: number, operator: OperatorEnums): boolean => {
  switch (operator) {
    case OperatorEnums.lt:
      //  成立日期 在  baseMonthCount 个月 之前
      return value1 < vlalue2;
    case OperatorEnums.le:
      //  成立日期 在  baseMonthCount 个月 之前
      return value1 <= vlalue2;
    case OperatorEnums.gt:
      return value1 > vlalue2;
    case OperatorEnums.ge:
      return value1 >= vlalue2;
    case OperatorEnums.eq:
      return value1 == vlalue2;
    default:
      throw new Error(`operator is not defined!: ${operator}`);
  }
};

/**
 * 担保风险金额筛选获取 start，end
 * @param value
 * @param operator
 */
export const getStartEnd = (value: number, operator: OperatorEnums): { start?: number; end?: number } => {
  //因为是左闭右开的，所以等于的不适合，只能让右边稍微大一点点
  switch (operator) {
    case OperatorEnums.lt:
    case OperatorEnums.le: {
      return { end: value + 0.1 };
    }
    case OperatorEnums.gt:
    case OperatorEnums.ge: {
      return { start: value };
    }
    case OperatorEnums.eq:
      return { start: value, end: value + 0.1 };
    default:
      throw new Error(`operator is not defined!: ${operator}`);
  }
};

export class DimensionQueryPO {
  @ApiProperty({ description: '定义的字段', enum: QueryParamsEnums })
  field: string;

  @ApiProperty({ description: '定义的描述', required: false })
  fieldName?: string;

  @ApiProperty({ description: '运算符 大于， 大于等于， 包含，不包含', required: false, enum: OperatorEnums })
  @IsOptional()
  fieldOperator?: OperatorEnums;

  @ApiProperty({ description: '字段值', required: false })
  fieldVal?: any;

  @ApiProperty({ description: '具体的code数组', required: false })
  activeCodes?: number[];

  @ApiProperty({ description: '前端是否显示,默认false', type: Boolean })
  isHidden?: boolean = false;

  @IsIn([0, 1])
  @IsNumber()
  status?: number = 1;

  @ApiProperty({ description: '排序', type: Number })
  sort?: number = 0;

  @ApiProperty({ description: '版本,在 v1.7.9迭代中添加, 用于标识查询条件是否是v2版本', required: false })
  version?: string;
}

export class ConditionValPO {
  @ApiProperty({ description: '键', required: false })
  key?: string;

  @ApiProperty({ description: '名称', required: false })
  keyName?: string;

  @ApiProperty({ description: '是否启用', required: false })
  @IsIn([0, 1])
  @IsNumber()
  status?: number = 1;

  //目前仅使用在持股/投资股权比例（含历史）的值
  @ApiProperty({ description: '设置项值', required: false })
  @IsNumber()
  value?: number;

  child?: ConditionValPO[];
}
