import { PaginationParams } from '@commons/model/common';
import { ApiProperty } from '@nestjs/swagger';
import { ArrayNotEmpty, IsNotEmpty, IsNumber, IsOptional } from 'class-validator';
import { JointBiddingAnalysisModel } from '../../../../../tender/JointBiddingAnalysisModel';

export class HitDetailsBidBaseQueryParams extends PaginationParams {
  @ApiProperty({ required: false, description: '公司ids' })
  keyNos?: string[];

  @ApiProperty({ required: true, description: '' })
  keyNoAndNames?: JointBiddingAnalysisModel[];

  @ApiProperty({ required: false, description: '排序字段，默认为空' })
  @IsOptional()
  sortField?: string;

  @ApiProperty({ required: false, default: false, description: '是否升序(默认false' })
  isSortAsc?: boolean = false;

  orgId?: number;
  diligenceId?: number;
  status?: number[];

  /**
   * 招标排查创建时间
   */
  createDate?: Date;

  @ApiProperty({ required: true, description: '模型设置id,' })
  @IsOptional()
  settingId: number;

  @ApiProperty({ required: false, default: true, description: '是否是WEB端招标排查查询(默认true)' })
  isB?: boolean = true;

  constructor() {
    super();
    this.keyNos = this.keyNoAndNames?.map((e) => e.companyId);
  }
}

/**
 * 共同投标分析详情
 */
export class JointBiddingAnalysisDetailsRequest extends PaginationParams {
  @ApiProperty({ required: true, description: '公司keyNo' })
  companyId: string;

  @ApiProperty({ required: true, description: '关联公司keyNo' })
  relCompanyId: string;

  @ApiProperty({ description: 'lastpublishdate' })
  sortField?: string;

  @ApiProperty({ default: false, description: '是否升序(默认false' })
  isSortAsc?: boolean = false;
}

/**
 * 查信用大数据相关维度
 */
export class JointBiddingAnalysisDetailsQueryParams extends PaginationParams {
  @ApiProperty({ required: true, description: '共同投标公司keynos, 必须两个公司都填' })
  @ArrayNotEmpty()
  openbidkeynos: string[];

  @ApiProperty({ required: false, description: '中标公司keynos' })
  wtbkeynos?: string[];
}

export class BidCollusiveDetailsQueryParams {
  @ApiProperty({ required: true, description: '数据来源类型' })
  sourcetype: number;

  @ApiProperty({ required: true, description: '公司ids' })
  keyNos: string[];

  @ApiProperty({ required: true, description: '项目信息 ID' })
  projectSourceId: string;
}

export class BidCollusiveDetailListQueryParams extends PaginationParams {
  @ApiProperty({ required: true, description: '项目信息 ID' })
  collusiveId: string;

  @ApiProperty({ required: true, description: '公司ids' })
  keyNos: string[];

  @ApiProperty({ description: '排序字段，默认为空：decisiondate、removedate' })
  sortField?: string;

  @ApiProperty({ default: false, description: '是否升序(默认false' })
  isSortAsc?: boolean = false;
}
