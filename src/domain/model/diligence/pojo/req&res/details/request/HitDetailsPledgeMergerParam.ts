import { ApiProperty } from '@nestjs/swagger';
import { HitDetailsBaseQueryParams } from '@domain/model/diligence/pojo/req&res/details/request/HitDetailsBaseQueryParams';
import { IsOptional } from 'class-validator';

export class HitDetailsPledgeMergerParam extends HitDetailsBaseQueryParams {
  @ApiProperty({ required: true, description: '模型设置id' })
  settingId: number;

  @ApiProperty({ required: true, description: '关联编号' })
  relatedNo: string;

  @ApiProperty({
    required: true,
    description: '业务类型编号 (1: 生产设备抵押, 2: 融资租赁)',
    enum: [1, 2],
  })
  businessTypeNo: number[];

  @ApiProperty({ required: false, description: '排序字段，默认为空' })
  @IsOptional()
  sortField?: string;

  @ApiProperty({ required: false, default: false, description: '是否升序(默认false' })
  isSortAsc?: boolean = false;
}
