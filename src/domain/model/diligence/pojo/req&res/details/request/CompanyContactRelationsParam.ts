import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { ArrayNotEmpty, IsIn, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';

export class CompanyContactRelationsParam {
  @ApiProperty({ required: true, description: '公司ids' })
  @ArrayNotEmpty()
  companyIds: string[];

  @ApiProperty({ required: true, description: '类型' })
  @IsString()
  @IsNotEmpty()
  @IsIn(['ContactNumber', 'Address', 'Mail'])
  type: string;

  @ApiProperty({ required: false, description: '匹配模式,1-模糊匹配,2-精确匹配' })
  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  matchMode?: number;
}
