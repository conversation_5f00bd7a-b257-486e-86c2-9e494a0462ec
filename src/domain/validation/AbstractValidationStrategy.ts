import { RoverExceptions } from '@commons/constants/exceptionConstants';
import { isRedlockAcquireLock } from '@commons/utils/redis.utils';
import { GROUP_LIMIT_COUNT, SPEC_GROUP_LIMIT_COUNT, specialOrgIds } from '@domain/constants/common';
import { DepartmentLimitDetail, ValidateCountResponse } from '@domain/dto/customer/ValidateCountResponse';
import { DepartmentEntity } from '@domain/entities/DepartmentEntity';
import { GroupsEntity } from '@domain/entities/GroupsEntity';
import { BatchStatusEnums } from '@domain/enums/batch/BatchStatusEnums';
import { DepartmentTypeEnum } from '@domain/enums/department/DepartmentTypeEnum';
import { ValidateCountModel, ValidationType } from '@domain/model/customer/ValidateCountModel';
import { GroupType } from '@domain/model/element/CreateGroupModel';
import { RoverUserModel } from '@domain/model/RoverUserModel';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from 'log4js';
import Redlock, { Lock } from 'redlock';
import { FindOptionsWhere, In, Not, Repository } from 'typeorm';
import { IValidatableEntity } from './IValidatableEntity';
import { RedisCounterService } from './redis-counter.service';
import { BusinessType, RedisKeyBuilder } from './redis-key-builder';
import { BatchFailedRecord, BatchValidationResult, ValidationResult, ValidationStrategy } from './ValidationStrategy';
import { BadParamsException } from '@kezhaozhao/qcc-exception-handler';

/**
 * 抽象校验策略基类
 * 使用模板方法模式，定义校验算法骨架，子类实现具体细节
 */
@Injectable()
export abstract class AbstractValidationStrategy<TEntity extends IValidatableEntity> implements ValidationStrategy {
  protected readonly logger: Logger;
  protected readonly redlock: Redlock;

  constructor(
    @InjectRepository(GroupsEntity) protected readonly groupRepo: Repository<GroupsEntity>,
    @InjectRepository(DepartmentEntity) protected readonly departmentRepo: Repository<DepartmentEntity>,
    protected readonly redisService: RedisService,
    protected readonly redisCounterService: RedisCounterService, // 新增
  ) {
    this.logger = QccLogger.getLogger(this.constructor.name);
    this.redlock = new Redlock([redisService.getClient()], {
      retryCount: 2,
    });
  }

  // 抽象方法，子类必须实现
  protected abstract getEntityRepo(): Repository<TEntity>;

  protected abstract getEntityPrimaryKeyField(): string;

  protected abstract getGroupType(): GroupType;

  protected abstract getEntityName(): string;

  protected abstract validateEntityCountLimit(currentUser: RoverUserModel, data: ValidateCountModel): Promise<ValidateCountResponse>;

  protected abstract getDepartmentType(): DepartmentTypeEnum;

  // 新增抽象方法，子类返回对应的业务类型
  protected abstract getBusinessType(): BusinessType;

  protected abstract getGroupCurrentCounts(orgId: number): Promise<Array<{ groupId: number; count: number }>>;

  protected abstract getDepartmentCurrentCounts(orgId: number): Promise<
    Array<{
      departmentName: string;
      count: number;
    }>
  >;

  protected abstract getGroupLimit(orgId: number, groupId: number): Promise<number>;

  protected abstract getDepartmentLimit(orgId: number, departmentName: string): Promise<number>;

  protected abstract getGroupLimitedException(): any;

  protected abstract getDepartmentLimitedException(): any;

  async validate(chunk: any[], user: RoverUserModel): Promise<ValidationResult> {
    const groupLimitCheckMap = new Map<number, boolean>();
    const groupsByName = new Map<string, GroupsEntity>();
    const departmentLimitCheckMap = new Map<number, boolean>();
    const departmentsByName = new Map<string, DepartmentEntity>();

    // 获取当前chunk中所有唯一的分组名称
    const groupNames = [...new Set(chunk.map((x) => x.group).filter(Boolean))];

    // 批量查询分组信息
    await this.validateGroups(groupNames, user, groupsByName, groupLimitCheckMap);

    // 获取当前chunk中所有唯一的部门名称
    const departmentNames = this.extractDepartmentNames(chunk);

    // 批量查询部门信息
    await this.validateDepartments(departmentNames, user, departmentsByName, departmentLimitCheckMap);

    return { groupsByName, groupLimitCheckMap, departmentsByName, departmentLimitCheckMap };
  }

  /**
   * 校验分组 - 模板方法
   */
  protected async validateGroups(
    groupNames: string[],
    user: RoverUserModel,
    groupsByName: Map<string, GroupsEntity>,
    groupLimitCheckMap: Map<number, boolean>,
  ): Promise<void> {
    let groups: GroupsEntity[] = [];

    // 没有指定分组，使用虚拟的"未分组"分组
    const ungroupedGroup = new GroupsEntity();
    ungroupedGroup.groupId = -1;
    ungroupedGroup.name = '未分组';
    ungroupedGroup.orgId = user.currentOrg;
    ungroupedGroup.groupType = this.getGroupType();
    ungroupedGroup.order = 0;
    ungroupedGroup.scene = 0;
    ungroupedGroup.createDate = new Date();
    ungroupedGroup.updateDate = new Date();

    if (groupNames.length === 0) {
      groups = [ungroupedGroup];
    } else {
      groups = await this.groupRepo.find({
        where: {
          orgId: user.currentOrg,
          name: In(groupNames),
          groupType: this.getGroupType(),
        },
      });
    }

    // 建立分组名称到分组实体的映射
    groups.forEach((group) => {
      groupsByName.set(group.name, group);
    });

    // 为每个分组校验数量限制
    for (const group of groups) {
      try {
        if (group.groupId === -1) {
          groupLimitCheckMap.set(group.groupId, true);
          continue;
        }
        const validateData = Object.assign(new ValidateCountModel(), {
          validationType: ValidationType.GROUP,
          groupId: group.groupId,
          addCount: 1,
        });
        const result = await this.validateEntityCountLimit(user, validateData);
        groupLimitCheckMap.set(group.groupId, result.allowed);
      } catch (error) {
        console.warn(`校验${this.getEntityName()}分组${group.name}(ID:${group.groupId})数量限制失败: ${error.message}`);
        groupLimitCheckMap.set(group.groupId, false);
      }
    }
  }

  /**
   * 校验部门 - 模板方法
   */
  protected async validateDepartments(
    departmentNames: string[],
    user: RoverUserModel,
    departmentsByName: Map<string, DepartmentEntity>,
    departmentLimitCheckMap: Map<number, boolean>,
  ): Promise<void> {
    if (departmentNames.length === 0) return;

    const departments = await this.departmentRepo.find({
      where: {
        orgId: user.currentOrg,
        name: In(departmentNames),
        departmentType: this.getDepartmentType(),
      },
    });

    // 建立部门名称到部门实体的映射
    departments.forEach((department) => {
      departmentsByName.set(department.name, department);
    });

    // 为每个部门校验数量限制
    for (const department of departments) {
      try {
        const validateData = Object.assign(new ValidateCountModel(), {
          validationType: ValidationType.DEPARTMENT,
          departmentNames: [department.name],
          addCount: 1,
        });
        const result = await this.validateEntityCountLimit(user, validateData);
        departmentLimitCheckMap.set(department.departmentId, result.allowed);
      } catch (error) {
        console.warn(`校验${this.getEntityName()}部门${department.name}(ID:${department.departmentId})数量限制失败: ${error.message}`);
        departmentLimitCheckMap.set(department.departmentId, false);
      }
    }
  }

  /**
   * 提取所有部门名称 - 通用方法
   */
  protected extractDepartmentNames(chunk: any[]): string[] {
    const departmentNamesSet = new Set<string>();

    chunk.forEach((item) => {
      if (item.departmentNames) {
        const names = Array.isArray(item.departmentNames) ? item.departmentNames : item.departmentNames.split(',');
        names.forEach((name: string) => {
          const trimmedName = name.trim();
          if (trimmedName) {
            departmentNamesSet.add(trimmedName);
          }
        });
      }
    });

    return Array.from(departmentNamesSet);
  }

  /**
   * 校验businessIds的存在性 - 模板方法
   */
  protected async validateBusinessIds(businessIds: number[] | undefined, orgId: number): Promise<TEntity[]> {
    if (!businessIds || businessIds.length === 0) {
      return [];
    }

    const primaryKeyField = this.getEntityPrimaryKeyField();
    const whereCondition = { [primaryKeyField]: In(businessIds), orgId } as FindOptionsWhere<TEntity>;
    const entities = await this.getEntityRepo().find({
      where: whereCondition,
      relations: ['departments'],
    });

    if (entities.length !== businessIds.length) {
      const foundIds = entities.map((entity) => (entity as any)[primaryKeyField]);
      const missingIds = businessIds.filter((id) => !foundIds.includes(id));
      throw new BadRequestException(`业务ID [${missingIds.join(', ')}] 对应的${this.getEntityName()}不存在`);
    }

    return entities;
  }

  /**
   * 校验分组限制 - 模板方法
   */
  protected async validateGroupLimit(
    orgId: number,
    groupId: number | undefined,
    limitCount: number,
    existingEntities: TEntity[],
    requestedAddCount?: number,
  ): Promise<ValidateCountResponse> {
    if (groupId === undefined) {
      throw new BadRequestException('分组ID不能为空');
    }

    if (groupId === -1) {
      return {
        allowed: true,
        currentCount: 0,
        limitCount,
        afterAddCount: 0,
        message: '未分组，无需校验',
      };
    }

    // 计算实际需要增加的数量
    let actualAddCount: number;
    if (!existingEntities || existingEntities.length === 0) {
      // 新增场景：直接使用传入的addCount，默认为1
      this.logger.info(`新增场景，groupId=${groupId}, requestedAddCount=${requestedAddCount}`);
      actualAddCount = requestedAddCount || 1;
    } else {
      // 编辑场景：计算实际增加的数量
      this.logger.info(`编辑场景，groupId=${groupId}, requestedAddCount=${requestedAddCount}`);
      const entitiesInTargetGroup = existingEntities.filter((entity) => entity.groupId === groupId);
      actualAddCount = existingEntities.length - entitiesInTargetGroup.length;
    }

    // 计算当前数量，排除将要移动的实体
    const primaryKeyField = this.getEntityPrimaryKeyField();
    // 因为可能在批量任务中执行，所以这个 status 不能是 Done
    const whereCondition = {
      orgId,
      groupId,
      status: In([BatchStatusEnums.Done, BatchStatusEnums.Waiting, BatchStatusEnums.Processing]),
    } as FindOptionsWhere<TEntity>;

    // 排除所有现有实体（无论是否在目标分组中）
    if (existingEntities.length > 0) {
      const entityIds = existingEntities.map((entity) => (entity as any)[primaryKeyField]);
      whereCondition[primaryKeyField] = Not(In(entityIds));
    }

    const currentCount = await this.getEntityRepo().count({ where: whereCondition });
    const afterAddCount = currentCount + actualAddCount;
    const allowed = afterAddCount <= limitCount;

    const message = `分组ID ${groupId}`;
    const finalMessage = `${message} 当前有 ${currentCount} 家${this.getEntityName()}，${
      allowed ? '允许' : '不允许'
    }添加 ${actualAddCount} 家，限制数量为 ${limitCount} 家`;

    return {
      allowed,
      currentCount,
      limitCount,
      afterAddCount,
      message: finalMessage,
    };
  }

  /**
   * 校验部门限制 - 模板方法
   */
  protected async validateDepartmentLimit(
    orgId: number,
    departmentNames: string[] | undefined,
    limitCount: number,
    existingEntities: TEntity[],
    requestedAddCount?: number,
  ): Promise<ValidateCountResponse> {
    if (!departmentNames || departmentNames.length === 0) {
      // 如果没有传入部门名称，则无需校验，直接返回允许
      return {
        allowed: true,
        currentCount: 0,
        limitCount,
        afterAddCount: 0,
        message: '未指定部门，无需校验',
      };
    }

    // 根据部门名称获取部门信息
    const departments = await this.departmentRepo.find({
      where: {
        name: In(departmentNames),
        orgId,
        departmentType: this.getDepartmentType(),
      },
    });

    if (departments.length !== departmentNames.length) {
      //实际的业务场景下，这里如果有部门不存在，其实表明是用户新增的部门，对于新增的部门，不进行校验，存在的部门还是需要继续校验
      const foundNames = departments.map((d) => d.name);
      const missingNames = departmentNames.filter((name) => !foundNames.includes(name));
      this.logger.info(`部门名称 [${missingNames.join(', ')}] 为新增部门，跳过校验。存在的部门 [${foundNames.join(', ')}] 继续校验`);

      // 如果所有部门都不存在（都是新增部门），则无需校验，直接返回允许
      if (departments.length === 0) {
        return {
          allowed: true,
          currentCount: 0,
          limitCount,
          afterAddCount: 0,
          message: '所有部门均为新增部门，无需校验',
        };
      }
      // 否则继续对存在的部门进行校验
    }

    // 为每个部门单独计算数量
    const departmentDetails: DepartmentLimitDetail[] = [];
    let totalCurrentCount = 0;
    let hasOverLimit = false;
    const overLimitDepartments: string[] = [];

    const primaryKeyField = this.getEntityPrimaryKeyField();

    for (const department of departments) {
      // 计算该部门当前已有的实体数量（排除现有实体）
      // 因为可能在批量任务中执行，所以这个 status 不能是 Done
      const qb = this.getEntityRepo()
        .createQueryBuilder('entity')
        .leftJoin('entity.departments', 'department')
        .where('entity.orgId = :orgId', { orgId })
        .andWhere('entity.status IN (:...statuses)', { statuses: [BatchStatusEnums.Done, BatchStatusEnums.Waiting, BatchStatusEnums.Processing] })
        .andWhere('department.departmentId = :departmentId', { departmentId: department.departmentId });

      // 排除所有现有实体
      if (existingEntities.length > 0) {
        const entityIds = existingEntities.map((entity) => (entity as any)[primaryKeyField]);
        qb.andWhere(`entity.${primaryKeyField} NOT IN (:...entityIds)`, { entityIds });
      }

      const departmentCurrentCount = await qb.getCount();

      // 计算实际需要增加的数量
      let actualAddCount: number;
      if (!existingEntities || existingEntities.length === 0) {
        // 新增场景：直接使用传入的addCount，默认为1
        actualAddCount = requestedAddCount || 1;
      } else {
        // 编辑场景：计算实际增加的数量
        const existingInThisDepartment = existingEntities.filter((entity) => entity.departments?.some((dept) => dept.departmentId === department.departmentId));
        actualAddCount = existingEntities.length - existingInThisDepartment.length;
      }

      const departmentAfterAddCount = departmentCurrentCount + actualAddCount;
      const isOverLimit = departmentAfterAddCount > limitCount;

      if (isOverLimit) {
        hasOverLimit = true;
        overLimitDepartments.push(department.name);
      }

      departmentDetails.push({
        departmentId: department.departmentId,
        departmentName: department.name,
        currentCount: departmentCurrentCount,
        afterAddCount: departmentAfterAddCount,
        isOverLimit,
      });

      totalCurrentCount = Math.max(totalCurrentCount, departmentCurrentCount);
    }

    const maxAddCount = Math.max(...departmentDetails.map((d) => d.afterAddCount - d.currentCount));
    const allowed = !hasOverLimit;

    // 检查是否有新增部门（未校验的部门）
    const validatedDepartmentNames = departments.map((d) => d.name);
    const newDepartmentNames = departmentNames.filter((name) => !validatedDepartmentNames.includes(name));

    let message: string;
    if (hasOverLimit) {
      message = `部门 [${overLimitDepartments.join(', ')}] 超出限制`;
    } else {
      message = `部门 [${validatedDepartmentNames.join(', ')}]`;
    }

    let finalMessage = `${message} - ${allowed ? '允许' : '不允许'}添加 ${maxAddCount} 家${this.getEntityName()}，限制数量为 ${limitCount} 家`;
    if (newDepartmentNames.length > 0) {
      finalMessage += `。新增部门 [${newDepartmentNames.join(', ')}] 已跳过校验`;
    }

    return {
      allowed,
      currentCount: totalCurrentCount,
      limitCount,
      afterAddCount: totalCurrentCount + maxAddCount,
      message: finalMessage,
      departmentDetails,
    };
  }

  /**
   * 新的原子计数器校验方法 - 替代分布式锁
   * 使用Redis原子计数器确保并发安全
   * @returns 返回已预留的Redis keys和addCount，用于后续补偿
   */
  protected async validateGroupAndDepartmentCountLimitWithAtomicCounter(
    currentUser: RoverUserModel,
    postData: any,
    businessIds?: number[],
  ): Promise<{ reservedKeys: string[]; addCount: number }> {
    this.logger.info(
      `开始校验${this.getEntityName()}数量限制,orgId=${currentUser.currentOrg},groupId=${postData?.groupId},departmentNames=${
        postData?.departmentNames
      },businessIds=${businessIds?.length},businessType=${this.getBusinessType()}`,
    );

    const addCount = businessIds?.length || 1;
    const reservedKeys: string[] = []; // 记录已预留的key，用于失败时补偿

    try {
      // 分组校验 - 使用原子计数器
      if (postData.groupId !== -1) {
        // 修改判断条件，不支持groupId为-1的情况，因为-1表示未分组，未分组不进行校验
        await this.validateGroupWithAtomicCounter(currentUser, postData.groupId, addCount, reservedKeys);
      }

      // 部门校验 - 使用原子计数器
      if (postData.departmentNames?.length > 0) {
        await this.validateDepartmentsWithAtomicCounter(currentUser, postData.departmentNames, addCount, reservedKeys);
      }

      this.logger.info(
        `${this.getEntityName()}数量限制校验通过,orgId=${currentUser.currentOrg},reservedKeys=${reservedKeys.length},businessType=${this.getBusinessType()}`,
      );

      return { reservedKeys, addCount };
    } catch (error) {
      // 校验失败，补偿所有已预留的计数
      await this.compensateReservedCounts(reservedKeys, addCount);
      throw error;
    }
  }

  /**
   * 分组原子计数器校验
   */
  private async validateGroupWithAtomicCounter(currentUser: RoverUserModel, groupId: number, addCount: number, reservedKeys: string[]): Promise<void> {
    // 确保计数器已预热
    await this.ensureCounterWarmed(currentUser.currentOrg);

    const groupCountKey = RedisKeyBuilder.buildGroupCountKey(this.getBusinessType(), currentUser.currentOrg, groupId);

    // 获取分组限制
    const groupLimit = await this.getGroupLimit(currentUser.currentOrg, groupId);

    // 原子操作：检查并递增
    const newCount = await this.redisCounterService.checkAndIncrement(groupCountKey, groupLimit, addCount);

    if (newCount === -1) {
      const currentCount = await this.redisCounterService.getCurrentCount(groupCountKey);
      const groupName = groupId === -1 ? '未分组' : `分组ID:${groupId}`;
      throw new BadParamsException({
        message: `${groupName}${this.getEntityName()}数量已达上限，当前: ${currentCount}，限制: ${groupLimit}，尝试新增: ${addCount}`,
        ...this.getGroupLimitedException(),
      });
    }

    // 记录已预留的key
    reservedKeys.push(groupCountKey);

    this.logger.debug(
      `分组原子计数器校验通过,orgId=${
        currentUser.currentOrg
      },groupId=${groupId},businessType=${this.getBusinessType()},groupCountKey=${groupCountKey},newCount=${newCount}`,
    );
  }

  /**
   * 部门原子计数器校验
   */
  private async validateDepartmentsWithAtomicCounter(
    currentUser: RoverUserModel,
    departmentNames: string[],
    addCount: number,
    reservedKeys: string[],
  ): Promise<void> {
    // 确保计数器已预热
    await this.ensureCounterWarmed(currentUser.currentOrg);

    for (const departmentName of departmentNames) {
      const deptCountKey = RedisKeyBuilder.buildDepartmentCountKey(this.getBusinessType(), currentUser.currentOrg, departmentName);

      // 获取部门限制
      const deptLimit = await this.getDepartmentLimit(currentUser.currentOrg, departmentName);

      // 原子操作：检查并递增
      const newCount = await this.redisCounterService.checkAndIncrement(deptCountKey, deptLimit, addCount);

      if (newCount === -1) {
        const currentCount = await this.redisCounterService.getCurrentCount(deptCountKey);
        throw new BadParamsException({
          message: `部门${departmentName}${this.getEntityName()}数量已达上限，当前: ${currentCount}，限制: ${deptLimit}，尝试新增: ${addCount}`,
          ...this.getDepartmentLimitedException(),
        });
      }

      // 记录已预留的key
      reservedKeys.push(deptCountKey);

      this.logger.debug(
        `部门原子计数器校验通过,orgId=${
          currentUser.currentOrg
        },departmentName=${departmentName},businessType=${this.getBusinessType()},deptCountKey=${deptCountKey},newCount=${newCount},deptLimit=${deptLimit},addCount=${addCount}`,
      );
    }
  }

  /**
   * 补偿已预留的计数（用于校验失败或数据库操作失败时）
   * 改为public，允许外部在数据库操作失败后调用
   */
  public async compensateReservedCounts(reservedKeys: string[], addCount: number): Promise<void> {
    if (reservedKeys.length === 0) return;

    this.logger.warn(`开始补偿已预留的计数,reservedKeys=${reservedKeys.length},addCount=${addCount},businessType=${this.getBusinessType()}`);

    const compensatePromises = reservedKeys.map(async (key) => {
      try {
        await this.redisCounterService.compensateDecrement(key, addCount);
        this.logger.debug('补偿计数成功', { key, addCount });
      } catch (error) {
        this.logger.error('补偿计数失败', { key, addCount, error });
        // 补偿失败需要告警，但不应该阻塞主流程
      }
    });

    await Promise.all(compensatePromises);
    this.logger.info('补偿已预留的计数完成', { count: reservedKeys.length });
  }

  /**
   * 确保计数器已预热（延迟加载）
   */
  private async ensureCounterWarmed(orgId: number): Promise<void> {
    this.logger.info(`确保计数器已预热,orgId=${orgId},businessType=${this.getBusinessType()}`);
    const warmedKey = RedisKeyBuilder.buildWarmedStatusKey(this.getBusinessType(), orgId);
    const redis = this.redisService.getClient();

    const isWarmed = await redis.get(warmedKey);
    if (!isWarmed) {
      await this.warmupCounters(orgId);
      await redis.setex(warmedKey, 3600, '1'); // 1小时过期
    }
  }

  /**
   * 预热计数器（从数据库加载当前数量到Redis）
   */
  private async warmupCounters(orgId: number): Promise<void> {
    this.logger.info(`开始预热计数器,orgId=${orgId},businessType=${this.getBusinessType()}`);

    // 获取所有分组的当前数量（包括未分组 groupId=-1）
    const groupCounts = await this.getGroupCurrentCounts(orgId);
    const deptCounts = await this.getDepartmentCurrentCounts(orgId);

    const counters: Array<{ key: string; count: number }> = [];

    // 构建分组计数器
    groupCounts.forEach(({ groupId, count }) => {
      const key = RedisKeyBuilder.buildGroupCountKey(this.getBusinessType(), orgId, groupId);
      counters.push({ key, count });
    });

    // 构建部门计数器
    deptCounts.forEach(({ departmentName, count }) => {
      const key = RedisKeyBuilder.buildDepartmentCountKey(this.getBusinessType(), orgId, departmentName);
      counters.push({ key, count });
    });

    // 批量设置到Redis
    if (counters.length > 0) {
      await this.redisCounterService.batchSetCount(counters);
    }

    this.logger.info(`预热计数器完成,orgId=${orgId},count=${counters.length},businessType=${this.getBusinessType()}`);
  }

  /**
   * 通用的校验分组、部门数量限制方法
   * 使用Redis分布式锁确保并发安全
   */
  protected async validateGroupAndDepartmentCountLimitInternal(currentUser: RoverUserModel, postData: any, businessIds?: number[]) {
    this.logger.info(`开始校验${this.getEntityName()}数量限制，orgId=${currentUser.currentOrg}, postData=${JSON.stringify(postData)}`);
    let addCount = 1; // 默认新增1个实体
    if (businessIds?.length) {
      addCount = businessIds.length; // 如果是批量操作，使用businessIds的长度
    }

    // 分组校验时的锁
    if (postData.groupId) {
      const groupLockKey = `validation_lock:${currentUser.currentOrg}:group:${postData.groupId}`;
      let groupLock: Lock | null = null;

      try {
        this.logger.info(
          `尝试获取分组校验锁: ${groupLockKey},orgId=${currentUser.currentOrg},groupId=${postData.groupId},businessType=${this.getBusinessType()}`,
        );
        groupLock = await this.redlock.acquire([groupLockKey], 10000); // 修改为10秒锁定时间，与用户提到的一致

        // 校验分组数量限制
        const validationResult1 = await this.validateEntityCountLimit(currentUser, {
          validationType: ValidationType.GROUP,
          groupId: postData.groupId,
          departmentNames: postData.departmentNames,
          addCount,
          businessIds,
        });

        if (!validationResult1.allowed) {
          this.logger.error(
            `${this.getEntityName()}分组校验失败，orgId=${currentUser.currentOrg}, postData=${JSON.stringify(postData)},businessType=${this.getBusinessType()}`,
          );
          throw new BadParamsException({ message: validationResult1.message, ...RoverExceptions.Customer.GroupLimited });
        }

        // 成功情况下释放锁
        if (groupLock) {
          try {
            await groupLock.release();
            this.logger.info(
              `释放分组校验锁: ${groupLockKey},orgId=${currentUser.currentOrg},groupId=${postData.groupId},businessType=${this.getBusinessType()}`,
            );
            groupLock = null; // 重置为null，避免在finally中重复释放
          } catch (releaseError) {
            this.logger.error(
              `释放分组校验锁失败: ${groupLockKey},orgId=${currentUser.currentOrg},groupId=${postData.groupId},businessType=${this.getBusinessType()}`,
              releaseError,
            );
          }
        }
      } catch (error) {
        // 异常情况下的处理
        if (isRedlockAcquireLock(error)) {
          this.logger.warn(
            `获取分组校验锁失败，可能存在并发操作: ${groupLockKey},orgId=${currentUser.currentOrg},groupId=${
              postData.groupId
            },businessType=${this.getBusinessType()}`,
          );
          throw new BadParamsException({ message: '系统繁忙，请稍后重试', ...RoverExceptions.Customer.GroupLimited });
        }

        // 其他异常情况下也要释放锁
        if (groupLock) {
          try {
            await groupLock.release();
            this.logger.info(
              `异常情况释放分组校验锁: ${groupLockKey},orgId=${currentUser.currentOrg},groupId=${postData.groupId},businessType=${this.getBusinessType()}`,
            );
            groupLock = null;
          } catch (releaseError) {
            this.logger.error(
              `异常情况释放分组校验锁失败: ${groupLockKey},orgId=${currentUser.currentOrg},groupId=${postData.groupId},businessType=${this.getBusinessType()}`,
              releaseError,
            );
          }
        }
        throw error;
      } finally {
        // 确保任何情况下都释放锁（防御性编程）
        if (groupLock) {
          try {
            await groupLock.release();
            this.logger.info(
              `finally块释放分组校验锁: ${groupLockKey},orgId=${currentUser.currentOrg},groupId=${postData.groupId},businessType=${this.getBusinessType()}`,
            );
          } catch (releaseError) {
            this.logger.error(
              `finally块释放分组校验锁失败: ${groupLockKey},orgId=${currentUser.currentOrg},groupId=${postData.groupId},businessType=${this.getBusinessType()}`,
              releaseError,
            );
          }
        }
      }
    }

    // 部门校验时的锁
    if (postData.departmentNames?.length > 0) {
      // 为每个部门分别加锁，避免不同部门之间的互相阻塞
      for (const departmentName of postData.departmentNames) {
        const departmentLockKey = `validation_lock:${currentUser.currentOrg}:department:${departmentName}`;
        let departmentLock: Lock | null = null;

        try {
          this.logger.info(
            `尝试获取部门校验锁: ${departmentLockKey},orgId=${currentUser.currentOrg},departmentName=${departmentName},businessType=${this.getBusinessType()}`,
          );
          departmentLock = await this.redlock.acquire([departmentLockKey], 10000); // 10秒锁定时间

          // 校验部门数量限制
          const validationResult2 = await this.validateEntityCountLimit(currentUser, {
            validationType: ValidationType.DEPARTMENT,
            departmentNames: [departmentName], // 单个部门校验
            addCount,
            businessIds,
          });

          if (!validationResult2.allowed) {
            this.logger.error(`${this.getEntityName()}部门校验失败，orgId=${currentUser.currentOrg}, departmentName=${departmentName}`);
            throw new BadParamsException({ message: validationResult2.message, ...RoverExceptions.Customer.DepartmentLimited });
          }

          // 成功情况下释放锁
          if (departmentLock) {
            try {
              await departmentLock.release();
              this.logger.info(`释放部门校验锁: ${departmentLockKey}`);
              departmentLock = null; // 重置为null，避免在finally中重复释放
            } catch (releaseError) {
              this.logger.error(`释放部门校验锁失败: ${departmentLockKey}`, releaseError);
            }
          }
        } catch (error) {
          // 异常情况下的处理
          if (isRedlockAcquireLock(error)) {
            this.logger.warn(`获取部门校验锁失败，可能存在并发操作: ${departmentLockKey}`);
            throw new BadParamsException({ message: '系统繁忙，请稍后重试', ...RoverExceptions.Customer.DepartmentLimited });
          }

          // 其他异常情况下也要释放锁
          if (departmentLock) {
            try {
              await departmentLock.release();
              this.logger.info(`异常情况释放部门校验锁: ${departmentLockKey}`);
              departmentLock = null;
            } catch (releaseError) {
              this.logger.error(`异常情况释放部门校验锁失败: ${departmentLockKey}`, releaseError);
            }
          }
          throw error;
        } finally {
          // 确保任何情况下都释放锁（防御性编程）
          if (departmentLock) {
            try {
              await departmentLock.release();
              this.logger.info(`finally块释放部门校验锁: ${departmentLockKey}`);
            } catch (releaseError) {
              this.logger.error(`finally块释放部门校验锁失败: ${departmentLockKey}`, releaseError);
            }
          }
        }
      }
    }
  }

  /**
   * 通用的实体校验逻辑 - 模板方法
   */
  protected async validateEntityCountLimitInternal(currentUser: RoverUserModel, data: ValidateCountModel): Promise<ValidateCountResponse> {
    const { currentOrg: orgId } = currentUser;
    const { validationType, groupId, departmentNames, businessIds, addCount } = data;

    // 根据组织ID确定限制数量
    const limitCount = specialOrgIds.includes(orgId) ? SPEC_GROUP_LIMIT_COUNT : GROUP_LIMIT_COUNT;

    // 校验businessIds并获取现有实体信息
    const existingEntities = await this.validateBusinessIds(businessIds, orgId);

    if (validationType === ValidationType.GROUP) {
      return this.validateGroupLimit(orgId, groupId, limitCount, existingEntities, addCount);
    } else if (validationType === ValidationType.DEPARTMENT) {
      return this.validateDepartmentLimit(orgId, departmentNames, limitCount, existingEntities, addCount);
    } else {
      throw new BadRequestException('无效的校验类型');
    }
  }

  /**
   * 批量校验数量限制的通用实现
   * 子类可以重写此方法来实现特定的校验逻辑
   */
  async validateBatchLimits<T>(currentUser: RoverUserModel, data: T[], batchId: number, jobId: number): Promise<BatchValidationResult<T>> {
    return this.validateBatchLimitsInternal(currentUser, data, batchId, jobId);
  }

  /**
   * 批量校验的内部实现 - 模板方法
   * 这个方法定义了批量校验的算法骨架，子类可以重写具体步骤
   */
  protected async validateBatchLimitsInternal<T>(currentUser: RoverUserModel, data: T[], batchId: number, jobId: number): Promise<BatchValidationResult<T>> {
    // 统计当前Job中每个分组的数量
    const groupCounts = new Map<string, T[]>();
    const departmentCounts = new Map<string, T[]>();

    // 按分组和部门分类记录
    this.categorizeRecords(data, groupCounts, departmentCounts);

    const validRecords: T[] = [];
    const failedRecords: BatchFailedRecord<T>[] = [];
    const processedRecords = new Set<T>();

    // 校验分组限制
    await this.validateGroupsInBatch(currentUser, groupCounts, validRecords, failedRecords, processedRecords);

    // 校验部门限制
    await this.validateDepartmentsInBatch(currentUser, departmentCounts, validRecords, failedRecords, processedRecords);

    this.logger.info(`批量校验完成: 有效记录${validRecords.length}, 失败记录${failedRecords.length}`);
    return { validRecords, failedRecords };
  }

  /**
   * 将记录按分组和部门分类 - 抽象方法，子类需要实现
   */
  protected abstract categorizeRecords<T>(data: T[], groupCounts: Map<string, T[]>, departmentCounts: Map<string, T[]>): void;

  /**
   * 根据分组名称获取分组ID - 抽象方法，子类需要实现
   */
  protected abstract getGroupIdByName(orgId: number, groupName: string): Promise<number>;

  /**
   * 批量校验分组限制
   */
  protected async validateGroupsInBatch<T>(
    currentUser: RoverUserModel,
    groupCounts: Map<string, T[]>,
    validRecords: T[],
    failedRecords: BatchFailedRecord<T>[],
    processedRecords: Set<T>,
  ): Promise<void> {
    for (const [groupName, records] of groupCounts.entries()) {
      try {
        const groupId = await this.getGroupIdByName(currentUser.currentOrg, groupName);
        const validationResult = await this.validateEntityCountLimit(currentUser, {
          validationType: ValidationType.GROUP,
          groupId,
          addCount: records.length,
        });

        if (!validationResult.allowed) {
          // 分组超限，计算可以导入的数量
          const availableCount = validationResult.limitCount - validationResult.currentCount;
          const canImportCount = Math.max(0, availableCount);

          this.logger.warn(
            `分组"${groupName}"超限: 当前${validationResult.currentCount}, 限制${validationResult.limitCount}, 尝试导入${records.length}, 可导入${canImportCount}`,
          );

          // 前N个记录可以导入，剩余的标记为失败
          records.forEach((record, index) => {
            if (index < canImportCount && !processedRecords.has(record)) {
              validRecords.push(record);
              processedRecords.add(record);
            } else if (!processedRecords.has(record)) {
              failedRecords.push({
                record,
                reason: `分组"${groupName}"已达到限制数量${validationResult.limitCount}`,
                errorType: 'GROUP_LIMIT',
              });
              processedRecords.add(record);
            }
          });
        } else {
          // 分组未超限，所有记录都可以导入
          records.forEach((record) => {
            if (!processedRecords.has(record)) {
              validRecords.push(record);
              processedRecords.add(record);
            }
          });
        }
      } catch (error) {
        this.logger.error(`分组校验失败: ${error.message}`);
        // 分组校验失败，所有相关记录都标记为失败
        records.forEach((record) => {
          if (!processedRecords.has(record)) {
            failedRecords.push({
              record,
              reason: `分组"${groupName}"校验失败: ${error.message}`,
              errorType: 'VALIDATION_ERROR',
            });
            processedRecords.add(record);
          }
        });
      }
    }
  }

  /**
   * 批量校验部门限制
   */
  protected async validateDepartmentsInBatch<T>(
    currentUser: RoverUserModel,
    departmentCounts: Map<string, T[]>,
    validRecords: T[],
    failedRecords: BatchFailedRecord<T>[],
    processedRecords: Set<T>,
  ): Promise<void> {
    for (const [departmentName, records] of departmentCounts.entries()) {
      try {
        const validationResult = await this.validateEntityCountLimit(currentUser, {
          validationType: ValidationType.DEPARTMENT,
          departmentNames: [departmentName],
          addCount: records.length,
        });

        if (!validationResult.allowed) {
          // 部门超限，移除已在有效记录中的
          const availableCount = validationResult.limitCount - validationResult.currentCount;
          const canImportCount = Math.max(0, availableCount);

          this.logger.warn(`部门"${departmentName}"超限: 可导入${canImportCount}, 尝试导入${records.length}`);

          let importedCount = 0;
          records.forEach((record) => {
            if (validRecords.includes(record) && importedCount >= canImportCount) {
              // 从有效记录中移除并添加到失败记录
              const index = validRecords.indexOf(record);
              if (index > -1) {
                validRecords.splice(index, 1);
                failedRecords.push({
                  record,
                  reason: `部门"${departmentName}"已达到限制数量${validationResult.limitCount}`,
                  errorType: 'DEPARTMENT_LIMIT',
                });
              }
            } else if (validRecords.includes(record)) {
              importedCount++;
            }
          });
        }
      } catch (error) {
        this.logger.error(`部门校验失败: ${error.message}`);
        // 部门校验失败，从有效记录中移除相关记录
        records.forEach((record) => {
          if (validRecords.includes(record)) {
            const index = validRecords.indexOf(record);
            if (index > -1) {
              validRecords.splice(index, 1);
              failedRecords.push({
                record,
                reason: `部门"${departmentName}"校验失败: ${error.message}`,
                errorType: 'VALIDATION_ERROR',
              });
            }
          }
        });
      }
    }
  }
}
