import { GROUP_LIMIT_COUNT } from '@domain/constants/common';
import { CustomerEntity } from '@domain/entities/CustomerEntity';
import { DepartmentEntity } from '@domain/entities/DepartmentEntity';
import { GroupsEntity } from '@domain/entities/GroupsEntity';
import { RoverUserModel } from '@domain/model/RoverUserModel';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { generateUniqueTestIds } from '@testing/test.user';
import { Repository } from 'typeorm';
import { RedisCounterService } from '../redis-counter.service';
import { BusinessType, RedisKeyBuilder } from '../redis-key-builder';
import { CustomerValidationStrategy } from './CustomerValidationStrategy';
import { BadParamsException } from '@kezhaozhao/qcc-exception-handler';

jest.setTimeout(300000);

describe('CustomerValidationStrategy - Atomic Counter Integration Tests', () => {
  const [testOrgId, testUserId] = generateUniqueTestIds('customer.service.spec.ts');
  let strategy: CustomerValidationStrategy;
  let redisCounterService: RedisCounterService;
  let redisService: RedisService;
  let customerRepo: Repository<CustomerEntity>;
  let app: TestingModule;

  // 模拟Redis存储
  const mockRedisStorage = new Map<string, number>();

  const testUser: RoverUserModel = {
    currentOrg: testOrgId,
    userId: testUserId,
  } as RoverUserModel;

  beforeAll(async () => {
    app = await Test.createTestingModule({
      providers: [
        CustomerValidationStrategy,
        RedisCounterService,
        {
          provide: getRepositoryToken(CustomerEntity),
          useValue: {
            createQueryBuilder: jest.fn().mockReturnValue({
              select: jest.fn().mockReturnThis(),
              where: jest.fn().mockReturnThis(),
              andWhere: jest.fn().mockReturnThis(),
              groupBy: jest.fn().mockReturnThis(),
              getRawMany: jest.fn().mockResolvedValue([]),
            }),
          },
        },
        {
          provide: getRepositoryToken(GroupsEntity),
          useValue: {
            find: jest.fn().mockResolvedValue([]),
          },
        },
        {
          provide: getRepositoryToken(DepartmentEntity),
          useValue: {
            find: jest.fn().mockResolvedValue([]),
          },
        },
        {
          provide: RedisService,
          useValue: {
            getClient: jest.fn().mockReturnValue({
              script: jest.fn().mockResolvedValue('test_script_sha'),
              evalsha: jest.fn().mockImplementation((sha, numKeys, key, limit, increment) => {
                // 模拟 Lua 脚本的行为
                const limitNum = parseInt(limit);
                const incrementNum = parseInt(increment);

                // 从内存中获取当前值（模拟Redis存储）
                const currentValue = mockRedisStorage.get(key) || 0;
                const newValue = currentValue + incrementNum;

                if (newValue > limitNum) {
                  return Promise.resolve(-1); // 超限
                } else {
                  mockRedisStorage.set(key, newValue);
                  return Promise.resolve(newValue); // 返回新值
                }
              }),
              get: jest.fn().mockImplementation((key) => {
                const value = mockRedisStorage.get(key);
                return Promise.resolve(value ? value.toString() : null);
              }),
              setex: jest.fn().mockImplementation((key, ttl, value) => {
                mockRedisStorage.set(key, parseInt(value));
                return Promise.resolve('OK');
              }),
              pipeline: jest.fn().mockReturnValue({
                setex: jest.fn().mockImplementation((key, ttl, value) => {
                  mockRedisStorage.set(key, parseInt(value));
                  return this;
                }),
                exec: jest.fn().mockResolvedValue([]),
              }),
              del: jest.fn().mockImplementation((...keys) => {
                keys.forEach((key) => mockRedisStorage.delete(key));
                return Promise.resolve(keys.length);
              }),
            }),
          },
        },
      ],
    }).compile();

    strategy = app.get<CustomerValidationStrategy>(CustomerValidationStrategy);
    redisCounterService = app.get<RedisCounterService>(RedisCounterService);
    redisService = app.get<RedisService>(RedisService);
    customerRepo = app.get<Repository<CustomerEntity>>(getRepositoryToken(CustomerEntity));

    // 初始化 RedisCounterService
    await redisCounterService.onModuleInit();
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(async () => {
    // 清理模拟Redis存储
    mockRedisStorage.clear();
  });

  describe('批量上传场景测试', () => {
    it('批量上传：多个记录同分组，总数未超限，应该成功', async () => {
      // Arrange - 模拟当前分组已有 10 个客户
      const groupId = 1;
      const initialCount = 10;
      const batchSize = 20; // 批量增加 20 个
      const expectedFinalCount = initialCount + batchSize;

      // 设置初始计数和预热状态
      const groupKey = RedisKeyBuilder.buildGroupCountKey(BusinessType.CUSTOMER, testOrgId, groupId);
      const warmedKey = RedisKeyBuilder.buildWarmedStatusKey(BusinessType.CUSTOMER, testOrgId);
      mockRedisStorage.set(groupKey, initialCount);
      mockRedisStorage.set(warmedKey, 1);

      // Act - 模拟批量校验
      const postData = { groupId } as any;

      const result = await strategy.validateGroupAndDepartmentCountLimit(
        testUser,
        postData,
        new Array(batchSize).fill(0).map((_, i) => i + 1),
      );

      // 验证返回了 reservedKeys 和 addCount
      expect(result).toHaveProperty('reservedKeys');
      expect(result).toHaveProperty('addCount');
      expect(result.reservedKeys.length).toBeGreaterThan(0);
      expect(result.addCount).toBe(batchSize);

      // Assert - 验证最终计数
      const finalCount = mockRedisStorage.get(groupKey) || 0;
      expect(finalCount).toBe(expectedFinalCount);
    });

    it('批量上传：总数超限，应该抛出异常', async () => {
      // Arrange - 模拟当前分组已接近限制
      const groupId = 1;
      const initialCount = GROUP_LIMIT_COUNT - 5; // 还剩 5 个名额
      const batchSize = 10; // 尝试增加 10 个，会超限

      // 设置初始计数和预热状态
      const groupKey = RedisKeyBuilder.buildGroupCountKey(BusinessType.CUSTOMER, testOrgId, groupId);
      const warmedKey = RedisKeyBuilder.buildWarmedStatusKey(BusinessType.CUSTOMER, testOrgId);
      mockRedisStorage.set(groupKey, initialCount);
      mockRedisStorage.set(warmedKey, 1);

      // Act & Assert
      const postData = { groupId } as any;

      await expect(
        strategy.validateGroupAndDepartmentCountLimit(
          testUser,
          postData,
          new Array(batchSize).fill(0).map((_, i) => i + 1),
        ),
      ).rejects.toThrow(BadParamsException);

      // 验证计数没有增加（原子操作失败）
      const finalCount = mockRedisStorage.get(groupKey) || 0;
      expect(finalCount).toBe(initialCount);
    });

    it('部门校验：单个部门未超限时应该成功', async () => {
      // Arrange
      const departmentNames = ['销售部'];
      const initialCount = 30;
      const addCount = 5;

      // 设置初始计数和预热状态
      const deptKey = RedisKeyBuilder.buildDepartmentCountKey(BusinessType.CUSTOMER, testOrgId, '销售部');
      const warmedKey = RedisKeyBuilder.buildWarmedStatusKey(BusinessType.CUSTOMER, testOrgId);
      mockRedisStorage.set(deptKey, initialCount);
      mockRedisStorage.set(warmedKey, 1);

      // Act
      const postData = { departmentNames } as any;

      const result = await strategy.validateGroupAndDepartmentCountLimit(
        testUser,
        postData,
        new Array(addCount).fill(0).map((_, i) => i + 1),
      );

      // 验证返回了 reservedKeys 和 addCount
      expect(result).toHaveProperty('reservedKeys');
      expect(result).toHaveProperty('addCount');

      // Assert - 验证最终计数
      const finalCount = mockRedisStorage.get(deptKey) || 0;
      expect(finalCount).toBe(initialCount + addCount);
    });
  });

  describe('并发操作场景测试', () => {
    it('并发单个插入：多个请求同时进行，总数不应该超限', async () => {
      // Arrange
      const groupId = 1;
      const initialCount = GROUP_LIMIT_COUNT - 10; // 还剩 10 个名额
      const concurrentRequests = 20; // 20 个并发请求

      // 设置初始计数和预热状态
      const groupKey = RedisKeyBuilder.buildGroupCountKey(BusinessType.CUSTOMER, testOrgId, groupId);
      const warmedKey = RedisKeyBuilder.buildWarmedStatusKey(BusinessType.CUSTOMER, testOrgId);
      mockRedisStorage.set(groupKey, initialCount);
      mockRedisStorage.set(warmedKey, 1);

      // Act - 并发执行多个校验请求
      const postData = { groupId } as any;
      const promises = Array.from({ length: concurrentRequests }, () =>
        strategy
          .validateGroupAndDepartmentCountLimit(testUser, postData)
          .then(() => ({ success: true, error: null }))
          .catch((error) => ({ success: false, error })),
      );

      const results = await Promise.all(promises);

      // Assert
      const successCount = results.filter((r) => r.success).length;
      const failureCount = results.filter((r) => !r.success).length;

      // 验证成功的请求数不超过剩余名额
      expect(successCount).toBeLessThanOrEqual(10);
      expect(failureCount).toBeGreaterThan(0);

      // 验证最终计数不超过限制
      const finalCount = mockRedisStorage.get(groupKey) || 0;
      expect(finalCount).toBeLessThanOrEqual(GROUP_LIMIT_COUNT);
      expect(finalCount).toBe(initialCount + successCount);

      console.log(`并发测试结果: 成功=${successCount}, 失败=${failureCount}, 最终计数=${finalCount}`);
    });
  });

  describe('预热机制集成测试', () => {
    it('首次访问：应该自动预热并正确设置计数器', async () => {
      // Arrange - 模拟数据库中的现有数据
      const mockGroupCounts = [
        { groupId: 1, count: 25 },
        { groupId: -1, count: 10 }, // 未分组
      ];
      const mockDeptCounts = [{ departmentName: '销售部', count: 15 }];

      // Mock 分组数量查询
      const mockQueryBuilder = customerRepo.createQueryBuilder() as any;
      mockQueryBuilder.getRawMany.mockResolvedValue(mockGroupCounts);

      // Mock 部门数量查询 - 由于在测试环境中我们的getDepartmentCurrentCounts会返回空数组
      // 我们需要通过spy来模拟返回正确的部门数量
      const getDepartmentCountsSpy = jest.spyOn(strategy as any, 'getDepartmentCurrentCounts');
      getDepartmentCountsSpy.mockResolvedValue(mockDeptCounts);

      // Act - 触发预热
      const postData = { groupId: 1 } as any;
      const result = await strategy.validateGroupAndDepartmentCountLimit(testUser, postData);

      // 验证返回了正确的结构
      expect(result).toHaveProperty('reservedKeys');
      expect(result).toHaveProperty('addCount');

      // Assert - 验证预热设置了正确的计数
      const group1Count = mockRedisStorage.get(RedisKeyBuilder.buildGroupCountKey(BusinessType.CUSTOMER, testOrgId, 1)) || 0;
      const ungroupedCount = mockRedisStorage.get(RedisKeyBuilder.buildGroupCountKey(BusinessType.CUSTOMER, testOrgId, -1)) || 0;
      const salesDeptCount = mockRedisStorage.get(RedisKeyBuilder.buildDepartmentCountKey(BusinessType.CUSTOMER, testOrgId, '销售部')) || 0;

      expect(group1Count).toBe(26); // 25 + 1 (刚才的操作)
      expect(ungroupedCount).toBe(10);
      expect(salesDeptCount).toBe(15);

      console.log(`预热测试结果: 分组1=${group1Count}, 未分组=${ungroupedCount}, 销售部=${salesDeptCount}`);

      // 恢复spy
      getDepartmentCountsSpy.mockRestore();
    });
  });
});
