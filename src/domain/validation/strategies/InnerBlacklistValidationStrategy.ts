import { CreateInnerBlacklistModelRequest } from '@domain/dto/blacklist/CreateInnerBlacklistModelRequest';
import { UpdateInnerBlacklistModelRequest } from '@domain/dto/blacklist/UpdateInnerBlacklistModelRequest';
import { ValidateCountResponse } from '@domain/dto/customer/ValidateCountResponse';
import { DepartmentEntity } from '@domain/entities/DepartmentEntity';
import { GroupsEntity } from '@domain/entities/GroupsEntity';
import { InnerBlacklistEntity } from '@domain/entities/InnerBlacklistEntity';
import { DepartmentTypeEnum } from '@domain/enums/department/DepartmentTypeEnum';
import { ValidateCountModel } from '@domain/model/customer/ValidateCountModel';
import { GroupType } from '@domain/model/element/CreateGroupModel';
import { RoverUserModel } from '@domain/model/RoverUserModel';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Cacheable } from '@type-cacheable/core';
import { Repository } from 'typeorm';
import { AbstractValidationStrategy } from '../AbstractValidationStrategy';
import { RedisCounterService } from '../redis-counter.service';
import { BusinessType } from '../redis-key-builder';

/**
 * InnerBlacklist校验策略
 * 负责InnerBlacklist_File类型的分组和部门校验逻辑
 */
@Injectable()
export class InnerBlacklistValidationStrategy extends AbstractValidationStrategy<InnerBlacklistEntity> {
  constructor(
    @InjectRepository(InnerBlacklistEntity) private readonly innerBlacklistRepo: Repository<InnerBlacklistEntity>,
    @InjectRepository(GroupsEntity) groupRepo: Repository<GroupsEntity>,
    @InjectRepository(DepartmentEntity) departmentRepo: Repository<DepartmentEntity>,
    redisService: RedisService,
    redisCounterService: RedisCounterService,
  ) {
    super(groupRepo, departmentRepo, redisService, redisCounterService);
  }

  // 实现抽象方法
  protected getEntityRepo(): Repository<InnerBlacklistEntity> {
    return this.innerBlacklistRepo;
  }

  protected getEntityPrimaryKeyField(): string {
    return 'id';
  }

  protected getGroupType(): GroupType {
    return GroupType.InnerBlacklistGroup;
  }

  protected getEntityName(): string {
    return '内部黑名单';
  }

  protected getDepartmentType(): DepartmentTypeEnum {
    return DepartmentTypeEnum.InnerBlacklist;
  }

  protected async validateEntityCountLimit(currentUser: RoverUserModel, data: ValidateCountModel): Promise<ValidateCountResponse> {
    return this.validateEntityCountLimitInternal(currentUser, data);
  }

  // 公共方法，暴露给外部调用
  public async validateInnerBlacklistCountLimit(currentUser: RoverUserModel, data: ValidateCountModel): Promise<ValidateCountResponse> {
    return this.validateEntityCountLimit(currentUser, data);
  }

  /**
   * 同时校验分组、部门数量限制，在 allowed=false 时抛出异常
   * @returns 返回已预留的Redis keys和addCount，用于后续补偿
   */
  async validateGroupAndDepartmentCountLimit(
    currentUser: RoverUserModel,
    postData: CreateInnerBlacklistModelRequest | UpdateInnerBlacklistModelRequest,
    businessIds?: number[],
  ): Promise<{ reservedKeys: string[]; addCount: number }> {
    // 使用新的原子计数器校验方法
    return this.validateGroupAndDepartmentCountLimitWithAtomicCounter(currentUser, postData, businessIds);
  }

  // 实现抽象方法 - 业务类型相关
  protected getBusinessType(): BusinessType {
    return BusinessType.INNER_BLACKLIST;
  }

  protected getGroupLimitedException(): any {
    return { code: 'INNER_BLACKLIST_GROUP_LIMITED', message: '内部黑名单分组数量已达上限' };
  }

  protected getDepartmentLimitedException(): any {
    return { code: 'INNER_BLACKLIST_DEPARTMENT_LIMITED', message: '内部黑名单部门数量已达上限' };
  }

  // 实现抽象方法 - 数据库查询相关
  protected async getGroupCurrentCounts(orgId: number): Promise<Array<{ groupId: number; count: number }>> {
    return this.innerBlacklistRepo
      .createQueryBuilder('blacklist')
      .select('COALESCE(blacklist.groupId, -1) as groupId, COUNT(*) as count')
      .where('blacklist.orgId = :orgId', { orgId })
      .groupBy('COALESCE(blacklist.groupId, -1)')
      .getRawMany();
  }

  protected async getDepartmentCurrentCounts(orgId: number): Promise<Array<{ departmentName: string; count: number }>> {
    try {
      // 使用原生SQL查询部门数量（避免QueryBuilder兼容性问题）
      const query = `
        SELECT d.name as departmentName, COUNT(DISTINCT ib.id) as count
        FROM inner_blacklist ib
        INNER JOIN inner_blacklist_department ibd ON ib.id = ibd.inner_blacklist_id
        INNER JOIN department d ON ibd.department_id = d.id
        WHERE ib.org_id = ?
        GROUP BY d.name
      `;

      const results = await this.innerBlacklistRepo.query(query, [orgId]);
      return results.map((result: any) => ({
        departmentName: result.departmentName,
        count: parseInt(result.count),
      }));
    } catch (error) {
      // 如果原生SQL失败，返回空数组避免阻塞流程
      console.warn(`查询部门数量失败: ${error.message}`);
      return [];
    }
  }

  protected async getGroupLimit(orgId: number, groupId: number): Promise<number> {
    // 内部黑名单默认限制数量（可根据实际业务需求调整）
    return 10000;
  }

  protected async getDepartmentLimit(orgId: number, departmentName: string): Promise<number> {
    // 内部黑名单默认限制数量（可根据实际业务需求调整）
    return 10000;
  }

  /**
   * 将记录按分组和部门分类
   */
  protected categorizeRecords<T>(data: T[], groupCounts: Map<string, T[]>, departmentCounts: Map<string, T[]>): void {
    data.forEach((record: any) => {
      // 处理分组
      const groupName = record.group || '未分组';
      if (!groupCounts.has(groupName)) {
        groupCounts.set(groupName, []);
      }
      groupCounts.get(groupName)!.push(record);

      // 处理部门
      if (record.departmentNames?.length) {
        record.departmentNames.forEach((dept: string) => {
          if (!departmentCounts.has(dept)) {
            departmentCounts.set(dept, []);
          }
          departmentCounts.get(dept)!.push(record);
        });
      }
    });
  }

  /**
   * 获取组织下所有分组（带缓存优化）
   * @param orgId 组织ID
   * @returns 分组数组，包含未分组的特殊项
   */
  @Cacheable({
    ttlSeconds: 300, // 5分钟缓存
    cacheKey: (args: any[]) => `cache:inner_blacklist_groups:${args[0]}`,
  })
  protected async getAllGroupsByOrgId(orgId: number): Promise<GroupsEntity[]> {
    const groups = await this.groupRepo.find({
      where: {
        orgId,
        groupType: GroupType.InnerBlacklistGroup,
      },
    });

    // 添加"未分组"这个特殊分组
    const ungroupedGroup = new GroupsEntity();
    ungroupedGroup.groupId = -1;
    ungroupedGroup.name = '未分组';
    ungroupedGroup.orgId = orgId;
    ungroupedGroup.groupType = GroupType.InnerBlacklistGroup;

    return [ungroupedGroup, ...groups];
  }

  /**
   * 根据分组名称获取分组ID（优化版本，使用内存过滤）
   * @param orgId 组织ID
   * @param groupName 分组名称
   * @returns 分组ID
   */
  protected async getGroupIdByName(orgId: number, groupName: string): Promise<number> {
    // 获取所有分组（使用缓存）
    const allGroups = await this.getAllGroupsByOrgId(orgId);

    // 在内存中查找匹配的分组
    const targetGroup = allGroups.find((group) => group.name === groupName);

    if (!targetGroup) {
      throw new Error(`分组"${groupName}"不存在`);
    }

    return targetGroup.groupId;
  }
}
