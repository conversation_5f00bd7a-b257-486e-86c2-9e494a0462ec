import { OrgSettingsLogEntity } from '@domain/entities/OrgSettingsLogEntity';
import { DimensionLevel1Enums } from '@domain/enums/diligence/DimensionLevel1Enums';
import { processTenderConfig } from './settings.utils';

describe('settings.utils 单元测试', () => {
  describe('processTenderConfig', () => {
    it('重复调用应保持幂等且不污染输入对象', () => {
      const inputEntity = new OrgSettingsLogEntity();
      inputEntity.content = [
        {
          key: DimensionLevel1Enums.Risk_InterestConflict,
          status: 1,
          types: [
            { key: 'StaffSameName', status: 1 },
            { key: 'StaffSamePhone', status: 0 },
          ],
          subDimensionList: [
            {
              key: 'SubKey',
              status: 1,
              types: [{ key: 'SubType', status: 1 }],
            },
          ],
        },
      ];

      const first = processTenderConfig(inputEntity);
      const second = processTenderConfig(inputEntity);

      expect(first).toEqual(second);
      expect(first[0].types).toEqual(['StaffSameName']);
      expect(first[0].subDimensionList?.[0].types).toEqual(['SubType']);

      const originalConfig = inputEntity.content as unknown as Array<{ types?: unknown[] }>;
      expect(originalConfig[0].types?.[0]).toEqual({ key: 'StaffSameName', status: 1 });
    });
  });
});
