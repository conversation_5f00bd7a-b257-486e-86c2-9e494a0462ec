import { compact, findIndex, get, isNil, isObject, isString, orderBy, pick, sortBy, uniq } from 'lodash';
import { OptionsObjectModel } from '@domain/model/batch/export/OptionsObjectModel';
import { correctRoles, getRelationTag, isEdge, relationParserContact, relationsPathParser, relationsPathParser2 } from '../../pdf/pdf-table.util';
import { OriginalRelationData } from '@domain/model/tender/OriginalRelationData';
import { drawEdge, getRolesNames, isCompany, isPerson } from '@domain/pdf/relation-path-diligence.util';

export function transformPersonName(val: any) {
  let personName = val.name || '-';
  if (val?.isSameName) {
    personName = `疑似同名（${val.name}）`;
  }
  if (val?.isSameContact) {
    const phoneEmail = [];
    Array.prototype.push.apply(
      phoneEmail,
      val?.phones?.map((r) => `${r?.n} ${r?.t}`),
    );
    Array.prototype.push.apply(
      phoneEmail,
      val.emails?.map((e) => e?.e),
    );
    personName = `相同联系方式（${phoneEmail.join(',')}）`;
  }
  if (val?.isSameContact && val?.isSameName) {
    const phoneEmail = [];
    Array.prototype.push.apply(
      phoneEmail,
      val?.phones?.map((r) => `${r?.n} ${r?.t}`),
    );
    Array.prototype.push.apply(
      phoneEmail,
      val?.emails?.map((e) => e?.e),
    );
    personName = `疑似同名（${val.name}）\n相同联系方式（${phoneEmail.join(',')}）`;
  }
  return personName;
}

export function processPledgor(val: any) {
  if (typeof val?.Pledgor == 'object') {
    return val?.Pledgor?.length ? val?.Pledgor?.map((p) => p?.toString().replace(/<[^>]*>/g, ''))?.join('\n') : '-';
  } else if (typeof val?.Pledgor == 'string') {
    return val?.Pledgor?.toString()?.replace(/<[^>]*>/g, '');
  }
}

/**
 * 处理受益人变更数据
 * @param val
 * @param timing 时间点 before/after
 */
export function processBeneficiary(val: any, timing: string) {
  const changeExtend = JSON.parse(val?.ChangeExtend)[0];
  const beforeContent = JSON.parse(changeExtend.BeforeContent);
  const afterContent = JSON.parse(changeExtend.AfterContent);
  let response = '-';
  if (timing === 'before') {
    if (Array.isArray(beforeContent)) {
      response = beforeContent[0].PercentTotal ? `${beforeContent[0].Name},受益股份（${beforeContent[0].PercentTotal}）` : `${beforeContent[0].Name}`;
    } else {
      response = beforeContent.PercentTotal ? `${beforeContent.Name},受益股份（${beforeContent.PercentTotal}）` : `${beforeContent.Name}`;
    }
  } else if (timing === 'after') {
    if (Array.isArray(afterContent)) {
      response = afterContent[0].PercentTotal ? `${afterContent[0].Name},受益股份（${afterContent[0].PercentTotal}）` : `${afterContent[0].Name}`;
    } else {
      response = afterContent.PercentTotal ? `${afterContent.Name},受益股份（${afterContent.PercentTotal}）` : `${afterContent.Name}`;
    }
  }
  return response;
}

export const processHoldAndBeneficiaryUpdateInfo = (val: any, changeContent: string) => {
  const changeExtend = JSON.parse(val?.ChangeExtend);
  const actualController = changeExtend.ActualControl;
  const benefitPerson = changeExtend.BenefitPerson;

  const changeContentArr = [];
  if (actualController?.length > 0) {
    actualController.forEach((item) => {
      const actualControllerBefore = JSON.parse(item.BeforeContent);
      const actualControllerAfter = JSON.parse(item.AfterContent);
      let actualControllerChangeContent = '';
      if (actualControllerBefore?.Name && actualControllerAfter?.Name) {
        if (actualControllerBefore.Name !== actualControllerAfter.Name) {
          actualControllerChangeContent = `实际控制人:由 ${actualControllerBefore?.Name}(股份占比:${actualControllerBefore?.PercentTotal || '-'}) 变更为 ${
            actualControllerAfter?.Name || '-'
          }(股份占比:${actualControllerAfter?.PercentTotal || '-'})`;
        } else {
          actualControllerChangeContent = `实际控制人:${actualControllerBefore?.Name}，股份占比:由 ${actualControllerBefore?.PercentTotal || '-'} 变更为 ${
            actualControllerAfter?.PercentTotal || '-'
          }`;
        }
        if (actualControllerChangeContent.length > 0) {
          changeContentArr.push(actualControllerChangeContent);
        }
      }
      if (actualControllerBefore.length > 0 && actualControllerAfter.length > 0) {
        let actualControllerChangeContent = '实际控制人:由 ';

        for (let i = 0; i < actualControllerBefore.length; i++) {
          actualControllerChangeContent += `${actualControllerBefore[i].Name}(股份占比:${actualControllerBefore[i].PercentTotal || '-'})`;
          if (i < actualControllerBefore.length - 1) {
            actualControllerChangeContent += '、';
          }
        }
        actualControllerChangeContent += ' 变更为 ';
        for (let i = 0; i < actualControllerAfter.length; i++) {
          actualControllerChangeContent += `${actualControllerAfter[i].Name}(股份占比:${actualControllerAfter[i].PercentTotal || '-'})`;
          if (i < actualControllerAfter.length - 1) {
            actualControllerChangeContent += '、';
          }
        }
        if (actualControllerChangeContent !== '实际控制人:由 ') {
          changeContentArr.push(actualControllerChangeContent);
        }
      }
    });
  }
  if (benefitPerson?.length > 0) {
    benefitPerson.forEach((item) => {
      const benefitPersonBefore = JSON.parse(item.BeforeContent);
      const benefitPersonAfter = JSON.parse(item.AfterContent);
      let benefitPersonChangeContent = '';
      if (benefitPersonBefore?.Name && benefitPersonAfter?.Name) {
        if (benefitPersonBefore.Name !== benefitPersonAfter.Name) {
          benefitPersonChangeContent = `受益所有人:由 ${benefitPersonBefore?.Name || '-'}(股份占比:${benefitPersonBefore?.PercentTotal || '-'}) 变更为 ${
            benefitPersonAfter?.Name || '-'
          }(股份占比:${benefitPersonAfter?.PercentTotal || '-'})`;
        } else {
          benefitPersonChangeContent = `受益所有人:${benefitPersonBefore?.Name}，股份占比:由 ${benefitPersonBefore?.PercentTotal || '-'} 变更为 ${
            benefitPersonAfter?.PercentTotal || '-'
          }`;
        }
        if (benefitPersonChangeContent.length > 0) {
          changeContentArr.push(benefitPersonChangeContent);
        }
      }

      if (benefitPersonBefore.length > 0 && benefitPersonAfter.length > 0) {
        let benefitPersonChangeContent = '受益所有人:由 ';
        for (let i = 0; i < benefitPersonBefore.length; i++) {
          benefitPersonChangeContent += `${benefitPersonBefore[i].Name}(股份占比:${benefitPersonBefore[i].PercentTotal || '-'})`;
          if (i < benefitPersonBefore.length - 1) {
            benefitPersonChangeContent += '、';
          }
        }
        benefitPersonChangeContent += ' 变更为 ';
        for (let i = 0; i < benefitPersonAfter.length; i++) {
          benefitPersonChangeContent += `${benefitPersonAfter[i].Name}(股份占比:${benefitPersonAfter[i].PercentTotal || '-'})`;
          if (i < benefitPersonAfter.length - 1) {
            benefitPersonChangeContent += '、';
          }
        }
        if (benefitPersonChangeContent !== '受益所有人:由 ') {
          changeContentArr.push(benefitPersonChangeContent);
        }
      }
    });
  }
  return changeContentArr.join('\n');
};

export function processNegativeNews(val: any) {
  const compactCodedesc = val?.codedesc ?? [];
  const compactTagsnew = val?.tagsnew ?? [];
  return uniq(compact([...compactCodedesc, ...compactTagsnew]))
    ?.map((m) => `#${m}`)
    ?.join(' ');
}

export function processCaseRoleGroup(r: any) {
  const result = [];
  r?.caserolegroupbyrolename?.forEach((group) => {
    if (group?.LawyerTag == 0) {
      const roleName = group?.Role || '';
      group?.DetailList?.forEach((detail) => {
        result.push(roleName + ' - ' + detail.Name);
      });
    }
  });
  r?.involveRole?.forEach((involveRole) => {
    result.push(involveRole.Tag + ' - ' + involveRole.Name);
  });
  return result.length > 0 ? result.join('\n') : '-';
}

export function generateRelationChain(left: string, right: string, middle: string, leftInfo: string, rightInfo: string, options: OptionsObjectModel) {
  const leftNode = left ? `${left}` : '';
  const rightNode = right ? `${right}` : '';
  const middleNode = middle ? `${middle}` : '';

  const createLine = (content: string, direction: 'left' | 'right') => {
    let output = content;
    // 特殊字段处理
    if (['stockpercent'].indexOf(options[`${direction}Info`]) > -1) {
      output = `${content}%`;
    }
    output = content ? `（${output}）` : '';
    return `${direction === 'left' ? '←' : ''}${output}${direction === 'right' ? '→' : ''}`;
  };

  return `${leftNode}${leftNode ? createLine(leftInfo, 'left') : ''}${middleNode}${rightNode ? createLine(rightInfo, 'right') : ''}${rightNode}`;
}

// 处理 <div> 中的路径换行符，替换为 \n
const regexHtmlElement = (relations) => {
  const preserveNewlines = relations.replace(/<div[^>]*>路径：(\d+)<\/div>/g, '\n路径：$1\n');
  // 移除所有其他 HTML 标签
  const cleanText = preserveNewlines.replace(/<[^>]+>/g, '');
  return cleanText.trim(); // 去除首尾多余的空白
};
/**
 * 处理招标排查关系链数据
 * @param record
 */
export function generateCompanyRelationChain(record) {
  const relations = relationsPathParser2(record, true);
  return regexHtmlElement(relations);
}
/**
 * 处理招标排查关系 联系方式 链数据（他的处理比较特殊，比如：命中相同电话，那该电话也是一个节点）
 * @param record
 */
export function generateContactRelationChain(record) {
  const relations = relationParserContact(record.relations[0]);
  return regexHtmlElement(relations);
}
export function getRelationChain(items: any[]) {
  const historyMap = {
    HISEMPLOY: '历史高管',
    HISLEGAL: '历史法人',
    HISINVEST: '历史股东',
  };
  const relationChains = items.map((item) => {
    const { typeDD, roleDD, typeRelated, roleRelated, companyNameDD, companyNameRelated, personName, stockpercent } = item;
    const leftInfo = historyMap[typeDD] || roleDD;
    const rightInfo = historyMap[typeRelated] || roleRelated;
    return leftInfo || rightInfo
      ? generateRelationChain(companyNameDD, companyNameRelated, personName, leftInfo, rightInfo, {
          left: 'companyNameDD',
          leftInfo: 'leftInfo',
          middle: 'personName',
          rightInfo: 'rightInfo',
          right: 'companyNameRelated',
        })
      : `${companyNameDD}-${stockpercent}%→${companyNameRelated}`;
  });
  return relationChains.join('\n');
}

export function getControllerChain(name: string, paths: any[][]) {
  const relationChains = paths.map((path) => {
    return name + '->' + path.map((p) => `(${p.Percent})->${p.Name}`).join('->');
  });
  return relationChains.join('\n');
}

/**
 * 处理当事人
 * @param r
 */
export function proceeParty(val: any) {
  // 安全地获取 caserolegroupbyrolename 和 involveRole 数组，如果为空则返回空数组
  const caseRoleGroupByRoleName = Array.isArray(val?.caserolegroupbyrolename) ? val.caserolegroupbyrolename : [];
  const involveRole = Array.isArray(val?.involveRole) ? val.involveRole : [];

  // 使用 map 时安全访问每个元素的属性，并在缺失时提供默认值
  let caseRoles = [];
  caseRoleGroupByRoleName?.forEach((c) => {
    const role = c?.Role;
    const lawyerTag = c?.LawyerTag;
    c?.DetailList?.forEach((d) => {
      const name = d?.Name || '';
      if (role && lawyerTag != 1 && name) {
        caseRoles.push(`${role}-${name}`);
      }
    });
  });
  const caseRoleGroupByRoleName2 = Array.isArray(val?.caserole) ? val.caserole : [];
  if (!caseRoleGroupByRoleName?.length && caseRoleGroupByRoleName2.length) {
    caseRoles = caseRoleGroupByRoleName2.map((c) => `${c.P || c.ShowName}-${c.R}`) || [];
  }
  const involveRoles = involveRole
    .map((c) => {
      const tag = c?.Tag || '';
      const name = c?.Name || '';
      return tag && name ? `${tag}-${name}` : '';
    })
    .filter((item) => item); // 过滤掉空字符串

  // 合并两个数组并加入换行符
  return caseRoles.concat(involveRoles).join('\n');
}

/**
 * 渲染名称列表
 * @param roleObj 按角色分组的人员信息对象
 * @param keyMap 名字和编号的字段映射
 * @param indexLimit 超过该数量展示序号
 * @param customRender 自定义渲染函数，用于处理裁定文书状态、董监高法 tag、涉案人状态等
 * @param item 包含涉案人信息的对象，用于涉案人状态渲染
 * @returns 格式化后的字符串
 */
export const renderNameList = (
  roleObj: Record<string, any[]>,
  keyMap: { name: string | string[]; no: string },
  indexLimit: number,
  customRender?: (listItem: any, item?: any) => string,
  item?: any,
) => {
  if (Object.keys(roleObj).length === 0) {
    return '-';
  }
  let resultText = '';

  // 遍历每个角色
  Object.keys(roleObj).forEach((key: string) => {
    const list = roleObj[key];
    if (list.length > 0) {
      const listLength = list.length;
      const needNo = listLength > indexLimit;
      const nameListArr = list
        .map((listItem, index) => {
          const KeyNo = get(listItem, keyMap.no);
          const NameList = (Array.isArray(keyMap.name) ? keyMap.name : [keyMap.name]).map((nameKey) => get(listItem, nameKey)).filter(Boolean);
          const uniqNameList = uniq(NameList);
          let nameStr = '';
          if (uniqNameList.length > 0) {
            if (uniqNameList.length === 1 || !KeyNo) {
              nameStr = uniqNameList[0];
            } else {
              nameStr = `${uniqNameList[0]}（${uniqNameList[1] || ''}）`;
            }
          }

          // 调用自定义渲染函数
          const customDes = customRender ? customRender(listItem, item) : '';

          const parts = [nameStr, customDes].filter(Boolean);
          const baseComp = parts.join('');

          if (baseComp) {
            if (needNo) {
              return `${index + 1}. ${baseComp}`;
            }
            return baseComp;
          }
          return '';
        })
        .filter(Boolean); // 过滤掉空字符串

      if (nameListArr.length > 0) {
        resultText += `${key}：`;
        if (!needNo) {
          resultText += nameListArr.join(', ');
        } else {
          resultText += nameListArr.join('\n');
        }
        resultText += '\n';
      }
    }
  });

  return resultText.trim() || '-';
};

export function processRiskChangeCaseRoleGroup(record: any) {
  const caseRoleGroup = record?.caseRoleGroup;
  if (caseRoleGroup?.length) {
    // 里面可能不止原被告，不能用R === 0判断，先按角色排序，再用reduce分类
    const RolesArrMap = sortBy(caseRoleGroup, 'R').reduce((acc, cur) => {
      if (acc[cur.RN]) {
        acc[cur.RN].push(cur);
      } else {
        acc[cur.RN] = [cur];
      }
      return acc;
    }, {});
    return renderNameList(RolesArrMap, { name: ['ShowName', 'P'], no: 'N' }, 2);
  }
}

export function formatPartyNames(val: any): string {
  if (!val) {
    return '-';
  }

  const getParties = () => {
    let rolesObj: Record<string, any> = {};
    if (val.caserolegroupbyrolename?.length) {
      rolesObj = val.caserolegroupbyrolename
        ?.filter((item2) => item2.LawyerTag === 0 || isNil(item2.LawyerTag))
        .reduce((obj, { Role, DetailList }) => {
          // 过滤掉 不承担责任的数据
          obj[Role] = DetailList.filter((cur) => cur.JudgeResultDescription !== '不承担责任').map((detail) => ({
            ...detail,
            ...pick(detail.SupNameAndKeyNo, ['Name', 'KeyNo']),
          }));
          return obj;
        }, {});
    } else if (val.caserole?.length) {
      rolesObj = val.caserole.reduce((obj, roleObj) => {
        const { R } = roleObj;
        if (obj[R]) {
          obj[R].push(roleObj);
        } else {
          obj[R] = [roleObj];
        }
        return obj;
      }, {});
    }
    if (val.involveRole) {
      val.involveRole.forEach((role) => {
        if (rolesObj[role.Tag]) {
          rolesObj[role.Tag].push(role);
        } else {
          rolesObj[role.Tag] = [role];
        }
      });
    }
    return rolesObj;
  };

  const parties = getParties();
  // 自定义渲染函数，处理裁定文书状态和董监高法 tag，移除前端样式
  const customRender = (listItem: any) => {
    // 裁定文书的状态
    const judgeDes = listItem?.JudgeResultDescription ? `[${listItem.JudgeResultDescription}]` : '';
    // 董监高法 tag
    const jobDesList = listItem?.Job?.split(',').filter(Boolean);
    const jobDes = jobDesList?.length ? `[${jobDesList.join(', ')}]` : '';
    return `${judgeDes}${jobDes}`;
  };

  return renderNameList(parties, { name: ['ShowName', 'Name'], no: 'KeyNo' }, 2, customRender);
}

export function formatRoleAmt(val: any): string {
  if (!val) {
    return '-';
  }
  const entities = val?.RoleAmt || [];
  if (!entities.length) {
    return '-';
  }
  const rolesObj = entities.reduce((acc, cur) => {
    if (acc[cur.R]) {
      acc[cur.R].push(cur);
    } else {
      acc[cur.R] = [cur];
    }
    return acc;
  }, {});

  // 自定义渲染函数，处理涉案人状态和董监高法 tag
  const customRender = (listItem: any, item: any) => {
    // 涉案人状态
    const findTag = item?.involveRole?.find((role) => role.KeyNo === listItem.N);
    const involveDes = findTag?.Tag ? `[${findTag.Tag}]` : '';
    // 董监高法 tag
    const jobDesList = listItem?.Job?.split(',').filter(Boolean);
    const jobDes = jobDesList?.length ? `[${jobDesList.join(', ')}]` : '';
    return `${involveDes}${jobDes}`;
  };

  const r = renderNameList(rolesObj, { name: ['ShowName', 'P'], no: 'N' }, 1, customRender, val);
  return r;
}

/**
 * 合并相同起始点的关系路径: 依据相同的 startCompanyKeyno 和 endCompanyKeyno 合并
 */
export function mergeRelationPaths(dataSource: OriginalRelationData[]) {
  const uniquePathsMap = new Map<string, OriginalRelationData>();

  dataSource.forEach((item) => {
    // NOTE: 当两家公司处于不同位置时（A-B、B-A），需要合并关系路径
    const startToEnd = [item.startCompanyKeyno, item.endCompanyKeyno].join('-');
    const endToStart = [item.endCompanyKeyno, item.startCompanyKeyno].join('-');

    // 合并相同起始点的关系路径
    let uniquePathKey: string | undefined;
    uniquePathKey = uniquePathsMap.has(startToEnd) ? startToEnd : uniquePathKey;
    uniquePathKey = uniquePathsMap.has(endToStart) ? endToStart : uniquePathKey;

    if (uniquePathKey) {
      const targetItem = uniquePathsMap.get(uniquePathKey);
      uniquePathsMap.set(uniquePathKey, {
        ...targetItem,
        relations: [...targetItem.relations, item.relations],
        data: item.data !== undefined && item.data !== null ? [...targetItem.data, item.data] : [...targetItem.data],
      });
    } else {
      // 转换 `relations` 为嵌套数组
      const targetItem = {
        ...item,
        relations: [item.relations],
        data: item.data !== undefined && item.data !== null ? [item.data] : [],
      };
      uniquePathsMap.set(startToEnd, targetItem);
    }
  });
  const result = [...uniquePathsMap.values()];
  return result;
}

/**
 * 合并处理逻辑
 * @param dimensionData
 */
export function processRelationsDataPipeline(dimensionData: Record<string, any>[]) {
  let result = [];
  if (!dimensionData) {
    return result;
  }

  // 处理 role 别名
  result = transformRelationsData(dimensionData);
  // 按 startCompany 和 endCompany 合并关联关系: { startCompany, endCompany, relations: [][] }
  result = mergeBySameRelationGroup(result);
  // 处理关系并遍历 `relations` 为独立项, 恢复原来的数据结构
  result = mergeBySameRelationEdge(result);
  // 按风险等级倒序排序
  result = orderBy(result, ['level'], ['desc']);
  return result;
}

/**
 * 合并处理逻辑
 * @param dimensionData
 */
export function processRelationsDataPipeline2(dimensionData: Record<string, any>[]) {
  let result = [];
  if (!dimensionData) {
    return result;
  }

  //处理相同的互为担保对象，仅保留一条
  const RelationExistMap = new Map();
  dimensionData?.forEach((item) => {
    if (item.type === 'Guarantor') {
      const relationshipKey = item.startCompanyKeyno + item.endCompanyKeyno;
      const relationshipKey2 = item.endCompanyKeyno + item.startCompanyKeyno;
      if (!RelationExistMap.has(relationshipKey) && !RelationExistMap.has(relationshipKey2)) {
        RelationExistMap.set(relationshipKey, true);
        RelationExistMap.set(relationshipKey2, true);
        result.push(item);
      }
    } else {
      result.push(item);
    }
  });

  result = result.map((item) => {
    const relations = [];
    // 处理角色
    item.relations.forEach((relation) => {
      if (isEdge(relation)) {
        const role = getRelationRole(relation);
        relations.push({
          ...relation,
          role,
        });
      } else {
        relations.push(relation);
      }
    });

    return {
      ...item,
      relations,
    };
  });

  return result;
}

/**
 * 合并相同的关联关系的边 (relations)
 */
export function mergeBySameRelationEdge(dimensionData: Record<string, any>[]) {
  const result = [];
  dimensionData.forEach((item) => {
    const edgeKeyMap = {};
    const relations = [];

    // 处理现有关系数据（合并相同边及角色）
    item.relations.forEach((nodes) => {
      // 基于关系中的所有边 `edge` 属性生成唯一ID
      const edgeKey = nodes
        .reduce((r, c) => {
          if (isEdge(c)) {
            return r.concat([`${c.startid}:${c.endid}:${c.direction}`]);
          }
          return r.concat();
        }, [])
        .join('-');

      if (edgeKeyMap[edgeKey] !== undefined) {
        // 命中执行合并（修改结果数据）
        relations[edgeKeyMap[edgeKey]].forEach((node, i) => {
          if (isEdge(node)) {
            const roles = [node.role, nodes[i].role].reduce((prev, current) => prev.concat(current.split(',')), []);
            node.role = uniq(roles).filter(Boolean).join(',');
            // node.data =
            // node.groups =
          }
        });
        // edgeKeyMap[edgeKey].map((node, index) => {});
      } else {
        // 未命中加入到 map, pendding
        relations.push(nodes);
        edgeKeyMap[edgeKey] = relations.length - 1;
      }
    });

    // 将合并后的关系数据添加到结果集
    relations.forEach((nodes) => {
      result.push({
        ...item,
        relations: nodes,
      });
    });
  });
  return result;
}

/**
 * 通过 startCompanyName 和 endCompanyName 合并相同的关联关系 (relations)
 */
export function mergeBySameRelationGroup(dimensionData: Record<string, any>[]) {
  const result = [];
  dimensionData.forEach((item) => {
    const existIndex = findIndex(result, {
      startCompanyName: item.startCompanyName,
      endCompanyName: item.endCompanyName,
    });
    // 合并相同关联企业(startCompanyName, endCompanyName)
    if (existIndex > -1) {
      // 修改
      result[existIndex].relations = result[existIndex].relations.concat([item.relations]);
    } else {
      // 新增
      result.push({
        ...item,
        relations: [item.relations],
      });
    }
  });
  return result;
}

/**
 * 针对关联关系数据进行数据转换
 */
export function transformRelationsData(dimensionData: Record<string, any>[]) {
  if (!dimensionData) {
    return [];
  }

  const result = [];

  dimensionData.forEach((item) => {
    const relations = [];
    // 处理角色
    item.relations.forEach((relation) => {
      if (isEdge(relation)) {
        const role = getRelationRole(relation);
        relations.push({
          ...relation,
          role,
        });
      } else {
        relations.push(relation);
      }
    });

    result.push({
      ...item,
      relations,
    });
  });

  return result;
}

/**
 * 处理关联关系及 role 别名
 */
export function getRelationRole(node: Record<string, any>) {
  const RELATION_TYPE_MAP = {
    LEGAL: '法定代表人',
    HISLEGAL: '历史法定代表人',
    INVEST: '持股/投资关联',
    EMPLOY: '董监高',
    HISEMPLOY: '历史董监高',
    HISINVEST: '历史持股/投资关联',
    BRANCH: '分支机构',
    ACTUALCONTROLLER: '实际控制人',
    SAMENAMEEMPLOYEE: '疑似同名主要人员',
    CONTACTNUMBER: '相同电话号码',
    WEBSITE: '相同域名信息',
    ADDRESS: '相同地址',
    MAIL: '相同邮箱',
    PATENT: '相同专利信息',
    INTPATENT: '相同国际专利信息',
    SOFTWARECOPYRIGHT: '相同软件著作权',
    CASE: '相同司法案件',
  };

  let result = [];
  let roles = [];

  // 角色数组处理
  if (Array.isArray(node?.roles)) {
    roles = node.roles.map(({ role }) => role);
  } else {
    // 转为数组
    roles = (node?.role ?? '').split(',');
  }

  const typeUpperCase = String(node.type).toUpperCase();

  // 角色逻辑处理
  if (['HISEMPLOY', 'HISLEGAL'].includes(typeUpperCase)) {
    result = result.concat(roles.map((roleName: string) => `历史${roleName}`)); // 历史任职
  } else if (['HISINVEST'].includes(typeUpperCase) && node.stockpercent !== undefined) {
    // result.push(`历史投资${node.stockpercent}%`);
    result.push(RELATION_TYPE_MAP[typeUpperCase]); // 历史投资关系
  } else if (['INVEST'].includes(typeUpperCase) && node.stockpercent !== undefined) {
    // result.push(`投资${node.stockpercent}%`);
    result.push(RELATION_TYPE_MAP[typeUpperCase]); // 投资关系
  } else if (['BRANCH'].includes(typeUpperCase)) {
    result.push('分支机构');
  } else if (RELATION_TYPE_MAP[typeUpperCase]) {
    result.push(RELATION_TYPE_MAP[typeUpperCase]);
  } else if (['AC'].includes(node.type)) {
    // 特殊角色需要跳过不渲染
  } else {
    result = roles;
  }

  result = uniq(result).filter(Boolean);
  return result.join(',') || '';
}

export const convertPathToGraphTender = (relationPaths: any[], withPathTip = true) => {
  if (!Array.isArray(relationPaths) || relationPaths.length === 0) {
    return '-';
  }
  // const pathStr = convertPathToGraph(relationPaths, withPathTip);
  // // 处理 <div> 中的路径换行符，替换为 \n
  // const preserveNewlines = pathStr.replace(/<div[^>]*>路径 (\d+)<\/div>/g, '\n路径：$1\n');
  // // 移除所有其他 HTML 标签
  // const cleanText = preserveNewlines.replace(/<[^>]+>/g, '');
  // return cleanText.trim(); // 去除首尾多余的空白
  const result = [];
  relationPaths.forEach((nodes, index, source) => {
    const row = [];
    nodes.forEach((node) => {
      if (isEdge(node)) {
        let roles = getRolesNames(node);
        const groupRoles = (node?.groups ?? []).flatMap((group) => getRolesNames(group));
        roles = [...roles, ...groupRoles, ...(node?.roles || [])];
        roles = uniq(roles);
        row.push(drawEdge(node.direction, roles.join(', ')));
      } else if (isPerson(node)) {
        row.push(node['Person.name']);
      } else if (isCompany(node)) {
        row.push(node['Company.name'] || node.name);
      }
    });
    if (source.length > 1 && withPathTip) {
      result.push(`路径：${index + 1}`);
    }
    result.push(`${row.join('')}`);
  });

  return result.join('\n');
};
