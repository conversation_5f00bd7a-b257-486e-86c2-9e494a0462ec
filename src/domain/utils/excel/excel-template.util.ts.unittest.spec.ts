import { DimensionDetailCommonFields } from './excel-template.util';
import { DimensionLevel3Enums } from '@domain/enums/diligence/DimensionLevel3Enums';
import { DimensionLevel2Enums } from '@domain/enums/diligence/DimensionLevel2Enums';
import { dateTransform } from '@commons/utils/date.utils';
import { formatMoney } from '@commons/utils/utils';

// Mock 外部依赖
jest.mock('@commons/utils/date.utils');
jest.mock('@commons/utils/utils');
jest.mock('./excel-table.util');
jest.mock('@modules/batch/common/file.export.template', () => ({
  billAcceptanceRiskStatusMap: {},
  DurationConstants: {},
  separationNoticeTypeMap: {},
}));

const mockDateTransform = dateTransform as jest.MockedFunction<typeof dateTransform>;
const mockFormatMoney = formatMoney as jest.MockedFunction<typeof formatMoney>;

describe('ExcelTemplateUtil 单元测试', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('DimensionDetailCommonFields 配置测试', () => {
    it('应该包含正确的维度配置', () => {
      // Act & Assert
      expect(DimensionDetailCommonFields).toBeDefined();
      expect(Array.isArray(DimensionDetailCommonFields)).toBe(true);
      expect(DimensionDetailCommonFields.length).toBeGreaterThan(0);
    });

    it('应该包含SeparationNotice维度的配置', () => {
      // Act
      const separationNoticeConfig = DimensionDetailCommonFields.find((config) => config.dimensions.includes(DimensionLevel3Enums.SeparationNotice));

      // Assert
      expect(separationNoticeConfig).toBeDefined();
      expect(separationNoticeConfig.fields).toBeDefined();
      expect(Array.isArray(separationNoticeConfig.fields)).toBe(true);
    });

    it('SeparationNotice维度的日期格式化函数应该正常工作', () => {
      // Arrange
      const separationNoticeConfig = DimensionDetailCommonFields.find((config) => config.dimensions.includes(DimensionLevel3Enums.SeparationNotice));
      const decideDateField = separationNoticeConfig.fields.find((field) => field.key === 'DecideDate');

      const mockDate = '2024-01-01';
      const expectedFormattedDate = '2024年01月01日';
      mockDateTransform.mockReturnValue(expectedFormattedDate);

      // Act
      const result = decideDateField.format({ DecideDate: mockDate });

      // Assert
      expect(mockDateTransform).toHaveBeenCalledWith(mockDate);
      expect(result).toBe(expectedFormattedDate);
    });

    it('NoCapital维度的金额格式化函数应该正常工作', () => {
      // Arrange
      const noCapitalConfig = DimensionDetailCommonFields.find((config) => config.dimensions.includes(DimensionLevel2Enums.NoCapital));

      // Assert configuration exists
      expect(noCapitalConfig).toBeDefined();

      const registCapiField = noCapitalConfig.fields.find((field) => field.key === 'RegistCapi');

      const mockMoney = 1000000;
      const expectedFormattedMoney = '1,000,000';
      mockFormatMoney.mockReturnValue(expectedFormattedMoney);

      // Act
      const result = registCapiField.format([{ value: mockMoney }]);

      // Assert
      expect(mockFormatMoney).toHaveBeenCalledWith(mockMoney);
      expect(result).toBe(expectedFormattedMoney);
    });

    it('应该正确处理空值或undefined的情况', () => {
      // Arrange
      const separationNoticeConfig = DimensionDetailCommonFields.find((config) => config.dimensions.includes(DimensionLevel3Enums.SeparationNotice));
      const noticeTypeField = separationNoticeConfig.fields.find((field) => field.key === 'NoticeType');

      // Act - 测试空值
      const result1 = noticeTypeField.format({ NoticeType: null });

      // Act - 测试undefined
      const result2 = noticeTypeField.format({});

      // Assert
      expect(result1).toBe('-');
      expect(result2).toBe('-');
    });

    it('应该为不同维度提供不同的字段配置', () => {
      // Act
      const dimensionKeys = new Set<string>();
      DimensionDetailCommonFields.forEach((config) => {
        config.dimensions.forEach((dimension) => dimensionKeys.add(String(dimension)));
      });

      // Assert
      expect(dimensionKeys.size).toBeGreaterThan(1);
    });

    it('每个维度配置应该包含fields数组', () => {
      // Act & Assert
      DimensionDetailCommonFields.forEach((config) => {
        expect(config.fields).toBeDefined();
        expect(Array.isArray(config.fields)).toBe(true);

        // 对fields数组内容进行验证，但不强制要求长度大于0
        config.fields.forEach((field) => {
          expect(field.key).toBeDefined();
          expect(typeof field.key).toBe('string');
          expect(field.format).toBeDefined();
          expect(typeof field.format).toBe('function');
        });
      });
    });

    it('格式化函数应该能处理各种输入类型', () => {
      // Arrange
      const separationNoticeConfig = DimensionDetailCommonFields.find((config) => config.dimensions.includes(DimensionLevel3Enums.SeparationNotice));
      const noticeDurationField = separationNoticeConfig.fields.find((field) => field.key === 'NoticeDuration');

      mockDateTransform.mockImplementation((date) => date || '-');

      // 测试不同的输入组合
      const testCases = [
        { StartDate: '2024-01-01', EndDate: '2024-12-31', expected: '2024-01-01 至 2024-12-31' },
        { StartDate: '2024-01-01', EndDate: null, expected: '2024-01-01 至 -' },
        { StartDate: null, EndDate: '2024-12-31', expected: '- 至 2024-12-31' },
        { StartDate: null, EndDate: null, expected: '- 至 -' },
      ];

      // Act & Assert
      testCases.forEach((testCase) => {
        const result = noticeDurationField.format(testCase);
        expect(result).toBe(testCase.expected);
      });
    });
  });
});
