import { DimensionLevel2Enums } from '@domain/enums/diligence/DimensionLevel2Enums';
import { formatMoney } from '@commons/utils/utils';
import { DimensionLevel3Enums } from '@domain/enums/diligence/DimensionLevel3Enums';
import { dateTransform } from '@commons/utils/date.utils';
import { billAcceptanceRiskStatusMap, DurationConstants, separationNoticeTypeMap } from '@modules/batch/common/file.export.template';
import { RiskLevelConst } from '@domain/model/diligence/pojo/dimension/DimensionStrategyPO';
import { isNumber, uniq } from 'lodash';

import {
  convertPathToGraphTender,
  formatPartyNames,
  formatRoleAmt,
  generateCompanyRelationChain,
  getControllerChain,
  getRelationChain,
  generateContactRelationChain,
  proceeParty,
  processBeneficiary,
  processCaseRoleGroup,
  processHoldAndBeneficiaryUpdateInfo,
  processNegativeNews,
  processPledgor,
  processRiskChangeCaseRoleGroup,
  transformPersonName,
} from './excel-table.util';
import { ExcelGroupVersionParserSettingPO, ExcelParserSettingPO } from '@domain/model/batch/po/parse/ExcelParserSettingPO';
import { GroupDimensionVersionEnums } from '@domain/model/diligence/pojo/dimension/group/GroupDimensionVersionEnums';
import { directRelationalTypes, relationsTypeParser } from '../../pdf/pdf-table.util';
import { DATE_TIME_FORMAT } from '@domain/constants/common';
import * as moment from 'moment';
import { convertSuspectPathToGraph } from '@domain/pdf/table-columns-diligence.config';

export const formatDateRange = (rObj) => {
  const hasDate = (date) => date && date !== -1;
  const formatDate = (date) => moment.unix(date).format('YYYY-MM');

  if (!rObj) return '-';
  if (hasDate(rObj.StartDate) && hasDate(rObj.EndDate)) {
    return `${formatDate(rObj.StartDate)} 至 ${formatDate(rObj.EndDate)} `;
  } else if (hasDate(rObj.StartDate)) {
    return `${formatDate(rObj.StartDate)} 至今`;
  } else if (hasDate(rObj.EndDate)) {
    return `截止 ${formatDate(rObj.EndDate)}`;
  } else if (rObj.StartDate === -1 && rObj.EndDate === -1) {
    return '-';
  } else if (!rObj.StartDate && !rObj.EndDate) {
    return '-';
  }
  return '至今';
};

const getLabel = (obj) => {
  if (!obj) return '';
  return obj.Type === '2' ? obj.Value : obj.TypeDesc;
};

export const renderDateRange = (item, types: string[]) => {
  if (!item.companyRelations) return '-';
  const rObjs = types.map((type) => item.companyRelations.find((r) => r.Type === type)).filter(Boolean);
  const res = rObjs.map((obj) => (rObjs.length > 1 ? `${getLabel(obj)}：${formatDateRange(obj)}` : formatDateRange(obj))).join('\n');
  return res || '-';
};

// export const renderDateRange = (item, type) => {
//   const rObj = item.companyRelations?.filter((r) => r.Type === type)?.[0] || {};
//   const hasDate = (date) => date && date !== -1;
//   const formatDate = (date) => moment.unix(date).format('YYYY-MM');
//   if (!rObj) return '-';
//   if (hasDate(rObj.StartDate) && hasDate(rObj.EndDate)) {
//     return `${formatDate(rObj.StartDate)} 至 ${formatDate(rObj.EndDate)} `;
//   } else if (hasDate(rObj.StartDate)) {
//     return `${formatDate(rObj.StartDate)} 至今`;
//   } else if (hasDate(rObj.EndDate)) {
//     return `截止 ${formatDate(rObj.EndDate)}`;
//   } else if (rObj.StartDate === -1 && rObj.EndDate === -1) {
//     return '-';
//   } else if (!rObj.StartDate && !rObj.EndDate) {
//     return '-';
//   }
//   return '至今';
// };
/**
 * 准入排查维度详情导出维度字段格式化配置
 */
export const DimensionDetailCommonFields = [
  {
    dimensions: [DimensionLevel3Enums.SeparationNotice],
    fields: [
      { key: 'DecideDate', format: (val) => dateTransform(val?.DecideDate) },
      { key: 'NoticeDate', format: (val) => dateTransform(val?.NoticeDate) },
      { key: 'NoticeDuration', format: (val) => `${dateTransform(val?.StartDate) || '-'} 至 ${dateTransform(val?.EndDate) || '-'}` },
      {
        key: 'NoticeType',
        format: (val) => (val?.NoticeType ? separationNoticeTypeMap[val.NoticeType] : '-'),
      },
    ],
  },
  {
    dimensions: [DimensionLevel2Enums.ChattelSeizure],
    fields: [
      { key: 'StartDate', format: (val) => dateTransform(val?.StartDate) },
      { key: 'EndDate', format: (val) => dateTransform(val?.EndDate) },
      { key: 'PublicDate', format: (val) => dateTransform(val?.PublicDate) },
      { key: 'ExecuteCompanyNames', format: (val) => val?.Companys?.map((c) => c.Name).join(',') },
    ],
  },
  {
    dimensions: [DimensionLevel2Enums.PersistentBillOverdue],
    fields: [
      { key: 'beginDate', format: (val) => dateTransform(val?.beginDate) },
      { key: 'publishDate', format: (val) => dateTransform(val?.publishDate) },
      {
        key: 'listTag',
        format: (val) => (val?.listTag && DurationConstants[val.listTag] ? billAcceptanceRiskStatusMap[val.listTag] : '-'),
      },
    ],
  },
  {
    dimensions: [DimensionLevel2Enums.IPRPledge],
    fields: [
      { key: 'PublishDate', format: (val) => dateTransform(val?.PublishDate) },
      { key: 'PledgorName', format: (val) => val?.PledgorInfo?.map((p) => p.Name)?.join(',') },
      { key: 'PledgeeName', format: (val) => val?.PledgeeInfo?.map((p) => p.Name)?.join(',') },
      {
        key: 'PledgeDuration',
        format: (val) => `${dateTransform(val?.PledgeStartDate) || '-'} 至 ${dateTransform(val?.PledgeEndDate) || '-'}`,
      },
    ],
  },
  {
    dimensions: [DimensionLevel2Enums.NoCapital],
    fields: [
      { key: 'RegistCapi', format: (val) => formatMoney(val?.[0]?.value) },
      { key: 'RecCap', format: (val) => formatMoney(val?.[1]?.value) },
      { key: 'PaidInCapitalRatio', format: (val) => val?.[2]?.value },
      { key: 'ParentName', format: (val) => val?.[3]?.value },
      { key: 'ParentStockPercent', format: (val) => val?.[4]?.value },
      { key: 'ParentRegistCapi', format: (val) => formatMoney(val?.[5]?.value) },
      { key: 'ParentRecCap', format: (val) => formatMoney(val?.[6]?.value) },
    ],
  },
  {
    dimensions: [DimensionLevel3Enums.BusinessAbnormal4],
    fields: [
      {
        key: 'TaxNo',
        format: (val) => {
          if (val.version === 'v2') {
            return val.CaseNo || '-';
          }
          return val?.[0]?.value || '-';
        },
      },
      {
        key: 'IsValid',
        format: (val) => {
          if (val.version === 'v2') {
            return val.IsValid ? '税务非正常户' : '税务非正常户(失效)';
          }
          return '-';
        },
      },
      {
        key: 'Court',
        format: (val) => val?.ExecuteGov || val?.[1]?.value || '-',
      },
      {
        key: 'AddDate',
        format: (val) => {
          if (val.version === 'v2') {
            return dateTransform(val.JoinDate);
          }
          return val?.[2]?.value || '-';
        },
      },
    ],
  },
  {
    dimensions: [DimensionLevel2Enums.EstablishedTime],
    fields: [
      { key: 'StartDate', format: (val) => val?.value },
      { key: 'establishmentDuration', format: (val) => val?.value1 },
    ],
  },
  {
    dimensions: [
      DimensionLevel3Enums.BusinessAbnormal1,
      DimensionLevel3Enums.BusinessAbnormal6,
      DimensionLevel3Enums.BusinessAbnormal7,
      DimensionLevel3Enums.BusinessAbnormal8,
      DimensionLevel3Enums.BusinessAbnormal9,
      DimensionLevel2Enums.LowCapital,
    ],
    fields: [{ key: 'value', format: (val) => formatMoney(val?.value) }],
  },
  {
    dimensions: [
      DimensionLevel3Enums.BusinessAbnormal2,
      DimensionLevel3Enums.BusinessAbnormal5,
      DimensionLevel2Enums.NoQualityCertification,
      DimensionLevel2Enums.NoCertification,
      DimensionLevel2Enums.FraudList,
      DimensionLevel3Enums.Liquidation,
      DimensionLevel2Enums.FakeRegister,
      DimensionLevel3Enums.MainInfoUpdateRegisteredCapital,
      DimensionLevel3Enums.EmployeeReduction,
      DimensionLevel3Enums.DebtOverdue,
      DimensionLevel3Enums.LatestInsuredNumber,
      DimensionLevel3Enums.FinancialHealth,
      DimensionLevel2Enums.NonPerformingAssets,
    ],
    fields: [],
  },
  {
    dimensions: [DimensionLevel2Enums.Certification],
    fields: [
      {
        key: 'validPeriod',
        format: (val) => (val?.startDate || val?.endDate ? `${dateTransform(val?.startDate)}至${dateTransform(val?.endDate)}` : '-'),
      },
    ],
  },
  {
    dimensions: [DimensionLevel3Enums.CancellationOfFiling],
    fields: [
      { key: 'CancelReason', format: (val) => (val.Detail ? val.Detail?.LiqBAInfo?.CancelReason : '-') },
      { key: 'NoticeContent', format: (val) => (val.Detail ? val.Detail?.CreditorNoticeInfo?.NoticeContent : '-') },
      {
        key: 'ClaimsDeclarationMember',
        format: (val) => (val?.Detail ? val.Detail?.CreditorNoticeInfo?.ClaimsDeclarationMember : '-'),
      },
      {
        key: 'ClaimsDeclarationTelNo',
        format: (val) => (val?.Detail ? val.Detail?.CreditorNoticeInfo?.ClaimsDeclarationTelNo : '-'),
      },
      {
        key: 'ClaimsDeclarationAddress',
        format: (val) => (val?.Detail ? val.Detail?.CreditorNoticeInfo?.ClaimsDeclarationAddress : '-'),
      },
      { key: 'BelongOrg', format: (val) => (val?.Detail ? val.Detail?.CreditorNoticeInfo?.BelongOrg : '-') },
    ],
  },
  {
    dimensions: [DimensionLevel3Enums.StaffWorkingOutsideForeignInvestment, DimensionLevel3Enums.SuspectedInterestConflict],
    fields: [
      { key: 'personName', format: (val) => transformPersonName(val) },
      {
        key: 'relationPersonName',
        format: (val) =>
          val?.relationPersonName
            ? `${val?.personNo.split('_')[0]}-${val?.relationPersonName} - ${val?.name}（${val?.relationship}）`
            : val?.personNo + ' - ' + val?.name,
      },
      { key: 'investDate', format: (val) => renderDateRange(val, ['1']) },
      { key: 'jobDate', format: (val) => renderDateRange(val, ['2', '0']) },
    ],
  },
  {
    dimensions: [DimensionLevel2Enums.HitInnerBlackList],
    fields: [
      {
        key: 'duration',
        format: (val) => (val?.duration ? DurationConstants[val.duration] : '-'),
      },
      { key: 'joinDate', format: (val) => dateTransform(val?.joinDate) },
      { key: 'expiredDate', format: (val) => dateTransform(val?.expiredDate) },
    ],
  },
  {
    dimensions: [DimensionLevel2Enums.HitOuterBlackList],
    fields: [
      {
        key: 'level',
        format: (val) => (val?.level ? RiskLevelConst[val.level] : '-'),
      },
      { key: 'Publishdate', format: (val) => dateTransform(val?.Publishdate) },
    ],
  },
  {
    dimensions: [DimensionLevel2Enums.Bankruptcy],
    fields: [
      {
        key: 'Name',
        format: (val) => val?.SubjectInfo?.map((p) => (p.description ? p.description : p.Name)).join(',') || '-',
      },
      {
        key: 'LiAnDate',
        format: (val) => dateTransform(val?.LiAnDate),
      },
      {
        key: 'LianDate',
        format: (val) => dateTransform(val?.LianDate),
      },
      {
        key: 'EndDate',
        format: (val) => dateTransform(val?.EndDate),
      },
      {
        key: 'PublicDate',
        format: (val) => dateTransform(val?.PublicDate),
      },
      {
        key: 'PublishDate',
        format: (val) => dateTransform(val?.PublishDate),
      },
      {
        key: 'judgedate',
        format: (val) => dateTransform(val?.judgedate),
      },
      {
        key: 'courtdate',
        format: (val) => dateTransform(val?.courtdate),
      },
      {
        key: 'Amount',
        format: (val) => formatMoney(val?.Amount),
      },
      {
        key: 'amount',
        format: (val) => formatMoney(val?.amount),
      },
      {
        key: 'amountinvolved',
        format: (val) => formatMoney(val?.amountinvolved),
      },
      {
        key: 'FailureAct',
        format: (val) => formatMoney(val?.FailureAct),
      },
      {
        key: 'ExecuteObject',
        format: (val) => formatMoney(val?.ExecuteObject),
      },
      { key: 'yiwu', format: (val) => formatMoney(val?.yiwu) },
      { key: 'EvaluationPrice', format: (val) => formatMoney(val?.EvaluationPrice) },
      { key: 'CaseRoleGroup', format: (val) => processCaseRoleGroup(val) },
      {
        key: 'caserolegroupbyrolename',
        format: (val) => proceeParty(val),
      },
      {
        key: 'RoleAmt',
        format: (val) => {
          if (!val?.RoleAmt) return '';
          return val.RoleAmt.map((c) => {
            const R = c?.R || '';
            const P = c?.P || '';
            return `${R}-${P}`;
          }).join('\n');
        },
      },
      {
        key: 'CourtList',
        format: (val) => val?.CourtList?.join('\n'),
      },
      {
        key: 'NameAndKeyNo',
        format: (val) => val?.NameAndKeyNo?.map((n) => n?.Name + (n?.Job ? '【' : '') + (n?.Job || '') + (n?.Job ? '】' : ''))?.join('\n'),
      },
      {
        key: 'AnNoList',
        format: (val) => val?.AnNoList?.join('\n'),
      },
      {
        key: 'SqrInfo',
        format: (val) => val?.SqrInfo?.map((s) => s?.Name)?.join('\n'),
      },
      {
        key: 'Pledgor',
        format: (val) => val?.PledgorInfo?.map((n) => n?.Name + (n?.Job ? '【' : '') + (n?.Job || '') + (n?.Job ? '】' : ''))?.join('\n'),
      },
      {
        key: 'SubjectInfoName',
        format: (val) => val?.SubjectInfo?.map((p) => p.Name).join(','),
      },
      {
        key: 'TotalAmt',
        format: (val) => formatMoney(parseFloat(val?.TotalAmt) / 10000 || ''),
      },
    ],
  },
  {
    dimensions: [
      DimensionLevel3Enums.PersonCreditCurrent,
      DimensionLevel2Enums.TaxationOffences,
      DimensionLevel3Enums.RestrictedConsumptionCurrent,
      DimensionLevel3Enums.RestrictedConsumptionHistory,
      DimensionLevel3Enums.PersonCreditHistory,
      DimensionLevel2Enums.PersonExecution,
      DimensionLevel2Enums.ContractBreach,
      DimensionLevel2Enums.JudicialAuction,
      DimensionLevel3Enums.MainMembersRestrictedConsumptionCurrent,
      DimensionLevel3Enums.SalesContractDispute,
      DimensionLevel3Enums.MajorDispute,
      DimensionLevel3Enums.MainMembersRestrictedOutbound,
      DimensionLevel3Enums.CompanyOrMainMembersCriminalInvolve,
      DimensionLevel3Enums.CompanyOrMainMembersCriminalInvolveHistory,
      DimensionLevel3Enums.CompanyOrMainMembersCriminalOffence,
      DimensionLevel3Enums.CompanyOrMainMembersCriminalOffenceHistory,
      DimensionLevel3Enums.EndExecutionCase,
    ],
    fields: [
      {
        key: 'relatedObject',
        format: (val) => val?.SubjectInfo?.map((p) => (p.description ? p.description : p.Name)).join(',') || '-',
      },
      {
        key: 'LiAnDate',
        format: (val) => dateTransform(val?.LiAnDate),
      },
      {
        key: 'LianDate',
        format: (val) => dateTransform(val?.LianDate),
      },
      {
        key: 'EndDate',
        format: (val) => dateTransform(val?.EndDate),
      },
      {
        key: 'PublicDate',
        format: (val) => dateTransform(val?.PublicDate),
      },
      {
        key: 'PublishDate',
        format: (val) => dateTransform(val?.PublishDate),
      },
      {
        key: 'judgedate',
        format: (val) => dateTransform(val?.judgedate),
      },
      {
        key: 'courtdate',
        format: (val) => dateTransform(val?.courtdate),
      },
      {
        key: 'Amount',
        format: (val) => formatMoney(val?.Amount),
      },
      {
        key: 'amount',
        format: (val) => formatMoney(val?.amount),
      },
      {
        key: 'amountinvolved',
        format: (val) => formatMoney(val?.amountinvolved),
      },
      {
        key: 'FailureAct',
        format: (val) => formatMoney(val?.FailureAct),
      },
      {
        key: 'ExecuteObject',
        format: (val) => formatMoney(val?.ExecuteObject),
      },
      { key: 'yiwu', format: (val) => formatMoney(val?.yiwu) },
      { key: 'EvaluationPrice', format: (val) => formatMoney(val?.EvaluationPrice) },
      { key: 'CaseRoleGroup', format: (val) => processCaseRoleGroup(val) },
      {
        key: 'caserolegroupbyrolename',
        format: (val) => formatPartyNames(val),
      },
      {
        key: 'RoleAmt',
        format: (val) => formatRoleAmt(val),
      },
      {
        key: 'CourtList',
        format: (val) => val?.CourtList?.join('\n'),
      },
      {
        key: 'NameAndKeyNo',
        format: (val) => val?.NameAndKeyNo?.map((n) => n?.Name + (n?.Job ? '【' : '') + (n?.Job || '') + (n?.Job ? '】' : ''))?.join('\n'),
      },
      {
        key: 'AnNoList',
        format: (val) => val?.AnNoList?.join('\n'),
      },
      {
        key: 'SqrInfo',
        format: (val) => val?.SqrInfo?.map((s) => s?.Name)?.join('\n'),
      },
      {
        key: 'Pledgor',
        format: (val) =>
          val?.PledgorInfo?.map((n) => (n.description ? n.description : n?.Name + (n?.Job ? '【' : '') + (n?.Job || '') + (n?.Job ? '】' : '')))?.join('\n'),
      },
      {
        key: 'SubjectInfoName',
        format: (val) => val?.SubjectInfo?.map((p) => (p.description ? p.description : p.Name)).join(','),
      },
      {
        key: 'TotalAmt',
        format: (val) => formatMoney(parseFloat(val?.TotalAmt) / 10000 || ''),
      },
    ],
  },
  {
    dimensions: [DimensionLevel3Enums.LaborContractDispute, DimensionLevel3Enums.UnfairCompetition, DimensionLevel3Enums.CompanyOrMainMembersLitigationInfo],
    fields: [
      { key: 'Amt', format: (val) => formatMoney(val?.Amt) },
      {
        key: 'CaseName',
        format: (val) => val?.CaseNameClean || '-',
      },
      { key: 'AnNoList', format: (val) => (val?.AnNoList?.length ? val.AnNoList.join('\n') : '-') },
      {
        key: 'CourtList',
        format: (val) => (val?.CourtList?.length ? val.CourtList.join('\n') : '-'),
      },
      {
        key: 'RoleAmt',
        format: (val) => formatRoleAmt(val),
      },
    ],
  },
  {
    dimensions: [DimensionLevel2Enums.FreezeEquity],
    fields: [
      {
        key: 'Name',
        format: (val) => val?.SubjectInfo?.map((p) => (p.description ? p.description : p.Name)).join(',') || '-',
      },
      {
        key: 'Pledgor',
        format: (val) =>
          val?.PledgorInfo?.map((n) => (n.description ? n.description : n?.Name + (n?.Job ? '【' : '') + (n?.Job || '') + (n?.Job ? '】' : '')))?.join('\n'),
      },
      {
        key: 'TypeDescExeCuteStatus',
        format: (val) => {
          if (val == null) return '-'; // 增加错误处理机制
          const typeDesc = val.TypeDesc || '';
          const executeStatus = isNumber(val?.ExecuteStatus) ? val.statusdesc || '-' : val.ExecuteStatus;
          return `${typeDesc} | ${executeStatus}`;
        },
      },
      {
        key: 'LianDateRemark',
        format: (val) => (val?.LianDate || val?.ActionRemark ? `${dateTransform(val?.LianDate)}至${dateTransform(val?.ActionRemark)}` : '-'),
      },
      {
        key: 'PublishDate',
        format: (val) => dateTransform(val?.PublishDate),
      },
    ],
  },
  {
    dimensions: [DimensionLevel3Enums.MainMembersPersonCreditCurrent],
    fields: [
      { key: 'LianDate', format: (val) => dateTransform(val?.LianDate) },
      {
        key: 'PublishDate',
        format: (val) => dateTransform(val?.PublishDate),
      },
      { key: 'Amount', format: (val) => formatMoney(val?.Amount) },
      {
        key: 'ApplicantNames',
        format: (val) => val?.SqrInfo?.map((p) => p.Name).join(','),
      },
      {
        key: 'SubjectNames',
        format: (val) => val?.SubjectInfo?.map((p) => (p.description ? p.description : p.Name)).join(','),
      },
    ],
  },
  {
    dimensions: [DimensionLevel3Enums.ProductQualityProblem6],
    fields: [
      {
        key: 'PublishDate',
        format: (val) => dateTransform(val?.PublishDate),
      },
      {
        key: 'LianDate',
        format: (val) => dateTransform(val?.LianDate, 'YYYY年MM月'),
      },
    ],
  },
  {
    dimensions: [
      DimensionLevel2Enums.CompanyCredit,
      DimensionLevel2Enums.CompanyCreditHistory,
      DimensionLevel3Enums.BusinessAbnormal3,
      DimensionLevel2Enums.OperationAbnormal,
      DimensionLevel2Enums.AdministrativePenalties2,
      DimensionLevel2Enums.AdministrativePenalties3,
    ],
    fields: [
      {
        key: 'relatedObject',
        format: (val) => val?.SubjectInfo?.map((p) => (p.description ? p.description : p.Name)).join(',') || '-',
      },
      {
        key: 'PublishDate',
        format: (val) => dateTransform(val?.PublishDate),
      },
      {
        key: 'PunishDate',
        format: (val) => dateTransform(val?.PunishDate),
      },
      {
        key: 'publicdate',
        format: (val) => dateTransform(val?.publicdate),
      },
      {
        key: 'AddDate',
        format: (val) => dateTransform(val?.AddDate),
      },
      {
        key: 'RemoveDate',
        format: (val) => dateTransform(val?.RemoveDate),
      },
      {
        key: 'CurrenceDate',
        format: (val) => dateTransform(val?.CurrenceDate),
      },
      {
        key: 'Amount',
        format: (val) => formatMoney(val?.Amount),
      },
      {
        key: 'NewAmount',
        format: (val) => formatMoney(val?.NewAmount),
      },
      {
        key: 'LianDate',
        format: (val) => dateTransform(val?.LianDate),
      },
      {
        key: 'SamplingCompanyNames',
        format: (val) => val?.SamplingCompanys?.map((company) => company.Name)?.join(','),
      },
      {
        key: 'ProdCompanyNames',
        format: (val) => val?.ProdCompanys?.map((company) => company.Name)?.join(','),
      },
    ],
  },
  {
    dimensions: [
      DimensionLevel2Enums.TaxArrearsNotice,
      DimensionLevel3Enums.ProductQualityProblem1,
      DimensionLevel3Enums.ProductQualityProblem2,
      DimensionLevel3Enums.ProductQualityProblem7,
      DimensionLevel3Enums.ProductQualityProblem9,
      DimensionLevel2Enums.AdministrativePenalties,
      DimensionLevel2Enums.TaxPenalties,
      DimensionLevel2Enums.EnvironmentalPenalties,
      DimensionLevel2Enums.SpotCheck,
      DimensionLevel2Enums.TaxCallNotice,
    ],
    fields: [
      {
        key: 'PublishDate',
        format: (val) => dateTransform(val?.PublishDate),
      },
      {
        key: 'PunishDate',
        format: (val) => dateTransform(val?.PunishDate),
      },
      {
        key: 'publicdate',
        format: (val) => dateTransform(val?.publicdate),
      },
      {
        key: 'AddDate',
        format: (val) => dateTransform(val?.AddDate),
      },
      {
        key: 'RemoveDate',
        format: (val) => dateTransform(val?.RemoveDate),
      },
      {
        key: 'CurrenceDate',
        format: (val) => dateTransform(val?.CurrenceDate),
      },
      {
        key: 'Amount',
        format: (val) => formatMoney(val?.Amount),
      },
      {
        key: 'NewAmount',
        format: (val) => formatMoney(val?.NewAmount),
      },
      {
        key: 'LianDate',
        format: (val) => dateTransform(val?.LianDate),
      },
      {
        key: 'SamplingCompanyNames',
        format: (val) => val?.SamplingCompanys?.map((company) => company.Name)?.join(','),
      },
      {
        key: 'ProdCompanyNames',
        format: (val) => val?.ProdCompanys?.map((company) => company.Name)?.join(','),
      },
    ],
  },
  {
    dimensions: [DimensionLevel2Enums.TaxReminder, DimensionLevel2Enums.TaxCallNoticeV2],
    fields: [
      {
        key: 'TaxCategory',
        format: (val) => val?.TaxCategory || '-',
      },
      {
        key: 'PeriodStartDate',
        format: (val) => dateTransform(val?.PeriodStartDate),
      },
      {
        key: 'PeriodEndDate',
        format: (val) => dateTransform(val?.PeriodEndDate),
      },
      {
        key: 'PaymentDate',
        format: (val) => dateTransform(val?.PaymentDate),
      },
      {
        key: 'courtname',
        format: (val) => val?.TaxKeyNoArray?.[0].Name || '-',
      },
      {
        key: 'PublishDate',
        format: (val) => dateTransform(val?.PublishDate),
      },
      {
        key: 'AmountOwed',
        format: (val) => formatMoney(val?.AmountOwed),
      },
    ],
  },
  {
    dimensions: [DimensionLevel3Enums.NoticeInTimePeriod],
    fields: [
      { key: 'courtDate', format: (val) => dateTransform(val?.courtDate) },
      { key: 'caseRoleGroup', format: (val) => processRiskChangeCaseRoleGroup(val) },
    ],
  },
  {
    dimensions: [DimensionLevel2Enums.SecurityNotice],
    fields: [{ key: 'publishDate', format: (val) => dateTransform(val?.publishDate) }],
  },
  {
    dimensions: [DimensionLevel2Enums.RegulateFinance],
    fields: [
      { key: 'PunishDate', format: (val) => dateTransform(val?.PunishDate) },
      {
        key: 'relatedObject',
        format: (val) => val?.SubjectInfo?.map((p) => (p.description ? p.description : p.Name)).join(',') || '-',
      },
    ],
  },
  {
    dimensions: [DimensionLevel2Enums.ChattelMortgage],
    fields: [
      {
        key: 'PledgeName',
        format: (val) => (val?.MPledgeDetail?.PledgeeList?.length ? uniq(val.MPledgeDetail.PledgeeList.map((m) => m.Name)).join(',') : '-'),
      },
      {
        key: 'GuaranteeName',
        format: (val) => (val?.MPledgeDetail?.GuaranteeList?.length ? uniq(val.MPledgeDetail.GuaranteeList.map((m) => m.Ownership)).join(',') : '-'),
      },
      {
        key: 'FulfillObligation',
        format: (val) => val?.MPledgeDetail?.GuaranteedCredRight?.FulfillObligation || '-',
      },
      {
        key: 'Amount',
        format: (val) => formatMoney(val?.MPledgeDetail?.GuaranteedCredRight?.Amount),
      },
      {
        key: 'RegisterDate',
        format: (val) => dateTransform(val?.RegisterDate),
      },
    ],
  },
  {
    dimensions: [DimensionLevel2Enums.PledgeMerger],
    fields: [
      {
        key: 'Debtors',
        format: (val) => (val?.Debtors?.length ? uniq(val.Debtors.map((m) => m.Name)).join(',') : '-'),
      },
      {
        key: 'Pledges',
        format: (val) => (val?.Pledges?.length ? uniq(val.Pledges.map((m) => m.Name)).join(',') : '-'),
      },
      {
        key: 'DebtTerm',
        format: (val) => `${dateTransform(val?.DebtTermStart) || '-'} 至 ${dateTransform(val?.DebtTermEnd) || '-'}`,
      },
      {
        key: 'Amount',
        format: (val) => `${formatMoney(val?.PledgedAmount)}${val?.PledgedAmountUnit}`,
      },
      {
        key: 'RegisterDate',
        format: (val) => dateTransform(val?.RegisterDate, DATE_TIME_FORMAT),
      },
    ],
  },
  {
    dimensions: [DimensionLevel2Enums.StockPledge],
    fields: [
      {
        key: 'Holders',
        format: (val) => (val?.Holders?.length ? uniq(val.Holders.map((m) => m.Name)).join(',') : '-'),
      },
      {
        key: 'Companys',
        format: (val) => (val?.Companys?.length ? uniq(val.Companys.map((m) => m.Name)).join(',') : '-'),
      },
      {
        key: 'Pledgees',
        format: (val) => (val?.Pledgees?.length ? uniq(val.Pledgees.map((m) => m.Name)).join(',') : '-'),
      },
      {
        key: 'NoticeDate',
        format: (val) => dateTransform(val?.NoticeDate),
      },
    ],
  },
  {
    dimensions: [DimensionLevel2Enums.EquityPledge],
    fields: [
      {
        key: 'PledgorName',
        format: (val) => val?.PledgorInfo?.map((p) => p.Name).join(','),
      },
      { key: 'RelatedCompanyName', format: (val) => val?.RelatedCompanyInfo.Name },
      { key: 'PledgeeName', format: (val) => val?.PledgeeInfo?.map((p) => p.Name).join(',') },
      { key: 'PledgedAmount', format: (val) => formatMoney(val?.PledgedAmount) },
      { key: 'RegDate', format: (val) => dateTransform(val?.RegDate) },
    ],
  },
  {
    dimensions: [DimensionLevel2Enums.LandMortgage],
    fields: [
      {
        key: 'MortgagorNames',
        format: (val) => val.MortgagorNames?.map((p) => p.Name).join(','),
      },
      { key: 'MortgagePeoples', format: (val) => val?.MortgagePeoples?.map((p) => p.Name).join(',') },
      { key: 'MortgagePrice', format: (val) => formatMoney(val?.MortgagePrice) },
      { key: 'StartEndDate', format: (val) => `${dateTransform(val?.StartDate)}至${dateTransform(val?.EndDate)}` },
    ],
  },
  {
    dimensions: [DimensionLevel2Enums.BondDefaults],
    fields: [
      {
        key: 'FirstDefaultDate',
        format: (val) => dateTransform(val?.FirstDefaultDate),
      },
      {
        key: 'MaturityDate',
        format: (val) => dateTransform(val?.MaturityDate),
      },
      {
        key: 'AccuOverdueCapital',
        format: (val) => formatMoney(val?.AccuOverdueCapital),
      },
      {
        key: 'AccuOverdueInterest',
        format: (val) => formatMoney(val?.AccuOverdueInterest),
      },
    ],
  },
  {
    dimensions: [DimensionLevel2Enums.GuaranteeRisk],
    fields: [
      {
        key: 'VoucheeName',
        format: (val) => val?.Vouchee?.map((m) => m.ShowName).join(','),
      },
      {
        key: 'GuaranteeName',
        format: (val) => val?.Guarantee?.map((m) => m.ShowName).join(','),
      },
      {
        key: 'CreditorName',
        format: (val) => val?.Creditor?.map((m) => m.ShowName).join(','),
      },
      {
        key: 'GuaranteeMoney',
        format: (val) => formatMoney(Number(val?.GuaranteeMoney) / 10000),
      },
      {
        key: 'Judgedate',
        format: (val) => dateTransform(val?.Judgedate),
      },
      {
        key: 'PublicDate',
        format: (val) => dateTransform(val?.PublicDate),
      },
    ],
  },
  {
    dimensions: [DimensionLevel2Enums.GuaranteeInfo],
    fields: [
      {
        key: 'GuaranteeName',
        format: (val) => val?.Guarantee?.map((m) => m.Name).join(','),
      },
      {
        key: 'VoucheeName',
        format: (val) => val?.Vouchee?.map((m) => m.Name).join(','),
      },
      {
        key: 'GuaranteeMoney',
        format: (val) => formatMoney(Number(val?.GuaranteeMoney)),
      },
      {
        key: 'PublicDate',
        format: (val) => dateTransform(val?.PublicDate),
      },
    ],
  },
  {
    dimensions: [DimensionLevel3Enums.MainInfoUpdateScope],
    fields: [
      {
        key: 'afterScope',
        format: (val) => val?.after?.Scope,
      },
    ],
  },
  {
    dimensions: [DimensionLevel3Enums.MainInfoUpdateAddress],
    fields: [
      {
        key: 'afterAddress',
        format: (val) => val?.after?.Address,
      },
    ],
  },
  {
    dimensions: [DimensionLevel3Enums.MainInfoUpdateName],
    fields: [
      {
        key: 'afterCompanyName',
        format: (val) => val?.after?.CompanyName,
      },
    ],
  },
  {
    dimensions: [DimensionLevel3Enums.MainInfoUpdateLegalPerson],
    fields: [
      {
        key: 'afterOperName',
        format: (val) => val?.after?.OperName,
      },
    ],
  },
  {
    dimensions: [DimensionLevel3Enums.MainInfoUpdateHolder, DimensionLevel3Enums.MainInfoUpdatePerson],
    fields: [
      {
        key: 'BeforeName',
        format: (val) => JSON.parse(val?.BeforeContent)?.Name,
      },
      {
        key: 'AfterName',
        format: (val) => JSON.parse(val?.AfterContent)?.Name,
      },
      {
        key: 'ChangeDate',
        format: (val) => dateTransform(val?.ChangeDate),
      },
    ],
  },
  {
    dimensions: [DimensionLevel3Enums.MainInfoUpdateBeneficiary],
    fields: [
      {
        key: 'BeforeName',
        format: (val) => processBeneficiary(val, 'before'),
      },
      {
        key: 'AfterName',
        format: (val) => processBeneficiary(val, 'after'),
      },
      {
        key: 'ChangeDate',
        format: (val) => dateTransform(val?.ChangeDate),
      },
    ],
  },
  {
    dimensions: [DimensionLevel3Enums.MainHoldAndBeneficiaryUpdate],
    fields: [
      {
        key: 'ChangeContent',
        format: (val) => processHoldAndBeneficiaryUpdateInfo(val, 'changeContent'),
      },
      {
        key: 'ChangeDate',
        format: (val) => dateTransform(val?.ChangeDate),
      },
    ],
  },
  {
    dimensions: [DimensionLevel3Enums.MainInfoUpdateMainPersonnel],
    fields: [
      {
        key: 'changeName',
        format: (val) => (val?.changeObject || []).map((item) => item.name).join(',') || '-',
      },
      {
        key: 'exitName',
        format: (val) => (val?.exitObject || []).map((item) => item.name).join(',') || '-',
      },
      {
        key: 'addName',
        format: (val) => (val?.addObject || []).map((item) => item.name).join(',') || '-',
      },
      {
        key: 'changeDate',
        format: (val) => dateTransform(val?.changeDate) || '-',
      },
    ],
  },
  {
    dimensions: [DimensionLevel2Enums.NoTender, DimensionLevel2Enums.BillDefaults],
    fields: [
      {
        key: 'EndDate',
        format: (val) => dateTransform(val?.EndDate),
      },
      {
        key: 'PublishDate',
        format: (val) => dateTransform(val?.PublishDate),
      },
      {
        key: 'OverdueBalance',
        format: (val) => formatMoney(val?.OverdueBalance),
      },
      {
        key: 'OverdueStatus',
        format: (val) => val?.OverdueStatus?.Desc,
      },
    ],
  },
  {
    dimensions: [DimensionLevel2Enums.NegativeNewsRecent, DimensionLevel2Enums.NegativeNewsHistory],
    fields: [
      {
        key: 'tagsnew',
        format: (val) => processNegativeNews(val),
      },
      {
        key: 'publishtime',
        format: (val) => dateTransform(val?.publishtime),
      },
    ],
  },
  {
    dimensions: [DimensionLevel3Enums.CapitalReduction],
    fields: [{ key: 'NoticeDate', format: (val) => dateTransform(val?.NoticeDate) }],
  },
];

export const TenderDimensionDetails: ExcelParserSettingPO[] = [
  {
    explainRows: 3,
    limit: 5000,
    sheetName: DimensionLevel2Enums.DirectConnection, //被列入内部黑名单
    templateTitle: ['项目名称', '项目编号', '黑名单企业名称', '列入原因', '列入时间', '黑名单有效期', '黑名单截止日期'],
    exportExcelColumns: {
      projectName: { header: '项目名称', width: 30 },
      projectNo: { header: '项目编号', width: 30 },
      companyNameDD: { header: '黑名单企业名称', width: 30 },
      reason: { header: '列入原因', width: 30 },
      joinDate: { header: '列入时间', width: 30 },
      duration: { header: '黑名单有效期', width: 30 },
      expiredDate: { header: '黑名单截止日期', width: 30 },
    },
  },
  {
    explainRows: 3,
    limit: 5000,
    sheetName: DimensionLevel2Enums.BlackListInvestigations, //内部黑名单关联关系
    templateTitle: ['项目名称', '项目编号', '企业名称', '关联黑名单企业名称', '关联路径详情'],
    exportExcelColumns: {
      projectName: { header: '项目名称', width: 30 },
      projectNo: { header: '项目编号', width: 30 },
      startCompanyName: { header: '企业名称', width: 40 },
      endCompanyName: { header: '关联黑名单企业名称', width: 40 },
      relationChain: { header: '关联路径详情', width: 60 },
    },
  },
  {
    explainRows: 3,
    limit: 5000,
    sheetName: DimensionLevel2Enums.BlacklistSuspectedRelation, //内部黑名单疑似关系
    templateTitle: ['项目名称', '项目编号', '企业名称', '关联黑名单企业名称', '关联路径详情'],
    exportExcelColumns: {
      projectName: { header: '项目名称', width: 30 },
      projectNo: { header: '项目编号', width: 30 },
      startCompanyName: { header: '企业名称', width: 40 },
      endCompanyName: { header: '关联黑名单企业名称', width: 40 },
      relationChain: { header: '关联路径详情', width: 60 },
    },
  },
  {
    explainRows: 3,
    limit: 5000,
    sheetName: '潜在利益冲突-投资任职', //DimensionLevel3Enums.StaffWorkingOutsideForeignInvestment
    templateTitle: ['项目名称', '项目编号', '被排查企业', '姓名', '人员类型/职务', '持股起止时间', '任职起止时间', '“潜在利冲”人员', '“潜在利冲”人员分组'],
    exportExcelColumns: {
      projectName: { header: '项目名称', width: 30 },
      projectNo: { header: '项目编号', width: 30 },
      sourceCompanyName: { header: '被排查企业', width: 30 },
      personName: { header: '姓名', width: 30 },
      job: { header: '人员类型/职务', width: 30 },
      investDate: { header: '持股起止时间', width: 30 },
      jobDate: { header: '任职起止时间', width: 30 },
      relationPersonName: { header: '“潜在利冲”人员', width: 30 },
      group: { header: '“潜在利冲”人员分组', width: 60 },
    },
  },
  {
    explainRows: 3,
    limit: 5000,
    sheetName: DimensionLevel3Enums.SuspectedInterestConflict,
    templateTitle: [
      '项目名称',
      '项目编号',
      '疑似关系',
      '被排查企业',
      '人员类型/职务',
      '持股起止时间',
      '任职起止时间',
      '“疑似潜在利冲”人员',
      '“疑似潜在利冲”人员分组',
    ],
    exportExcelColumns: {
      projectName: { header: '项目名称', width: 30 },
      projectNo: { header: '项目编号', width: 30 },
      personName: { header: '疑似关系', width: 30 },
      sourceCompanyName: { header: '被排查企业', width: 30 },
      job: { header: '人员类型/职务', width: 30 },
      investDate: { header: '持股起止时间', width: 30 },
      jobDate: { header: '任职起止时间', width: 30 },
      relationPersonName: { header: '“疑似潜在利冲”人员', width: 30 },
      group: { header: '“疑似潜在利冲”人员分组', width: 60 },
    },
  },
  {
    explainRows: 3,
    limit: 5000,
    sheetName: DimensionLevel2Enums.BiddingCompanyRelationship,
    templateTitle: ['项目名称', '项目编号', '企业名称', '关联类型', '关联层级', '关联企业', '关联链'],
    exportExcelColumns: {
      projectName: { header: '项目名称', width: 30 },
      projectNo: { header: '项目编号', width: 30 },
      startCompanyName: { header: '企业名称', width: 30 },
      relationName: { header: '关联类型', width: 30 },
      steps: { header: '关联层级', width: 30 },
      endCompanyName: { header: '关联企业', width: 30 },
      relationChain: { header: '关联链', width: 60 },
    },
  },
  {
    explainRows: 3,
    limit: 5000,
    sheetName: DimensionLevel2Enums.ContactRelationship,
    templateTitle: ['项目名称', '项目编号', '企业名称', '关联企业', '关联类型', '关联详情'],
    exportExcelColumns: {
      projectName: { header: '项目名称', width: 30 },
      projectNo: { header: '项目编号', width: 30 },
      startCompanyName: { header: '企业名称', width: 30 },
      endCompanyName: { header: '关联企业', width: 30 },
      relationName: { header: '关联类型', width: 30 },
      relationChain: { header: '关联详情', width: 60 },
    },
  },
  {
    explainRows: 3,
    limit: 5000,
    sheetName: DimensionLevel2Enums.BiddingCompanyRelationship2,
    templateTitle: ['项目名称', '项目编号', '企业名称', '关联企业', '关联类型', '关联详情'],
    exportExcelColumns: {
      projectName: { header: '项目名称', width: 30 },
      projectNo: { header: '项目编号', width: 30 },
      startCompanyName: { header: '企业名称', width: 30 },
      endCompanyName: { header: '关联企业', width: 30 },
      relationName: { header: '关联类型', width: 30 },
      relationChain: { header: '关联详情', width: 60 },
    },
  },
  {
    explainRows: 3,
    limit: 5000,
    sheetName: DimensionLevel2Enums.JointBiddingAnalysis,
    templateTitle: ['项目名称', '项目编号', '企业名称', '关联企业名称', '共同投标项目数', '企业中标项目数', '关联企业中标数', '企业中标率', '关联企业中标率'],
    exportExcelColumns: {
      projectName: { header: '项目名称', width: 30 },
      projectNo: { header: '项目编号', width: 30 },
      companyName: { header: '企业名称', width: 30 },
      competitionCompanyName: { header: '关联企业名称', width: 30 },
      jointTenderCount: { header: '共同投标项目数', width: 30 },
      tenderCount: { header: '企业中标项目数', width: 30 },
      competitionTenderCount: { header: '关联企业中标数', width: 30 },
      tenderPercent: { header: '企业中标率', width: 30 },
      competitionTenderPercent: { header: '关联企业中标率', width: 30 },
    },
  },
  {
    explainRows: 3,
    limit: 5000,
    sheetName: DimensionLevel2Enums.HitInnerBlackList,
    templateTitle: ['黑名单名称', '列入原因', '列入时间', '黑名单有效期', '黑名单截止日期'],
    exportExcelColumns: {
      companyNameDD: { header: '黑名单名称', width: 40 },
      reason: { header: '列入原因', width: 60 },
      joinDate: { header: '列入时间', width: 30 },
      duration: { header: '黑名单有效期', width: 30 },
      expiredDate: { header: '黑名单截止日期', width: 30 },
    },
  },
  {
    explainRows: 3,
    limit: 5000,
    sheetName: DimensionLevel2Enums.Shareholder,
    templateTitle: ['被排查企业', '关联黑名单企业名称', '持股比例', '列入原因', '列入时间', '黑名单有效期'],
    exportExcelColumns: {
      companyNameDD: { header: '被排查企业', width: 40 },
      companyNameRelated: { header: '关联黑名单企业名称', width: 40 },
      stockpercent: { header: '持股比例', width: 15 },
      reason: { header: '列入原因', width: 30 },
      joinDate: { header: '列入时间', width: 30 },
      duration: { header: '黑名单有效期', width: 30 },
    },
  },
  {
    explainRows: 3,
    limit: 5000,
    sheetName: DimensionLevel2Enums.ForeignInvestment,
    templateTitle: ['被排查企业', '关联黑名单企业名称', '持股比例', '列入原因', '列入时间', '黑名单有效期'],
    exportExcelColumns: {
      companyNameDD: { header: '被排查企业', width: 40 },
      companyNameRelated: { header: '关联黑名单企业名称', width: 40 },
      stockpercent: { header: '持股比例', width: 15 },
      reason: { header: '列入原因', width: 30 },
      joinDate: { header: '列入时间', width: 30 },
      duration: { header: '黑名单有效期', width: 30 },
    },
  },
  {
    explainRows: 3,
    limit: 5000,
    sheetName: DimensionLevel2Enums.EmploymentRelationship,
    templateTitle: ['被排查企业', '关联人员', '关联黑名单企业名称', '关联链'],
    exportExcelColumns: {
      companyNameDD: { header: '被排查企业', width: 40 },
      personName: { header: '关联人员', width: 40 },
      companyNameRelated: { header: '关联黑名单企业名称', width: 40 },
      relationChain: { header: '关联链', width: 60 },
    },
  },
  {
    explainRows: 3,
    limit: 5000,
    sheetName: '与内部黑名单列表存在相同实际控制人关联', //DimensionLevel2Enums.BlacklistSameSuspectedActualController
    templateTitle: ['被排查企业', '关联黑名单企业名称', '控制链'],
    exportExcelColumns: {
      companyNameDD: { header: '被排查企业', width: 40 },
      companyNameRelated: { header: '关联黑名单企业名称', width: 40 },
      relationChain: { header: '控制链', width: 30 },
    },
  },
  /**
   * DimensionLevel2Enums.PurchaseIllegal 涉采购不良行为
   */
  {
    explainRows: 3,
    limit: 5000,
    sheetName: DimensionLevel2Enums.CompanyBranch,
    templateTitle: ['被排查企业', '关联黑名单企业名称', '关联链'],
    exportExcelColumns: {
      companyNameDD: { header: '被排查企业', width: 40 },
      companyNameRelated: { header: '关联黑名单企业名称', width: 40 },
      relationChain: { header: '关联链', width: 60 },
    },
  },
  {
    explainRows: 3,
    limit: 5000,
    sheetName: DimensionLevel2Enums.CompanyCredit, //被列入严重违法失信企业名录
    templateTitle: ['项目名称', '项目编号', '列入对象', '列入日期', '作出决定机关(列入)', '列入严重违法失信企业名单原因'],
    exportExcelColumns: {
      projectName: { header: '项目名称', width: 30 },
      projectNo: { header: '项目编号', width: 30 },
      companyName: { header: '列入对象', width: 60 },
      AddDate: { header: '列入日期', width: 30 },
      AddOffice: { header: '作出决定机关(列入)', width: 30 },
      AddReason: { header: '列入严重违法失信企业名单原因', width: 60 },
    },
  },
  {
    explainRows: 3,
    limit: 5000,
    sheetName: DimensionLevel3Enums.BusinessAbnormal3, //被列入经营异常名录
    templateTitle: ['项目名称', '项目编号', '列入对象', '列入日期', '作出决定机关（列入）', '列入经营异常名录原因'],
    exportExcelColumns: {
      projectName: { header: '项目名称', width: 30 },
      projectNo: { header: '项目编号', width: 30 },
      companyName: { header: '列入对象', width: 60 },
      CurrenceDate: { header: '列入时间', width: 30 },
      Court: { header: '作出决定机关（列入）', width: 30 },
      CaseReason: { header: '列入经营异常名录原因', width: 60 },
    },
  },
  {
    explainRows: 3,
    limit: 5000,
    sheetName: DimensionLevel3Enums.PersonCreditCurrent,
    templateTitle: ['项目名称', '项目编号', '企业名称', '案号', '疑似申请执行人', '执行法院', '涉案金额（元）', '履行情况', '失信行为', '立案日期', '发布日期'],
    exportExcelColumns: {
      projectName: { header: '项目名称', width: 30 },
      projectNo: { header: '项目编号', width: 30 },
      companyName: { header: '企业名称', width: 60 },
      CaseNo: { header: '案号', width: 60 },
      SqrInfo: { header: '疑似申请执行人', width: 30 },
      ExecuteGov: { header: '执行法院', width: 60 },
      Amount: { header: '涉案金额（元）', width: 30 },
      ExecuteStatus: { header: '履行情况', width: 60 },
      ActionRemark: { header: '失信行为', width: 60 },
      LiAnDate: { header: '立案日期', width: 30 },
      PublicDate: { header: '发布日期', width: 30 },
    },
  },
  {
    explainRows: 3,
    limit: 5000,
    sheetName: DimensionLevel2Enums.TaxationOffences,
    templateTitle: ['项目名称', '项目编号', '企业名称', '发布日期', '所属税务机关', '案件性质', '主要违法事实', '罚款金额(元)', '法律依据及处理处罚情况'],
    exportExcelColumns: {
      projectName: { header: '项目名称', width: 30 },
      projectNo: { header: '项目编号', width: 30 },
      companyName: { header: '企业名称', width: 60 },
      PublishDate: { header: '发布日期', width: 30 },
      Court: { header: '所属税务机关', width: 60 },
      RemoveReason: { header: '案件性质', width: 60 },
      CaseReason: { header: '主要违法事实', width: 60 },
      amount: { header: '罚款金额(元)', width: 60 },
      Title: { header: '法律依据及处理处罚情况', width: 60 },
    },
  },
  {
    explainRows: 3,
    limit: 5000,
    sheetName: DimensionLevel3Enums.MigrantWorkers,
    templateTitle: ['项目名称', '项目编号', '企业名称', '黑名单类型', '黑名单领域', '列入原因', '列入机关', '列入日期'],
    exportExcelColumns: {
      projectName: { header: '项目名称', width: 30 },
      projectNo: { header: '项目编号', width: 30 },
      Name: { header: '企业名称', width: 40 },
      listtype: { header: '黑名单类型', width: 40 },
      field: { header: '黑名单领域', width: 60 },
      CaseReason: { header: '列入原因', width: 60 },
      Court: { header: '列入机关', width: 60 },
      decisiondatestr: { header: '列入日期', width: 60 },
    },
  },
  {
    explainRows: 3,
    limit: 5000,
    sheetName: DimensionLevel3Enums.GovernmentPurchaseIllegal,
    templateTitle: ['项目名称', '项目编号', '企业名称', '黑名单类型', '黑名单领域', '列入原因', '列入机关', '列入日期'],
    exportExcelColumns: {
      projectName: { header: '项目名称', width: 30 },
      projectNo: { header: '项目编号', width: 30 },
      Name: { header: '企业名称', width: 40 },
      listtype: { header: '黑名单类型', width: 40 },
      field: { header: '黑名单领域', width: 60 },
      CaseReason: { header: '列入原因', width: 60 },
      Court: { header: '列入机关', width: 60 },
      decisiondatestr: { header: '列入日期', width: 60 },
    },
  },
  {
    explainRows: 3,
    limit: 5000,
    sheetName: DimensionLevel3Enums.ArmyProcurementIllegal,
    templateTitle: ['项目名称', '项目编号', '企业名称', '黑名单类型', '黑名单领域', '列入原因', '列入机关', '列入日期'],
    exportExcelColumns: {
      projectName: { header: '项目名称', width: 30 },
      projectNo: { header: '项目编号', width: 30 },
      Name: { header: '企业名称', width: 40 },
      listtype: { header: '黑名单类型', width: 40 },
      field: { header: '黑名单领域', width: 60 },
      CaseReason: { header: '列入原因', width: 60 },
      Court: { header: '列入机关', width: 60 },
      decisiondatestr: { header: '列入日期', width: 60 },
    },
  },
  {
    explainRows: 3,
    limit: 5000,
    sheetName: DimensionLevel3Enums.ArmyProcurementSuspended,
    templateTitle: ['项目名称', '项目编号', '企业名称', '黑名单类型', '黑名单领域', '列入原因', '列入机关', '列入日期'],
    exportExcelColumns: {
      projectName: { header: '项目名称', width: 30 },
      projectNo: { header: '项目编号', width: 30 },
      Name: { header: '企业名称', width: 40 },
      listtype: { header: '黑名单类型', width: 40 },
      field: { header: '黑名单领域', width: 60 },
      CaseReason: { header: '列入原因', width: 60 },
      Court: { header: '列入机关', width: 60 },
      decisiondatestr: { header: '列入日期', width: 60 },
    },
  },
  {
    explainRows: 3,
    limit: 5000,
    sheetName: DimensionLevel2Enums.FakeSOES,
    templateTitle: ['项目名称', '项目编号', '企业名称', '被列入假冒国企企业名称', '关联路径'],
    exportExcelColumns: {
      projectName: { header: '项目名称', width: 30 },
      projectNo: { header: '项目编号', width: 30 },
      companyName: { header: '企业名称', width: 60 },
      riskCompanyName: { header: '被列入假冒国企企业名称', width: 60 },
      pathDetail: { header: '关联路径', width: 120 },
    },
  },
  {
    explainRows: 3,
    limit: 5000,
    sheetName: DimensionLevel3Enums.GovProcurementIllegal,
    templateTitle: ['项目名称', '项目编号', '企业名称', '黑名单类型', '黑名单领域', '列入原因', '列入机关', '列入日期'],
    exportExcelColumns: {
      projectName: { header: '项目名称', width: 30 },
      projectNo: { header: '项目编号', width: 30 },
      Name: { header: '企业名称', width: 40 },
      listtype: { header: '黑名单类型', width: 40 },
      field: { header: '黑名单领域', width: 60 },
      CaseReason: { header: '列入原因', width: 60 },
      Court: { header: '列入机关', width: 60 },
      decisiondatestr: { header: '列入日期', width: 60 },
    },
  },
  {
    explainRows: 3,
    limit: 5000,
    sheetName: DimensionLevel2Enums.BidAdministrativePenalties,
    templateTitle: ['项目名称', '项目编号', '决定书文号', '企业名称', '违法事实', '处罚结果', '处罚金额（元）', '处罚单位', '处罚日期'],
    exportExcelColumns: {
      projectName: { header: '项目名称', width: 30 },
      projectNo: { header: '项目编号', width: 30 },
      CaseNo: { header: '决定书文号', width: 40 },
      Name: { header: '企业名称', width: 40 },
      CaseReason: { header: '违法事实', width: 60 },
      Title: { header: '处罚结果', width: 60 },
      amount: { header: '处罚金额（元）', width: 60 },
      Court: { header: '处罚单位', width: 60 },
      PunishDate: { header: '处罚日期', width: 60 },
    },
  },
  {
    explainRows: 3,
    limit: 5000,
    sheetName: DimensionLevel2Enums.BidAdministrativeJudgement,
    templateTitle: ['项目名称', '项目编号', '案号', '案由', '当事人', '案件金额（元）', '发布日期'],
    exportExcelColumns: {
      projectName: { header: '项目名称', width: 30 },
      projectNo: { header: '项目编号', width: 30 },
      caseno: { header: '案号', width: 40 },
      casereason: { header: '案由', width: 40 },
      party: { header: '当事人', width: 60 },
      amountinvolved: { header: '案件金额（元）', width: 60 },
      courtdate: { header: '发布日期', width: 60 },
    },
  },
];

export const TenderGroupDimensionDetails: ExcelGroupVersionParserSettingPO = {
  [GroupDimensionVersionEnums.V1]: TenderDimensionDetails,
};

/**
 * 招标排查维度详情导出维度字段格式化配置
 */
export const TenderDimensionDetailCommonFields = [
  {
    dimensions: [DimensionLevel2Enums.BiddingCompanyCertification],
    fields: [],
  },
  {
    dimensions: [DimensionLevel2Enums.BiddingCompanyRelationship],
    fields: [
      {
        key: 'relationName',
        format: (val) => directRelationalTypes(val, true),
      },
      {
        key: 'relationChain',
        format: (val) => generateCompanyRelationChain(val),
      },
    ],
  },
  {
    dimensions: [DimensionLevel2Enums.ContactRelationship],
    fields: [
      {
        key: 'relationName',
        format: (val) => directRelationalTypes(val, true),
      },
      {
        key: 'relationChain',
        format: (val) => generateContactRelationChain(val),
      },
    ],
  },
  {
    dimensions: [DimensionLevel2Enums.BiddingCompanyRelationship2],
    fields: [
      {
        key: 'relationName',
        format: (val) => relationsTypeParser(val, true),
      },
      {
        key: 'relationChain',
        format: (val) => generateCompanyRelationChain(val),
      },
    ],
  },
  {
    dimensions: [DimensionLevel2Enums.BidAdministrativeJudgement],
    fields: [
      {
        key: 'party',
        format: (val) => proceeParty(val),
      },
      {
        key: 'courtdate',
        format: (val) => dateTransform(val?.courtdate),
      },
    ],
  },
  {
    dimensions: [DimensionLevel2Enums.BidAdministrativePenalties],
    fields: [],
  },
  {
    dimensions: [
      DimensionLevel3Enums.GovernmentPurchaseIllegal,
      DimensionLevel3Enums.ArmyProcurementIllegal,
      DimensionLevel3Enums.GovProcurementIllegal,
      DimensionLevel3Enums.ArmyProcurementSuspended,
      DimensionLevel3Enums.MigrantWorkers,
    ],
    fields: [
      {
        key: 'removedate',
        format: (val) => dateTransform(val?.removedate),
      },
    ],
  },
  {
    dimensions: [DimensionLevel2Enums.JointBiddingAnalysis], //共同投标分析
    fields: [
      {
        key: 'tenderPercent',
        format: (val) => `${val?.tenderPercent}${val.tag ? '(' + val.tag + ')' : ''}`,
      },
      {
        key: 'competitionTenderPercent',
        format: (val) => `${val?.competitionTenderPercent}${val.competitionTag ? '(' + val.competitionTag + ')' : ''}`,
      },
    ],
  },
  {
    dimensions: [DimensionLevel2Enums.DirectConnection, DimensionLevel2Enums.Shareholder, DimensionLevel2Enums.ForeignInvestment],
    fields: [
      {
        key: 'duration',
        format: (val) => (val?.duration ? DurationConstants[val.duration] : '-'),
      },
      { key: 'joinDate', format: (val) => dateTransform(val?.joinDate) },
      { key: 'expiredDate', format: (val) => dateTransform(val?.expiredDate) },
    ],
  },
  {
    dimensions: [DimensionLevel2Enums.BlacklistSameSuspectedActualController],
    fields: [{ key: 'relationChain', format: (val) => getControllerChain(val.name, val.details.path) }],
  },
  {
    dimensions: [DimensionLevel2Enums.CompanyBranch],
    fields: [{ key: 'relationChain', format: (val) => getRelationChain([val]) }],
  },
  {
    dimensions: [DimensionLevel2Enums.BlackListInvestigations],
    fields: [
      {
        key: 'relationChain',
        format: (val) => convertPathToGraphTender(val?.relations),
      },
    ],
  },
  {
    dimensions: [DimensionLevel2Enums.BlacklistSuspectedRelation],
    fields: [
      {
        key: 'relationChain',
        format: (val) => convertSuspectPathToGraph(val?.relations, '\n'),
      },
    ],
  },
  {
    dimensions: [DimensionLevel3Enums.StaffWorkingOutsideForeignInvestment, DimensionLevel3Enums.SuspectedInterestConflict],
    fields: [
      { key: 'personName', format: (val) => transformPersonName(val) },
      {
        key: 'relationPersonName',
        format: (val) =>
          (val?.relationPersonName
            ? `${val?.personNo.split('_')[0]}-${val?.relationPersonName} - ${val?.name}（${val?.relationship}）`
            : val?.personNo + ' - ' + val?.name) + (val?.status > 0 ? '[是本人]' : ''),
      },
      { key: 'investDate', format: (val) => renderDateRange(val, ['1']) },
      { key: 'jobDate', format: (val) => renderDateRange(val, ['2', '0']) },
    ],
  },
  {
    dimensions: [DimensionLevel2Enums.CompanyCredit, DimensionLevel3Enums.PersonCreditCurrent, DimensionLevel2Enums.TaxationOffences],
    fields: [
      {
        key: 'relatedObject',
        format: (val) => val?.SubjectInfo?.map((p) => (p.description ? p.description : p.Name)).join(',') || '-',
      },
      {
        key: 'AddDate',
        format: (val) => dateTransform(val?.AddDate),
      },
      {
        key: 'SqrInfo',
        format: (val) => val?.SqrInfo?.map((s) => s?.Name)?.join('\n'),
      },
      {
        key: 'Amount',
        format: (val) => formatMoney(val?.Amount),
      },
      {
        key: 'amount',
        format: (val) => formatMoney(val?.amount),
      },
      {
        key: 'LiAnDate',
        format: (val) => dateTransform(val?.LiAnDate),
      },
      {
        key: 'LianDate',
        format: (val) => dateTransform(val?.LianDate),
      },
      {
        key: 'PublicDate',
        format: (val) => dateTransform(val?.PublicDate),
      },
    ],
  },
  {
    dimensions: [DimensionLevel2Enums.FakeSOES],
    fields: [
      {
        key: 'pathDetail',
        format: (val) => convertPathToGraphTender(val?.relations?.relations),
      },
    ],
  },
];
