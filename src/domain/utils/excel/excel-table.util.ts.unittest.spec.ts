// 导入我们需要测试的函数
import { OptionsObjectModel } from '@domain/model/batch/export/OptionsObjectModel';
import * as PdfTableUtil from '@domain/pdf/pdf-table.util';
import * as ExcelTableUtil from './excel-table.util';

// Mock 外部依赖
jest.mock('@domain/pdf/pdf-table.util', () => {
  const actual = jest.requireActual('@domain/pdf/pdf-table.util');
  return {
    __esModule: true,
    ...actual,
    correctRoles: jest.fn().mockReturnValue([{ role: '股东' }]),
    getRelationTag: jest.fn().mockReturnValue('[股东]'),
    relationsPathParser: jest.fn().mockReturnValue('公司A->公司B'),
    isEdge: jest.fn(),
  };
});

jest.mock('@domain/pdf/relation-path-diligence.util', () => ({
  drawEdge: jest.fn(),
  getRolesNames: jest.fn(),
  isCompany: jest.fn(),
  isPerson: jest.fn(),
}));

// 创建可以在测试中使用的 mock 函数引用
let mockRelationsPathParser2: jest.SpyInstance<string, Parameters<typeof PdfTableUtil.relationsPathParser2>>;

// 在测试执行前 mock 需要的函数
beforeAll(() => {
  // Mock relationsPathParser2 函数
  mockRelationsPathParser2 = jest.spyOn(PdfTableUtil, 'relationsPathParser2').mockImplementation(() => '');
});

afterAll(() => {
  mockRelationsPathParser2.mockRestore();
});

// 统一清理所有 mock
beforeEach(() => {
  jest.clearAllMocks();
});

describe('ExcelTableUtil 单元测试', () => {
  describe('transformPersonName 函数测试', () => {
    it('应该返回普通人名', () => {
      // Arrange
      const input = { name: '张三' };

      // Act
      const result = ExcelTableUtil.transformPersonName(input);

      // Assert
      expect(result).toBe('张三');
    });

    it('应该返回疑似同名标识', () => {
      // Arrange
      const input = { name: '张三', isSameName: true };

      // Act
      const result = ExcelTableUtil.transformPersonName(input);

      // Assert
      expect(result).toBe('疑似同名（张三）');
    });

    it('应该返回相同联系方式标识', () => {
      // Arrange
      const input = {
        isSameContact: true,
        phones: [{ n: '13800138000', t: '手机' }],
        emails: [{ e: '<EMAIL>' }],
      };

      // Act
      const result = ExcelTableUtil.transformPersonName(input);

      // Assert
      expect(result).toContain('相同联系方式');
      expect(result).toContain('13800138000 手机');
      expect(result).toContain('<EMAIL>');
    });

    it('应该返回疑似同名和相同联系方式的组合标识', () => {
      // Arrange
      const input = {
        name: '张三',
        isSameName: true,
        isSameContact: true,
        phones: [{ n: '13800138000', t: '手机' }],
        emails: [{ e: '<EMAIL>' }],
      };

      // Act
      const result = ExcelTableUtil.transformPersonName(input);

      // Assert
      expect(result).toContain('疑似同名');
      expect(result).toContain('相同联系方式');
    });
  });

  describe('processPledgor 函数测试', () => {
    it('应该处理Pledgor为对象的情况', () => {
      // Arrange
      const input = { Pledgor: [{ toString: () => '张三' }, { toString: () => '李四' }] };

      // Act
      const result = ExcelTableUtil.processPledgor(input);

      // Assert
      expect(result).toBe('张三\n李四');
    });

    it('应该处理Pledgor为字符串的情况', () => {
      // Arrange
      const input = { Pledgor: '<div>张三</div>' };

      // Act
      const result = ExcelTableUtil.processPledgor(input);

      // Assert
      expect(result).toBe('张三');
    });

    it('应该处理Pledgor为空数组的情况', () => {
      // Arrange
      const input = { Pledgor: [] };

      // Act
      const result = ExcelTableUtil.processPledgor(input);

      // Assert
      expect(result).toBe('-');
    });
  });

  describe('processBeneficiary 函数测试', () => {
    it('应该处理before时间点的受益人数据', () => {
      // Arrange
      const input = {
        ChangeExtend: JSON.stringify([
          {
            BeforeContent: JSON.stringify({ Name: '张三', PercentTotal: '50%' }),
            AfterContent: JSON.stringify({ Name: '李四', PercentTotal: '50%' }),
          },
        ]),
      };

      // Act
      const result = ExcelTableUtil.processBeneficiary(input, 'before');

      // Assert
      expect(result).toBe('张三,受益股份（50%）');
    });

    it('应该处理after时间点的受益人数据', () => {
      // Arrange
      const input = {
        ChangeExtend: JSON.stringify([
          {
            BeforeContent: JSON.stringify({ Name: '张三', PercentTotal: '50%' }),
            AfterContent: JSON.stringify({ Name: '李四', PercentTotal: '50%' }),
          },
        ]),
      };

      // Act
      const result = ExcelTableUtil.processBeneficiary(input, 'after');

      // Assert
      expect(result).toBe('李四,受益股份（50%）');
    });

    it('应该处理没有PercentTotal的情况', () => {
      // Arrange
      const input = {
        ChangeExtend: JSON.stringify([
          {
            BeforeContent: JSON.stringify({ Name: '张三' }),
            AfterContent: JSON.stringify({ Name: '李四' }),
          },
        ]),
      };

      // Act
      const result = ExcelTableUtil.processBeneficiary(input, 'before');

      // Assert
      expect(result).toBe('张三');
    });
  });

  describe('processNegativeNews 函数测试', () => {
    it('应该处理负面新闻标签', () => {
      // Arrange
      const input = {
        codedesc: ['警告', '罚款'],
        tagsnew: ['经营异常', '罚款'],
      };

      // Act
      const result = ExcelTableUtil.processNegativeNews(input);

      // Assert
      expect(result).toBe('#警告 #罚款 #经营异常');
    });

    it('应该处理空标签数组', () => {
      // Arrange
      const input = {};

      // Act
      const result = ExcelTableUtil.processNegativeNews(input);

      // Assert
      expect(result).toBe('');
    });
  });

  describe('processCaseRoleGroup 函数测试', () => {
    it('应该处理案件角色组数据', () => {
      // Arrange
      const input = {
        caserolegroupbyrolename: [
          {
            Role: '原告',
            LawyerTag: 0,
            DetailList: [{ Name: '张三' }],
          },
        ],
        involveRole: [
          {
            Tag: '证人',
            Name: '李四',
          },
        ],
      };

      // Act
      const result = ExcelTableUtil.processCaseRoleGroup(input);

      // Assert
      expect(result).toBe('原告 - 张三\n证人 - 李四');
    });

    it('应该处理空数据的情况', () => {
      // Arrange
      const input = {};

      // Act
      const result = ExcelTableUtil.processCaseRoleGroup(input);

      // Assert
      expect(result).toBe('-');
    });
  });

  describe('generateRelationChain 函数测试', () => {
    it('应该生成基本的关系链', () => {
      // Arrange
      const options: OptionsObjectModel = {
        left: 'left',
        right: 'right',
        middle: 'middle',
        leftInfo: '股东',
        rightInfo: '高管',
      };

      // Act
      const result = ExcelTableUtil.generateRelationChain('公司A', '公司B', '张三', '股东', '高管', options);

      // Assert
      expect(result).toBe('公司A←（股东）张三（高管）→公司B');
    });

    it('应该处理空节点的情况', () => {
      // Arrange
      const options: OptionsObjectModel = {
        left: 'left',
        right: 'right',
        middle: 'middle',
        leftInfo: '股东',
        rightInfo: '高管',
      };

      // Act
      const result = ExcelTableUtil.generateRelationChain('', '公司B', '张三', '股东', '高管', options);

      // Assert
      expect(result).toBe('张三（高管）→公司B');
    });

    it('应该处理stockpercent特殊字段', () => {
      // Arrange
      const options: OptionsObjectModel = {
        left: 'left',
        right: 'right',
        middle: 'middle',
        leftInfo: 'leftInfo',
        rightInfo: '高管',
      };

      // Act
      const result = ExcelTableUtil.generateRelationChain('公司A', '公司B', '张三', '30', '高管', options);

      // Assert
      expect(result).toBe('公司A←（30）张三（高管）→公司B');
    });
  });

  describe('relationsPathParser2 函数测试', () => {
    beforeEach(() => {
      // 恢复真实实现来测试实际功能
      mockRelationsPathParser2.mockRestore();
    });

    afterEach(() => {
      // 测试后重新 mock，避免影响其他测试
      mockRelationsPathParser2 = jest.spyOn(PdfTableUtil, 'relationsPathParser2').mockImplementation(() => '');
    });

    it('应该返回默认值-当没有关系数据时', () => {
      // Arrange
      const record = {};

      // Act
      const result = PdfTableUtil.relationsPathParser2(record, true);

      // Assert
      expect(result).toBe('-');
    });

    it('应该处理有多个关系的情况', () => {
      // Arrange
      const record = {
        relations: [
          [{ role: '股东', type: 'invest', startid: '1', endid: '2', direction: '1' }],
          [{ role: '高管', type: 'employ', startid: '1', endid: '3', direction: '1' }],
        ],
        data: [['北京市朝阳区'], ['上海市浦东新区']],
      };

      // Act
      const result = PdfTableUtil.relationsPathParser2(record, true);

      // Assert
      expect(typeof result).toBe('string');
      expect(result.includes('\n')).toBe(true); // 应该包含换行符
      // 验证多个关系的解析结果
      expect(result).toContain('股东');
      expect(result).toContain('高管');
    });

    it('应该在data为null时处理关系', () => {
      // Arrange
      const record = {
        relations: [[{ role: '股东', type: 'invest', startid: '1', endid: '2', direction: '1' }]],
        data: [null],
      };

      // Act
      const result = PdfTableUtil.relationsPathParser2(record, true);

      // Assert
      expect(typeof result).toBe('string');
      expect(result).not.toBe('-');
      expect(result).toContain('股东'); // 验证结果包含关系角色信息
    });

    it('应该处理对象类型的data数据', () => {
      // Arrange
      const record = {
        relations: [[{ role: '股东', type: 'invest', startid: '1', endid: '2', direction: '1' }]],
        data: [{ Paging: { TotalRecords: 5 } }],
      };

      // Act
      const result = PdfTableUtil.relationsPathParser2(record, true);

      // Assert
      expect(typeof result).toBe('string');
      expect(result).toContain('股东');
      expect(result).toContain('数量: 5');
    });
  });

  describe('generateCompanyRelationChain 函数测试', () => {
    beforeEach(() => {
      // 恢复真实实现来测试实际功能
      mockRelationsPathParser2.mockRestore();
    });

    afterEach(() => {
      // 测试后重新 mock，避免影响其他测试
      mockRelationsPathParser2 = jest.spyOn(PdfTableUtil, 'relationsPathParser2').mockImplementation(() => '');
    });

    it('应该生成公司关系链并处理HTML标签', () => {
      // Arrange
      const record = {
        relations: [[{ role: '股东' }]],
        data: [{ t: '公司A->公司B' }],
      };

      // Act
      const result = ExcelTableUtil.generateCompanyRelationChain(record);

      // Assert
      expect(typeof result).toBe('string');
      expect(result).not.toContain('<div>'); // 确保HTML标签被移除
    });

    it('应该处理关系数据并清理HTML标签', () => {
      // Arrange
      const record = {
        relations: [[{ role: '股东' }]],
        data: [{ t: '公司A->公司B' }],
      };

      // Act
      const result = ExcelTableUtil.generateCompanyRelationChain(record);

      // Assert
      expect(typeof result).toBe('string');
      expect(result).not.toContain('<'); // 确保所有HTML标签都被移除
    });

    it('应该处理空记录情况', () => {
      // Arrange
      const record = {
        relations: [],
      };

      // Act
      const result = ExcelTableUtil.generateCompanyRelationChain(record);

      // Assert
      expect(result).toBe('-');
    });
  });

  describe('getRelationChain 函数测试', () => {
    it('应该获取关系链', () => {
      // Arrange
      const items = [
        {
          typeDD: 'HISEMPLOY',
          roleDD: '高管',
          typeRelated: 'HISINVEST',
          roleRelated: '股东',
          companyNameDD: '公司A',
          companyNameRelated: '公司B',
          personName: '张三',
          stockpercent: '30',
        },
      ];

      // Act
      const result = ExcelTableUtil.getRelationChain(items);

      // Assert
      expect(typeof result).toBe('string');
      expect(result).toContain('公司A');
      expect(result).toContain('公司B');
      expect(result).toContain('张三');
    });

    it('应该处理没有leftInfo和rightInfo的情况', () => {
      // Arrange
      const items = [
        {
          typeDD: '',
          roleDD: '',
          typeRelated: '',
          roleRelated: '',
          companyNameDD: '公司A',
          companyNameRelated: '公司B',
          personName: '张三',
          stockpercent: '30',
        },
      ];

      // Act
      const result = ExcelTableUtil.getRelationChain(items);

      // Assert
      expect(result).toBe('公司A-30%→公司B');
    });
  });

  describe('getControllerChain 函数测试', () => {
    it('应该获取控制链', () => {
      // Arrange
      const name = '公司A';
      const paths = [
        [
          { Percent: '51', Name: '公司B' },
          { Percent: '60', Name: '公司C' },
        ],
      ];

      // Act
      const result = ExcelTableUtil.getControllerChain(name, paths);

      // Assert
      expect(result).toBe('公司A->(51)->公司B->(60)->公司C');
    });

    it('应该处理多个路径', () => {
      // Arrange
      const name = '公司A';
      const paths = [
        [
          { Percent: '51', Name: '公司B' },
          { Percent: '60', Name: '公司C' },
        ],
        [{ Percent: '100', Name: '公司D' }],
      ];

      // Act
      const result = ExcelTableUtil.getControllerChain(name, paths);

      // Assert
      expect(result).toBe('公司A->(51)->公司B->(60)->公司C\n公司A->(100)->公司D');
    });
  });

  describe('proceeParty 函数测试', () => {
    it('应该处理当事人数据', () => {
      // Arrange
      const input = {
        caserolegroupbyrolename: [
          {
            Role: '原告',
            LawyerTag: 0,
            DetailList: [{ Name: '张三' }],
          },
        ],
        involveRole: [
          {
            Tag: '证人',
            Name: '李四',
          },
        ],
      };

      // Act
      const result = ExcelTableUtil.proceeParty(input);

      // Assert
      expect(result).toBe('原告-张三\n证人-李四');
    });

    it('应该处理空数据的情况', () => {
      // Arrange
      const input = {};

      // Act
      const result = ExcelTableUtil.proceeParty(input);

      // Assert
      expect(result).toBe('');
    });

    it('应该处理caserole字段的情况', () => {
      // Arrange
      const input = {
        caserolegroupbyrolename: [],
        caserole: [
          { P: '原告', R: '张三' },
          { ShowName: '被告', R: '李四' },
        ],
        involveRole: [],
      };

      // Act
      const result = ExcelTableUtil.proceeParty(input);

      // Assert
      expect(result).toBe('原告-张三\n被告-李四');
    });
  });
});
