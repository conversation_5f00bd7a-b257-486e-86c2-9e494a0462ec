import { OrgSettingsLogEntity } from '@domain/entities/OrgSettingsLogEntity';
import { DimensionGroupDefinitionType } from '@domain/model/diligence/pojo/dimension/group/DimensionGroupDefinitionType';
import { DimensionDefinitionPO, SubDimensionDefinitionPO } from '@domain/model/diligence/pojo/dimension/DimensionDefinitionPO';
import { cloneDeep, find, flattenDeep } from 'lodash';
import { DimensionLevel2Enums } from '@domain/enums/diligence/DimensionLevel2Enums';
import { DimensionTypeEnums } from '@domain/enums/diligence/DimensionTypeEnums';
import { ConfigurationTenderRiskDimension } from '@domain/model/bidding/ConfigurationTenderRiskDimensionPo';
import { QueryParamsEnums } from '@domain/model/diligence/pojo/dimension/dimension.filter.params';
import { DimensionLevel1Enums } from '@domain/enums/diligence/DimensionLevel1Enums';
import { DetailsParamEnums } from '@domain/enums/diligence/DetailsParamEnums';
import { DimensionLevel3Enums } from '@domain/enums/diligence/DimensionLevel3Enums';

/**
 *
 * @param po
 * @param ignoreDisabled true -不返回关闭的维度
 * @param showAll  true - 维度有子维度把自身也同时返回
 * @export const
 */
const findRecursive = (po: DimensionDefinitionPO | SubDimensionDefinitionPO, ignoreDisabled = false, showAll = false) => {
  if (ignoreDisabled && po.status != 1) {
    return null;
  }
  if (!po.isVirtualDimension) {
    // 非虚拟维度，直接返回
    return po;
  }
  if (po.isVirtualDimension && po?.subDimensionList?.length > 0) {
    // 虚拟维度，返回所有子节点
    const result = [
      po,
      ...po.subDimensionList.map((s) => {
        if (ignoreDisabled && s.status != 1) {
          return null;
        }
        s.groupKey = po.groupKey;
        s.groupKeyList = s.groupKeyList || [];
        s.groupKeyList.push(po.key);
        if (!s.isVirtualDimension) {
          // 非虚拟维度，直接返回
          return { ...s, subDimensionList: s?.subDimensionList || [] };
        }
        if (s.isVirtualDimension && s?.subDimensionList?.length > 0) {
          return findRecursive(s, ignoreDisabled, showAll);
          // const array: any[] = [this.findRecursive(s, ignoreDisabled, showAll)];
          // if (showAll) {
          //   array.unshift(s);
          // }
          // return array;
        }
      }),
    ];
    if (!showAll && po.key !== DimensionLevel2Enums.HitOuterBlackList) {
      // 外部黑名单 HitOuterBlackList 只要开启就需要包 维度本身和subDimensionList一起返回
      result.shift();
    }
    return result;
  }
};

export const getDimensionGroupDefinition = (orgSetting: OrgSettingsLogEntity, returnVersion = false): DimensionGroupDefinitionType => {
  const dimensions: DimensionGroupDefinitionType = orgSetting.content;
  if (!returnVersion) {
    delete dimensions.version;
  }
  return dimensions;
};

export const getAllDimension = (orgSetting: OrgSettingsLogEntity, ignoreDisabled = false, showAll = false): DimensionDefinitionPO[] => {
  const groupSetting: DimensionGroupDefinitionType = getDimensionGroupDefinition(orgSetting);
  //这里要确保 po.subDimensionList也返回, 便于查询使用(同时es聚合时候也会按照子维度使用)
  return flattenDeep(
    Object.keys(groupSetting).map((groupKey) => {
      if (groupKey === 'version') {
        return null;
      }
      if (ignoreDisabled && !groupSetting[groupKey].status) {
        return null;
      }
      if (!groupSetting[groupKey].items) {
        // 可能是  非DimensionGroupDefinitionType里面的一些key ，这里可以直接过滤掉
        return null;
      }
      return groupSetting[groupKey].items.map((po) => {
        po.groupKey = groupKey;
        return findRecursive(po, ignoreDisabled, showAll);
      });
    }),
  ).filter((t) => t);
};

export const getDimensionDefinition = (dimensionKey: DimensionTypeEnums, orgSetting: OrgSettingsLogEntity): DimensionDefinitionPO => {
  const allDimensionDefines = getAllDimension(orgSetting, false, true);
  return find(allDimensionDefines, { key: dimensionKey });
};

type TenderConfigTypeItem = {
  readonly key?: unknown;
  readonly status?: unknown;
};

const isRecord = (value: unknown): value is Record<string, unknown> => typeof value === 'object' && value !== null;

const normalizeTenderTypes = (types: unknown): string[] => {
  if (!Array.isArray(types)) {
    return [];
  }
  const normalized = types
    .map((item: unknown) => {
      if (typeof item === 'string') {
        return item;
      }
      if (!isRecord(item)) {
        return null;
      }
      const typedItem: TenderConfigTypeItem = item;
      if (typedItem.status !== 1) {
        return null;
      }
      return typeof typedItem.key === 'string' ? typedItem.key : null;
    })
    .filter((value: string | null): value is string => typeof value === 'string' && value.length > 0);
  return normalized;
};

export const processTenderConfig = (tenderConfigEntity: OrgSettingsLogEntity): ConfigurationTenderRiskDimension[] => {
  const rawConfig: unknown = tenderConfigEntity.content;
  if (!Array.isArray(rawConfig)) {
    return [];
  }
  const tenderConfig = cloneDeep(rawConfig as ConfigurationTenderRiskDimension[]);
  tenderConfig.forEach((x) => {
    x.types = normalizeTenderTypes(x.types);
    x.subDimensionList?.forEach((sub) => {
      sub.types = normalizeTenderTypes(sub.types);
    });
  });
  return tenderConfig;
};

const handleInnerBlacklistDimension = (subItem: SubDimensionDefinitionPO, types: string[]) => {
  subItem.status = 0; // 默认关闭所有子维度

  if (types.includes(DetailsParamEnums.HitInnerBlackList) && subItem.key === DimensionLevel2Enums.HitInnerBlackList) {
    subItem.status = 1;
  }
  if (types.includes(DetailsParamEnums.Shareholder) && subItem.key === DimensionLevel2Enums.Shareholder) {
    subItem.status = 1;
    subItem.strategyModel.detailsParams.forEach((param) => {
      if (param.field === QueryParamsEnums.isValid) {
        param.fieldVal = '1';
      }
    });
  }
  if (types.includes(DetailsParamEnums.HisShareholder) && subItem.key === DimensionLevel2Enums.Shareholder) {
    subItem.status = 1;
    subItem.strategyModel.detailsParams.forEach((param) => {
      if (param.field === QueryParamsEnums.isValid) {
        param.fieldVal = '-1';
      }
    });
  }
  if (types.includes(DetailsParamEnums.Invest) && subItem.key === DimensionLevel2Enums.ForeignInvestment) {
    subItem.status = 1;
    subItem.strategyModel.detailsParams.forEach((param) => {
      if (param.field === QueryParamsEnums.isValid) {
        param.fieldVal = '1';
      }
    });
  }
  if (types.includes(DetailsParamEnums.HisInvest) && subItem.key === DimensionLevel2Enums.ForeignInvestment) {
    subItem.status = 1;
    subItem.strategyModel.detailsParams.forEach((param) => {
      if (param.field === QueryParamsEnums.isValid) {
        param.fieldVal = '-1';
      }
    });
  }
  if (types.includes(DetailsParamEnums.Employ) && subItem.key === DimensionLevel2Enums.EmploymentRelationship) {
    subItem.status = 1;
    subItem.strategyModel.detailsParams.forEach((param) => {
      if (param.field === QueryParamsEnums.isValid) {
        param.fieldVal = '1';
      }
    });
  }
  if (types.includes(DetailsParamEnums.HisEmploy) && subItem.key === DimensionLevel2Enums.EmploymentRelationship) {
    subItem.status = 1;
    subItem.strategyModel.detailsParams.forEach((param) => {
      if (param.field === QueryParamsEnums.isValid) {
        param.fieldVal = '-1';
      }
    });
  }
  if (types.includes(DetailsParamEnums.ActualController) && subItem.key === DimensionLevel2Enums.BlacklistSameSuspectedActualController) {
    subItem.status = 1;
  }
  if (types.includes(DetailsParamEnums.FinalBenefit) && subItem.key === DimensionLevel2Enums.BlacklistSameFinalBenefit) {
    subItem.status = 1;
  }
  if (types.includes(DetailsParamEnums.Hold) && subItem.key === DimensionLevel2Enums.Hold) {
    subItem.status = 1;
  }
  if (types.includes(DetailsParamEnums.Branch) && subItem.key === DimensionLevel2Enums.CompanyBranch) {
    subItem.status = 1;
  }
  if (types.includes(DetailsParamEnums.Guarantee) && subItem.key === DimensionLevel2Enums.Guarantee) {
    subItem.status = 1;
  }
  if (types.includes(DetailsParamEnums.EquityPledgeRelation) && subItem.key === DimensionLevel2Enums.EquityPledgeRelation) {
    subItem.status = 1;
  }
  if (types.includes(DetailsParamEnums.HasPhone) && subItem.key === DimensionLevel2Enums.HasPhone) {
    subItem.status = 1;
  }
  if (types.includes(DetailsParamEnums.HasAddress) && subItem.key === DimensionLevel2Enums.HasAddress) {
    subItem.status = 1;
  }
  if (types.includes(DetailsParamEnums.HasEmail) && subItem.key === DimensionLevel2Enums.HasEmail) {
    subItem.status = 1;
  }
};

const handleInterestConflictDimension = (subItem: SubDimensionDefinitionPO, types: string[]) => {
  subItem.status = 0; // 默认关闭所有子维度

  if (types.includes(DimensionLevel3Enums.StaffWorkingOutsideForeignInvestment) && subItem.key === DimensionLevel3Enums.StaffWorkingOutsideForeignInvestment) {
    subItem.status = 1;
  }
  if ((types.includes('StaffSameName') || types.includes('StaffSamePhone')) && subItem.key === DimensionLevel3Enums.SuspectedInterestConflict) {
    subItem.status = 1;
    subItem.strategyModel.detailsParams.forEach((param) => {
      if (param.field === QueryParamsEnums.types) {
        if (types.includes('StaffSameName')) {
          param.fieldVal.push('SameName');
        }
        if (types.includes('StaffSamePhone')) {
          param.fieldVal.push('SamePhone');
        }
      }
    });
  }
};

export const updateSubItemWithConfig = (subItem: SubDimensionDefinitionPO, config: ConfigurationTenderRiskDimension, itemStatus: number) => {
  if (itemStatus === 0) {
    subItem.status = 0;
  } else {
    const subConfig = config.subDimensionList?.find((x) => x.key === subItem.key);
    if (subConfig) {
      subItem.status = subConfig.status;
      if (subItem.strategyModel) {
        subItem.strategyModel.cycle = subConfig.cycle || subItem.strategyModel.cycle;
        if (subItem.strategyModel?.detailsParams?.length) {
          subItem.strategyModel.detailsParams.forEach((param) => {
            if (param.field === QueryParamsEnums.percentage) {
              param.fieldVal = subConfig[param.field]; //默认股比支持设置为 0
            } else {
              param.fieldVal = subConfig[param.field] || param.fieldVal;
            }
          });
        } else {
          subItem.strategyModel.detailsParams = subConfig.detailsParams;
        }
      }
    }
    //config 可能没有 subDimensionList,只有 types,处理 types
    const types = config?.types;
    if (config.key === DimensionLevel1Enums.Risk_InnerBlacklist) {
      handleInnerBlacklistDimension(subItem, types);
    }
    if (config.key === DimensionLevel1Enums.Risk_InterestConflict) {
      handleInterestConflictDimension(subItem, types);
    }
  }
};

export const updateItemWithConfig = (item: DimensionDefinitionPO, config: ConfigurationTenderRiskDimension) => {
  item.status = config.status;
  if (config.cycle) {
    item.cycle = config.cycle;
  }
  if (config?.sort) {
    item.sort = config.sort;
  }
  if (item.strategyModel) {
    item.strategyModel.cycle = config.cycle || item.strategyModel.cycle;
    if (item.strategyModel?.detailsParams?.length) {
      item.strategyModel.detailsParams.forEach((param) => {
        param.fieldVal = config[param.field] || param.fieldVal;
      });
    } else {
      item.strategyModel.detailsParams = config.detailsParams;
    }
  }
  if (item.subDimensionList?.length) {
    item.subDimensionList.forEach((subItem) => updateSubItemWithConfig(subItem, config, item.status));
  }
};

const processBiddingCompanyRelation = (item: DimensionDefinitionPO, config: ConfigurationTenderRiskDimension) => {
  item.subDimensionList.forEach((subItem) => {
    const subConfig = config.subDimensionList.find((x) => x.key === subItem.key);
    if (!subConfig?.types?.length) {
      subItem.status = 0;
    }
  });
};

const processShareholder = (subItem: SubDimensionDefinitionPO, config: ConfigurationTenderRiskDimension) => {
  subItem.status = config.types.includes('Shareholder') || config.types.includes('HisShareholder') ? 1 : 0;
  if (subItem.status === 1) {
    const param = subItem.strategyModel.detailsParams.find((x) => x.field === QueryParamsEnums.isValid);
    if (config.types.includes('Shareholder') && config.types.includes('HisShareholder')) {
      param.fieldVal = '-1';
    } else if (config.types.includes('Shareholder')) {
      param.fieldVal = '1';
    } else {
      param.fieldVal = '0';
    }
  }
};

const processForeignInvestment = (subItem: SubDimensionDefinitionPO, config: ConfigurationTenderRiskDimension) => {
  subItem.status = config.types.includes('Invest') || config.types.includes('HisInvest') ? 1 : 0;
  if (subItem.status === 1) {
    const param = subItem.strategyModel.detailsParams.find((x) => x.field === QueryParamsEnums.isValid);
    if (config.types.includes('Invest') && config.types.includes('HisInvest')) {
      param.fieldVal = '-1';
    } else if (config.types.includes('Invest')) {
      param.fieldVal = '1';
    } else {
      param.fieldVal = '0';
    }
  }
};

const processEmploymentRelationship = (subItem: SubDimensionDefinitionPO, config: ConfigurationTenderRiskDimension) => {
  subItem.status = config.types.includes('Employ') || config.types.includes('HisEmploy') ? 1 : 0;
  if (subItem.status === 1) {
    const param = subItem.strategyModel.detailsParams.find((x) => x.field === QueryParamsEnums.isValid);
    if (config.types.includes('Employ') && config.types.includes('HisEmploy')) {
      param.fieldVal = '-1';
    } else if (config.types.includes('Employ')) {
      param.fieldVal = '1';
    } else {
      param.fieldVal = '0';
    }
  }
};

const processRiskInnerBlacklist = (item: DimensionDefinitionPO, config: ConfigurationTenderRiskDimension) => {
  item.subDimensionList.forEach((subItem) => {
    switch (subItem.key) {
      case DimensionLevel2Enums.HitInnerBlackList:
        subItem.status = config.types.includes('HitInnerBlackList') ? 1 : 0;
        break;
      case DimensionLevel2Enums.Shareholder:
        processShareholder(subItem, config);
        break;
      case DimensionLevel2Enums.ForeignInvestment:
        processForeignInvestment(subItem, config);
        break;
      case DimensionLevel2Enums.EmploymentRelationship:
        processEmploymentRelationship(subItem, config);
        break;
      case DimensionLevel2Enums.BlacklistSameSuspectedActualController:
        subItem.status = config.types.includes('ActualController') ? 1 : 0;
        break;
      case DimensionLevel2Enums.CompanyBranch:
        subItem.status = config.types.includes(DetailsParamEnums.Branch) ? 1 : 0;
        break;
    }
  });
};

const processSuspectedInterestConflict = (subItem: SubDimensionDefinitionPO, config: ConfigurationTenderRiskDimension) => {
  subItem.status = config.types.includes('StaffSameName') || config.types.includes('StaffSamePhone') ? 1 : 0;
  if (subItem.status === 1) {
    const param = subItem.strategyModel.detailsParams.find((x) => x.field === QueryParamsEnums.types);
    const fieldVal = [];
    if (config.types.includes('StaffSameName')) {
      fieldVal.push('SameName');
    }
    if (config.types.includes('StaffSamePhone')) {
      fieldVal.push('SamePhone');
    }
    param.fieldVal = fieldVal;
  }
};

const processRiskInterestConflict = (item: DimensionDefinitionPO, config: ConfigurationTenderRiskDimension) => {
  item.strategyModel.detailsParams = config.detailsParams;
  item.subDimensionList.forEach((subItem) => {
    if (subItem?.strategyModel?.detailsParams?.length && config?.detailsParams?.length) {
      subItem.strategyModel.detailsParams.push(config.detailsParams.find((x) => x.field === QueryParamsEnums.dataRange));
    }
    switch (subItem.key) {
      case DimensionLevel3Enums.StaffWorkingOutsideForeignInvestment:
        subItem.status = config.types.includes('StaffWorkingOutsideForeignInvestment') ? 1 : 0;
        break;
      case DimensionLevel3Enums.SuspectedInterestConflict:
        processSuspectedInterestConflict(subItem, config);
        break;
    }
  });
};

export const processDimensionLevel = (item: DimensionDefinitionPO, config: ConfigurationTenderRiskDimension) => {
  switch (item.key) {
    case DimensionLevel2Enums.BiddingCompanyRelation:
      processBiddingCompanyRelation(item, config);
      break;
    case DimensionLevel1Enums.Risk_InnerBlacklist:
      processRiskInnerBlacklist(item, config);
      break;
    case DimensionLevel1Enums.Risk_InterestConflict:
      processRiskInterestConflict(item, config);
      break;
  }
};
