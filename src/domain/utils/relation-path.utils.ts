/**
 * 合并路径，对于 startCompanyKeyno 和 endCompanyKeyno 相同的，合并关系relations路径，添加到 relationsPath 二维数组中
 * @param relations
 */
export function mergeExtractResult(relations: any[]) {
  const mergedMap = new Map<string, any>();
  for (const current of relations) {
    if (!current.endCompanyKeyno || !current.startCompanyKeyno || current.endCompanyKeyno === current.startCompanyKeyno) {
      continue;
    }
    const mergeKey = `${current.startCompanyKeyno}_${current.endCompanyKeyno}`;
    const existing = mergedMap.get(mergeKey);
    if (existing) {
      if (current.relations && current.relations.length > 0) {
        existing.relationPaths.push([...current.relations]);
      }
      if (current.type && !existing.types.includes(current.type)) {
        existing.types.push(current.type);
      }
    } else {
      const newCompany = {
        startCompanyName: current.startCompanyName,
        startCompanyKeyno: current.startCompanyKeyno,
        endCompanyName: current.endCompanyName,
        endCompanyKeyno: current.endCompanyKeyno,
        role: current.role,
        history: current.history,
        steps: current.steps,
        type: current.type,
        types: current.type ? [current.type] : [],
        relationPaths: current.relations && current.relations.length > 0 ? [[...current.relations]] : [],
      };
      mergedMap.set(mergeKey, newCompany);
    }
  }
  return Array.from(mergedMap.values());
}

/**
 * 合并相同两个人的共同任职/投资信息
 * @param relations 关系数组
 * @returns 合并后的关系数组
 */
export function mergeSamePersonRelations(relations: any[]) {
  if (!relations || relations.length === 0) {
    return [];
  }
  const mergedMap = new Map<string, any>();
  for (const relation of relations) {
    const { person1KeyNo, person2KeyNo } = relation;
    const mergeKey = [person1KeyNo, person2KeyNo].sort().join('_');
    const existing = mergedMap.get(mergeKey);
    if (existing) {
      const { midCompanyKeyNo, midCompanyName } = relation;
      if (!existing.midCompanies.some((c: any) => c.keyNo === midCompanyKeyNo)) {
        existing.midCompanies.push({ keyNo: midCompanyKeyNo, name: midCompanyName });
      }
      const r1Role = relation.path?.r1?.role;
      const r1RelationType = relation.path?.r1?.relationType;
      if (r1Role && r1Role.trim()) {
        const isHistorical = r1RelationType?.includes('HIS');
        const roleList = isHistorical ? existing.r1HistoryRoles : existing.r1CurrentRoles;
        if (!roleList.includes(r1Role)) {
          roleList.push(r1Role);
        }
      }
      const r4Role = relation.path?.r4?.role;
      const r4RelationType = relation.path?.r4?.relationType;
      if (r4Role && r4Role.trim()) {
        const isHistorical = r4RelationType?.includes('HIS');
        const roleList = isHistorical ? existing.r4HistoryRoles : existing.r4CurrentRoles;
        if (!roleList.includes(r4Role)) {
          roleList.push(r4Role);
        }
      }
      if (r1RelationType && !existing.r1RelationTypes.includes(r1RelationType)) {
        existing.r1RelationTypes.push(r1RelationType);
      }
      if (r4RelationType && !existing.r4RelationTypes.includes(r4RelationType)) {
        existing.r4RelationTypes.push(r4RelationType);
      }
      if (r1RelationType === 'HISEMPLOY' || r4RelationType === 'HISEMPLOY') {
        existing.hasHistoryEmploy = true;
      }
    } else {
      const r1Role = relation.path?.r1?.role;
      const r4Role = relation.path?.r4?.role;
      const r1RelationType = relation.path?.r1?.relationType;
      const r4RelationType = relation.path?.r4?.relationType;

      const r1CurrentRoles: string[] = [];
      const r1HistoryRoles: string[] = [];
      if (r1Role && r1Role.trim()) {
        if (r1RelationType?.includes('HIS')) {
          r1HistoryRoles.push(r1Role);
        } else {
          r1CurrentRoles.push(r1Role);
        }
      }

      const r4CurrentRoles: string[] = [];
      const r4HistoryRoles: string[] = [];
      if (r4Role && r4Role.trim()) {
        if (r4RelationType?.includes('HIS')) {
          r4HistoryRoles.push(r4Role);
        } else {
          r4CurrentRoles.push(r4Role);
        }
      }

      mergedMap.set(mergeKey, {
        person1KeyNo: relation.person1KeyNo,
        person1Name: relation.person1Name,
        person2KeyNo: relation.person2KeyNo,
        person2Name: relation.person2Name,
        midCompanies: [{ keyNo: relation.midCompanyKeyNo, name: relation.midCompanyName }],
        r1CurrentRoles,
        r1HistoryRoles,
        r4CurrentRoles,
        r4HistoryRoles,
        r1RelationTypes: r1RelationType ? [r1RelationType] : [],
        r4RelationTypes: r4RelationType ? [r4RelationType] : [],
        hasHistoryEmploy: r1RelationType === 'HISEMPLOY' || r4RelationType === 'HISEMPLOY',
      });
    }
  }
  return Array.from(mergedMap.values());
}

/**
 * 判断边的类型是否为历史关系
 * @param edgeType 边的类型
 * @returns 是否为历史关系
 */
function isHistoricalEdge(edgeType: string): boolean {
  return edgeType.includes('His');
}

/**
 * 分析路径中边的类型分布
 * @param path 单条路径（点边点结构）
 * @returns 路径类型分类结果
 */
interface PathTypeAnalysis {
  hasCurrentEdge: boolean;
  hasHistoricalEdge: boolean;
  pathType: 'current' | 'mixed' | 'historical';
}

function analyzePathType(path: any[]): PathTypeAnalysis {
  let hasCurrentEdge = false;
  let hasHistoricalEdge = false;

  for (let i = 1; i < path.length; i += 2) {
    const edge = path[i];
    if (edge.type) {
      if (isHistoricalEdge(edge.type)) {
        hasHistoricalEdge = true;
      } else {
        hasCurrentEdge = true;
      }
    }
  }

  let pathType: 'current' | 'mixed' | 'historical';
  if (hasCurrentEdge && !hasHistoricalEdge) {
    pathType = 'current';
  } else if (hasCurrentEdge && hasHistoricalEdge) {
    pathType = 'mixed';
  } else {
    pathType = 'historical';
  }

  return { hasCurrentEdge, hasHistoricalEdge, pathType };
}

/**
 * 根据优先级过滤关系路径
 * 优先级：
 * 1. 所有边都是当前关系（不包含"His"）的路径
 * 2. 既包含当前关系又包含历史关系的路径
 * 3. 所有边都是历史关系（包含"His"）的路径
 *
 * @param relationPaths 路径数组（二维数组，每个路径是点边点结构）
 * @returns 过滤后的路径数组
 */
export function filterRelationPathsByPriority(relationPaths: any[][]): any[][] {
  if (!relationPaths || relationPaths.length === 0) {
    return [];
  }

  const currentPaths: any[][] = [];
  const mixedPaths: any[][] = [];
  const historicalPaths: any[][] = [];

  for (const path of relationPaths) {
    const analysis = analyzePathType(path);
    switch (analysis.pathType) {
      case 'current':
        currentPaths.push(path);
        break;
      case 'mixed':
        mixedPaths.push(path);
        break;
      case 'historical':
        historicalPaths.push(path);
        break;
    }
  }

  if (currentPaths.length > 0) {
    return currentPaths;
  }
  if (mixedPaths.length > 0) {
    return mixedPaths;
  }
  return historicalPaths;
}

/**
 * 过滤企业联系关系数据，对每组公司关系的 relationPaths 按优先级筛选
 * @param enterpriseContactRelations 企业联系关系数组
 * @returns 过滤后的企业联系关系数组
 */
export function filterEnterpriseContactRelations(enterpriseContactRelations: any[]): any[] {
  if (!enterpriseContactRelations || enterpriseContactRelations.length === 0) {
    return [];
  }

  return enterpriseContactRelations.map((relation) => {
    if (!relation.relationPaths || relation.relationPaths.length === 0) {
      return relation;
    }

    const filteredPaths = filterRelationPathsByPriority(relation.relationPaths);

    return {
      ...relation,
      relationPaths: filteredPaths,
    };
  });
}

/**
 * 分析管理层投资关系中单个relation的path类型
 * @param relation 包含path的关系对象
 * @returns 路径类型分类结果
 */
interface ManagementPathTypeAnalysis {
  hasCurrentRelation: boolean;
  hasHistoricalRelation: boolean;
  pathType: 'current' | 'mixed' | 'historical';
}

function analyzeManagementPathType(relation: any): ManagementPathTypeAnalysis {
  let hasCurrentRelation = false;
  let hasHistoricalRelation = false;

  if (!relation.path) {
    return { hasCurrentRelation: false, hasHistoricalRelation: false, pathType: 'historical' };
  }

  const pathKeys = ['r1', 'r2', 'r3', 'r4'];
  for (const key of pathKeys) {
    const relationNode = relation.path[key];
    if (relationNode && relationNode.relationType) {
      if (relationNode.relationType.includes('HIS')) {
        hasHistoricalRelation = true;
      } else {
        hasCurrentRelation = true;
      }
    }
  }

  let pathType: 'current' | 'mixed' | 'historical';
  if (hasCurrentRelation && !hasHistoricalRelation) {
    pathType = 'current';
  } else if (hasCurrentRelation && hasHistoricalRelation) {
    pathType = 'mixed';
  } else {
    pathType = 'historical';
  }

  return { hasCurrentRelation, hasHistoricalRelation, pathType };
}

/**
 * 根据优先级过滤管理层投资关系的relations数组
 * 优先级：
 * 1. path 中 r1, r2, r3, r4 的 relationType 全为当前关系类型（不包含"HIS"）
 * 2. 既包含当前关系类型又包含历史关系类型
 * 3. 全部为历史关系类型（包含"HIS"）
 *
 * @param relations 关系数组
 * @returns 过滤后的关系数组
 */
export function filterManagementRelationsByPriority(relations: any[]): any[] {
  if (!relations || relations.length === 0) {
    return [];
  }

  const currentRelations: any[] = [];
  const mixedRelations: any[] = [];
  const historicalRelations: any[] = [];

  for (const relation of relations) {
    const analysis = analyzeManagementPathType(relation);
    switch (analysis.pathType) {
      case 'current':
        currentRelations.push(relation);
        break;
      case 'mixed':
        mixedRelations.push(relation);
        break;
      case 'historical':
        historicalRelations.push(relation);
        break;
    }
  }

  if (currentRelations.length > 0) {
    return currentRelations;
  }
  if (mixedRelations.length > 0) {
    return mixedRelations;
  }
  return historicalRelations;
}

/**
 * 过滤管理层投资关系数据，对每组公司关系的 relations 按优先级筛选
 * @param managementInvestmentResults 管理层投资关系数组
 * @returns 过滤后的管理层投资关系数组
 */
export function filterManagementInvestmentRelations(managementInvestmentResults: any[]): any[] {
  if (!managementInvestmentResults || managementInvestmentResults.length === 0) {
    return [];
  }

  return managementInvestmentResults.map((result) => {
    if (!result.relations || result.relations.length === 0) {
      return result;
    }

    const filteredRelations = filterManagementRelationsByPriority(result.relations);

    return {
      ...result,
      relations: filteredRelations,
      relationCount: filteredRelations.length,
    };
  });
}
