import {
  filterEnterpriseContactRelations,
  filterManagementInvestmentRelations,
  filterManagementRelationsByPriority,
  filterRelationPathsByPriority,
} from './relation-path.utils';

describe('RelationPathUtils', () => {
  describe('filterRelationPathsByPriority', () => {
    it('应返回所有边都是当前关系的路径（第一优先级）', () => {
      const relationPaths = [
        [
          { NodeType: 'Company', 'Company.keyno': 'key1' },
          { type: 'HasEmail', role: '相同邮箱' },
          { NodeType: 'Email', 'Email.keyno': 'em1' },
          { type: 'HasEmail', role: '相同邮箱' },
          { NodeType: 'Company', 'Company.keyno': 'key2' },
        ],
        [
          { NodeType: 'Company', 'Company.keyno': 'key1' },
          { type: 'HisInvest', role: '曾投资' },
          { NodeType: 'Company', 'Company.keyno': 'key2' },
        ],
        [
          { NodeType: 'Company', 'Company.keyno': 'key1' },
          { type: 'Branch', role: '分支机构' },
          { NodeType: 'Company', 'Company.keyno': 'key2' },
        ],
      ];

      const result = filterRelationPathsByPriority(relationPaths);

      expect(result).toHaveLength(2);
      expect(result[0]).toBe(relationPaths[0]);
      expect(result[1]).toBe(relationPaths[2]);
    });

    it('应返回既包含当前又包含历史关系的路径（第二优先级）', () => {
      const relationPaths = [
        [
          { NodeType: 'Company', 'Company.keyno': 'key1' },
          { type: 'HisInvest', role: '曾投资' },
          { NodeType: 'Company', 'Company.keyno': 'key3' },
          { type: 'Invest', role: '投资' },
          { NodeType: 'Company', 'Company.keyno': 'key2' },
        ],
        [
          { NodeType: 'Company', 'Company.keyno': 'key1' },
          { type: 'HisLegal', role: '曾法人' },
          { NodeType: 'Person', 'Person.keyno': 'person1' },
          { type: 'HisEmploy', role: '曾任职' },
          { NodeType: 'Company', 'Company.keyno': 'key2' },
        ],
      ];

      const result = filterRelationPathsByPriority(relationPaths);

      expect(result).toHaveLength(1);
      expect(result[0]).toBe(relationPaths[0]);
    });

    it('应返回所有边都是历史关系的路径（第三优先级）', () => {
      const relationPaths = [
        [
          { NodeType: 'Company', 'Company.keyno': 'key1' },
          { type: 'HisInvest', role: '曾投资' },
          { NodeType: 'Company', 'Company.keyno': 'key2' },
        ],
        [
          { NodeType: 'Company', 'Company.keyno': 'key1' },
          { type: 'HisLegal', role: '曾法人' },
          { NodeType: 'Person', 'Person.keyno': 'person1' },
          { type: 'HisEmploy', role: '曾任职' },
          { NodeType: 'Company', 'Company.keyno': 'key2' },
        ],
      ];

      const result = filterRelationPathsByPriority(relationPaths);

      expect(result).toHaveLength(2);
      expect(result).toEqual(relationPaths);
    });

    it('应处理空数组', () => {
      const result = filterRelationPathsByPriority([]);
      expect(result).toEqual([]);
    });

    it('应处理null或undefined', () => {
      const result1 = filterRelationPathsByPriority(null as any);
      expect(result1).toEqual([]);

      const result2 = filterRelationPathsByPriority(undefined as any);
      expect(result2).toEqual([]);
    });
  });

  describe('filterEnterpriseContactRelations', () => {
    it('应对每组公司关系的relationPaths进行过滤', () => {
      const enterpriseContactRelations = [
        {
          startCompanyKeyno: '5c5affe5cfb3710694ea10c7e368f8c4',
          startCompanyName: '甘肃兴百安工程有限公司',
          endCompanyKeyno: '18924e3a7c475830c3f626ae9c15e02a',
          endCompanyName: '四川安泰信建设集团有限公司',
          relationPaths: [
            [
              { NodeType: 'Company', 'Company.keyno': 'key1' },
              { type: 'HasEmail', role: '相同邮箱' },
              { NodeType: 'Email', 'Email.keyno': 'em1' },
              { type: 'HasEmail', role: '相同邮箱' },
              { NodeType: 'Company', 'Company.keyno': 'key2' },
            ],
            [
              { NodeType: 'Company', 'Company.keyno': 'key1' },
              { type: 'HisInvest', role: '曾投资' },
              { NodeType: 'Company', 'Company.keyno': 'key2' },
            ],
          ],
        },
      ];

      const result = filterEnterpriseContactRelations(enterpriseContactRelations);

      expect(result).toHaveLength(1);
      expect(result[0].relationPaths).toHaveLength(1);
      expect(result[0].relationPaths[0]).toBe(enterpriseContactRelations[0].relationPaths[0]);
    });

    it('应保留没有relationPaths的关系对象', () => {
      const enterpriseContactRelations = [
        {
          startCompanyKeyno: 'key1',
          startCompanyName: 'Company 1',
          endCompanyKeyno: 'key2',
          endCompanyName: 'Company 2',
          relationPaths: [],
        },
      ];

      const result = filterEnterpriseContactRelations(enterpriseContactRelations);

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual(enterpriseContactRelations[0]);
    });

    it('应处理空数组', () => {
      const result = filterEnterpriseContactRelations([]);
      expect(result).toEqual([]);
    });

    it('应处理null或undefined', () => {
      const result1 = filterEnterpriseContactRelations(null as any);
      expect(result1).toEqual([]);

      const result2 = filterEnterpriseContactRelations(undefined as any);
      expect(result2).toEqual([]);
    });
  });

  describe('完整场景测试', () => {
    it('应正确处理用户提供的真实数据格式', () => {
      const enterpriseContactRelations = [
        {
          endCompanyKeyno: '18924e3a7c475830c3f626ae9c15e02a',
          endCompanyName: '四川安泰信建设集团有限公司',
          history: false,
          relationPaths: [
            [
              {
                'Company.labels': '',
                'Company.shortstatus': '存续',
                NodeType: 'Company',
                'Company.keyno': '5c5affe5cfb3710694ea10c7e368f8c4',
                'Company.name': '甘肃兴百安工程有限公司',
              },
              {
                role: '相同邮箱',
                create_time: '1768005796',
                type: 'HasEmail',
                direction: -1,
              },
              {
                NodeType: 'Email',
                'Email.keyno': 'em_cc93295dcd202c85',
                'Email.name': '<EMAIL>',
              },
              {
                role: '相同邮箱',
                create_time: '1768005796',
                type: 'HasEmail',
                direction: 1,
              },
              {
                NodeType: 'Company',
                'Company.keyno': '9c262cc909cb3faf9da1506f801ff14a',
                'Company.name': '四川安泰信建设集团有限公司七里河分公司',
              },
              {
                role: '分支机构',
                type: 'Branch',
                direction: -1,
              },
              {
                NodeType: 'Company',
                'Company.keyno': '18924e3a7c475830c3f626ae9c15e02a',
                'Company.name': '四川安泰信建设集团有限公司',
              },
            ],
            [
              {
                NodeType: 'Company',
                'Company.keyno': '5c5affe5cfb3710694ea10c7e368f8c4',
                'Company.name': '甘肃兴百安工程有限公司',
              },
              {
                role: '曾投资',
                type: 'HisInvest',
              },
              {
                NodeType: 'Company',
                'Company.keyno': '18924e3a7c475830c3f626ae9c15e02a',
                'Company.name': '四川安泰信建设集团有限公司',
              },
            ],
          ],
          relations: [],
          startCompanyKeyno: '5c5affe5cfb3710694ea10c7e368f8c4',
          startCompanyName: '甘肃兴百安工程有限公司',
          steps: 0,
        },
      ];

      const result = filterEnterpriseContactRelations(enterpriseContactRelations);

      expect(result).toHaveLength(1);
      expect(result[0].relationPaths).toHaveLength(1);
      expect(result[0].relationPaths[0]).toBe(enterpriseContactRelations[0].relationPaths[0]);
      const edges = result[0].relationPaths[0].filter((_: any, index: number) => index % 2 === 1);
      edges.forEach((edge: any) => {
        expect(edge.type).not.toContain('His');
      });
    });
  });

  describe('filterManagementRelationsByPriority', () => {
    it('应返回path中所有relationType都是当前关系的relation（第一优先级）', () => {
      const relations = [
        {
          midCompanyKeyNo: 'company1',
          midCompanyName: 'Company 1',
          path: {
            r1: { relationType: 'INVEST', role: '投资' },
            r2: { relationType: 'EMPLOY', role: '任职' },
            r3: { relationType: 'INVEST', role: '投资' },
            r4: { relationType: 'EMPLOY', role: '任职' },
          },
          person1KeyNo: 'p1',
          person2KeyNo: 'p2',
        },
        {
          midCompanyKeyNo: 'company2',
          midCompanyName: 'Company 2',
          path: {
            r1: { relationType: 'HISINVEST', role: '曾投资' },
            r2: { relationType: 'HISEMPLOY', role: '曾任职' },
            r3: { relationType: 'HISINVEST', role: '曾投资' },
            r4: { relationType: 'HISEMPLOY', role: '曾任职' },
          },
          person1KeyNo: 'p1',
          person2KeyNo: 'p2',
        },
        {
          midCompanyKeyNo: 'company3',
          midCompanyName: 'Company 3',
          path: {
            r1: { relationType: 'EMPLOY', role: '任职' },
            r2: { relationType: 'EMPLOY', role: '任职' },
            r3: { relationType: 'EMPLOY', role: '任职' },
            r4: { relationType: 'EMPLOY', role: '任职' },
          },
          person1KeyNo: 'p3',
          person2KeyNo: 'p4',
        },
      ];

      const result = filterManagementRelationsByPriority(relations);

      expect(result).toHaveLength(2);
      expect(result[0]).toBe(relations[0]);
      expect(result[1]).toBe(relations[2]);
    });

    it('应返回path中既包含当前又包含历史关系的relation（第二优先级）', () => {
      const relations = [
        {
          midCompanyKeyNo: 'company1',
          midCompanyName: 'Company 1',
          path: {
            r1: { relationType: 'INVEST', role: '投资' },
            r2: { relationType: 'HISINVEST', role: '曾投资' },
            r3: { relationType: 'EMPLOY', role: '任职' },
            r4: { relationType: 'HISEMPLOY', role: '曾任职' },
          },
          person1KeyNo: 'p1',
          person2KeyNo: 'p2',
        },
        {
          midCompanyKeyNo: 'company2',
          midCompanyName: 'Company 2',
          path: {
            r1: { relationType: 'HISINVEST', role: '曾投资' },
            r2: { relationType: 'HISEMPLOY', role: '曾任职' },
            r3: { relationType: 'HISINVEST', role: '曾投资' },
            r4: { relationType: 'HISEMPLOY', role: '曾任职' },
          },
          person1KeyNo: 'p1',
          person2KeyNo: 'p2',
        },
      ];

      const result = filterManagementRelationsByPriority(relations);

      expect(result).toHaveLength(1);
      expect(result[0]).toBe(relations[0]);
    });

    it('应返回path中所有relationType都是历史关系的relation（第三优先级）', () => {
      const relations = [
        {
          midCompanyKeyNo: 'company1',
          midCompanyName: 'Company 1',
          path: {
            r1: { relationType: 'HISINVEST', role: '曾投资' },
            r2: { relationType: 'HISEMPLOY', role: '曾任职' },
            r3: { relationType: 'HISINVEST', role: '曾投资' },
            r4: { relationType: 'HISEMPLOY', role: '曾任职' },
          },
          person1KeyNo: 'p1',
          person2KeyNo: 'p2',
        },
        {
          midCompanyKeyNo: 'company2',
          midCompanyName: 'Company 2',
          path: {
            r1: { relationType: 'HISLEGAL', role: '曾法人' },
            r2: { relationType: 'HISEMPLOY', role: '曾任职' },
            r3: { relationType: 'HISINVEST', role: '曾投资' },
            r4: { relationType: 'HISEMPLOY', role: '曾任职' },
          },
          person1KeyNo: 'p3',
          person2KeyNo: 'p4',
        },
      ];

      const result = filterManagementRelationsByPriority(relations);

      expect(result).toHaveLength(2);
      expect(result).toEqual(relations);
    });

    it('应处理空数组', () => {
      const result = filterManagementRelationsByPriority([]);
      expect(result).toEqual([]);
    });

    it('应处理null或undefined', () => {
      const result1 = filterManagementRelationsByPriority(null as any);
      expect(result1).toEqual([]);

      const result2 = filterManagementRelationsByPriority(undefined as any);
      expect(result2).toEqual([]);
    });
  });

  describe('filterManagementInvestmentRelations', () => {
    it('应对每组公司关系的relations进行过滤并更新relationCount', () => {
      const managementInvestmentResults = [
        {
          startCompanyKeyNo: 'start1',
          startCompanyName: 'Start Company 1',
          endCompanyKeyNo: 'end1',
          endCompanyName: 'End Company 1',
          relationCount: 2,
          relations: [
            {
              midCompanyKeyNo: 'mid1',
              midCompanyName: 'Mid Company 1',
              path: {
                r1: { relationType: 'INVEST', role: '投资' },
                r2: { relationType: 'EMPLOY', role: '任职' },
                r3: { relationType: 'INVEST', role: '投资' },
                r4: { relationType: 'EMPLOY', role: '任职' },
              },
              person1KeyNo: 'p1',
              person2KeyNo: 'p2',
            },
            {
              midCompanyKeyNo: 'mid2',
              midCompanyName: 'Mid Company 2',
              path: {
                r1: { relationType: 'HISINVEST', role: '曾投资' },
                r2: { relationType: 'HISEMPLOY', role: '曾任职' },
                r3: { relationType: 'HISINVEST', role: '曾投资' },
                r4: { relationType: 'HISEMPLOY', role: '曾任职' },
              },
              person1KeyNo: 'p1',
              person2KeyNo: 'p2',
            },
          ],
        },
      ];

      const result = filterManagementInvestmentRelations(managementInvestmentResults);

      expect(result).toHaveLength(1);
      expect(result[0].relations).toHaveLength(1);
      expect(result[0].relationCount).toBe(1);
      expect(result[0].relations[0]).toBe(managementInvestmentResults[0].relations[0]);
    });

    it('应保留没有relations的结果对象', () => {
      const managementInvestmentResults = [
        {
          startCompanyKeyNo: 'start1',
          startCompanyName: 'Start Company 1',
          endCompanyKeyNo: 'end1',
          endCompanyName: 'End Company 1',
          relationCount: 0,
          relations: [],
        },
      ];

      const result = filterManagementInvestmentRelations(managementInvestmentResults);

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual(managementInvestmentResults[0]);
    });

    it('应处理空数组', () => {
      const result = filterManagementInvestmentRelations([]);
      expect(result).toEqual([]);
    });

    it('应处理null或undefined', () => {
      const result1 = filterManagementInvestmentRelations(null as any);
      expect(result1).toEqual([]);

      const result2 = filterManagementInvestmentRelations(undefined as any);
      expect(result2).toEqual([]);
    });
  });

  describe('完整场景测试 - 管理层投资关系', () => {
    it('应正确处理用户提供的真实数据格式', () => {
      const managementInvestmentResults = [
        {
          endCompanyKeyNo: '84c72a52324aea1bae950c4fcffc5897',
          endCompanyName: '上海知彼网络科技有限公司',
          relationCount: 5,
          relations: [
            {
              midCompanyKeyNo: '08de25e86d0aa1d11677a17eeca6988e',
              midCompanyName: '苏州方塘网络科技有限公司',
              path: {
                r1: {
                  inDate: '1394553600000',
                  outDate: '0',
                  relationType: 'INVEST',
                  role: '',
                  stockPercent: 4.7345,
                },
                r2: {
                  inDate: '0',
                  outDate: '1597852800',
                  relationType: 'HISINVEST',
                  role: '自然人股东',
                },
                r3: {
                  inDate: '0',
                  outDate: '1647273600',
                  relationType: 'EMPLOY',
                  role: '监事',
                },
                r4: {
                  inDate: '0',
                  outDate: '1671552000',
                  relationType: 'HISEMPLOY',
                  role: '监事',
                },
              },
              person1KeyNo: 'p78c9e5065ffdd42f396bb8d8165abeb',
              person1Name: '施阳',
              person2KeyNo: 'pcfe53afcbdb53c6e5facf9e7c023d46',
              person2Name: '刘培彬',
            },
            {
              midCompanyKeyNo: '4e1213c71f14cd183bdecccab19b43ba',
              midCompanyName: '海南企查查网络科技有限公司',
              path: {
                r1: {
                  inDate: '0',
                  outDate: '0',
                  relationType: 'EMPLOY',
                  role: '董事',
                },
                r2: {
                  inDate: '1587312000000',
                  outDate: '0',
                  relationType: 'EMPLOY',
                  role: '监事',
                },
                r3: {
                  inDate: '1662480000000',
                  outDate: '0',
                  relationType: 'EMPLOY',
                  role: '财务负责人',
                },
                r4: {
                  inDate: '0',
                  outDate: '0',
                  relationType: 'EMPLOY',
                  role: '经理',
                },
              },
              person1KeyNo: 'p78c9e5065ffdd42f396bb8d8165abeb',
              person1Name: '施阳',
              person2KeyNo: 'p9e55b5d48140bf14b61c08bd7ef98ec',
              person2Name: '杨京',
            },
          ],
          startCompanyKeyNo: 'f625a5b661058ba5082ca508f99ffe1b',
          startCompanyName: '企查查科技股份有限公司',
        },
      ];

      const result = filterManagementInvestmentRelations(managementInvestmentResults);

      expect(result).toHaveLength(1);
      expect(result[0].relations).toHaveLength(1);
      expect(result[0].relationCount).toBe(1);
      expect(result[0].relations[0].midCompanyKeyNo).toBe('4e1213c71f14cd183bdecccab19b43ba');
      const pathKeys = ['r1', 'r2', 'r3', 'r4'];
      pathKeys.forEach((key) => {
        const relationType = result[0].relations[0].path[key].relationType;
        expect(relationType).not.toContain('HIS');
      });
    });
  });
});
