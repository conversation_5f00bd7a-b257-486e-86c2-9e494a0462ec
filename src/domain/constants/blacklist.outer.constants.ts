import { IndicatorTypeEnums } from '@domain/model/settings/IndicatorTypeEnums';
import { DimensionDefinitionPO } from '@domain/model/diligence/pojo/dimension/DimensionDefinitionPO';
import { DimensionRiskLevelEnum } from '@domain/enums/diligence/DimensionRiskLevelEnum';
import { DimensionLevel3Enums } from '@domain/enums/diligence/DimensionLevel3Enums';
import { DimensionLevel2Enums } from '@domain/enums/diligence/DimensionLevel2Enums';
import { QueryParamsEnums } from '@domain/model/diligence/pojo/dimension/dimension.filter.params';

export const DefaultOuterBlacklistItems: DimensionDefinitionPO[] = [
  {
    key: DimensionLevel3Enums.GovernmentPurchaseIllegal,
    name: '政府采购严重违法失信行为记录名单',
    description:
      '依据《关于报送政府采购严重违法失信行为信息记录的通知》（财办库[2014]526号）发布公示的存在政府采购领域违法失信行为的信用主体名单。数据来源：中国政府采购网、信用中国等',
    strategyModel: {
      level: DimensionRiskLevelEnum.High,
      detailsParams: [
        {
          field: QueryParamsEnums.source,
          fieldVal: [1, 2, 3, 4],
        },
      ],
    },
    status: 1,
    child: ['25'],
    type: IndicatorTypeEnums.generalItems,
    sort: 1,
    template: '',
    template2: '',
  },
  {
    key: DimensionLevel3Enums.SafetyProductionEnterprise,
    name: '安全生产领域失信生产经营单位',
    description: '应急管理部门认定的在安全生产领域存在失信行为的生产经营单位及其法定代表人、主要负责人（含实际控制人）。数据来源：信用中国',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 1,
    child: ['2'],
    type: IndicatorTypeEnums.generalItems,
    sort: 2,
    template: '',
    template2: '',
  },
  {
    key: DimensionLevel3Enums.CustomsList,
    name: '海关失信企业名单',
    description: '由海关部门依据《中华人民共和国海关企业信用管理暂行办法》认定的失信企业名单。数据来源：信用中国',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 1,
    child: ['7'],
    type: IndicatorTypeEnums.generalItems,
    sort: 3,
    template: '',
    template2: '',
  },
  {
    key: DimensionLevel3Enums.IntellectualPropertyIllegal,
    name: '知识产权（专利）领域严重失信联合戒对象名单',
    description:
      '知识产权局依法依规认定存在重复专利侵权行为、不依法执行行为、专利代理严重违法行为、专利代理师资格证书挂靠行为、非正常申请专利行为、提供虚假文件行为的信用主体名单。数据来源：信用中国',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 1,
    child: ['43'],
    type: IndicatorTypeEnums.generalItems,
    sort: 4,
    template: '',
    template2: '',
  },
  {
    key: DimensionLevel3Enums.EnvironmentalProtection,
    name: '环保失信黑名单',
    description: '由生态环境部依法认定并予以公示的失信企业名录。数据来源：信用中国',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 1,
    child: ['13', '65'],
    type: IndicatorTypeEnums.generalItems,
    sort: 5,
    template: '',
    template2: '',
  },
  {
    key: DimensionLevel3Enums.LaborGuarantee,
    name: '劳动保障违法',
    description: '人社部门认定的存在重大违法事项的企业名录。数据来源：信用中国',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 1,
    child: ['6'],
    type: IndicatorTypeEnums.generalItems,
    sort: 6,
    template: '',
    template2: '',
  },
  // RA-14334 数据停用
  {
    key: DimensionLevel3Enums.ListedCompanyIllegal,
    name: '违法失信上市公司',
    description: '由中国证监会及其派出机构认定予以行政处罚、市场禁入的上市公司及相关机构和人员等责任主体。数据来源：信用中国',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 1,
    child: ['18'],
    type: IndicatorTypeEnums.generalItems,
    sort: 7,
    template: '',
    template2: '',
    isHidden: true,
  },
  {
    key: DimensionLevel3Enums.GovProcurementIllegal,
    name: '国央企采购黑名单',
    description: '各个国企/央企发布公示的存在采购领域失信行为的信用主体名单。数据来源：各个国企/央企官网等',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 0,
    child: ['74', '77', '78', '121'],
    type: IndicatorTypeEnums.generalItems,
    sort: 8,
    template: '',
    template2: '',
  },
  {
    key: DimensionLevel3Enums.FgwBlackList,
    name: '发改委黑名单',
    description: '发展改革委员会认定的严重失信行为的市场主体',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 1,
    child: ['57'],
    type: IndicatorTypeEnums.generalItems,
    sort: 9,
    template: '',
    template2: '',
  },
  {
    key: DimensionLevel2Enums.ForeignExportControls,
    name: '出口管制合规风险企业清单',
    description:
      '被国外相关部门（机构）列入出口管制合规风险企业清单，包含美国出口管制企业清单、亚投行禁止名单、联合国综合制裁名单、欧盟金融制裁综合清单、英国制裁名单、加拿大自治制裁综合清单、澳大利亚综合制裁清单、 世界银行不合格公司和个人名单、欧洲投资银行排除名单、日本经济产业省贸易管制最终用户名单、英国财政部金融制裁目标综合清单等',
    strategyModel: {
      level: DimensionRiskLevelEnum.High,
      detailsParams: [
        {
          field: 'listtypecode',
          fieldVal: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '12', '13', '14', '15', '17', '18', '19', '22', '23', '24', '25'],
        },
      ],
    },
    status: 1,
    child: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '12', '13', '14', '15', '17', '18', '19', '22', '23', '24', '25'],
    type: IndicatorTypeEnums.generalItems,
    sort: 10,
    template: '',
    template2: '',
  },
  {
    key: DimensionLevel2Enums.WorldBankBlacklist,
    name: '世界银行“黑名单”',
    description: '世界银行(World Bank)在其网站公布的制裁名单中涉及的实体企业等',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 0,
    child: [],
    type: IndicatorTypeEnums.generalItems,
    sort: 11,
    template: '',
    template2: '',
    isHidden: true,
  },
  {
    key: DimensionLevel2Enums.SupervisionOfKeyIndustry,
    name: '重点行业领域监管黑名单',
    description:
      '包括出入境检验检疫信用管理严重失信企业名单、电力行业严重违法失信主体、拖欠农民工工资黑名单、统计领域严重失信企业及其有关人员、运输物流行业严重失信黑名单、电子商务领域黑名单、建筑工程领域黑名单、医疗医领域黑名单、消防安全领域黑名单、价格失信黑名单、石油天然气行业严重违法失信主体、招投标活动失信行为企业、建筑行业不良行为、严重违法超限超载运输当事人名单、文化和旅游市场严重失信主体名单、快递领域违法失信主体黑名单、食品安全严重违法生产经营者黑名单、高频失信市场主体信息、淘汰落后和过剩产能企业名单等',
    strategyModel: {
      level: DimensionRiskLevelEnum.Medium,
      detailsParams: [
        {
          field: 'listtypecode',
          fieldVal: ['1', '2', '3', '4', '5', '6', '7', '8'],
        },
      ],
    },
    status: 1,
    child: [
      '40',
      '41',
      '68',
      '79',
      '39',
      '4',
      '38',
      '5',
      '54',
      '3',
      '24',
      '28',
      '33',
      '34',
      '55',
      '56',
      '69',
      '98',
      '11',
      '1',
      '46',
      '8',
      '16',
      '29',
      '30',
      '32',
      '35',
      '36',
      '37',
      '44',
      '47',
      '48',
      '49',
      '50',
      '51',
      '52',
      '53',
      '97',
      '99',
      '106',
      '105',
      '108',
    ],
    type: IndicatorTypeEnums.generalItems,
    sort: 12,
    template: '',
    template2: '',
  },
  {
    key: DimensionLevel3Enums.MedicalMedicineIllegal,
    name: '医疗医药领域黑名单',
    description: '医药、医药卫生行业企业被主管部门纳入黑名单',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 1,
    child: ['40', '41', '68', '79', '39'],
    type: IndicatorTypeEnums.generalItems,
    sort: 13,
    template: '',
    template2: '',
    isHidden: true,
  },
  {
    key: DimensionLevel3Enums.InspectionAuthority,
    name: '出入境检验检疫信用管理严重失信企业名单',
    description: '国家质量监督检验检疫总局依法认定的因严重违法违规行为多次受到处罚的企业名单。数据来源：信用中国',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 1,
    child: ['4'],
    type: IndicatorTypeEnums.generalItems,
    sort: 14,
    template: '',
    template2: '',
    isHidden: true,
  },
  {
    key: DimensionLevel3Enums.PriceBreakPromise,
    name: '价格失信黑名单',
    description: '经营者违反价格法律、法规和规章，发生情节严重的价格违法行为被主管单位列入黑名单',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 1,
    child: ['38'],
    type: IndicatorTypeEnums.generalItems,
    sort: 15,
    template: '',
    template2: '',
    isHidden: true,
  },
  // RA-14334 数据停用
  // {
  //   key: DimensionLevel3Enums.QualityCredit,
  //   name: '严重质量失信黑名单',
  //   description: '质监部门依法依规认定为D级的企业，且情节特别严重、造成恶劣社会影响的企业纳入严重质量失信“黑名单”。数据来源：信用中国',
  //   strategyModel: { level: DimensionRiskLevelEnum.High },
  //   status: 1,
  //   child: ['14'],
  //   type: IndicatorTypeEnums.generalItems,
  //   sort: 16,
  //   template: '',
  //   template2: '',
  //   isHidden: true,
  // },
  {
    key: DimensionLevel3Enums.MigrantWorkers,
    name: '拖欠农民工工资黑名单',
    description: '人社部门认定的违反国家工资支付法律法规规章规定，存在拖欠工资情形的用人单位及其法定代表人 、其他责任人。数据来源：信用中国',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 1,
    child: ['5'],
    type: IndicatorTypeEnums.generalItems,
    sort: 17,
    template: '',
    template2: '',
    isHidden: true,
  },
  {
    key: DimensionLevel3Enums.ElectricityIndustryIllegal,
    name: '电力行业严重违法失信主体',
    description: '经政府主管部门认定存在严重违法失信行为并纳入电力行业“黑名单”的市场主体。数据来源：中国电力企业联合会',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 1,
    child: ['54'],
    type: IndicatorTypeEnums.generalItems,
    sort: 18,
    template: '',
    template2: '',
    isHidden: true,
  },
  {
    key: DimensionLevel3Enums.Statistics,
    name: '统计领域严重失信企业及其有关人员',
    description: '统计部门依法认定并通过国家统计局网站公示。数据来源：信用中国',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 1,
    child: ['3'],
    type: IndicatorTypeEnums.generalItems,
    sort: 19,
    template: '',
    template2: '',
    isHidden: true,
  },
  {
    key: DimensionLevel3Enums.EcBlacklist,
    name: '电子商务领域黑名单',
    description: '由国家发展改革委员会及各相关部门在电子商务及分享经济领域认定的炒信行为主体。数据来源：全国信用信息共享平台（对外公示主要为信用中国）',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 1,
    child: ['24'],
    type: IndicatorTypeEnums.generalItems,
    sort: 20,
    template: '',
    template2: '',
    isHidden: true,
  },
  {
    key: DimensionLevel3Enums.LogisticsIndustryIllegal,
    name: '运输物流行业严重失信黑名单',
    description: '依法依规对涉及性质恶劣、情节严重、社会危害较大的违法失信行为的运输物流行业市场主体列入“黑名单”。数据来源：信用中国',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 1,
    child: ['28'],
    type: IndicatorTypeEnums.generalItems,
    sort: 21,
    template: '',
    template2: '',
    isHidden: true,
  },
  {
    key: DimensionLevel3Enums.ConstructionEngineeringBlacklist,
    name: '建筑工程领域黑名单',
    description: '建筑工程领域(行业)特定严重失信行为的市场主体',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 1,
    child: ['33', '34'],
    type: IndicatorTypeEnums.generalItems,
    sort: 22,
    template: '',
    template2: '',
    isHidden: true,
  },
  {
    key: DimensionLevel3Enums.ArmyProcurementIllegal,
    name: '军队采购失信名单',
    description: '根据军队供应商管理相关规定被列入采购失信名单的企业；数据来源：军队采购网',
    strategyModel: {
      level: DimensionRiskLevelEnum.High,
      detailsParams: [
        {
          field: 'listtypecode',
          fieldVal: ['75', '76', '104'],
        },
      ],
    },
    status: 0,
    child: ['75', '76', '104'],
    type: IndicatorTypeEnums.generalItems,
    sort: 23,
    template: '',
    template2: '',
  },
  {
    key: DimensionLevel3Enums.PrivateEnterpriseCooperationBlacklist,
    name: '名企不合作企业清单',
    description: '大型企业依据《公司供应商管理办法》及合规准则，正式发布的永久禁止合作主体清单',
    strategyModel: { level: DimensionRiskLevelEnum.Medium },
    status: 1,
    child: ['103'],
    type: IndicatorTypeEnums.keyItems,
    sort: 24,
    template: '',
    template2: '',
  },
  {
    key: DimensionLevel3Enums.AIIBBlackList,
    name: '亚投行禁止名单',
    description:
      '亚投行自行调查公布的禁止名单，以及基于非洲开发银行集团、亚洲开发银行、欧洲复兴开发银行、美洲开发银行和世界银行集团(简称多边开发银行)之间相互强制执行对方禁止行动的协议中涉及的实体企业。数据来源：亚洲开发银行等银行官网',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 0,
    child: [],
    type: IndicatorTypeEnums.generalItems,
    sort: 25,
    template: '',
    template2: '',
    isHidden: true,
  },
];

export const CompOvsSanctionsFieldNameMapping = {
  nationcode: '国家代码',
  sanctionscode: '制裁代码',
  sanctionstypecode: '制裁种类代码',
  sanctionstype: '制裁种类',
  name: '名称',
  sanctionsregulations: '制裁条例/名单',
  sanctionsmeasure: '制裁措施/项目',
  sanctionsreason: '制裁原因',
  designateddate: '指定日期',
  remark: '备注',
  identifications: '认证信息',
  alias: '别名信息',
  address: '地址信息',
  keynolist: '企业匹配信息',
  federalregisternotice: '联邦公报通知',
  expirationdate: '截止日期',
  startdate: '开始日期',
  lastupdatedate: '最后更新日期',
  sourceurl: '来源',
};

export const ForeignExportControlsCodeTranslation = {
  '1': {
    name: '美国财政部办公室OFAC制裁名单',
    court: '美国财政部办公室OFAC',
    fields: [
      'nationcode',
      'sanctionscode',
      'sanctionstypecode',
      'sanctionstype',
      'name',
      'sanctionsregulations',
      'sanctionsmeasure',
      'remark',
      'alias',
      'address',
      'sourceurl',
    ],
  },
  '2': {
    name: '英国外交、联邦与发展办公室制裁局FCDO制裁名单',
    court: '英国外交、联邦与发展办公室制裁局FCDO',
    fields: [
      'nationcode',
      'sanctionscode',
      'sanctionstypecode',
      'sanctionstype',
      'name',
      'sanctionsregulations',
      'sanctionsmeasure',
      'sanctionsreason',
      'designateddate',
      'lastupdatedate',
      'alias',
      'address',
      'sourceurl',
    ],
  },
  '3': {
    name: '美国国土安全部UFLPA实体清单',
    court: '美国国土安全部UFLPA',
    fields: ['nationcode', 'sanctionstypecode', 'sanctionstype', 'name', 'sanctionsregulations', 'sanctionsreason', 'designateddate', 'sourceurl'],
  },
  '4': {
    name: '美国国防部CMCC中国军事公司名单',
    court: '美国国防部CMCC',
    fields: ['nationcode', 'sanctionstypecode', 'sanctionstype', 'name', 'sanctionsregulations', 'designateddate', 'keynolist', 'sourceurl'],
  },
  '5': {
    name: '美国国务院国防贸易管制局禁止名单',
    court: '美国国务院国防贸易管制局',
    fields: ['nationcode', 'sanctionstypecode', 'sanctionstype', 'name', 'sanctionsregulations', 'designateddate', 'federalregisternotice', 'sourceurl'],
  },
  '6': {
    name: '美国BIS实体清单',
    court: '美国商业部工业安全局BIS',
    fields: [
      'nationcode',
      'sanctionstypecode',
      'sanctionstype',
      'name',
      'sanctionsregulations',
      'sanctionsmeasure',
      'sanctionsreason',
      'alias',
      'address',
      'keynolist',
      'federalregisternotice',
      'sourceurl',
    ],
  },
  '7': {
    name: '美国BIS被拒绝人员名单',
    court: '美国商业部工业安全局BIS',
    fields: [
      'nationcode',
      'sanctionstypecode',
      'name',
      'sanctionsregulations',
      'designateddate',
      'address',
      'expirationdate',
      'federalregisternotice',
      'sourceurl',
    ],
  },
  '8': {
    name: '美国BIS未经验证的列表',
    court: '美国商业部工业安全局BIS',
    fields: ['nationcode', 'sanctionstypecode', 'name', 'sanctionsregulations', 'address', 'keynolist', 'federalregisternotice', 'sourceurl'],
  },
  '9': {
    name: '美国BIS军事最终用户列表',
    court: '美国商业部工业安全局BIS',
    fields: ['nationcode', 'sanctionstypecode', 'sanctionstype', 'name', 'sanctionsregulations', 'address', 'keynolist', 'federalregisternotice', 'sourceurl'],
  },
  '10': {
    name: '欧盟金融制裁综合清单',
    court: '欧盟委员会',
    fields: [
      'nationcode',
      'sanctionstypecode',
      'name',
      'sanctionsregulations',
      'sanctionsreason',
      'designateddate',
      'address',
      'expirationdate',
      'startdate',
      'sourceurl',
    ],
  },
  '12': {
    name: '亚投行禁止名单',
    court: '',
    fields: [
      'nationcode',
      'sanctionstypecode',
      'name',
      'sanctionsregulations',
      'sanctionsreason',
      'designateddate',
      'address',
      'expirationdate',
      'startdate',
      'sourceurl',
    ],
  },
  '13': {
    name: '加拿大自治综合制裁名单',
    court: '',
    fields: ['nationcode', 'sanctionstypecode', 'sanctionstype', 'name', 'address', 'sourceurl'],
  },
  '14': {
    name: '联合国安理会综合清单',
    court: '',
    fields: [
      'nationcode',
      'sanctionscode',
      'sanctionstypecode',
      'sanctionstype',
      'name',
      'sanctionsregulations',
      'designateddate',
      'lastupdatedate',
      'remark',
      'alias',
      'address',
      'sourceurl',
    ],
  },
  '15': {
    name: '联合国制裁委员会制裁名单',
    court: '',
    fields: [
      'nationcode',
      'sanctionscode',
      'sanctionstypecode',
      'sanctionstype',
      'name',
      'sanctionsregulations',
      'sanctionsmeasure',
      'designateddate',
      'lastupdatedate',
      'remark',
      'alias',
      'address',
      'sourceurl',
    ],
  },
  '17': {
    name: '澳大利亚综合制裁清单',
    court: '',
    fields: [
      'nationcode',
      'sanctionscode',
      'sanctionstypecode',
      'sanctionstype',
      'name',
      'sanctionsregulations',
      'sanctionsmeasure',
      'remark',
      'alias',
      'address',
      'sourceurl',
    ],
  },
  '18': {
    name: '世界银行不合格公司和个人名单',
    court: '世界银行',
    fields: [
      'nationcode',
      'sanctionscode',
      'sanctionstypecode',
      'sanctionstype',
      'name',
      'sanctionsregulations',
      'sanctionsmeasure',
      'remark',
      'alias',
      'address',
      'sourceurl',
    ],
  },
  '19': {
    name: '美国国务院防扩散制裁',
    court: '美国国务院',
    fields: [
      'nationcode',
      'sanctionscode',
      'sanctionstypecode',
      'sanctionstype',
      'name',
      'sanctionsregulations',
      'sanctionsmeasure',
      'remark',
      'alias',
      'address',
      'sourceurl',
    ],
  },
  '22': {
    name: '欧洲投资银行排除名单',
    court: '欧洲投资银行',
    fields: [
      'nationcode',
      'sanctionscode',
      'sanctionstypecode',
      'sanctionstype',
      'name',
      'sanctionsregulations',
      'sanctionsmeasure',
      'remark',
      'alias',
      'address',
      'sourceurl',
      'expirationdate',
    ],
  },
  '23': {
    name: '日本经济产业省贸易管制最终用户名单',
    court: '日本经济产业省',
    fields: [
      'nationcode',
      'sanctionscode',
      'sanctionstypecode',
      'sanctionstype',
      'name',
      'sanctionsregulations',
      'sanctionsmeasure',
      'remark',
      'alias',
      'address',
      'sourceurl',
      'expirationdate',
    ],
  },
  '24': {
    name: '英国财政部金融制裁目标综合清单',
    court: '英国财政部',
    fields: [
      'nationcode',
      'sanctionscode',
      'sanctionstypecode',
      'sanctionstype',
      'name',
      'sanctionsregulations',
      'sanctionsmeasure',
      'remark',
      'alias',
      'address',
      'sourceurl',
      'expirationdate',
    ],
  },
  '25': {
    name: '美国国务院外国恐怖组织',
    court: '美国国务院',
    fields: [
      'nationcode',
      'sanctionscode',
      'sanctionstypecode',
      'sanctionstype',
      'name',
      'sanctionsregulations',
      'sanctionsmeasure',
      'remark',
      'alias',
      'address',
      'sourceurl',
      'expirationdate',
    ],
  },
};

// 政府采购严重违法失信行为记录source映射表
export const GovernmentPurchaseSourceMapping = {
  // 来源类型映射
  sourceTypes: {
    '1': {
      name: '中国政府采购网',
      sources: ['中国政府采购网'],
    },
    '2': {
      name: '信用中国',
      sources: ['信用中国'],
    },
    '3': {
      name: '信用中国地方源',
      sources: ['信用中国（陕西咸阳）', '信用中国（甘肃白银）', '信用中国（四川绵阳）', '信用中国（湖南长沙）', '信用中国（北京）', '信用中国（甘肃定西）'],
    },
    '4': {
      name: '其他',
      sources: [
        '军队采购网',
        '江苏政府采购-江苏政府购买服务信息平台',
        '中国湖北政府采购网-湖北政府购买服务信息平台',
        '财政部',
        '福建省政府采购网-中国政府采购网分网',
        '四川政府采购网-中国政府采购网四川分网',
        '国家医疗保障局',
      ],
    },
  },

  // 获取指定类型对应的所有来源
  getSourcesByType: (types: string[] | number[] | string | number): string[] => {
    // 统一处理为数组
    const typeArray = Array.isArray(types) ? types : [types];
    const sources = [];

    typeArray.forEach((type) => {
      const typeStr = String(type);
      if (GovernmentPurchaseSourceMapping.sourceTypes[typeStr]?.sources) {
        sources.push(...GovernmentPurchaseSourceMapping.sourceTypes[typeStr].sources);
      }
    });

    return sources;
  },
};

// 拖欠农民工工资黑名单source映射表
export const MigrantWorkersSourceMapping = {
  // 来源类型映射
  sourceTypes: {
    '1': {
      name: '信用中国',
      sources: ['信用中国'],
    },
    '2': {
      name: '信用中国地方源',
      sources: [
        '信用中国（河北石家庄）',
        '信用中国（陕西咸阳）',
        '信用长宁',
        '信用中国（山西）',
        '信用中国（广东珠海）',
        '信用中国（四川雅安）',
        '信用中国（浙江温州）',
        '信用中国（安徽蚌埠）',
        '信用中国（新疆·吐鲁番）',
        '信用中国（甘肃）',
        '信用中国（江西南昌）',
        '信用中国（河北保定）',
        '信用中国（河南洛阳）',
        '信用中国（浙江杭州）',
        '信用中国（甘肃酒泉）',
        '信用中国（江西九江）',
        '信用中国（安徽池州）',
        '云南省交通运输厅-信用交通·云南',
        '信用中国（云南昆明）',
        '信用中国（湖南湘潭）',
        '信用中国(安徽）',
        '信用中国（广西贵港）',
        '信用中国（甘肃嘉峪关）',
        '信用中国（湖南常德）',
        '信用中国（四川资阳）',
        '信用中国（贵州）',
        '信用中国（四川南充）',
        '信用中国（安徽宿州）',
        '信用中国（宁夏银川）',
        '信用中国（四川·眉山）',
        '信用中国（河南·新乡）',
        '信用中国四川·泸州',
        '信用中国（湖南）',
        '信用中国（广西梧州）',
        '信用中国（河南信阳）',
        '信用中国（贵州铜仁）',
        '信用中国（云南）',
        '信用中国（山西忻州）',
        '信用中国（陕西）',
        '信用中国（山东济南）',
        '信用中国（辽宁大连）',
        '信用中国（辽宁锦州）',
        '信用交通·江西',
        '信用中国（四川德阳）',
        '信用中国（安徽黄山）',
        '信用中国（陕西西安）',
        '信用中国（甘肃庆阳）',
        '信用中国（湖南岳阳）',
        '信用中国（湖南邵阳）',
        '信用中国（广东湛江）',
        '信用中国（安徽芜湖）',
        '信用中国（河南三门峡）',
        '信用中国（甘肃天水）',
        '信用中国（广东佛山）',
        '信用中国（云南丽江）',
        '信用中国（广东惠州）',
        '信用中国（浙江宁波）',
        '信用中国（内蒙古乌兰察布）',
        '信用中国（浙江舟山）',
        '信用中国（浙江衢州）',
        '信用中国（云南曲靖）',
        '信用中国（内蒙古包头）',
        '信用中国（天津）',
        '信用中国（内蒙古通辽）',
        '信用中国（内蒙古巴彦淖尔）',
        '信用丰台',
        '信用中国（贵州毕节）',
        '信用中国（陕西延安）',
        '信用中国（内蒙古）',
        '信用中国（天津蓟州）',
        '信用中国（河北邢台）',
        '信用中国（江西）',
        '信用中国（内蒙古呼和浩特）',
        '信用中国（江苏常州）',
        '信用中国（湖南湘西）',
        '信用中国（福建）',
        '信用中国（内蒙古锡林郭勒）',
        '信用中国（广东中山）',
        '信用中国（甘肃兰州）',
        '信用中国（广东阳江）',
        '信用中国（山西晋中）',
        '信用中国（辽宁辽阳）',
        '信用中国（双鸭山）',
        '信用中国（广东·深圳）',
        '信用中国（安徽·阜阳）',
        '信用中国（陕西商洛）',
        '信用中国（上海·青浦）',
        '信用中国（河北·沧州）',
        '信用中国（山西太原）',
        '信用中国（浙江）',
        '信用中国（黑龙江）',
        '信用中国（青海）',
        '信用中国（吉林）',
        '信用中国（安徽六安）',
        '信用中国（山西朔州）',
        '信用中国（广东·东莞）',
        '信用中国（吉林白山）',
        '信用中国-广西·柳州',
        '信用中国（福建厦门）',
        '信用中国（云南玉溪）',
        '信用中国（辽宁）',
        '信用中国（陕西安康）',
        '信用中国（广东韶关）',
        '信用中国（山东）',
        '信用中国（北京）',
        '信用中国（福建福州）',
        '信用中国（陕西汉中）',
        '信用中国（湖南长沙）',
        '信用中国（湖南濮阳）',
        '信用中国（湖南郴州）',
        '信用中国（湖北·咸宁）',
        '信用中国（山西晋城）',
        '信用中国（广东汕尾）',
        '信用河北',
        '信用中国（辽宁葫芦岛）',
        '信用中国（河南）',
        '信用中国（江门）',
        '信用中国（湖南衡阳）',
        '信用中国（山西运城）',
        '信用中国（四川达州）',
        '信用中国（河北张家口）',
        '信用中国（辽宁丹东）',
      ],
    },
    '3': {
      name: '人力资源和社会保障地方源',
      sources: [
        '江苏省人力资源和社会保障厅',
        '宁夏回族自治区人力资源和社会保障厅',
        '甘肃省人力资源和社会保障厅',
        '广西壮族自治区人力资源和社会保障厅',
        '山西省人力资源和社会保障厅',
        '北京市人力资源和社会保障局',
        '广西省人力资源和社会保障厅',
        '安徽省人力资源和社会保障厅',
        '广东省人力资源和社会保障厅',
        '浙江省人力资源和社会保障厅',
        '云南人力资源和社会保障网',
        '吉林省人力资源和社会保障厅',
        '福建省人力资源和社会保障厅',
        '西藏自治区人力资源和社会保障厅',
        '陕西省人力资源和社会保障厅',
        '河北省人力资源和社会保障厅',
        '青海省人力资源和社会保障厅',
        '贵州省人力资源和社会保障厅',
        '云南省人力资源和社会保障网',
        '四川省人力资源和社会保障厅',
        '江西省人力资源和社会保障厅',
        '新疆生产建设兵团人力资源和社会保障局',
        '泉州市人力资源和社会保障局',
        '包头市人力资源和社会保障局',
        '海南省人力资源和社会保障厅',
        '内蒙古自治区人力资源和社会保障厅',
        '湖南省人力资源和社会保障厅',
        '新疆维吾尔自治区人力资源和社会保障厅',
        '河南省人力资源和社会保障厅',
        '上海市人力资源和社会保障局',
      ],
    },
    '4': {
      name: '其他',
      sources: [
        '广西百色市人民政府',
        '湖北省建筑市场监督与诚信一体化平台',
        '黄山市人民政府',
        '全国企业采购交易寻源询价系统',
        '天津市武清区人民政府',
        '北京市密云区人民政府',
        '山东省公共资源交易网',
        '新闻提取',
        '濮阳市人民政府',
        '',
      ],
    },
  },

  // 获取指定类型对应的所有来源
  getSourcesByType: (types: string[] | number[] | string | number): string[] => {
    // 统一处理为数组
    const typeArray = Array.isArray(types) ? types : [types];
    const sources = [];

    typeArray.forEach((type) => {
      const typeStr = String(type);
      if (MigrantWorkersSourceMapping.sourceTypes[typeStr]?.sources) {
        sources.push(...MigrantWorkersSourceMapping.sourceTypes[typeStr].sources);
      }
    });

    return sources;
  },
};

// 重点行业领域监管黑名单类型映射表
export const KeyIndustrySupervisionMapping = {
  // 黑名单类型映射 - 使用类型编号作为键
  listTypeMapping: {
    '1': {
      name: '拖欠农民工工资黑名单',
      codes: ['5'],
    },
    '2': {
      name: '运输物流行业失信黑名单',
      codes: ['28', '1'],
    },
    '3': {
      name: '建筑工程领域黑名单',
      codes: ['33', '34', '69'],
    },
    '4': {
      name: '医疗医药领域黑名单',
      codes: ['39', '40', '41', '68', '79'],
    },
    '5': {
      name: '招投标活动失信行为企业',
      codes: ['56', '80'],
    },
    '6': {
      name: '消防安全领域黑名单',
      codes: ['46'],
    },
    '7': {
      name: '食品安全严重违法生产经营者黑名单',
      codes: ['97'],
    },
    '8': {
      name: '其他行业领域黑名单',
      codes: [
        '4',
        '54',
        '3',
        '24',
        '38',
        '55',
        '69',
        '98',
        '11',
        '8',
        '16',
        '29',
        '30',
        '32',
        '35',
        '36',
        '37',
        '44',
        '47',
        '48',
        '49',
        '50',
        '51',
        '52',
        '53',
        '99',
        '106',
        '108',
        '105',
      ],
    },
  },

  // 获取指定类型对应的所有code
  getCodesByType: (types: string[] | number[] | string | number): string[] => {
    // 统一处理为数组
    const typeArray = Array.isArray(types) ? types : [types];
    const codes = [];

    typeArray.forEach((type) => {
      const typeStr = String(type);
      if (KeyIndustrySupervisionMapping.listTypeMapping[typeStr]?.codes) {
        codes.push(...KeyIndustrySupervisionMapping.listTypeMapping[typeStr].codes);
      }
    });

    return codes;
  },
};
