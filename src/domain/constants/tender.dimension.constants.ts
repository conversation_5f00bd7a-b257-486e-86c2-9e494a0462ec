import { DimensionRiskLevelEnum } from '@domain/enums/diligence/DimensionRiskLevelEnum';
import { DimensionSourceEnums } from '@domain/enums/diligence/DimensionSourceEnums';
import { DimensionLevel2Enums } from '@domain/enums/diligence/DimensionLevel2Enums';
import { IndicatorTypeEnums } from '@domain/model/settings/IndicatorTypeEnums';
import { DimensionLevel3Enums } from '@domain/enums/diligence/DimensionLevel3Enums';
import { DimensionGroupItems } from '@domain/model/diligence/pojo/dimension/group/DimensionGroupItems';
import { DimensionLevel1Enums } from '@domain/enums/diligence/DimensionLevel1Enums';
import { QueryParamsEnums } from '@domain/model/diligence/pojo/dimension/dimension.filter.params';
import { ConfigurationTenderRiskDimension } from '@domain/model/bidding/ConfigurationTenderRiskDimensionPo';
import { DetailsParamEnums } from '@domain/enums/diligence/DetailsParamEnums';
import { RelationTypeConst } from './model.constants';
import { ExcludeNodeTypeEnums } from '@domain/enums/diligence/ExcludeNodeTypeEnums';
import { CompanyRelationType } from '@domain/enums/tender/DiligenceHistoryEnum';

// 招标排查设置中心配置页展示信息
export const TenderSettings: ConfigurationTenderRiskDimension[] = [
  {
    // 招标排查企业数量设置
    key: DimensionLevel2Enums.BiddingCompanyCountSetting,
    status: 1,
    subDimensionList: [
      {
        // 单次招标排查企业上传数量上限设置
        key: DimensionLevel2Enums.BiddingCompanyUploadMaxCount,
        value: 20,
        status: 1,
      },
    ],
    sort: 1,
  },
  {
    // 招标排查资质筛查设置
    key: DimensionLevel2Enums.BiddingCompanyCertification,
    status: 0,
    sort: 2,
  },
  {
    key: DimensionLevel2Enums.BiddingCompanyRelation,
    status: 1,
    sort: 3,
    subDimensionList: [
      {
        key: DimensionLevel2Enums.BiddingCompanyRelationship,
        status: 1,
        depth: 5,
        types: [
          { key: 'Legal', name: '法定代表人', status: 1 },
          { key: 'Employ', name: '董监高', status: 1 },
          { key: 'Invest', name: '持股/投资关联', status: 1 },
          { key: 'HisLegal', name: '历史法定代表人', status: 1 },
          { key: 'HisEmploy', name: '历史董监高', status: 1 },
          { key: 'HisInvest', name: '历史持股/投资关联', status: 1 },
          { key: 'Branch', name: '分支机构', status: 1 },
          { key: 'ActualController', name: '实际控制人', status: 1 },
          { key: 'ControlRelation', name: '控制关系', status: 1 },
          { key: 'FinalBenefit', name: '受益所有人', status: 1 },
        ],
        percentage: 0.01,
        excludedTypes: [ExcludeNodeTypeEnums.InvestmentAgency], // 默认排除投资机构
      },
      {
        key: DimensionLevel2Enums.ContactRelationship,
        status: 1,
        types: [
          { key: DetailsParamEnums.HasPhone, name: '相同电话号码', status: 1 },
          { key: DetailsParamEnums.HasEmail, name: '相同邮箱', status: 1 }, // mode: 1-模糊匹配，2-精确匹配
          { key: DetailsParamEnums.HasAddress, name: '相同地址', status: 1 },
        ],
      },
      {
        key: DimensionLevel2Enums.BiddingCompanyRelationship2,
        status: 1,
        types: [
          // { key: CompanyRelationType.ContactNumber, name: '相同电话号码', status: 1 },
          { key: CompanyRelationType.Mail, name: '疑似相同邮箱', status: 1 }, // mode: 1-模糊匹配，2-精确匹配
          { key: CompanyRelationType.Address, name: '疑似相同地址', status: 1 },
          { key: CompanyRelationType.Guarantor, name: '相互担保关联', status: 1 },
          { key: CompanyRelationType.EquityPledge, name: '股权出质关联', status: 1 },
          { key: CompanyRelationType.ChattelMortgage, name: '动产抵押关联', status: 1 },
          { key: CompanyRelationType.UpAndDownRelation, name: '上下游关联', status: 1 },
          { key: CompanyRelationType.BidCollusive, name: '围串标关联', status: 1 },
          { key: CompanyRelationType.Website, name: '相同域名信息', status: 1 },
          { key: CompanyRelationType.Patent, name: '相同专利信息', status: 1 },
          { key: CompanyRelationType.IntPatent, name: '相同国际专利信息', status: 1 },
          { key: CompanyRelationType.SoftwareCopyright, name: '相同软件著作权', status: 1 },
          { key: CompanyRelationType.Case, name: '相同司法案件', status: 1 },
          { key: CompanyRelationType.SameNameEmployee, name: '疑似同名主要人员', status: 0 },
        ],
        excludedTypes: [ExcludeNodeTypeEnums.SuspectedBookKeeping], // 默认排除疑似代记账
        // mailMatchingMode: 1, // 默认模糊匹配邮箱
        // addressMatchingMode: 1, // 默认模糊匹配地址
      },
    ],
  },
  {
    // 共同投标分析
    key: DimensionLevel2Enums.JointBiddingAnalysis,
    status: 1,
    sort: 5,
  },
  {
    key: DimensionLevel1Enums.Risk_InnerBlacklist,
    status: 1,
    sort: 6,
    types: [
      { key: DetailsParamEnums.HitInnerBlackList, name: '被列入内部黑名单', status: 1 },
      { key: DetailsParamEnums.Invest, name: '持股/投资关联', status: 1 },
      // { key: DetailsParamEnums.Shareholder, name: '参股股东（企业）被列入内部黑名单', status: 1 },
      { key: DetailsParamEnums.Employ, name: '董监高/法人关联', status: 1 },
      { key: DetailsParamEnums.ActualController, name: '相同实际控制人', status: 1 },
      { key: DetailsParamEnums.FinalBenefit, name: '相同受益所有人', status: 1 },
      { key: DetailsParamEnums.HisInvest, name: '历史持股/投资关联', status: 1 },
      // { key: DetailsParamEnums.HisShareholder, name: '历史参股股东', status: 1 },
      { key: DetailsParamEnums.HisEmploy, name: '历史董监高/法人关联', status: 1 },
      {
        key: DetailsParamEnums.Branch,
        name: RelationTypeConst[DetailsParamEnums.Branch],
        status: 1,
      },
      {
        key: DetailsParamEnums.Hold,
        name: RelationTypeConst[DetailsParamEnums.Hold],
        status: 1,
      },
      { key: DetailsParamEnums.Guarantee, name: RelationTypeConst[DetailsParamEnums.Guarantee], status: 0 },
      { key: DetailsParamEnums.EquityPledgeRelation, name: RelationTypeConst[DetailsParamEnums.EquityPledgeRelation], status: 0 },
      { key: DetailsParamEnums.HasPhone, name: RelationTypeConst[DetailsParamEnums.HasPhone], status: 0 },
      { key: DetailsParamEnums.HasAddress, name: RelationTypeConst[DetailsParamEnums.HasAddress], status: 0 },
      { key: DetailsParamEnums.HasEmail, name: RelationTypeConst[DetailsParamEnums.HasEmail], status: 0 },
    ],
    detailsParams: [
      {
        field: QueryParamsEnums.depth,
        fieldName: '关联层级',
        fieldVal: 1,
      },
      {
        field: QueryParamsEnums.excludedTypes,
        fieldVal: [ExcludeNodeTypeEnums.Revocation], // 默认排除已吊销、注销企业
      },
      {
        field: QueryParamsEnums.percentage,
        fieldVal: 0.01,
      },
    ],
  },
  {
    //潜在商业利益冲突
    key: DimensionLevel1Enums.Risk_InterestConflict,
    status: 1,
    sort: 7,
    detailsParams: [
      {
        field: QueryParamsEnums.dataRange,
        fieldVal: [
          {
            key: DetailsParamEnums.Groups,
            keyName: '人员分组',
            value: [],
            nameList: [], //分组名称
          },
        ],
      },
    ],
    types: [
      { key: 'StaffWorkingOutsideForeignInvestment', name: '潜在利益冲突-投资任职', status: 1 },
      { key: 'StaffSameName', name: '疑似潜在利益冲突-疑似同名', status: 1 },
      { key: 'StaffSamePhone', name: '疑似潜在利益冲突-相同联系方式', status: 1 },
    ],
  },
  {
    key: DimensionLevel2Enums.PurchaseIllegal,
    status: 1,
    sort: 4,
    cycle: 3,
    subDimensionList: [
      {
        key: DimensionLevel2Enums.CompanyCredit,
        status: 1,
        cycle: 3,
      },
      {
        key: DimensionLevel3Enums.BusinessAbnormal3,
        status: 1,
        cycle: 3,
      },
      {
        key: DimensionLevel3Enums.PersonCreditCurrent,
        status: 1,
        cycle: 3,
      },
      {
        key: DimensionLevel2Enums.TaxationOffences,
        status: 1,
        cycle: 3,
      },
      {
        key: DimensionLevel3Enums.MigrantWorkers,
        status: 1,
        cycle: 3,
        detailsParams: [
          {
            field: QueryParamsEnums.source,
            fieldVal: [1, 2, 3, 4],
          },
        ],
      },
      {
        key: DimensionLevel3Enums.GovernmentPurchaseIllegal,
        status: 1,
        cycle: 3,
        detailsParams: [
          {
            field: QueryParamsEnums.source,
            fieldVal: [1, 2, 3, 4],
          },
        ],
      },
      {
        key: DimensionLevel3Enums.ArmyProcurementIllegal,
        status: 1,
        cycle: 3,
      },
      {
        key: DimensionLevel3Enums.ArmyProcurementSuspended,
        status: 1,
        cycle: 3,
      },
      {
        key: DimensionLevel2Enums.FakeSOES,
        status: 1,
        cycle: 3,
      },
      {
        key: DimensionLevel3Enums.GovProcurementIllegal,
        status: 1,
        cycle: 3,
      },
      {
        key: DimensionLevel2Enums.BidAdministrativePenalties,
        status: 1,
        cycle: 3,
      },
      {
        key: DimensionLevel2Enums.BidAdministrativeJudgement,
        status: 1,
        cycle: 3,
      },
    ],
  },
];
/**
 * 招标排查执行过程中使用的相关维度模板
 */
export const TenderDimensions: DimensionGroupItems = {
  name: '招标排查',
  status: 1,
  sort: 1,
  items: [
    {
      key: DimensionLevel2Enums.BiddingCompanyCertification,
      name: '资质筛查',
      strategyModel: {
        boost: 1.0,
        baseScore: 5,
        level: DimensionRiskLevelEnum.Medium,
      },
      isVirtualDimension: 0,
      status: 0,
      template: '#companyNames##count#项资质缺失或失效',
      template2: '#companyNames##count#项资质缺失或失效',
      sort: 2,
      type: IndicatorTypeEnums.generalItems,
      description: '资质筛查',
    },
    {
      key: DimensionLevel2Enums.BiddingCompanyRelation,
      name: '关联关系排查',
      strategyModel: {
        boost: 1.0,
        baseScore: 5,
        level: DimensionRiskLevelEnum.Medium,
      },
      isVirtualDimension: 0,
      status: 1,
      template: '<em class="#level#">【#name#】 #count#条记录</em>',
      template2: '被列入#name# #count#条记录',
      sort: 3,
      type: IndicatorTypeEnums.generalItems,
      description: '关联关系排查',
      subDimensionList: [
        {
          key: DimensionLevel2Enums.BiddingCompanyRelationship,
          name: '投资任职关联',
          strategyModel: {
            boost: 1.0,
            baseScore: 5,
            level: DimensionRiskLevelEnum.Medium,
            sortField: { field: 'publishdate', order: 'DESC' },
            detailsParams: [
              {
                field: QueryParamsEnums.types,
                fieldVal: ['Legal', 'Employ', 'Invest', 'HisLegal', 'HisEmploy', 'HisInvest', 'Branch', 'ActualController', 'ControlRelation', 'FinalBenefit'],
              },
              {
                field: QueryParamsEnums.depth,
                fieldVal: 5,
              },
              {
                field: QueryParamsEnums.percentage,
                fieldVal: 0.01,
              },
              {
                field: QueryParamsEnums.excludedTypes,
                fieldVal: [ExcludeNodeTypeEnums.InvestmentAgency], // 默认排除投资机构
              },
            ],
          },
          isVirtualDimension: 0,
          source: DimensionSourceEnums.Tender,
          sourcePath: '',
          status: 1,
          template: '#companyNames#存在投资任职关联',
          template2: '与#companyNames#存在投资任职关联',
          sort: 1,
          description: '投资任职关联',
          type: IndicatorTypeEnums.generalItems,
        },
        {
          key: DimensionLevel2Enums.ContactRelationship,
          name: '联系方式关联',
          strategyModel: {
            boost: 1.0,
            baseScore: 5,
            level: DimensionRiskLevelEnum.Medium,
            detailsParams: [
              {
                field: QueryParamsEnums.types,
                fieldVal: [DetailsParamEnums.HasPhone, DetailsParamEnums.HasEmail, DetailsParamEnums.HasAddress],
              },
            ],
          },
          template: '#companyNames#存在联系方式关联',
          template2: '与#companyNames#存在联系方式关联',
          sort: 2,
          description: '联系方式关联',
          type: IndicatorTypeEnums.generalItems,
          status: 1,
        },
        {
          key: DimensionLevel2Enums.BiddingCompanyRelationship2,
          name: '其他关联',
          strategyModel: {
            boost: 1.0,
            baseScore: 5,
            level: DimensionRiskLevelEnum.Medium,
            detailsParams: [
              {
                field: QueryParamsEnums.types,
                fieldVal: [
                  // CompanyRelationType.ContactNumber,
                  CompanyRelationType.Mail,
                  CompanyRelationType.Address,
                  CompanyRelationType.Guarantor,
                  CompanyRelationType.EquityPledge,
                  CompanyRelationType.ChattelMortgage,
                  CompanyRelationType.UpAndDownRelation,
                  CompanyRelationType.BidCollusive,
                  CompanyRelationType.Website,
                  CompanyRelationType.Patent,
                  CompanyRelationType.IntPatent,
                  CompanyRelationType.SoftwareCopyright,
                  CompanyRelationType.Case,
                  CompanyRelationType.SameNameEmployee,
                ],
              },
              {
                field: QueryParamsEnums.excludedTypes, // 疑似关系默认排除节点
                fieldVal: [ExcludeNodeTypeEnums.SuspectedBookKeeping],
              },
              // {
              //   field: QueryParamsEnums.mailMatchingMode, // 邮箱匹配模式，1-模糊匹配，2-精确匹配
              //   fieldVal: 1,
              // },
              // {
              //   field: QueryParamsEnums.addressMatchingMode, // 地址匹配模式，1-模糊匹配，2-精确匹配
              //   fieldVal: 1,
              // },
            ],
          },
          isVirtualDimension: 0,
          status: 1,
          template: '#companyNames#存在其他关联',
          template2: '与#companyNames#存在其他关联',
          sort: 3,
          description: '其他关联',
          type: IndicatorTypeEnums.generalItems,
        },
      ],
    },
    {
      key: DimensionLevel2Enums.JointBiddingAnalysis,
      name: '共同投标分析',
      strategyModel: {
        boost: 1.0,
        baseScore: 5,
        level: DimensionRiskLevelEnum.Medium,
        sortField: { field: 'publishdate', order: 'DESC' },
        detailsParams: [
          // 不开放数据范围控制，默认当前有效
          // {
          //   field: QueryParamsEnums.isValid,
          //   fieldVal: '1',
          // },
        ],
      },
      isVirtualDimension: 0,
      source: DimensionSourceEnums.Tender,
      sourcePath: '',
      status: 1,
      template: '#companyNames#历史投标记录分析存在【异常中标率】',
      template2: '与#companyNames#历史投标记录分析存在【异常中标率】',
      sort: 5,
      description: '异常中标率',
      type: IndicatorTypeEnums.generalItems,
    },
    {
      key: DimensionLevel1Enums.Risk_InnerBlacklist,
      name: '内部黑名单',
      strategyModel: {
        boost: 1.0,
        baseScore: 5,
        level: DimensionRiskLevelEnum.High,
      },
      isVirtualDimension: 1,
      source: DimensionSourceEnums.Rover,
      sourcePath: '',
      status: 1,
      template: '#companyNames#与内部黑名单存在关联关系',
      template2: '与内部黑名单#companyNames#存在关联关系',
      sort: 6,
      description: '与内部黑名单存在关联关系',
      type: IndicatorTypeEnums.generalItems,
      subDimensionList: [
        {
          key: DimensionLevel2Enums.HitInnerBlackList,
          name: '被列入内部黑名单',
          strategyModel: {
            boost: 1.0,
            baseScore: 5,
            level: DimensionRiskLevelEnum.High,
            detailsParams: [
              {
                field: QueryParamsEnums.isValid,
                fieldVal: '-1',
              },
            ],
          },
          isVirtualDimension: 0,
          source: DimensionSourceEnums.Rover,
          sourcePath: '',
          status: 1,
          template: '<em class="#level#">【#name#】 #count#条记录</em> ',
          template2: '被列入内部黑名单#count#条记录',
          sort: 1,
          type: IndicatorTypeEnums.generalItems,
          description: '目标主体是否被列入内部黑名单',
        },
        // {
        //   key: DimensionLevel2Enums.Shareholder,
        //   name: '参股股东被列入内部黑名单',
        //   strategyModel: {
        //     boost: 1.0,
        //     baseScore: 5,
        //     level: DimensionRiskLevelEnum.High,
        //     detailsParams: [
        //       {
        //         field: QueryParamsEnums.isValid,
        //         fieldVal: '-1',
        //       },
        //     ],
        //   },
        //   isVirtualDimension: 0,
        //   source: DimensionSourceEnums.Rover,
        //   sourcePath: '',
        //   status: 1,
        //   template: '<em class="#level#">【#name#】 #count#条记录</em>',
        //   template2: '参股股东被列入内部黑名单#count#条记录',
        //   sort: 2,
        //   type: IndicatorTypeEnums.generalItems,
        //   description: '目标主体持股股东是否被列入内部黑名单',
        // },
        {
          key: DimensionLevel2Enums.ForeignInvestment,
          name: '对外投资主体被列入内部黑名单',
          strategyModel: {
            boost: 1.0,
            baseScore: 5,
            level: DimensionRiskLevelEnum.High,
            detailsParams: [
              {
                field: QueryParamsEnums.isValid,
                fieldVal: '-1',
              },
            ],
          },
          isVirtualDimension: 0,
          source: DimensionSourceEnums.Rover,
          sourcePath: '',
          status: 1,
          template: '<em class="#level#">【#name#】 #count#条记录</em>',
          template2: '对外投资主体被列入内部黑名单#count#条记录',
          sort: 3,
          type: IndicatorTypeEnums.generalItems,
          description: '目标主体对外投资主体是否被列入内部黑名单',
        },
        {
          key: DimensionLevel2Enums.EmploymentRelationship,
          name: '与内部黑名单列表存在人员关联',
          strategyModel: {
            boost: 1.0,
            baseScore: 5,
            level: DimensionRiskLevelEnum.High,
            detailsParams: [
              {
                field: QueryParamsEnums.isValid,
                fieldVal: '-1',
              },
            ],
          },
          isVirtualDimension: 0,
          source: DimensionSourceEnums.Rover,
          sourcePath: '',
          status: 1,
          template: '<em class="#level#">【#name#】 #count#条记录</em>',
          template2: '与黑名单（内部）存在人员任职关联#count#条记录',
          sort: 4,
          type: IndicatorTypeEnums.generalItems,
          description: '目标主体主要任职人员是否被列入内部黑名单',
        },
        {
          key: DimensionLevel2Enums.BlacklistSameSuspectedActualController,
          name: '与内部黑名单列表存在相同实际控制人关联',
          strategyModel: {
            boost: 1.0,
            baseScore: 5,
            level: DimensionRiskLevelEnum.High,
          },
          isVirtualDimension: 0,
          source: DimensionSourceEnums.Rover,
          sourcePath: '',
          status: 1,
          template: '<em class="#level#">【#name#】 #count#条记录</em>',
          template2: '与黑名单（内部）存在相同实际控制人关联#count#条记录',
          sort: 5,
          type: IndicatorTypeEnums.generalItems,
          description: '目标主体与内部黑名单存在相同实际控制人关联',
        },
        {
          key: DimensionLevel2Enums.BlacklistSameFinalBenefit,
          name: '与内部黑名单列表存在相同受益所有人关联',
          strategyModel: {
            boost: 1.0,
            baseScore: 5,
            level: DimensionRiskLevelEnum.High,
          },
          isVirtualDimension: 0,
          source: DimensionSourceEnums.Rover,
          sourcePath: '',
          status: 1,
          template: '<em class="#level#">【#name#】 #count#条记录</em>',
          template2: '与黑名单（内部）存在相同受益所有人关联#count#条记录',
          sort: 5,
          type: IndicatorTypeEnums.generalItems,
          description: '目标主体与内部黑名单存在相同受益所有人关联',
        },
        {
          key: DimensionLevel2Enums.CompanyBranch,
          name: '与内部黑名单列表存在分支机构关联',
          strategyModel: {
            boost: 1.0,
            baseScore: 5,
            level: DimensionRiskLevelEnum.High,
          },
          isVirtualDimension: 0,
          source: DimensionSourceEnums.Rover,
          sourcePath: '',
          status: 1,
          template: '<em class="#level#">【#name#】 #count#条记录</em>',
          template2: '与内部黑名单列表存在分支机构关联#count#条记录',
          sort: 5,
          type: IndicatorTypeEnums.generalItems,
          description: '与内部黑名单列表存在分支机构关联',
        },
        {
          key: DimensionLevel2Enums.Hold,
          name: '与内部黑名单列表存在控制关系关联',
          strategyModel: {
            boost: 1.0,
            baseScore: 5,
            level: DimensionRiskLevelEnum.High,
          },
          isVirtualDimension: 0,
          source: DimensionSourceEnums.Rover,
          sourcePath: '',
          status: 1,
          template: '<em class="#level#">【#name#】 #count#条记录</em>',
          template2: '与内部黑名单列表存在控制关系关联#count#条记录',
          sort: 5,
          type: IndicatorTypeEnums.generalItems,
          description: '与内部黑名单列表存在控制关系关联',
        },
        {
          key: DimensionLevel2Enums.Guarantee,
          name: '与内部黑名单列表存在担保关系关联',
          strategyModel: {
            boost: 1.0,
            baseScore: 5,
            level: DimensionRiskLevelEnum.High,
          },
          isVirtualDimension: 0,
          source: DimensionSourceEnums.Rover,
          sourcePath: '',
          status: 1,
          template: '<em class="#level#">【#name#】 #count#条记录</em>',
          template2: '与内部黑名单列表存在担保关系关联#count#条记录',
          sort: 5,
          type: IndicatorTypeEnums.generalItems,
          description: '与内部黑名单列表存在担保关系关联',
        },
        {
          key: DimensionLevel2Enums.EquityPledgeRelation,
          name: '与内部黑名单列表存在股权出质关联',
          strategyModel: {
            boost: 1.0,
            baseScore: 5,
            level: DimensionRiskLevelEnum.High,
          },
          isVirtualDimension: 0,
          source: DimensionSourceEnums.Rover,
          sourcePath: '',
          status: 1,
          template: '<em class="#level#">【#name#】 #count#条记录</em>',
          template2: '与内部黑名单列表存在股权出质关联#count#条记录',
          sort: 5,
          type: IndicatorTypeEnums.generalItems,
          description: '与内部黑名单列表存在股权出质关联',
        },
        {
          key: DimensionLevel2Enums.HasPhone,
          name: '与内部黑名单列表存在相同电话号码关联',
          strategyModel: {
            boost: 1.0,
            baseScore: 5,
            level: DimensionRiskLevelEnum.High,
          },
          isVirtualDimension: 0,
          source: DimensionSourceEnums.Rover,
          sourcePath: '',
          status: 1,
          template: '<em class="#level#">【#name#】 #count#条记录</em>',
          template2: '与内部黑名单列表存在相同电话号码关联#count#条记录',
          sort: 5,
          type: IndicatorTypeEnums.generalItems,
          description: '与内部黑名单列表存在相同电话号码关联',
        },
        {
          key: DimensionLevel2Enums.HasAddress,
          name: '与内部黑名单列表存在相同地址关联',
          strategyModel: {
            boost: 1.0,
            baseScore: 5,
            level: DimensionRiskLevelEnum.High,
          },
          isVirtualDimension: 0,
          source: DimensionSourceEnums.Rover,
          sourcePath: '',
          status: 1,
          template: '<em class="#level#">【#name#】 #count#条记录</em>',
          template2: '与内部黑名单列表存在相同地址关联#count#条记录',
          sort: 5,
          type: IndicatorTypeEnums.generalItems,
          description: '与内部黑名单列表存在相同地址关联',
        },
        {
          key: DimensionLevel2Enums.HasEmail,
          name: '与内部黑名单列表存在相同邮箱关联',
          strategyModel: {
            boost: 1.0,
            baseScore: 5,
            level: DimensionRiskLevelEnum.High,
          },
          isVirtualDimension: 0,
          source: DimensionSourceEnums.Rover,
          sourcePath: '',
          status: 1,
          template: '<em class="#level#">【#name#】 #count#条记录</em>',
          template2: '与内部黑名单列表存在相同邮箱关联#count#条记录',
          sort: 5,
          type: IndicatorTypeEnums.generalItems,
          description: '与内部黑名单列表存在相同邮箱关联',
        },
      ],
    },
    {
      key: DimensionLevel1Enums.Risk_InterestConflict,
      name: '潜在利益冲突',
      strategyModel: {
        level: DimensionRiskLevelEnum.Medium,
        detailsParams: [],
      },
      isVirtualDimension: 1,
      source: DimensionSourceEnums.Rover,
      sourcePath: '',
      status: 1,
      template: '',
      template2: '',
      sort: 7,
      type: IndicatorTypeEnums.generalItems,
      description: '潜在利益冲突',
      subDimensionList: [
        {
          key: DimensionLevel3Enums.StaffWorkingOutsideForeignInvestment,
          name: '潜在利益冲突-投资任职',
          strategyModel: {
            boost: 1.0,
            baseScore: 5,
            level: DimensionRiskLevelEnum.Medium,
            detailsParams: [
              {
                field: QueryParamsEnums.isValid,
                fieldVal: '-1', //当前有效
              },
            ],
          },
          isVirtualDimension: 0,
          source: DimensionSourceEnums.Rover,
          sourcePath: '',
          status: 1,
          template: '#companyNames#的主要人员与本公司内部员工存在【股权/任职关联】',
          template2: '与内部员工<em class="high">#personNames#</em>持股/投资企业存在关联关系',
          sort: 1,
          type: IndicatorTypeEnums.generalItems,
          description: '交叉持股任职',
        },
        {
          key: DimensionLevel3Enums.SuspectedInterestConflict,
          name: '疑似潜在利益冲突',
          strategyModel: {
            boost: 1.0,
            baseScore: 5,
            level: DimensionRiskLevelEnum.Medium,
            detailsParams: [
              {
                field: QueryParamsEnums.isValid,
                fieldVal: '-1', //当前有效
              },
              {
                field: QueryParamsEnums.types,
                fieldVal: ['SameName', 'SamePhone'],
              },
            ],
          },
          isVirtualDimension: 0,
          source: DimensionSourceEnums.Rover,
          sourcePath: '',
          status: 1,
          template: '#companyNames#的主要人员与本公司内部员工存在【疑似同名】、【相同联系方式】',
          template2: '与内部员工<em class="high">#personNames#</em>存在#dimension#',
          sort: 2,
          type: IndicatorTypeEnums.generalItems,
          description: '潜在利益冲突',
        },
      ],
    },
    {
      key: DimensionLevel2Enums.PurchaseIllegal,
      name: '涉采购不良行为',
      strategyModel: {
        boost: 1.0,
        baseScore: 5,
        level: DimensionRiskLevelEnum.Medium,
      },
      isVirtualDimension: 0,
      source: DimensionSourceEnums.Credit,
      sourcePath: '/api/search/search-credit',
      status: 1,
      template: '#companyNames#涉采购不良行为记录#count#条',
      template2: '存在涉采购不良行为记录#count#条',
      sort: 4,
      type: IndicatorTypeEnums.generalItems,
      description: '涉采购不良行为',
      cycle: 3, // 用于 RA-7750 需求，在升级模型设置之后，前端传参需要保持父级 cycle 字段与子级 cycle 字段值一致，避免排查周期对于排查逻辑产生影响；
      subDimensionList: [
        {
          key: DimensionLevel2Enums.CompanyCredit,
          name: '严重违法失信',
          strategyModel: {
            boost: 1.0,
            baseScore: 20,
            cycle: 3,
            level: DimensionRiskLevelEnum.High,
          },
          isVirtualDimension: 0,
          source: DimensionSourceEnums.EnterpriseLib,
          sourcePath: '/api/QccSearch/List/SeriousViolation',
          status: 0,
          template: '<em class="#level#">【#name#】 #count#条记录</em>',
          template2: '【#name#】 #count#条记录',
          type: IndicatorTypeEnums.generalItems,
          description: '企业及实控人、股东被市场监管部门列入严重违法失信企业名录',
          sort: 1,
        },
        {
          key: DimensionLevel3Enums.BusinessAbnormal3,
          name: '经营异常',
          strategyModel: {
            boost: 1.0,
            baseScore: 40,
            cycle: 3,
            level: DimensionRiskLevelEnum.High,
            sortField: { field: 'publishdate', order: 'DESC' },
            detailsParams: [
              {
                field: QueryParamsEnums.businessAbnormalType,
                fieldVal: ['0801', '0802', '0803', '0804', '0805', '0806', '0807'],
                sort: 0,
              },
            ],
          },
          isVirtualDimension: 0,
          source: DimensionSourceEnums.Credit,
          sourcePath: '',
          status: 0,
          template: '<em class="#level#">【#name#】 #count#条记录</em>',
          template2: '【#name#】',
          type: IndicatorTypeEnums.generalItems,
          description: '企业及实控人、股东被市场监管部门列为经营异常名录',
          sort: 2,
        },
        {
          key: DimensionLevel3Enums.PersonCreditCurrent,
          name: '失信被执行人',
          strategyModel: {
            boost: 1.0,
            baseScore: 30,
            cycle: 3,
            level: DimensionRiskLevelEnum.High,
            sortField: { field: 'pubdate', order: 'DESC', fieldSnapshot: 'PublicDate' },
          },
          isVirtualDimension: 0,
          // 不使用信用接口原因: 信用数据 未对属于同一司法案件的执行标的做去重处理
          source: DimensionSourceEnums.EnterpriseLib,
          sourcePath: '/api/Court/SearchShiXin',
          status: 0,
          template: '<em class="#level#">【#name#】#count#条记录</em><span class="#isHidden#">，涉及被执行金额：<em class="#level#">#amountW#</em></span>',
          template2: '【#name#】#count#条记录<span class="#isHidden#">，涉及被执行金额：#amountW#</span>',
          type: IndicatorTypeEnums.generalItems,
          description: '企业被加入失信被执行人名单',
          sort: 3,
        },
        {
          key: DimensionLevel2Enums.TaxationOffences,
          name: '重大税收违法',
          strategyModel: {
            boost: 1.0,
            baseScore: 30,
            level: DimensionRiskLevelEnum.High,
            cycle: 3,
          },
          isVirtualDimension: 0,
          source: DimensionSourceEnums.Credit,
          sourcePath: '',
          status: 0,
          template: '<em class="#level#">#cycle#【#name#】 #count#条记录</em>',
          template2: '#cycle#存在【#name#】案件#count#条记录',
          type: IndicatorTypeEnums.generalItems,
          description: '企业因虚开发票、偷税等税务违法行为被处罚',
          sort: 4,
        },
        {
          key: DimensionLevel3Enums.MigrantWorkers,
          name: '拖欠农民工工资黑名单',
          strategyModel: {
            boost: 1.0,
            baseScore: 5,
            cycle: 3,
            level: DimensionRiskLevelEnum.High,
            sortField: { field: 'decisiondate', order: 'DESC' },
          },
          isVirtualDimension: 0,
          source: DimensionSourceEnums.OuterBlacklist,
          status: 1,
          child: ['5'],
          template: '<em class="#level#">【#name#】 #count#条记录</em>',
          template2: '被列入#name# #count#条记录',
          type: IndicatorTypeEnums.generalItems,
          sort: 5,
        },
        {
          key: DimensionLevel3Enums.GovernmentPurchaseIllegal,
          name: '政府采购严重违法失信行为记录名单',
          strategyModel: {
            boost: 1.0,
            baseScore: 5,
            cycle: 3,
            level: DimensionRiskLevelEnum.High,
            sortField: { field: 'decisiondate', order: 'DESC' },
          },
          isVirtualDimension: 0,
          source: DimensionSourceEnums.OuterBlacklist,
          status: 1,
          child: ['25'],
          template: '<em class="#level#">【#name#】 #count#条记录</em>',
          template2: '被列入#name# #count#条记录',
          type: IndicatorTypeEnums.generalItems,
          sort: 6,
        },
        {
          key: DimensionLevel3Enums.ArmyProcurementIllegal,
          name: '军队采购失信名单',
          strategyModel: {
            boost: 1.0,
            baseScore: 5,
            cycle: 3,
            level: DimensionRiskLevelEnum.High,
            sortField: { field: 'decisiondate', order: 'DESC' },
          },
          isVirtualDimension: 0,
          source: DimensionSourceEnums.OuterBlacklist,
          status: 1,
          child: ['75', '104'],
          template: '<em class="#level#">【#name#】 #count#条记录</em>',
          template2: '被列入#name# #count#条记录',
          type: IndicatorTypeEnums.generalItems,
          sort: 7,
        },
        {
          key: DimensionLevel3Enums.ArmyProcurementSuspended,
          name: '军队采购暂停供应商资格名单',
          strategyModel: {
            boost: 1.0,
            baseScore: 5,
            cycle: 3,
            level: DimensionRiskLevelEnum.High,
            sortField: { field: 'decisiondate', order: 'DESC' },
          },
          isVirtualDimension: 0,
          source: DimensionSourceEnums.OuterBlacklist,
          status: 1,
          child: ['76'],
          template: '<em class="#level#">【#name#】 #count#条记录</em>',
          template2: '被列入#name# #count#条记录',
          type: IndicatorTypeEnums.generalItems,
          sort: 8,
        },
        {
          key: DimensionLevel2Enums.FakeSOES,
          name: '假冒国企',
          strategyModel: {
            boost: 1.0,
            baseScore: 40,
            cycle: 3,
            level: DimensionRiskLevelEnum.Medium,
          },
          isVirtualDimension: 0,
          source: DimensionSourceEnums.CompanyDetail,
          status: 0,
          template: '<em class="#level#">【#name#】 #count#条记录</em>',
          template2: '被列为【#name#】',
          type: IndicatorTypeEnums.generalItems,
          typeCode: '3101',
          description: '企业或控股股东、母公司因假冒国企被公示',
          sort: 9,
        },
        {
          key: DimensionLevel3Enums.GovProcurementIllegal,
          name: '国央企采购黑名单',
          strategyModel: {
            boost: 1.0,
            baseScore: 5,
            cycle: 3,
            level: DimensionRiskLevelEnum.High,
            sortField: { field: 'decisiondate', order: 'DESC' },
          },
          isVirtualDimension: 0,
          source: DimensionSourceEnums.OuterBlacklist,
          status: 1,
          child: ['74', '77', '78'],
          template: '<em class="#level#">【#name#】 #count#条记录</em>',
          template2: '被列入#name# #count#条记录',
          type: IndicatorTypeEnums.generalItems,
          sort: 10,
        },
        {
          key: DimensionLevel2Enums.BidAdministrativePenalties,
          name: '涉采购处罚',
          strategyModel: {
            boost: 1.0,
            baseScore: 5,
            level: DimensionRiskLevelEnum.Medium,
            cycle: 3,
            sortField: { field: 'punishdate', order: 'DESC' },
          },
          isVirtualDimension: 0,
          source: DimensionSourceEnums.SupervisePunish,
          sourcePath: '',
          status: 1,
          template: '<em class="#level#">#cycle#【#name#】 #count#条记录</em><span class="#isHidden#">，罚款总金额：<em class="#level#">#amountW#</em></span>',
          template2: '#cycle#【#name#】 #count#条记录<span class="#isHidden#">，罚款总金额：#amountW#</span>',
          type: IndicatorTypeEnums.generalItems,
          sort: 11,
        },
        {
          key: DimensionLevel2Enums.BidAdministrativeJudgement,
          name: '涉诉围串标记录',
          strategyModel: {
            boost: 1.0,
            baseScore: 5,
            level: DimensionRiskLevelEnum.Medium,
            cycle: 3,
            sortField: { field: 'submitdate', order: 'DESC' },
            // detailsParams: [
            // ],
          },
          isVirtualDimension: 0,
          source: DimensionSourceEnums.Judgement,
          sourcePath: '/api/search/search-credit',
          status: 1,
          template: '<em class="#level#">#cycle#【#name#】 #count#条记录</em><span class="#isHidden#">，罚款总金额：<em class="#level#">#amountW#</em></span>',
          template2: '#cycle#【#name#】 #count#条记录<span class="#isHidden#">，罚款总金额：#amountW#</span>',
          type: IndicatorTypeEnums.generalItems,
          sort: 12,
        },
      ],
    },
  ],
};

export const getTenderDimensionKeyName = () => {
  return TenderDimensions.items.reduce((acc, cur) => {
    acc[cur.key] = cur.name;
    return acc;
  }, {});
};
