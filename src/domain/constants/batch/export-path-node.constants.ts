export const RelationTypeOptions = [
  { label: '法定代表人', value: 'Legal', type: 0 },
  { label: '历史法定代表人', value: 'HisLegal', type: 0 },
  { label: '持股/投资关联', value: 'Invest', type: 0 },
  { label: '董监高', value: 'Employ', type: 0 },
  { label: '历史董监高', value: 'HisEmploy', type: 0 },
  { label: '历史持股/投资关联', value: 'HisInvest', type: 0 },
  { label: '分支机构', value: 'Branch', type: 0 },
  { label: '实际控制人', value: 'ActualController', type: 0 },
  { label: '控制关系', value: 'Hold', type: 0 },
  { label: '受益所有人', value: 'FinalBenefit', type: 0 },

  { label: '相同电话号码', value: 'ContactNumber', type: 1 },
  { label: '相同域名信息', value: 'Website', type: 1 },
  { label: '相同地址', value: 'Address', type: 1 },
  { label: '相同邮箱', value: 'Mail', type: 1 },
  { label: '相同专利信息', value: 'Patent', type: 1 },
  { label: '相同国际专利信息', value: 'IntPatent', type: 1 },
  { label: '相同软件著作权', value: 'SoftwareCopyright', type: 1 },
  { label: '相同司法案件', value: 'Case', type: 1 },
  { label: '疑似同名主要人员', value: 'SameNameEmployee', type: 1 },
];

export const RelationTypeMapV2 = {
  legal: '法定代表人',
  hislegal: '历史法定代表人',
  invest: '持股/投资关联',
  employ: '董监高',
  hisemploy: '历史董监高',
  hisinvest: '历史持股/投资关联',
  branch: '分支机构',
  actualcontroller: '相同实际控制人',
  hold: '控制关系',
  contactnumber: '相同电话号码',
  website: '相同域名信息',
  address: '相同地址',
  mail: '相同邮箱',
  patent: '相同专利信息',
  intpatent: '相同国际专利信息',
  softwarecopyright: '相同软件著作权',
  case: '相同司法案件',
  samenameemployee: '疑似同名主要人员',
  shareholdingrelationship: '持股关联',
  investorsrelationship: '投资关联',
  employmentrelationship: '董监高/法人关联',
  hisshareholdingrelationship: '持股关联（历史）',
  hisinvestorsrelationship: '投资关联（历史）',
  hislegalandemploy: '董监高/法人（历史）',
  maininfoupdatebeneficiary: '相同受益所有人',
  finalbenefit: '最终受益所有人',
  shareholdingratio: '持股/投资股权比例（含历史）',
};
