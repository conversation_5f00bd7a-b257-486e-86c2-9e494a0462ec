import { DimensionRiskLevelEnum } from '@domain/enums/diligence/DimensionRiskLevelEnum';
import { OperatorEnums } from '@domain/model/diligence/pojo/dimension/DimensionQueryPO';
import { QueryParamsEnums } from '@domain/model/diligence/pojo/dimension/dimension.filter.params';
import { MonitorDimensionType } from '@domain/model/monitor/MonitorDimensionPO';
import { MonitorRiskEnums } from '@domain/enums/monitor/MonitorRiskEnums';
import { CompanyCertificationConstants } from './company-certification.constants';
import { DimensionLevel3Enums } from '@domain/enums/diligence/DimensionLevel3Enums';
import { DimensionLevel2Enums } from '@domain/enums/diligence/DimensionLevel2Enums';

export const DefaultMonitorDimension: MonitorDimensionType[] = [
  {
    name: '工商风险',
    key: MonitorRiskEnums.MonitorMainInfoRisk,
    status: 1,
    sort: 1,
    items: [
      {
        name: '企业名称变更',
        category: [60],
        key: MonitorRiskEnums.MonitorUpdateName,
        status: 1,
        level: DimensionRiskLevelEnum.Medium,
        sort: 10,
      },
      {
        name: '法定代表人变更',
        category: [39],
        // subCategory: [1, 2, 3, 7],
        key: MonitorRiskEnums.MonitorUpdateLegalPerson,
        status: 1,
        level: DimensionRiskLevelEnum.Medium,
        sort: 20,
      },
      {
        name: '实际控制人变更',
        category: [25],
        key: MonitorRiskEnums.MonitorUpdatePerson,
        status: 0,
        level: DimensionRiskLevelEnum.High,
        sort: 30,
        isHidden: true, // 废弃指标，但需要在动态聚合部分展示
      },
      {
        name: '受益所有人变更',
        category: [114],
        key: MonitorRiskEnums.MonitorUpdateBeneficiary,
        status: 0,
        level: DimensionRiskLevelEnum.High,
        sort: 40,
        isHidden: true, // 废弃指标，但需要在动态聚合部分展示
      },
      {
        name: '实际控制人、受益所有人变更',
        category: [145],
        key: MonitorRiskEnums.ActualAndBenefitChange,
        status: 1,
        level: DimensionRiskLevelEnum.High,
        sort: 40,
      },
      {
        name: '主要人员变更',
        category: [46, 202],
        key: MonitorRiskEnums.MonitorKeyPersonnelChange,
        status: 1,
        level: DimensionRiskLevelEnum.Medium,
        sort: 41,
      },
      {
        name: '注册资本变更',
        category: [37],
        key: MonitorRiskEnums.MonitorUpdateRegisteredCapital,
        status: 1,
        level: DimensionRiskLevelEnum.Medium,
        params: [
          {
            field: QueryParamsEnums.registeredCapitalChangeRatio,
            fieldName: '注册资本变更比例',
            fieldOperator: OperatorEnums.ge,
            fieldVal: 10,
          },
        ],
        sort: 50,
      },
      {
        name: '股东变更',
        category: [44],
        // subCategory: [1, 3, 4, 6],
        key: MonitorRiskEnums.MonitorUpdateHolder,
        status: 1,
        level: DimensionRiskLevelEnum.Alert,
        sort: 60,
      },
      {
        name: '企业地址变更',
        category: [40],
        key: MonitorRiskEnums.MonitorUpdateAddress,
        status: 1,
        level: DimensionRiskLevelEnum.Medium,
        sort: 70,
      },
      {
        name: '大股东变更',
        category: [24],
        key: MonitorRiskEnums.MonitorMajorShareHolderChanged,
        status: 1,
        level: DimensionRiskLevelEnum.Medium,
        sort: 80,
      },
      {
        name: '经营范围变更',
        category: [41],
        key: MonitorRiskEnums.MonitorBusinessScopeChange,
        status: 1,
        level: DimensionRiskLevelEnum.Medium,
        sort: 90,
      },
      {
        name: '对外投资变更',
        category: [17, 68], //17新增 撤出对外投资 68持股比例上升 下降
        key: MonitorRiskEnums.MonitorForeignInvestmentChange,
        status: 1,
        level: DimensionRiskLevelEnum.Medium,
        params: [
          {
            field: QueryParamsEnums.foreignInvestmentChangeType,
            fieldName: '变更类型',
            fieldVal: [
              { key: '1', keyName: '撤出对外投资', status: 1 },
              { key: '2', keyName: '所持股份减少', status: 1 },
              { key: '3', keyName: '新增对外投资', status: 0 },
              { key: '4', keyName: '所持股份上升', status: 0 },
            ],
          },
        ],
        sort: 91,
      },
      {
        name: '减资公告',
        category: [123, 132],
        key: MonitorRiskEnums.MonitorReductionOfCapital,
        status: 1,
        level: DimensionRiskLevelEnum.Medium,
        params: [
          {
            field: QueryParamsEnums.capitalReductionRatio,
            fieldName: '减资幅度',
            fieldOperator: OperatorEnums.ge,
            fieldVal: 50,
          },
        ],
        sort: 100,
      },
      {
        name: '新增分支机构',
        category: [47],
        key: MonitorRiskEnums.MonitorNewBranchAdded,
        status: 1,
        level: DimensionRiskLevelEnum.Medium,
        sort: 110,
      },
    ],
  },
  {
    name: '司法风险',
    key: MonitorRiskEnums.MonitorLegalRisk,
    status: 1,
    sort: 2,
    items: [
      {
        name: '破产重整',
        category: [58],
        key: MonitorRiskEnums.MonitorBankruptcy,
        status: 1,
        level: DimensionRiskLevelEnum.High,
        sort: 1,
      },
      {
        name: '失信被执行人',
        category: [2],
        key: MonitorRiskEnums.MonitorDishonestDebtor,
        status: 1,
        level: DimensionRiskLevelEnum.High,
        sort: 2,
      },
      {
        name: '限制高消费',
        category: [55],
        key: MonitorRiskEnums.MonitorRestrictedConsumption,
        status: 1,
        level: DimensionRiskLevelEnum.High,
        sort: 3,
      },
      {
        name: '限制出境',
        category: [91],
        key: MonitorRiskEnums.MonitorRestrictedGoingAbroad,
        status: 1,
        level: DimensionRiskLevelEnum.High,
        sort: 4,
      },
      {
        name: '被执行人',
        category: [3],
        key: MonitorRiskEnums.MonitorPersonExecution,
        status: 1,
        level: DimensionRiskLevelEnum.Medium,
        params: [
          {
            field: QueryParamsEnums.executionSum,
            fieldName: '被执行金额',
            fieldOperator: OperatorEnums.ge,
            fieldVal: 0,
          },
        ],
        sort: 5,
      },
      {
        name: '终本案件',
        category: [56],
        key: MonitorRiskEnums.MonitorFinalCase,
        status: 1,
        level: DimensionRiskLevelEnum.Medium,
        sort: 6,
      },
      {
        name: '股权冻结',
        category: [26],
        key: MonitorRiskEnums.MonitorEquityFreeze,
        status: 1,
        level: DimensionRiskLevelEnum.Medium,
        sort: 7,
      },
      {
        name: '询价评估',
        category: [59],
        key: MonitorRiskEnums.MonitorInquiryEvaluation,
        status: 1,
        level: DimensionRiskLevelEnum.High,
        sort: 8,
      },
      {
        name: '司法拍卖',
        category: [57],
        key: MonitorRiskEnums.MonitorJudicialAuction,
        status: 1,
        level: DimensionRiskLevelEnum.High,
        sort: 9,
      },
      {
        name: '动产查封',
        category: [140],
        key: MonitorRiskEnums.MonitorMovablePropertySeizure,
        status: 1,
        level: DimensionRiskLevelEnum.High,
        sort: 10,
      },
      {
        name: '公安通告',
        category: [109],
        key: MonitorRiskEnums.MonitorPublicSecurityNotice,
        status: 1,
        level: DimensionRiskLevelEnum.High,
        sort: 11,
      },
      {
        name: '票据纠纷',
        category: [4],
        key: MonitorRiskEnums.MonitorBillDispute,
        status: 1,
        level: DimensionRiskLevelEnum.High,
        sort: 12,
      },
      {
        name: '不正当竞争',
        category: [4],
        key: MonitorRiskEnums.MonitorUnfairCompetition,
        status: 1,
        level: DimensionRiskLevelEnum.Medium,
        sort: 13,
      },
      {
        name: '贪污受贿',
        category: [4],
        key: MonitorRiskEnums.MonitorCorruptionAndBribery,
        status: 1,
        level: DimensionRiskLevelEnum.High,
        sort: 14,
      },
      {
        name: '借贷纠纷',
        category: [4],
        key: MonitorRiskEnums.MonitorLoanDispute,
        status: 1,
        level: DimensionRiskLevelEnum.Medium,
        sort: 15,
      },
      {
        name: '买卖纠纷',
        category: [4],
        key: MonitorRiskEnums.MonitorTradeDispute,
        status: 1,
        level: DimensionRiskLevelEnum.Medium,
        sort: 16,
      },
      {
        name: '劳动争议',
        category: [4],
        key: MonitorRiskEnums.MonitorLaborDispute,
        status: 1,
        level: DimensionRiskLevelEnum.Medium,
        sort: 17,
      },
      {
        name: '破产纠纷',
        category: [4],
        key: MonitorRiskEnums.MonitorBankruptcyDispute,
        status: 1,
        level: DimensionRiskLevelEnum.High,
        sort: 18,
      },
      {
        name: '重大纠纷',
        category: [4],
        key: MonitorRiskEnums.MonitorImportantDispute,
        status: 1,
        level: DimensionRiskLevelEnum.Medium,
        sort: 19,
        params: [
          {
            field: QueryParamsEnums.caseAmount,
            fieldName: '案件金额',
            fieldOperator: OperatorEnums.ge,
            fieldVal: 200000,
          },
        ],
      },
      {
        name: '短期多起开庭公告',
        category: [18],
        key: MonitorRiskEnums.NoticeInTimePeriod,
        status: 1,
        level: DimensionRiskLevelEnum.High,
        sort: 20,
        params: [
          {
            field: QueryParamsEnums.nearExpirationType,
            fieldName: '期限限制',
            fieldVal: 2, // 1-近 7 天；2-近一个月；3-近 3 个月；
          },
          {
            field: QueryParamsEnums.limitCount,
            fieldName: '数量设置',
            fieldOperator: OperatorEnums.ge,
            fieldVal: 3,
          },
        ],
      },
      {
        name: '立案信息',
        category: [49],
        key: MonitorRiskEnums.FilingInformation,
        status: 0,
        level: DimensionRiskLevelEnum.Medium,
        sort: 21,
        params: [],
      },
    ],
  },
  {
    name: '监管风险',
    key: MonitorRiskEnums.MonitorSupervisionRisk,
    status: 1,
    sort: 3,
    items: [
      {
        name: '严重违法',
        category: [20],
        key: MonitorRiskEnums.MonitorSeriousIllegality,
        status: 1,
        level: DimensionRiskLevelEnum.High,
        sort: 10,
      },
      {
        name: '经营异常',
        category: [11],
        key: MonitorRiskEnums.MonitorBusinessAbnormal,
        status: 1,
        level: DimensionRiskLevelEnum.High,
        params: [
          {
            field: QueryParamsEnums.reasonType,
            fieldName: '经营异常原因',
            fieldVal: [
              { key: '1', keyName: '公示信息隐瞒真实情况/弄虚作假', status: 1 },
              { key: '2', keyName: '登记的住所/经营场所无法联系企业', status: 1 },
              { key: '3', keyName: '未在规定期限公示年度报告', status: 1 },
              { key: '4', keyName: '未按规定公示企业信息', status: 1 },
              { key: '5', keyName: '未在登记所从事经营活动', status: 1 },
              { key: '6', keyName: '商事主体名称不适宜', status: 1 },
              { key: '7', keyName: '其他原因', status: 0 },
            ],
          },
        ],
        sort: 20,
      },
      // TODO: C端风险动态没有该维度
      {
        name: '被列入非正常户',
        category: [117],
        key: MonitorRiskEnums.MonitorAbnormalHousehold,
        status: 1,
        level: DimensionRiskLevelEnum.High,
        sort: 30,
      },
      {
        name: '税收违法',
        category: [29],
        key: MonitorRiskEnums.MonitorTaxationOffences,
        status: 1,
        level: DimensionRiskLevelEnum.High,
        sort: 40,
      },
      {
        name: '欠税公告',
        category: [31],
        key: MonitorRiskEnums.MonitorTaxArrearsNotice,
        status: 1,
        level: DimensionRiskLevelEnum.High,
        params: [
          {
            field: QueryParamsEnums.taxArrearsAmount,
            fieldName: '欠税金额',
            fieldOperator: OperatorEnums.ge,
            fieldVal: 0,
          },
        ],
        sort: 50,
      },
      {
        name: '税务催报',
        category: [130],
        key: MonitorRiskEnums.MonitorTaxReminder,
        status: 1,
        level: DimensionRiskLevelEnum.Medium,
        sort: 60,
      },
      {
        name: '税务催缴',
        category: [131],
        key: MonitorRiskEnums.MonitorTaxCall,
        status: 1,
        level: DimensionRiskLevelEnum.High,
        params: [
          {
            field: QueryParamsEnums.taxArrearsAmount,
            fieldName: '欠缴金额',
            fieldOperator: OperatorEnums.ge,
            fieldVal: 100000,
          },
        ],
        sort: 70,
      },
      {
        name: '行政处罚',
        category: [107],
        key: MonitorRiskEnums.AdministrativePenalties,
        status: 1,
        level: DimensionRiskLevelEnum.Medium,
        params: [
          {
            field: QueryParamsEnums.penaltiesType,
            fieldName: '处罚类型',
            fieldVal: [
              { key: 'A008', keyName: '吊销许可证件（含执照）', status: 1 },
              { key: 'A011', keyName: '责令关闭', status: 1 },
              { key: 'A015', keyName: '移送司法机关', status: 1 },
              { key: 'A010', keyName: '责令停产停业', status: 1 },
              { key: 'A014', keyName: '行政拘留', status: 1 },
              { key: 'A013', keyName: '限制从业', status: 1 },
              { key: 'A003', keyName: '罚款', status: 1 },
              { key: 'A004', keyName: '没收违法所得', status: 1 },
              { key: 'A005', keyName: '没收非法财物', status: 1 },
              { key: 'A006', keyName: '暂扣许可证件（含执照）', status: 1 },
              { key: 'A007', keyName: '降低资质等级', status: 1 },
              { key: 'A009', keyName: '限制开展生产经营活动', status: 1 },
            ],
          },
          {
            field: QueryParamsEnums.penaltiesAmount,
            fieldName: '处罚金额',
            fieldOperator: OperatorEnums.ge,
            fieldVal: 100000,
          },
        ],
        sort: 80,
      },
      {
        name: '环保处罚',
        category: [22],
        key: MonitorRiskEnums.EnvironmentalProtection,
        status: 1,
        level: DimensionRiskLevelEnum.Medium,
        sort: 81,
      },
      {
        name: '抽查检查',
        category: [14],
        key: MonitorRiskEnums.MonitorSpotCheck,
        status: 1,
        level: DimensionRiskLevelEnum.Medium,
        sort: 90,
      },
      // { name: '产品抽查不合格', category: [39], key: MonitorRiskEnums.MonitorSpotCheckUnqualified, status: 1, level: DimensionRiskLevelEnum.Medium, sort: 7 },
      // { name: '药品抽查检验不合格', category: [39], key: MonitorRiskEnums.MonitorDrugFailed, status: 1, level: DimensionRiskLevelEnum.Medium, sort: 8 },
      {
        name: '被列入黑名单',
        category: [77],
        key: MonitorRiskEnums.MonitorBlacklist,
        params: [
          {
            field: QueryParamsEnums.blackListType,
            fieldName: '黑名单类型',
            fieldVal: [
              { key: '2', keyName: '安全生产领域失信生产经营单位', status: 1 },
              { key: '3', keyName: '统计领域严重失信企业及其有关人员', status: 1 },
              { key: '5', keyName: '拖欠农民工工资黑名单', status: 1 },
              { key: '7', keyName: '海关失信企业名单', status: 1 },
              { key: '28', keyName: '运输物流行业严重失信黑名单', status: 1 },
              { key: '33', keyName: '建筑市场主体黑名单', status: 1 },
              { key: '34', keyName: '工程建设领域黑名单', status: 1 },
              { key: '38', keyName: '价格失信黑名单', status: 1 },
              { key: '39', keyName: '医疗保障领域失信联合惩戒对象名单', status: 1 },
              { key: '40', keyName: '医疗卫生行业黑名单', status: 1 },
              { key: '41', keyName: '医药行业失信企业黑名单', status: 1 },
              { key: '46', keyName: '消防安全领域黑名单', status: 1 },
              { key: '25', keyName: '政府采购严重违法失信行为记录名单', status: 1 },
              { key: '43', keyName: '知识产权（专利）领域严重失信联合戒对象名单', status: 1 },
              { key: '13', keyName: '环保失信黑名单', status: 1 },
              { key: '103', keyName: '名企不合作企业清单', status: 1 },
              { key: '10001', keyName: '美国财政部办公室OFAC制裁名单', status: 0 },
              { key: '10003', keyName: '美国国土安全部UFLPA实体清单', status: 0 },
              { key: '10004', keyName: '美国国防部CMCC中国军事公司名单', status: 0 },
              { key: '10005', keyName: '美国国务院国防贸易管制局禁止名单', status: 0 },
              { key: '10006', keyName: '美国商业部工业安全局BIS实体清单', status: 0 },
              { key: '10007', keyName: '美国商业部工业安全局BIS被拒绝人员名单', status: 0 },
              { key: '10008', keyName: '美国商业部工业安全局BIS未经验证的列表', status: 0 },
              { key: '10009', keyName: '美国商业部工业安全局BIS军事最终用户列表', status: 0 },
              { key: '10012', keyName: '亚投行禁止名单', status: 0 },
              { key: '10014', keyName: '联合国安理会综合清单', status: 0 },
              { key: '10015', keyName: '联合国制裁委员会制裁名单', status: 0 },
              { key: '10010', keyName: '欧盟金融制裁综合清单', status: 0 },
              { key: '10013', keyName: '加拿大自治综合制裁名单', status: 0 },
              { key: '10002', keyName: '英国外交、联邦与发展办公室制裁局FCDO制裁名单', status: 0 },
              { key: '10017', keyName: '澳大利亚综合制裁清单', status: 0 },
              { key: '10018', keyName: '世界银行不合格公司和个人名单', status: 0 },
              { key: '10019', keyName: '美国国务院防扩散制裁', status: 0 },
            ],
            isHidden: true,
          },
          // 新的监控黑名单配置，支持层级分类。
          // isCategory表示是否有子分类，showInFilter表示是否展示在筛选器中,并且控制层级关系，若为false则表示当前层级status控制所有子项status
          {
            field: QueryParamsEnums.newBlackListType,
            fieldName: '黑名单类型',
            // 保存所有已开启的黑名单类型code值
            activeCodes: [
              25, 2, 7, 43, 13, 65, 6, 57, 10001, 10003, 10004, 10005, 10006, 10007, 10008, 10009, 10025, 10019, 10014, 10015, 10010, 10013, 10002, 10024,
              10017, 10012, 10018, 10023, 10022, 5, 28, 1, 33, 34, 69, 39, 40, 41, 68, 79, 56, 80, 46, 97, 4, 54, 3, 24, 38, 55, 69, 98, 11, 8, 16, 29, 30, 32,
              35, 36, 37, 44, 47, 48, 49, 50, 51, 52, 53, 99, 106, 108, 105, 103,
            ],
            fieldVal: [
              {
                key: DimensionLevel3Enums.GovernmentPurchaseIllegal,
                keyName: '政府采购严重违法失信行为记录名单',
                status: 1,
                sort: 1,
                isCategory: true,
                showInFilter: false,
                items: [{ key: 'government_25', keyName: '政府采购严重违法失信行为记录名单', status: 1, sort: 1, code: 25 }],
              },
              {
                key: DimensionLevel3Enums.SafetyProductionEnterprise,
                keyName: '安全生产领域失信生产经营单位',
                status: 1,
                sort: 2,
                isCategory: true,
                showInFilter: false,
                items: [{ key: 'safety_2', keyName: '安全生产领域失信生产经营单位', status: 1, sort: 1, code: 2 }],
              },
              {
                key: DimensionLevel3Enums.CustomsList,
                keyName: '海关失信企业名单',
                status: 1,
                sort: 3,
                isCategory: true,
                showInFilter: false,
                items: [{ key: 'customs_7', keyName: '海关失信企业名单', status: 1, sort: 1, code: 7 }],
              },
              {
                key: DimensionLevel3Enums.IntellectualPropertyIllegal,
                keyName: '知识产权（专利）领域严重失信联合戒对象名单',
                status: 1,
                sort: 4,
                isCategory: true,
                showInFilter: false,
                items: [{ key: 'ip_43', keyName: '知识产权（专利）领域严重失信联合戒对象名单', status: 1, sort: 1, code: 43 }],
              },
              {
                key: DimensionLevel3Enums.EnvironmentalProtection,
                keyName: '环保失信黑名单',
                status: 1,
                sort: 5,
                isCategory: true,
                showInFilter: false,
                items: [
                  { key: 'environmental_13', keyName: '环境违法企业黑名单', status: 1, sort: 1, code: 13 },
                  { key: 'environmental_65', keyName: '环保行业不良行为', status: 1, sort: 2, code: 65 },
                ],
              },
              {
                key: DimensionLevel3Enums.LaborGuarantee,
                keyName: '劳动保障违法',
                status: 1,
                sort: 6,
                isCategory: true,
                showInFilter: false,
                items: [{ key: 'labor_6', keyName: '重大劳动保障违法失信名单', status: 1, sort: 1, code: 6 }],
              },
              {
                key: DimensionLevel3Enums.GovProcurementIllegal,
                keyName: '国央企采购黑名单',
                status: 0,
                sort: 7,
                isCategory: true,
                showInFilter: false,
                items: [
                  { key: 'gov_74', keyName: '央企不良供应商', status: 0, sort: 1, code: 74 },
                  { key: 'gov_77', keyName: '政府采购不良行为', status: 0, sort: 2, code: 77 },
                  { key: 'gov_78', keyName: '供应商诚信记录', status: 0, sort: 2, code: 78 },
                  { key: 'gov_121', keyName: '履行合同催告函', status: 0, sort: 3, code: 121 },
                ],
              },
              {
                key: DimensionLevel3Enums.FgwBlackList,
                keyName: '发改委黑名单',
                status: 1,
                sort: 8,
                isCategory: true,
                showInFilter: false,
                items: [{ key: 'fgw_57', keyName: '发改委黑名单', status: 1, sort: 1, code: 57 }],
              },
              // 出口管制合规风险企业清单（制裁类，code值设为10000+原code）
              {
                key: DimensionLevel2Enums.ForeignExportControls,
                keyName: '出口管制合规风险企业清单',
                status: 1,
                sort: 9,
                isCategory: true,
                showInFilter: true,
                items: [
                  { key: 'export_1', keyName: '美国财政部办公室OFAC制裁名单', status: 1, sort: 1, code: 10001 },
                  { key: 'export_3', keyName: '美国国土安全部UFLPA实体清单', status: 1, sort: 2, code: 10003 },
                  { key: 'export_4', keyName: '美国国防部CMCC中国军事公司名单', status: 1, sort: 3, code: 10004 },
                  { key: 'export_5', keyName: '美国国务院国防贸易管制局禁止名单', status: 1, sort: 4, code: 10005 },
                  { key: 'export_6', keyName: '美国商业部工业安全局BIS实体清单', status: 1, sort: 5, code: 10006 },
                  { key: 'export_7', keyName: '美国商业部工业安全局BIS被拒绝人员名单', status: 1, sort: 6, code: 10007 },
                  { key: 'export_8', keyName: '美国商业部工业安全局BIS未经验证的列表', status: 1, sort: 7, code: 10008 },
                  { key: 'export_9', keyName: '美国商业部工业安全局BIS军事最终用户列表', status: 1, sort: 8, code: 10009 },
                  { key: 'export_25', keyName: '美国国务院外国恐怖组织', status: 1, sort: 9, code: 10025 },
                  { key: 'export_19', keyName: '美国国务院防扩散制裁', status: 1, sort: 10, code: 10019 },
                  { key: 'export_14', keyName: '联合国安理会综合清单', status: 1, sort: 11, code: 10014 },
                  { key: 'export_15', keyName: '联合国制裁委员会制裁名单', status: 1, sort: 12, code: 10015 },
                  { key: 'export_10', keyName: '欧盟金融制裁综合清单', status: 1, sort: 13, code: 10010 },
                  { key: 'export_13', keyName: '加拿大自治综合制裁名单', status: 1, sort: 14, code: 10013 },
                  { key: 'export_2', keyName: '英国外交、联邦与发展办公室制裁局FCDO制裁名单', status: 1, sort: 15, code: 10002 },
                  { key: 'export_24', keyName: '英国财政部金融制裁目标综合清单', status: 1, sort: 16, code: 10024 },
                  { key: 'export_17', keyName: '澳大利亚综合制裁清单', status: 1, sort: 17, code: 10017 },
                  { key: 'export_12', keyName: '亚投行禁止名单', status: 1, sort: 18, code: 10012 },
                  { key: 'export_18', keyName: '世界银行不合格公司和个人名单', status: 1, sort: 19, code: 10018 },
                  { key: 'export_23', keyName: '日本经济产业省贸易管制最终用户名单', status: 1, sort: 20, code: 10023 },
                  { key: 'export_22', keyName: '欧洲投资银行排除名单', status: 1, sort: 21, code: 10022 },
                ],
              },
              {
                key: DimensionLevel2Enums.SupervisionOfKeyIndustry,
                keyName: '重点行业领域监管黑名单',
                status: 1,
                sort: 10,
                isCategory: true,
                showInFilter: true,
                items: [
                  {
                    key: DimensionLevel3Enums.MigrantWorkers,
                    keyName: '拖欠农民工工资黑名单',
                    status: 1,
                    sort: 1,
                    isCategory: true,
                    showInFilter: false,
                    items: [{ key: 'migrant_5', keyName: '拖欠农民工工资黑名单', status: 1, sort: 1, code: 5 }],
                  },
                  {
                    key: DimensionLevel3Enums.LogisticsIndustryIllegal,
                    keyName: '运输物流行业失信黑名单',
                    status: 1,
                    sort: 2,
                    isCategory: true,
                    showInFilter: false,
                    items: [
                      { key: 'logistics_28', keyName: '运输物流行业严重失信黑名单', status: 1, sort: 1, code: 28 },
                      { key: 'logistics_1', keyName: '严重违法超限超载运输当事人名单', status: 1, sort: 2, code: 1 },
                    ],
                  },
                  {
                    key: DimensionLevel3Enums.ConstructionEngineeringBlacklist,
                    keyName: '建筑工程领域黑名单',
                    status: 1,
                    sort: 3,
                    isCategory: true,
                    showInFilter: false,
                    items: [
                      { key: 'construction_33', keyName: '建筑市场主体黑名单', status: 1, sort: 1, code: 33 },
                      { key: 'construction_34', keyName: '工程建设领域黑名单', status: 1, sort: 2, code: 34 },
                      { key: 'construction_69', keyName: '建筑行业不良行为', status: 1, sort: 3, code: 69 },
                    ],
                  },
                  {
                    key: DimensionLevel3Enums.MedicalMedicineIllegal,
                    keyName: '医疗医药领域黑名单',
                    status: 1,
                    sort: 4,
                    isCategory: true,
                    showInFilter: false,
                    items: [
                      { key: 'medical_39', keyName: '医疗保障领域失信联合惩戒对象名单', status: 1, sort: 1, code: 39 },
                      { key: 'medical_40', keyName: '医药卫生行业黑名单', status: 1, sort: 2, code: 40 },
                      { key: 'medical_41', keyName: '医药行业失信黑名单', status: 1, sort: 3, code: 41 },
                      { key: 'medical_68', keyName: '医疗、医药行业不良行为', status: 1, sort: 4, code: 68 },
                      { key: 'medical_79', keyName: '医保价格招采', status: 1, sort: 5, code: 79 },
                    ],
                  },
                  {
                    key: DimensionLevel3Enums.BiddingActivityDishonesty,
                    keyName: '招投标活动失信行为企业',
                    status: 1,
                    sort: 5,
                    isCategory: true,
                    showInFilter: false,
                    items: [
                      { key: 'bidding_56', keyName: '招投标活动失信行为企业', status: 1, sort: 1, code: 56 },
                      { key: 'bidding_80', keyName: '其他招投标及交易', status: 1, sort: 2, code: 80 },
                    ],
                  },
                  {
                    key: DimensionLevel3Enums.FireSafetyBlacklist,
                    keyName: '消防安全领域黑名单',
                    status: 1,
                    sort: 6,
                    isCategory: true,
                    showInFilter: false,
                    items: [{ key: 'fire_46', keyName: '消防安全领域黑名单', status: 1, sort: 1, code: 46 }],
                  },
                  {
                    key: DimensionLevel3Enums.FoodSafetyBlacklist,
                    keyName: '食品安全严重违法生产经营者黑名单',
                    status: 1,
                    sort: 7,
                    isCategory: true,
                    showInFilter: false,
                    items: [{ key: 'food_97', keyName: '食品安全严重违法生产经营者黑名单', status: 1, sort: 1, code: 97 }],
                  },
                  {
                    key: DimensionLevel3Enums.OtherIndustryBlacklist,
                    keyName: '其他行业领域黑名单',
                    status: 1,
                    sort: 8,
                    isCategory: true,
                    showInFilter: false,
                    items: [
                      { key: 'other_4', keyName: '出入境检验检疫信用管理严重失信企业名单', status: 1, sort: 1, code: 4 },
                      { key: 'other_54', keyName: '电力行业严重违法失信主体', status: 1, sort: 2, code: 54 },
                      { key: 'other_3', keyName: '统计领域严重失信企业及其有关人员', status: 1, sort: 3, code: 3 },
                      { key: 'other_24', keyName: '电子商务领域黑名单', status: 1, sort: 4, code: 24 },
                      { key: 'other_38', keyName: '价格失信黑名单', status: 1, sort: 5, code: 38 },
                      { key: 'other_55', keyName: '石油天然气行业严重违法失信主体', status: 1, sort: 6, code: 55 },
                      { key: 'other_69', keyName: '建筑行业不良行为', status: 1, sort: 7, code: 69 },
                      { key: 'other_98', keyName: '电信网络诈骗严重失信主体名单', status: 1, sort: 8, code: 98 },
                      { key: 'other_11', keyName: '涉金融领域失信企业黑名单', status: 1, sort: 9, code: 11 },
                      { key: 'other_8', keyName: '文化和旅游市场严重失信主体名单', status: 1, sort: 10, code: 8 },
                      { key: 'other_16', keyName: '公共资源配置黑名单', status: 1, sort: 11, code: 16 },
                      { key: 'other_29', keyName: '危害残疾儿童康复救助权益严重失信主体名单', status: 1, sort: 12, code: 29 },
                      { key: 'other_30', keyName: '社会救助领域信用黑名单', status: 1, sort: 13, code: 30 },
                      { key: 'other_32', keyName: '网络信用黑名单', status: 1, sort: 14, code: 32 },
                      { key: 'other_35', keyName: '物业服务企业黑名单', status: 1, sort: 15, code: 35 },
                      { key: 'other_36', keyName: '信息消费领域企业黑名单', status: 1, sort: 16, code: 36 },
                      { key: 'other_37', keyName: '城市轨道交通领域黑名单', status: 1, sort: 17, code: 37 },
                      { key: 'other_44', keyName: '学术期刊黑名单', status: 1, sort: 18, code: 44 },
                      { key: 'other_47', keyName: '校外培训机构黑名单', status: 1, sort: 19, code: 47 },
                      { key: 'other_48', keyName: '矿业权人严重失信名单', status: 1, sort: 20, code: 48 },
                      { key: 'other_49', keyName: '地质勘查单位黑名单', status: 1, sort: 21, code: 49 },
                      { key: 'other_50', keyName: '注册会计师行业严重失信主体名单', status: 1, sort: 22, code: 50 },
                      { key: 'other_51', keyName: '社会保险领域严重失信主体名单', status: 1, sort: 23, code: 51 },
                      { key: 'other_52', keyName: '快递领域违法失信主体"黑名单"', status: 1, sort: 24, code: 52 },
                      { key: 'other_53', keyName: '境外投资黑名单', status: 1, sort: 25, code: 53 },
                      { key: 'other_99', keyName: '养老服务领域失信联合惩戒对象名单', status: 1, sort: 26, code: 99 },
                      { key: 'other_106', keyName: '高频失信市场主体信息', status: 1, sort: 27, code: 106 },
                      { key: 'other_108', keyName: '淘汰落后和过剩产能企业名单', status: 1, sort: 28, code: 108 },
                      { key: 'other_105', keyName: '家政领域问题企业名单', status: 1, sort: 29, code: 105 },
                    ],
                  },
                ],
              },
              {
                key: DimensionLevel3Enums.ArmyProcurementIllegal,
                keyName: '军队采购失信名单',
                status: 0,
                sort: 11,
                isCategory: true,
                showInFilter: true,
                items: [
                  { key: 'army_75', keyName: '军队采购失信名单', status: 1, sort: 1, code: 75 },
                  { key: 'army_76', keyName: '军队采购暂停供应商资格', status: 1, sort: 2, code: 76 },
                  { key: 'army_104', keyName: '军队采购预警名单', status: 1, sort: 3, code: 104 },
                  { key: 'army_107', keyName: '军队采购知情告知书', status: 0, sort: 4, code: 107 },
                ],
              },
              {
                key: DimensionLevel3Enums.PrivateEnterpriseCooperationBlacklist,
                keyName: '名企不合作企业清单',
                status: 1,
                sort: 12,
                isCategory: true,
                showInFilter: false,
                items: [{ key: 'private_103', keyName: '名企不合作企业清单', status: 1, sort: 1, code: 103 }],
              },
            ],
          },
        ],
        status: 1,
        level: DimensionRiskLevelEnum.High,
        sort: 100,
      },
      {
        name: '产品召回',
        category: [78],
        key: MonitorRiskEnums.MonitorProductRecall,
        status: 1,
        level: DimensionRiskLevelEnum.Medium,
        sort: 110,
      },
      {
        name: '食品安全',
        category: [79],
        key: MonitorRiskEnums.MonitorFoodSafety,
        status: 1,
        level: DimensionRiskLevelEnum.Medium,
        sort: 120,
      },
      {
        name: '未准入境',
        category: [98],
        key: MonitorRiskEnums.MonitorUnauthorizedEntry,
        status: 1,
        level: DimensionRiskLevelEnum.Medium,
        sort: 130,
      },
      {
        name: '监管处罚',
        category: [121],
        key: MonitorRiskEnums.FinancialRegulation,
        status: 1,
        level: DimensionRiskLevelEnum.Medium,
        sort: 140,
      },
    ],
  },
  {
    name: '经营风险',
    key: MonitorRiskEnums.MonitorOperatingRisk,
    status: 1,
    sort: 4,
    items: [
      {
        name: '注销备案',
        category: [61],
        key: MonitorRiskEnums.MonitorCancellationOfFiling,
        status: 1,
        level: DimensionRiskLevelEnum.High,
        sort: 1,
      },
      {
        name: '简易注销',
        category: [23],
        key: MonitorRiskEnums.MonitorSimpleCancellation,
        status: 1,
        level: DimensionRiskLevelEnum.High,
        params: [
          {
            field: QueryParamsEnums.simpleCancellationStep,
            fieldName: '简易注销结果',
            fieldVal: [
              { key: 'inProgress', keyName: '正在进行简易注销公告', status: 1 },
              { key: 'approvalForCancellation', keyName: '准予注销', status: 1 },
              { key: 'permissionToCancel', keyName: '准许简易注销（未开业、无债权债务）', status: 1 },
              { key: 'rejection', keyName: '不予受理', status: 0 },
              { key: 'cancelled', keyName: '已撤销简易注销公告', status: 0 },
            ],
          },
        ],
        sort: 20,
      },
      {
        name: '票据违约',
        category: [108],
        key: MonitorRiskEnums.MonitorBillDefaults,
        status: 1,
        level: DimensionRiskLevelEnum.High,
        sort: 30,
      },
      {
        name: '票据承兑风险',
        key: MonitorRiskEnums.MonitorBillAcceptanceRisk,
        status: 1,
        level: DimensionRiskLevelEnum.High,
        params: [
          {
            field: QueryParamsEnums.billAcceptanceRiskStatus,
            fieldName: '名单类型',
            fieldVal: [
              { key: '1', keyName: '持续逾期名单', status: 1 },
              { key: '2', keyName: '承兑人逾期名单', status: 1 },
              { key: '3', keyName: '延迟披露名单', status: 0 },
              { key: '4', keyName: '信用信息未披露名单', status: 0 },
            ],
          },
        ],
        sort: 40,
      },
      {
        name: '动产抵押',
        category: [15],
        key: MonitorRiskEnums.MonitorChattelMortgage,
        status: 1,
        level: DimensionRiskLevelEnum.Medium,
        params: [
          {
            field: QueryParamsEnums.chattelMortgageMainAmount,
            fieldName: '被担保主债权数额', // 万元
            fieldOperator: OperatorEnums.ge,
            fieldVal: 0,
          },
        ],
        sort: 50,
      },
      {
        name: '土地抵押',
        category: [30],
        key: MonitorRiskEnums.MonitorLandMortgage,
        status: 1,
        level: DimensionRiskLevelEnum.Medium,
        params: [
          {
            // 抵押金额大于等于【】
            field: QueryParamsEnums.landMortgageAmount,
            fieldName: '抵押金额', // （万元）
            fieldOperator: OperatorEnums.ge,
            fieldVal: 0,
          },
        ],
        sort: 60,
      },
      {
        name: '股权出质',
        category: [12],
        key: MonitorRiskEnums.MonitorEquityPledge,
        status: 1,
        level: DimensionRiskLevelEnum.Medium,
        params: [
          // 暂时不支持参数设置
          // {
          //   field: QueryParamsEnums.taxArrearsAmount,
          //   fieldName: '出质比例/总股本',
          //   fieldOperator: OperatorEnums.ge,
          //   fieldVal: 0,
          // },
        ],
        sort: 70,
      },
      {
        name: '知识产权出质',
        category: [86],
        key: MonitorRiskEnums.IPRPledge,
        status: 1,
        level: DimensionRiskLevelEnum.Medium,
        params: [],
        sort: 80,
      },
      {
        name: '被列入不良资产',
        key: MonitorRiskEnums.MonitorNonPerformingAssets,
        status: 1,
        level: DimensionRiskLevelEnum.Medium,
        params: [
          {
            field: QueryParamsEnums.nonPerformingAssetAmount,
            fieldName: '资产金额', // （万元）
            fieldOperator: OperatorEnums.ge,
            fieldVal: 0,
          },
        ],
        sort: 81,
      },
      {
        name: '股权质押',
        category: [50],
        key: MonitorRiskEnums.MonitorEquityPawn,
        level: DimensionRiskLevelEnum.Medium,
        status: 1,
        sort: 90,
      },
      {
        name: '资产拍卖',
        category: [75],
        key: MonitorRiskEnums.MonitorAssetsAuction,
        level: DimensionRiskLevelEnum.Medium,
        status: 1,
        sort: 100,
      },
      {
        name: '担保信息',
        category: [101],
        key: MonitorRiskEnums.MonitorGuaranteeInfo,
        status: 1,
        level: DimensionRiskLevelEnum.Medium,
        params: [
          {
            field: QueryParamsEnums.guaranteeAmount,
            fieldName: '担保金额', // （万元）
            fieldOperator: OperatorEnums.ge,
            fieldVal: 0,
          },
        ],
        sort: 110,
      },
      {
        name: '经营状态变更',
        category: [38],
        key: MonitorRiskEnums.MonitorBusinessStatus,
        status: 1,
        level: DimensionRiskLevelEnum.High,
        sort: 120,
        params: [
          {
            field: QueryParamsEnums.businessStatus,
            fieldName: '经营状态',
            fieldVal: [
              { key: 'liquidation', keyName: '清算', status: 1 },
              { key: 'revoke', keyName: '吊销', status: 1 },
              { key: 'orderToClose', keyName: '责令关闭', status: 1 },
              { key: 'removeFromTheList', keyName: '除名', status: 1 },
              { key: 'suspendBusiness', keyName: '停业', status: 1 },
              { key: 'cancel', keyName: '注销', status: 1 },
              { key: 'annul', keyName: '撤销', status: 1 },
              { key: 'goOutOfBusiness', keyName: '歇业', status: 1 },
              { key: 'moveIn', keyName: '迁入', status: 0 },
              { key: 'moveOut', keyName: '迁出', status: 0 },
            ],
          },
        ],
      },
      {
        name: '有效关键资质非存续或临期提醒',
        category: [],
        key: MonitorRiskEnums.MonitorCertificationExpired,
        status: 1,
        level: DimensionRiskLevelEnum.High,
        sort: 130,
        params: [
          {
            field: QueryParamsEnums.businessLicense,
            fieldName: '营业执照',
            status: 0,
          },
          {
            field: QueryParamsEnums.certification,
            fieldName: '资质证书',
            fieldVal: [
              { key: '000002001', keyName: '质量管理体系认证', status: 1 },
              { key: '000002002', keyName: '环境管理体系认证', status: 0 },
              { key: '000002004', keyName: '食品安全管理体系认证', status: 0 },
              { key: '000003', keyName: '服务认证', status: 0 },
              { key: '000004', keyName: '自愿性产品认证', status: 0 },
              { key: '000005', keyName: '食品农产品认证', status: 0 },
              { key: '001', keyName: '电信业务经营许可证', status: 0 },
              { key: '004004', keyName: '建筑业企业资质证书', status: 0 },
              { key: '012001001', keyName: '医疗器械生产许可证', status: 0 },
              { key: '012001002', keyName: '医疗器械生产备案凭证', status: 0 },
              { key: '012002001', keyName: '医疗器械经营许可证', status: 0 },
              { key: '*********', keyName: '医疗器械经营备案凭证', status: 0 },
              { key: '*********', keyName: '医疗器械注册证', status: 0 },
              { key: '*********', keyName: '医疗器械备案凭证', status: 0 },
              { key: '023001', keyName: '食品生产许可证', status: 0 },
              { key: '024', keyName: '工业产品生产许可证', status: 0 },
              { key: '065001', keyName: '印刷经营许可证', status: 0 },
              { key: '023003', keyName: CompanyCertificationConstants['023003'], status: 0 },
              { key: '093001', keyName: CompanyCertificationConstants['093001'], status: 0 },
              { key: '*********', keyName: CompanyCertificationConstants['*********'], status: 0 },
              { key: '002002001004', keyName: CompanyCertificationConstants['002002001004'], status: 0 },
              { key: '002002001007', keyName: CompanyCertificationConstants['002002001007'], status: 0 },
              { key: '002002001028', keyName: CompanyCertificationConstants['002002001028'], status: 0 },
              { key: '002004001012', keyName: CompanyCertificationConstants['002004001012'], status: 0 },
              { key: '002004001002', keyName: CompanyCertificationConstants['002004001002'], status: 0 },
              { key: '002004001003', keyName: CompanyCertificationConstants['002004001003'], status: 0 },
              { key: '002004001004', keyName: CompanyCertificationConstants['002004001004'], status: 0 },
              { key: '002004001005', keyName: CompanyCertificationConstants['002004001005'], status: 0 },
              { key: '002004001006', keyName: CompanyCertificationConstants['002004001006'], status: 0 },
              { key: '002004001007', keyName: CompanyCertificationConstants['002004001007'], status: 0 },
              { key: '002004001008', keyName: CompanyCertificationConstants['002004001008'], status: 0 },
              { key: '002004001009', keyName: CompanyCertificationConstants['002004001009'], status: 0 },
              { key: '002004001010', keyName: CompanyCertificationConstants['002004001010'], status: 0 },
              { key: '002004001011', keyName: CompanyCertificationConstants['002004001011'], status: 0 },
              { key: '*********', keyName: CompanyCertificationConstants['*********'], status: 0 },
              { key: '*********', keyName: CompanyCertificationConstants['*********'], status: 0 },
              { key: '*********', keyName: CompanyCertificationConstants['*********'], status: 0 },
              { key: '098001', keyName: CompanyCertificationConstants['098001'], status: 0 },
              { key: '098002', keyName: CompanyCertificationConstants['098002'], status: 0 },
              { key: '*********', keyName: CompanyCertificationConstants['*********'], status: 0 },
              { key: '*********', keyName: CompanyCertificationConstants['*********'], status: 0 },
              { key: '*********', keyName: CompanyCertificationConstants['*********'], status: 0 },
              { key: '*********', keyName: CompanyCertificationConstants['*********'], status: 0 },
            ],
          },
          {
            field: QueryParamsEnums.nearExpirationType,
            fieldVal: 2, // 1-近 7 天；2-近一个月；3-近 3 个月；
          },
        ],
      },
      {
        name: '债券违约',
        category: [110],
        key: MonitorRiskEnums.BondDefault,
        status: 1,
        level: DimensionRiskLevelEnum.High,
        sort: 140,
      },
      {
        name: '企查分变更',
        key: MonitorRiskEnums.QccCreditChange,
        status: 1,
        level: DimensionRiskLevelEnum.Medium,
        sort: 150,
      },
    ],
  },
];
