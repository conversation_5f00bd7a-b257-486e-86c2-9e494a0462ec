import { DimensionRiskLevelEnum } from '@domain/enums/diligence/DimensionRiskLevelEnum';
import { QueryParamsEnums } from '@domain/model/diligence/pojo/dimension/dimension.filter.params';
import { DetailsParamEnums } from '@domain/enums/diligence/DetailsParamEnums';
import { IndicatorTypeEnums } from '@domain/model/settings/IndicatorTypeEnums';

export enum PotentialDimensionKeyEnums {
  StaffWorkingOutsideForeignInvestment = 'StaffWorkingOutsideForeignInvestment', // 潜在利益冲突
  SuspectedInterestConflict = 'SuspectedInterestConflict', // 疑似潜在利益冲突
}

export enum PotentialDimensionGroupEnums {
  StaffWorkingOutsideForeignInvestment = 'StaffWorkingOutsideForeignInvestment', // 潜在利益冲突
  SuspectedInterestConflict = 'SuspectedInterestConflict', // 疑似潜在利益冲突
}

export enum PotentialDimensionKeys {
  CompanyMainPartner = 'CompanyMainPartner', // 公司主要人员
  CompanyHistoryMainPartner = 'CompanyHistoryMainPartner', // 公司历史主要人员
  ParentCompany = 'ParentCompany', // 母公司
  ChildCompany = 'ChildCompany', // 子公司
  PersonRelatedCompany = 'PersonRelatedCompany', // 员工关联公司
  // BusinessRelatedCompany = 'BusinessRelatedCompany', // 上游供应商、下游客户
  SameNameOrContact = 'SameNameOrContact', // 相同姓名或联系方式
}

export const PotentialBaseDimension = {
  [PotentialDimensionKeys.CompanyMainPartner]: {
    key: PotentialDimensionKeys.CompanyMainPartner,
    name: '员工在被排查企业存在持股或任职',
    status: 1,
    sort: 1,
    dimensionType: 1,
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.High,
      detailsParams: [
        {
          field: QueryParamsEnums.types,
          fieldVal: [
            { key: 'Legal', name: '法定代表人', status: 1 },
            { key: 'Employ', name: '董监高', status: 1 },
            { key: 'Invest', name: '受益股东', status: 1 },
            { key: 'ActualController', name: '实际控制人', status: 1 },
            { key: 'FinalBenefit', name: '受益所有人', status: 1 },
          ],
          percentage: 25,
          sort: 0,
        },
      ],
    },
    template: '<em class="#level#">【#name#】 #count#条记录</em>',
    template2: '<em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '员工在被排查企业存在持股、任职',
  },
  [PotentialDimensionKeys.CompanyHistoryMainPartner]: {
    key: PotentialDimensionKeys.CompanyHistoryMainPartner,
    name: '员工在被排查企业存在历史持股或任职',
    status: 1,
    sort: 10,
    dimensionType: 2,
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.High,
      detailsParams: [
        {
          field: QueryParamsEnums.types,
          fieldVal: [
            { key: 'HisLegal', name: '历史法定代表人', status: 1 },
            { key: 'HisEmploy', name: '历史董监高', status: 1 },
            { key: 'HisInvest', name: '历史受益股东', status: 1 },
          ],
          percentage: 25,
          sort: 0,
        },
      ],
    },
    template: '<em class="#level#">【#name#】 #count#条记录</em>',
    template2: '<em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '被排查企业的股东、法定代表人、董事、监事、高管等与内部员工存在相同',
  },
  [PotentialDimensionKeys.ParentCompany]: {
    key: PotentialDimensionKeys.ParentCompany,
    name: '员工在被排查企业的直接控股母公司存在持股或任职',
    status: 1,
    sort: 20,
    dimensionType: 2,
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.High,
      detailsParams: [
        {
          field: QueryParamsEnums.percentage,
          fieldName: '持股比例',
          fieldVal: 50,
        },
        {
          field: QueryParamsEnums.depth,
          fieldName: '关联层级',
          fieldVal: 2,
        },
      ],
    },
    template: '<em class="#level#">【#name#】 #count#条记录</em>',
    template2: '<em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '员工在被排查企业的直接控股母公司存在持股或任职',
  },
  [PotentialDimensionKeys.ChildCompany]: {
    key: PotentialDimensionKeys.ChildCompany,
    name: '员工在被排查企业的全资/控股子公司存在持股或任职',
    status: 1,
    sort: 30,
    dimensionType: 2,
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.High,
      detailsParams: [
        {
          field: QueryParamsEnums.percentage,
          fieldName: '持股比例',
          fieldVal: 50,
        },
        {
          field: QueryParamsEnums.depth,
          fieldName: '关联层级',
          fieldVal: 2,
        },
      ],
    },
    template: '<em class="#level#">【#name#】 #count#条记录</em>',
    template2: '<em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '员工在被排查企业的全资/控股子公司存在持股或任职',
  },
  [PotentialDimensionKeys.PersonRelatedCompany]: {
    key: PotentialDimensionKeys.PersonRelatedCompany,
    name: '员工与被排查企业的主要关联企业的股东和高管在同一公司持股或任职',
    status: 1,
    sort: 40,
    dimensionType: 2,
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.High,
      detailsParams: [
        {
          field: QueryParamsEnums.types,
          fieldVal: [
            { key: 'Legal', name: '关联公司法定代表人', status: 1 },
            { key: 'Employ', name: '关联公司董监高', status: 1 },
            { key: 'Invest', name: '关联公司受益股东', status: 1 },
          ],
          percentage: 25,
          sort: 0,
        },
      ],
    },
    template: '<em class="#level#">【#name#】 #count#条记录</em>',
    template2: '<em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '员工与被排查企业的主要关联企业的股东和高管在同一公司持股或任职',
  },
  // [PotentialDimensionKeys.BusinessRelatedCompany]: {
  //   key: PotentialDimensionKeys.BusinessRelatedCompany,
  //   name: '员工持股或任职公司与被排查企业存在业务合作（上游供应商、下游客户）',
  //   status: 0,
  //   sort: 50,
  //   dimensionType: 2,
  //   strategyModel: {
  //     boost: 1.0,
  //     baseScore: 5,
  //     level: DimensionRiskLevelEnum.High,
  //     detailsParams: [
  //       {
  //         field: QueryParamsEnums.types,
  //         fieldVal: [
  //           { key: 'Upstream', name: '上游供应商', status: 1 },
  //           { key: 'Downstream', name: '下游客户', status: 1 },
  //         ],
  //         sort: 0,
  //       },
  //     ],
  //   },
  //   template: '<em class="#level#">【#name#】 #count#条记录</em>',
  //   template2: '<em class="#level#">【#name#】 #count#条记录</em>',
  //   type: IndicatorTypeEnums.generalItems,
  //   description: '员工持股或任职公司与被排查企业存在业务合作（上游供应商、下游客户）',
  // },
  [PotentialDimensionKeys.SameNameOrContact]: {
    key: PotentialDimensionKeys.SameNameOrContact,
    name: '员工与被排查企业的主要人员存在同名、相同联系方式（电话、邮箱）',
    status: 1,
    sort: 1,
    dimensionType: 1,
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.High,
      detailsParams: [
        {
          field: QueryParamsEnums.types,
          fieldVal: [
            { key: 'Legal', name: '法定代表人', status: 1 },
            { key: 'Employ', name: '董监高', status: 1 },
            { key: 'Invest', name: '受益股东', status: 1 },
            { key: 'HisLegal', name: '历史法定代表人', status: 1 },
            { key: 'HisEmploy', name: '历史董监高', status: 1 },
            { key: 'HisInvest', name: '历史受益股东', status: 1 },
            { key: 'ActualController', name: '实际控制人', status: 1 },
            { key: 'FinalBenefit', name: '受益所有人', status: 1 },
          ],
          percentage: 25,
          sort: 0,
        },
      ],
    },
    template: '<em class="#level#">【#name#】 #count#条记录</em>',
    template2: '<em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '员工与被排查企业的主要人员存在同名、相同联系方式（电话、邮箱）',
  },
};

export const PotentialDimensionSettings = {
  [PotentialDimensionGroupEnums.StaffWorkingOutsideForeignInvestment]: {
    name: '潜在利益冲突',
    status: 1,
    sort: 1,
    activeDimensionType: [1, 2],
    items: [
      PotentialBaseDimension[PotentialDimensionKeys.CompanyMainPartner],
      PotentialBaseDimension[PotentialDimensionKeys.CompanyHistoryMainPartner],
      PotentialBaseDimension[PotentialDimensionKeys.ParentCompany],
      PotentialBaseDimension[PotentialDimensionKeys.ChildCompany],
      PotentialBaseDimension[PotentialDimensionKeys.PersonRelatedCompany],
    ],
    personGroups: {
      key: DetailsParamEnums.Groups,
      keyName: '人员分组',
      value: [],
      nameList: [], //分组名称
    },
    personType: [1, 2], // 1:员工，2：员工亲属
  },
  [PotentialDimensionGroupEnums.SuspectedInterestConflict]: {
    name: '疑似潜在利益冲突',
    status: 1,
    sort: 20,
    items: [PotentialBaseDimension[PotentialDimensionKeys.SameNameOrContact]],
    personGroups: {
      key: DetailsParamEnums.Groups,
      keyName: '人员分组',
      value: [],
      nameList: [], //分组名称
    },
    activeDimensionType: [1, 2],
    personType: [1, 2], // 1:员工，2：员工亲属
  },
};
