/* eslint-disable @typescript-eslint/naming-convention */
import { ModelDefinitionPO } from '@domain/model/diligence/pojo/model/ModelDefinitionPO';
import { ScenarioTypeEnums } from './scenario.constants';
import { DetailsParamEnums } from '@domain/enums/diligence/DetailsParamEnums';

export enum ModelEnums {
  Risk = 'Risk',
  Debt = 'Debt',
  Credit = 'Credit',
}

export enum ModelType {
  Default = 'Default',
  Custom = 'Custom',
}

/**
 * 违约等级
 */
export const ContractBreachDegree = {
  L5: 'L5/极高风险',
  L4: 'L4/高风险',
  L3: 'L3/中风险',
};

export type ModelDefinitionsType = {
  [key in ModelEnums]: ModelDefinitionPO;
};

export const ModelDefinitions: ModelDefinitionsType = {
  [ModelEnums.Risk]: {
    key: ModelEnums.Risk,
    name: '风险评估',
    description: '风险筛查维度',
    scenarioList: [
      {
        key: ScenarioTypeEnums.A,
        priority: 1,
        strategyModel: {
          scenarioBoost: 100,
        },
      },
      {
        key: ScenarioTypeEnums.B,
        priority: 1,
        strategyModel: {
          scenarioBoost: 100,
        },
      },
    ],
    dimensionList: [],
  },
  [ModelEnums.Debt]: null,
  [ModelEnums.Credit]: null,
};

export const RelationTypeConst = {
  [DetailsParamEnums.ForeignInvestment]: '持股/投资关联',
  [DetailsParamEnums.Shareholder]: '参股股东',
  [DetailsParamEnums.EmploymentRelationship]: '董监高/法人关联',
  [DetailsParamEnums.HisForeignInvestment]: '历史持股/投资关联',
  [DetailsParamEnums.HisShareholder]: '参股股东（历史）',
  [DetailsParamEnums.ShareholdingRelationship]: '持股关联',
  [DetailsParamEnums.InvestorsRelationship]: '持股/投资关联',
  [DetailsParamEnums.HisInvestorsRelationship]: '历史持股/投资关联',
  [DetailsParamEnums.HisShareholdingRelationship]: '持股关联（历史）',
  [DetailsParamEnums.HisLegalAndEmploy]: '董监高/法人（历史）',
  [DetailsParamEnums.ActualController]: '相同实际控制人',
  [DetailsParamEnums.MainInfoUpdateBeneficiary]: '相同受益所有人',
  [DetailsParamEnums.ShareholdingRatio]: '持股/投资股权比例（含历史）',
  [DetailsParamEnums.Branch]: '分支机构',
  [DetailsParamEnums.HitInnerBlackList]: '被列入内部黑名单',
  [DetailsParamEnums.Invest]: '对外投资企业被列入内部黑名单',
  [DetailsParamEnums.Employ]: '董监高/法人关联',
  [DetailsParamEnums.HisEmploy]: '历史董监高/法人关联',
  [DetailsParamEnums.HisInvest]: '历史对外投资',
  [DetailsParamEnums.Guarantee]: '相互担保关联',
  [DetailsParamEnums.EquityPledgeRelation]: '股权出质关联',
  [DetailsParamEnums.Pledge]: '股权出质',
  [DetailsParamEnums.HasPledgee]: '股权出质',
  [DetailsParamEnums.HasPhone]: '相同电话号码',
  [DetailsParamEnums.WebsiteRelation]: '相同域名信息',
  [DetailsParamEnums.HasAddress]: '相同地址',
  [DetailsParamEnums.HasEmail]: '相同邮箱',
  [DetailsParamEnums.Hold]: '控制关系',
};

export const AssociationTypeConst = {
  [DetailsParamEnums.Employ]: '董监高',
  [DetailsParamEnums.LegalRepresentative]: '法定代表人',
  [DetailsParamEnums.Benefit]: '受益所有人',
  [DetailsParamEnums.ActualController]: '实际控制人',
  [DetailsParamEnums.MajorShareholder]: '大股东',
  [DetailsParamEnums.Shareholder]: '股东',
  [DetailsParamEnums.ShareHolderInvest]: '股东类型为投资机构',
  [DetailsParamEnums.Plaintiff]: '原告',
  [DetailsParamEnums.Defendant]: '被告',
  [DetailsParamEnums.otherAssociateObject]: '其他关联方',
};
