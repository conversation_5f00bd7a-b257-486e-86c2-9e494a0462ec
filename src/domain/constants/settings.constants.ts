/* eslint-disable @typescript-eslint/naming-convention */

// export const DefaultSettingTemplate: TemplateSettingsModel = {
//   [DimensionLevel1Enums.Risk_BaseInfo]: {
//     status: 1,
//     items: DimensionGroupDefinitions[DimensionLevel1Enums.Risk_BaseInfo],
//   },
//   [DimensionLevel1Enums.Risk_Legal]: {
//     status: 1,
//     items: DimensionGroupDefinitions[DimensionLevel1Enums.Risk_Legal],
//   },
//   [DimensionLevel1Enums.Risk_AdministrativeSupervision]: {
//     status: 1,
//     items: DimensionGroupDefinitions[DimensionLevel1Enums.Risk_AdministrativeSupervision],
//   },
//   [DimensionLevel1Enums.Risk_OperateStability]: {
//     status: 1,
//     items: DimensionGroupDefinitions[DimensionLevel1Enums.Risk_OperateStability],
//   },
//   [DimensionLevel1Enums.Risk_PartnerInvestigation]: {
//     status: 1,
//     items: DimensionGroupDefinitions[DimensionLevel1Enums.Risk_PartnerInvestigation],
//   },
//   [DimensionLevel1Enums.Risk_InnerBlacklist]: {
//     status: 1,
//     items: DimensionGroupDefinitions[DimensionLevel1Enums.Risk_InnerBlacklist],
//   },
//   [DimensionLevel1Enums.Risk_OuterBlacklist]: {
//     status: 1,
//     items: DefaultOuterBlacklistItems,
//   },
//
//   [DimensionLevel1Enums.Risk_InterestConflict]: {
//     status: 1,
//     items: DimensionGroupDefinitions[DimensionLevel1Enums.Risk_InterestConflict],
//   },
//   // qualification: {
//   //   status: 1,
//   //   items: DefaultQualificationItems,
//   // },
// };

import { ConfigurationModel } from '@domain/model/settings/ConfigurationModel';
import { AutoDiligenceAnalyzeIntervalEnum } from '@domain/enums/diligence/AutoDiligenceAnalyzeIntervalEnum';
import { UserConfigurationTypeEnums } from '@domain/model/settings/UserConfigurationTypeEnums';
import { ConfigurationTypeEnums } from '@domain/model/settings/ConfigurationTypeEnums';
import { DefaultMonitorMultiPushSetting, DefaultMonitorPushSetting } from './push.setting.constants';

export const DefaultConfigurationModel: ConfigurationModel = {
  [ConfigurationTypeEnums.diligence_analyze]: {
    on: false,
    interval: AutoDiligenceAnalyzeIntervalEnum.Monthly,
    pushSetting: { mailEnable: false, mailAddress: '' },
  },
};

export const DefaultUserConfigurationModel = {
  // [UserConfigurationTypeEnums.monitor_notify]: {
  //   mailEnable: false,
  //   mobileEnable: false,
  //   notifyTime: '09:00',
  //   onlyWorkday: false,
  // },
  [UserConfigurationTypeEnums.monitor_notify2]: DefaultMonitorPushSetting,
  [UserConfigurationTypeEnums.monitor_notify3]: DefaultMonitorMultiPushSetting,
};
