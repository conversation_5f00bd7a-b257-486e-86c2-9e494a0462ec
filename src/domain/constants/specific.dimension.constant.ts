// 特定利益关系排查默认配置
import { QueryParamsEnums } from '@domain/model/diligence/pojo/dimension/dimension.filter.params';
import { ExcludeNodeTypeEnums } from '@domain/enums/diligence/ExcludeNodeTypeEnums';
import { DimensionRiskLevelEnum } from '@domain/enums/diligence/DimensionRiskLevelEnum';

export enum SpecificDimensionKeyEnums {
  ActualController = 'ActualController', //上下游为母子公司或由相同的实际控制人控制
  CrossShareHolding = 'CrossShareHolding', //上下游企业交叉持股
  SameEmployee = 'SameEmployee', // 上下游企业主要负责人、董事、监事、高级管理人员相同
  ContactWay = 'ContactWay', // 上下游企业注册地址、实际办公地址、业务联系人或联系电话相同
  GuaranteeRelation = 'GuaranteeRelation', //上下游企业一方为另一方贸易合同履约提供担保
  UpAndDownRelation = 'UpAndDownRelation', // 上下游企业存在长期业务关系，一方为另一方的重要供应商或特约经销商
  OtherRelations = 'OtherRelations', // 其他根据实质重于形式原则认定存在特定利益关系的情形
  CompanyCount = 'CompanyCount', // 公司数量
}

export enum SpecificDetailKeyEnums {
  Invest = 'Invest', //持股投资关联
  HisInvest = 'HisInvest', //历史持股投资关联

  ControlRelation = 'ControlRelation', // 控制关系
  Hold = 'Hold', // 控制关系
  ActualController = 'ActualController', // 相同实际控制人
  Branch = 'Branch', // 分支机构

  Legal = 'Legal', // 法人
  Employ = 'Employ', // 董监高
  HisLegal = 'HisLegal', // 历史法人
  HisEmploy = 'HisEmploy', // 历史董监高

  ContactNumber = 'ContactNumber', // 相同电话号码
  Mail = 'Mail', // 相同邮箱
  Address = 'Address', // 相同地址

  UpAndDownRelation = 'UpAndDownRelation', // 上下游关联

  Guarantor = 'Guarantor', // 相互担保关联

  EquityPledge = 'EquityPledge', // 股权出质关联
  ChattelMortgage = 'ChattelMortgage', // 动产抵押关联
  Website = 'Website', // 相同域名信息
  Patent = 'Patent', // 相同专利信息
  IntPatent = 'IntPatent', // 相同国际专利信息
  SoftwareCopyright = 'SoftwareCopyright', // 相同软件著作权
}

// types 表示共用分组设置，subDimensionList 表示支持子维度单独设置
export const SpecificDimensionSettings = [
  {
    key: SpecificDimensionKeyEnums.CompanyCount,
    name: '特定利益关系排查企业数量设置',
    status: 1,
    sort: 0,
    strategyModel: {
      detailsParams: [
        {
          field: QueryParamsEnums.limitCount,
          fieldVal: 20,
        },
      ],
    },
  },
  {
    key: SpecificDimensionKeyEnums.ActualController,
    name: '上下游为母子公司或由相同的实际控制人控制',
    status: 1,
    sort: 1,
    template: '#companyNames#存在上下游为母子公司或由相同的实际控制人控制',
    template2: '与#companyNames#存在上下游为母子公司或由相同的实际控制人控制',
    description: '上下游为母子公司或由相同的实际控制人控制',
    strategyModel: {
      level: DimensionRiskLevelEnum.High,
      // detailsParams: [
      //   {
      //     field: QueryParamsEnums.depth,
      //     fieldVal: 3,
      //   },
      //   {
      //     field: QueryParamsEnums.percentage,
      //     fieldVal: 0.01,
      //   },
      //   {
      //     field: QueryParamsEnums.excludedTypes,
      //     fieldVal: [ExcludeNodeTypeEnums.InvestmentAgency], // 默认排除投资机构
      //   },
      // ],
    },
    types: [
      {
        key: SpecificDetailKeyEnums.ControlRelation,
        name: '控制关系',
        status: 1,
        sort: 1,
      },
      {
        key: SpecificDetailKeyEnums.ActualController,
        name: '相同实际控制人',
        status: 1,
        sort: 2,
      },
      {
        key: SpecificDetailKeyEnums.Branch,
        name: '分支机构',
        status: 1,
        sort: 3,
      },
    ],
  },
  {
    key: SpecificDimensionKeyEnums.CrossShareHolding,
    name: '上下游企业交叉持股',
    status: 1,
    sort: 10,
    template: '#companyNames#存在上下游企业交叉持股',
    template2: '与#companyNames#存在上下游企业交叉持股',
    description: '上下游企业交叉持股',
    strategyModel: {
      level: DimensionRiskLevelEnum.High,
      detailsParams: [
        {
          field: QueryParamsEnums.depth,
          fieldVal: 3,
        },
        {
          field: QueryParamsEnums.percentage,
          fieldVal: 0.01,
        },
        {
          field: QueryParamsEnums.excludedTypes,
          fieldVal: [ExcludeNodeTypeEnums.InvestmentAgency], // 默认排除投资机构
        },
        {
          field: QueryParamsEnums.crossShareHoldingMode,
          fieldVal: 1, //1-交叉持股关联，2-自定义关联类型
        },
      ],
    },
    types: [
      {
        key: SpecificDetailKeyEnums.Invest,
        name: '持股投资关联',
        status: 1,
        sort: 1,
      },
      {
        key: SpecificDetailKeyEnums.HisInvest,
        name: '历史持股/投资关联',
        status: 1,
        sort: 2,
      },
    ],
  },
  {
    key: SpecificDimensionKeyEnums.SameEmployee,
    name: '上下游企业主要负责人、董事、监事、高级管理人员相同',
    status: 1,
    sort: 20,
    template: '#companyNames#存在上下游企业主要负责人、董事、监事、高级管理人员相同',
    template2: '与#companyNames#存在上下游企业主要负责人、董事、监事、高级管理人员相同',
    description: '上下游企业主要负责人、董事、监事、高级管理人员相同',
    strategyModel: {
      level: DimensionRiskLevelEnum.High,
      // detailsParams: [
      //   {
      //     field: QueryParamsEnums.depth,
      //     fieldVal: 3,
      //   },
      //   {
      //     field: QueryParamsEnums.percentage,
      //     fieldVal: 0.01,
      //   },
      //   {
      //     field: QueryParamsEnums.excludedTypes,
      //     fieldVal: [ExcludeNodeTypeEnums.InvestmentAgency], // 默认排除投资机构
      //   },
      // ],
    },
    types: [
      {
        key: SpecificDetailKeyEnums.Legal,
        name: '法定代表人',
        status: 1,
        sort: 1,
      },
      {
        key: SpecificDetailKeyEnums.Employ,
        name: '董监高',
        status: 1,
        sort: 2,
      },
      {
        key: SpecificDetailKeyEnums.HisLegal,
        name: '历史法定代表人',
        status: 1,
        sort: 3,
      },
      {
        key: SpecificDetailKeyEnums.HisEmploy,
        name: '历史董监高',
        status: 1,
        sort: 4,
      },
    ],
  },
  {
    key: SpecificDimensionKeyEnums.ContactWay,
    name: '上下游企业注册地址、实际办公地址、业务联系人或联系电话相同',
    status: 1,
    sort: 30,
    template: '#companyNames#存在上下游企业注册地址、实际办公地址、业务联系人或联系电话相同',
    template2: '与#companyNames#存在上下游企业注册地址、实际办公地址、业务联系人或联系电话相同',
    description: '上下游企业注册地址、实际办公地址、业务联系人或联系电话相同',
    strategyModel: {
      level: DimensionRiskLevelEnum.Medium,
    },
    types: [
      {
        key: SpecificDetailKeyEnums.ContactNumber,
        name: '相同电话号码',
        status: 1,
        sort: 1,
      },
      {
        key: SpecificDetailKeyEnums.Mail,
        name: '相同邮箱',
        status: 1,
        sort: 2,
      },
      {
        key: SpecificDetailKeyEnums.Address,
        name: '相同地址',
        status: 1,
        sort: 3,
      },
    ],
  },
  {
    key: SpecificDimensionKeyEnums.GuaranteeRelation,
    name: '上下游企业一方为另一方贸易合同履约提供担保',
    status: 1,
    sort: 40,
    template: '#companyNames#存在上下游企业一方为另一方贸易合同履约提供担保',
    template2: '与#companyNames#存在上下游企业一方为另一方贸易合同履约提供担保',
    description: '上下游企业一方为另一方贸易合同履约提供担保',
    strategyModel: {
      level: DimensionRiskLevelEnum.High,
    },
    types: [
      {
        key: SpecificDetailKeyEnums.Guarantor,
        name: '相互担保关联',
        status: 1,
        sort: 1,
      },
    ],
  },
  {
    key: SpecificDimensionKeyEnums.UpAndDownRelation,
    name: '上下游企业存在长期业务关系，一方为另一方的重要供应商或特约经销商',
    status: 1,
    sort: 50,
    template: '#companyNames#存在上下游企业存在长期业务关系，一方为另一方的重要供应商或特约经销商',
    template2: '与#companyNames#存在上下游企业存在长期业务关系，一方为另一方的重要供应商或特约经销商',
    description: '上下游企业存在长期业务关系，一方为另一方的重要供应商或特约经销商',
    strategyModel: {
      level: DimensionRiskLevelEnum.Medium,
    },
    types: [
      {
        key: SpecificDetailKeyEnums.UpAndDownRelation,
        name: '上下游业务关联',
        status: 1,
        sort: 1,
      },
    ],
  },
  {
    key: SpecificDimensionKeyEnums.OtherRelations,
    name: '其他根据实质重于形式原则认定存在特定利益关系的情形',
    status: 1,
    sort: 60,
    template: '#companyNames#存在其他根据实质重于形式原则认定存在特定利益关系的情形',
    template2: '与#companyNames#存在其他根据实质重于形式原则认定存在特定利益关系的情形',
    description: '其他根据实质重于形式原则认定存在特定利益关系的情形',
    strategyModel: {
      level: DimensionRiskLevelEnum.Medium,
    },
    types: [
      {
        key: SpecificDetailKeyEnums.EquityPledge,
        name: '股权出质关联',
        status: 1,
        sort: 1,
      },
      {
        key: SpecificDetailKeyEnums.ChattelMortgage,
        name: '动产抵押关联',
        status: 1,
        sort: 2,
      },
      {
        key: SpecificDetailKeyEnums.Website,
        name: '相同域名信息',
        status: 1,
        sort: 3,
      },
      {
        key: SpecificDetailKeyEnums.Patent,
        name: '相同专利信息',
        status: 1,
        sort: 4,
      },
      {
        key: SpecificDetailKeyEnums.IntPatent,
        name: '相同国际专利信息',
        status: 1,
        sort: 5,
      },
      {
        key: SpecificDetailKeyEnums.SoftwareCopyright,
        name: '相同软件著作权',
        status: 1,
        sort: 6,
      },
    ],
  },
];

export const getSpecificDimensionKeyNames = () => {
  return SpecificDimensionSettings.reduce((acc, cur) => {
    acc[cur.key] = cur.name;
    return acc;
  }, {});
};
