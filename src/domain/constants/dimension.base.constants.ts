import { ConditionOperatorEnums } from '@domain/enums/diligence/ConditionOperatorEnums';
import { DetailsParamEnums } from '@domain/enums/diligence/DetailsParamEnums';
import { DimensionLevel2Enums } from '@domain/enums/diligence/DimensionLevel2Enums';
import { DimensionLevel3Enums } from '@domain/enums/diligence/DimensionLevel3Enums';
import { DimensionRiskLevelEnum } from '@domain/enums/diligence/DimensionRiskLevelEnum';
import { DimensionSourceEnums } from '@domain/enums/diligence/DimensionSourceEnums';
import { ExcludeNodeTypeEnums } from '@domain/enums/diligence/ExcludeNodeTypeEnums';
import { QueryParamsEnums } from '@domain/model/diligence/pojo/dimension/dimension.filter.params';
import { OperatorEnums } from '@domain/model/diligence/pojo/dimension/DimensionQueryPO';
import { IndicatorTypeEnums } from '@domain/model/settings/IndicatorTypeEnums';
import { DefaultOuterBlacklistItems } from './blacklist.outer.constants';
import { SimpleCancellationTypes, TopicTypes } from './common';
import { CompanyCertificationConstants } from './company-certification.constants';
import { CreditType } from './credit.analyze.constants';
import { AssociationTypeConst, RelationTypeConst } from './model.constants';

export const templateString = '<em class="#level#">【#name#】</em>';
export const templateString2 = '【#name#】';

export const BaseDimensions: any = {
  [DimensionLevel3Enums.UnfairCompetition]: {
    key: DimensionLevel3Enums.UnfairCompetition,
    name: '不正当竞争纠纷',
    strategyModel: {
      boost: 1.0,
      baseScore: 30,
      level: DimensionRiskLevelEnum.Medium,
      sortField: { field: 'LastestDate', order: 'DESC', fieldSnapshot: 'LastestDate' },
      cycle: 3,
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Case,
    sourcePath: '',
    status: 1,
    template: '<em class="#level#">【#name#】#cycle# #count#条记录</em>',
    template2: '#name#',
    type: IndicatorTypeEnums.generalItems,
    description: '企业存在不正当竞争纠纷案件',
  },
  [DimensionLevel3Enums.NoticeInTimePeriod]: {
    key: DimensionLevel3Enums.NoticeInTimePeriod,
    name: '近期多起开庭公告',
    strategyModel: {
      boost: 1.0,
      baseScore: 10,
      level: DimensionRiskLevelEnum.Medium,
      sortField: { field: 'CreateDate', order: 'DESC' },
      detailsParams: [
        {
          field: QueryParamsEnums.recentTime,
          fieldName: '时间范围',
          fieldVal: 3, // 1-近 7 天；2-近一个月；3-近 3 个月；
        },
        {
          field: QueryParamsEnums.limitCount,
          fieldName: '数量设置',
          fieldOperator: OperatorEnums.ge,
          fieldVal: 3,
        },
        {
          field: QueryParamsEnums.isValid,
          fieldVal: '1', //当前有效
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.RiskChange,
    sourcePath: '',
    status: 1,
    template: '<em class="#level#">【#name#】#recentTime# #count#条记录</em>',
    template2: '【#name#】#recentTime# #count#条记录',
    type: IndicatorTypeEnums.generalItems,
    description: '企业在近期内发生多起开庭公告信息',
  },
  [DimensionLevel2Enums.FakeSOES]: {
    key: DimensionLevel2Enums.FakeSOES,
    name: '假冒国企',
    strategyModel: {
      boost: 1.0,
      baseScore: 40,
      level: DimensionRiskLevelEnum.Medium,
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.CompanyDetail,
    status: 1,
    template: '<em class="#level#">【#name#】 #count#条记录</em>',
    template2: '被列为【#name#】',
    type: IndicatorTypeEnums.generalItems,
    typeCode: '3101',
    description: '企业或控股股东、母公司因假冒国企被公示',
  },
  [DimensionLevel2Enums.NoCapital]: {
    // 久无实缴  无实缴资本-202503迭代修改为实缴资本异常RA14536
    key: DimensionLevel2Enums.NoCapital,
    name: '实缴资本异常',
    strategyModel: {
      boost: 1.0,
      baseScore: 20,
      level: DimensionRiskLevelEnum.Medium,
      detailsParams: [
        {
          field: QueryParamsEnums.paidInCapitalRatio,
          fieldOperator: OperatorEnums.le,
          fieldVal: 10, // 单位 %
          sort: 0,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.CompanyDetail,
    status: 0,
    template: templateString,
    template2: templateString2,
    type: IndicatorTypeEnums.generalItems,
    typeCode: '6310',
    description: '企业无实缴或实缴资本规模过低',
  },
  [DimensionLevel2Enums.FakeRegister]: {
    key: DimensionLevel2Enums.FakeRegister,
    name: '涉嫌冒名登记',
    strategyModel: {
      boost: 1.0,
      baseScore: 20,
      level: DimensionRiskLevelEnum.Medium,
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/Risk/FakeRegisters',
    status: 1,
    template: '<em class="#level#">#cycle#【#name#】 #count#条记录</em>',
    template2: '涉嫌冒名登记',
    type: IndicatorTypeEnums.generalItems,
    description: '企业因涉嫌未经授权或违反法律法规，利用他人或虚构的名义注册企业或进行经营活动，被有关机关撤销公司登记、备案等信息',
  },
  [DimensionLevel2Enums.LowCapital]: {
    key: DimensionLevel2Enums.LowCapital,
    name: '注册资本过低',
    strategyModel: {
      boost: 1.0,
      baseScore: 10,
      level: DimensionRiskLevelEnum.Medium,
      detailsParams: [
        {
          field: QueryParamsEnums.registrationAmount,
          fieldOperator: OperatorEnums.lt,
          fieldVal: 100,
          sort: 0,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.CompanyDetail,
    sourcePath: '',
    status: 1,
    template: '<em class="#level#">【#name#】#operator##amountW#</em>',
    template2: '#name##operator##amountW#',
    type: IndicatorTypeEnums.generalItems,
    description: '将企业注册资本作为准入门槛',
  },
  [DimensionLevel2Enums.CompanyShell]: {
    key: DimensionLevel2Enums.CompanyShell,
    name: '疑似空壳企业',
    strategyModel: {
      boost: 1.0,
      baseScore: 40,
      level: DimensionRiskLevelEnum.High,
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: 'api/innerslb/shellco/shellCompanyLabel',
    status: 1,
    template: templateString,
    template2: templateString2,
    type: IndicatorTypeEnums.generalItems,
    typeCode: '0',
    description: '企业疑似空壳企业',
  },
  //已废弃，原因：误报较多，且信息滞后
  [DimensionLevel2Enums.FraudList]: {
    key: DimensionLevel2Enums.FraudList,
    name: '涉诈高风险名单',
    strategyModel: {
      boost: 1.0,
      baseScore: 10,
      level: DimensionRiskLevelEnum.Medium,
    },
    isVirtualDimension: 0,
    // 数据来源 同步表 qfk_applydata.qfk_shell_company_black_list_unique
    source: DimensionSourceEnums.CompanyDetail,
    sourcePath: '',
    status: 0,
    template: templateString,
    template2: '#name#',
    type: IndicatorTypeEnums.generalItems,
    typeCode: '1310',
    description: '企业被列入涉诈高风险名单',
    isHidden: true,
  },
  [DimensionLevel2Enums.EstablishedTime]: {
    key: DimensionLevel2Enums.EstablishedTime,
    name: '新成立企业',
    strategyModel: {
      boost: 1.0,
      baseScore: 10,
      level: DimensionRiskLevelEnum.Medium,
      detailsParams: [
        {
          field: QueryParamsEnums.duration,
          fieldOperator: OperatorEnums.lt,
          fieldVal: 12,
          sort: 0,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.CompanyDetail,
    sourcePath: '',
    status: 1,
    template: '<em class="#level#">【成立时长】#operator##amountMonth#</em>',
    template2: '成立时长#operator##amountMonth#',
    type: IndicatorTypeEnums.generalItems,
    description: '将企业成立时长作为准入门槛',
  },
  [DimensionLevel2Enums.NoCertification]: {
    key: DimensionLevel2Enums.NoCertification,
    name: '无有效关键资质认证',
    strategyModel: {
      boost: 1.0,
      baseScore: 10,
      level: DimensionRiskLevelEnum.Medium,
      detailsParams: [
        {
          field: QueryParamsEnums.certification,
          fieldVal: [
            {
              key: '*********',
              keyName: CompanyCertificationConstants['*********'],
              status: 1,
            },
            {
              key: '*********',
              keyName: CompanyCertificationConstants['*********'],
              status: 0,
            },
            {
              key: '*********',
              keyName: CompanyCertificationConstants['*********'],
              status: 0,
            },
            {
              key: '000003',
              keyName: CompanyCertificationConstants['000003'],
              status: 0,
            },
            {
              key: '000004',
              keyName: CompanyCertificationConstants['000004'],
              status: 0,
            },
            {
              key: '000005',
              keyName: CompanyCertificationConstants['000005'],
              status: 0,
            },
            {
              key: '001',
              keyName: CompanyCertificationConstants['001'],
              status: 0,
            },
            {
              key: '004004',
              keyName: CompanyCertificationConstants['004004'],
              status: 0,
            },
            {
              key: '011',
              keyName: CompanyCertificationConstants['011'],
              status: 0,
            },
            {
              key: '*********',
              keyName: CompanyCertificationConstants['*********'],
              status: 0,
            },
            {
              key: '*********',
              keyName: CompanyCertificationConstants['*********'],
              status: 0,
            },
            {
              key: '*********',
              keyName: CompanyCertificationConstants['*********'],
              status: 0,
            },
            {
              key: '*********',
              keyName: CompanyCertificationConstants['*********'],
              status: 0,
            },
            {
              key: '*********',
              keyName: CompanyCertificationConstants['*********'],
              status: 0,
            },
            {
              key: '*********',
              keyName: CompanyCertificationConstants['*********'],
              status: 0,
            },
            {
              key: '027001',
              keyName: CompanyCertificationConstants['027001'],
              status: 0,
            },
            {
              key: '900',
              keyName: CompanyCertificationConstants['900'],
              status: 0,
            },
          ],
          sort: 0,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/IPR/Certification/Summary',
    status: 0,
    template: templateString,
    template2: '#name#',
    type: IndicatorTypeEnums.generalItems,
    description: '企业无有效关键资质或资质已过有效期',
    isHidden: true,
  },
  [DimensionLevel2Enums.Certification]: {
    key: DimensionLevel2Enums.Certification,
    name: '资质筛查',
    strategyModel: {
      boost: 1.0,
      baseScore: 10,
      level: DimensionRiskLevelEnum.Medium,
      sortField: { field: 'index', order: 'DESC', fieldSnapshot: 'index' },
      detailsParams: [
        {
          field: QueryParamsEnums.businessLicense,
          fieldName: '营业执照',
          status: 0,
        },
        {
          field: QueryParamsEnums.taxpayerList,
          fieldName: '纳税人资质',
          fieldVal: [
            {
              key: '一般纳税人',
              keyName: '一般纳税人资质',
              status: 0,
            },
            {
              key: '小规模纳税人',
              keyName: '小规模纳税人资质',
              status: 0,
            },
          ],
        },
        {
          field: QueryParamsEnums.certification,
          fieldVal: [
            {
              key: '*********',
              keyName: CompanyCertificationConstants['*********'],
              status: 1,
            },
            {
              key: '*********',
              keyName: CompanyCertificationConstants['*********'],
              status: 0,
            },
            {
              key: '*********',
              keyName: CompanyCertificationConstants['*********'],
              status: 0,
            },
            {
              key: '000003',
              keyName: CompanyCertificationConstants['000003'],
              status: 0,
            },
            {
              key: '000004',
              keyName: CompanyCertificationConstants['000004'],
              status: 0,
            },
            {
              key: '*********',
              keyName: CompanyCertificationConstants['*********'],
              status: 0,
            },
            {
              key: '*********',
              keyName: CompanyCertificationConstants['*********'],
              status: 0,
            },
            {
              key: '000005',
              keyName: CompanyCertificationConstants['000005'],
              status: 0,
            },
            {
              key: '001',
              keyName: CompanyCertificationConstants['001'],
              status: 0,
            },
            {
              key: '004004',
              keyName: CompanyCertificationConstants['004004'],
              status: 0,
            },
            {
              key: '011',
              keyName: CompanyCertificationConstants['011'],
              status: 0,
            },
            {
              key: '*********',
              keyName: CompanyCertificationConstants['*********'],
              status: 0,
            },
            {
              key: '*********',
              keyName: CompanyCertificationConstants['*********'],
              status: 0,
            },
            {
              key: '*********',
              keyName: CompanyCertificationConstants['*********'],
              status: 0,
            },
            {
              key: '*********',
              keyName: CompanyCertificationConstants['*********'],
              status: 0,
            },
            {
              key: '*********',
              keyName: CompanyCertificationConstants['*********'],
              status: 0,
            },
            {
              key: '*********',
              keyName: CompanyCertificationConstants['*********'],
              status: 0,
            },
            {
              key: '027001',
              keyName: CompanyCertificationConstants['027001'],
              status: 0,
            },
            {
              key: '900',
              keyName: CompanyCertificationConstants['900'],
              status: 0,
            },
            {
              key: '023001',
              keyName: CompanyCertificationConstants['023001'],
              status: 0,
            },
            {
              key: '024',
              keyName: CompanyCertificationConstants['024'],
              status: 0,
            },
            {
              key: '065001',
              keyName: CompanyCertificationConstants['065001'],
              status: 0,
            },
            {
              key: '023003',
              keyName: CompanyCertificationConstants['023003'],
              status: 0,
            },
            {
              key: '093001',
              keyName: CompanyCertificationConstants['093001'],
              status: 0,
            },
            {
              key: '*********',
              keyName: CompanyCertificationConstants['*********'],
              status: 0,
            },
            { key: '002004001012', keyName: CompanyCertificationConstants['002004001012'], status: 0 },
            { key: '002004001002', keyName: CompanyCertificationConstants['002004001002'], status: 0 },
            { key: '002004001003', keyName: CompanyCertificationConstants['002004001003'], status: 0 },
            { key: '002004001004', keyName: CompanyCertificationConstants['002004001004'], status: 0 },
            { key: '002004001005', keyName: CompanyCertificationConstants['002004001005'], status: 0 },
            { key: '002004001006', keyName: CompanyCertificationConstants['002004001006'], status: 0 },
            { key: '002004001007', keyName: CompanyCertificationConstants['002004001007'], status: 0 },
            { key: '002004001008', keyName: CompanyCertificationConstants['002004001008'], status: 0 },
            { key: '002004001009', keyName: CompanyCertificationConstants['002004001009'], status: 0 },
            { key: '002004001010', keyName: CompanyCertificationConstants['002004001010'], status: 0 },
            { key: '002004001011', keyName: CompanyCertificationConstants['002004001011'], status: 0 },
            { key: '*********', keyName: CompanyCertificationConstants['*********'], status: 0 },
            { key: '098001', keyName: CompanyCertificationConstants['098001'], status: 0 },
            { key: '098002', keyName: CompanyCertificationConstants['098002'], status: 0 },
            { key: '*********', keyName: CompanyCertificationConstants['*********'], status: 0 },
            { key: '*********', keyName: CompanyCertificationConstants['*********'], status: 0 },
            { key: '*********', keyName: CompanyCertificationConstants['*********'], status: 0 },
            { key: '*********', keyName: CompanyCertificationConstants['*********'], status: 0 },
          ],
          sort: 0,
        },
        {
          field: QueryParamsEnums.nearExpirationType,
          fieldVal: 2, // 1-近 7 天；2-近一个月；3-近 3 个月；
          sort: 1,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/IPR/Certification/List',
    status: 0,
    template: '<em class="#level#">【#name#】 #count#项资质缺失或临期 </em>',
    template2: '#name# 存在#count#项资质缺失或临期 ',
    type: IndicatorTypeEnums.generalItems,
    description: '企业关键资质（包含营业执照、纳税人资质、资质证书等）缺失/非有效状态 或即将到期',
  },
  [DimensionLevel2Enums.NoQualityCertification]: {
    key: DimensionLevel2Enums.NoQualityCertification,
    name: '无有效质量管理体系认证资质',
    strategyModel: {
      boost: 1.0,
      baseScore: 10,
      level: DimensionRiskLevelEnum.Medium,
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/IPR/Certification/Summary',
    status: 0,
    template: templateString,
    template2: '#name#',
    type: IndicatorTypeEnums.generalItems,
    description: '企业无质量管理系统认证资质或资质已过有效期',
    isHidden: true,
  },
  [DimensionLevel2Enums.TaxationOffences]: {
    key: DimensionLevel2Enums.TaxationOffences,
    name: '税收违法',
    strategyModel: {
      boost: 1.0,
      baseScore: 30,
      level: DimensionRiskLevelEnum.High,
      cycle: -1,
      detailsParams: [
        {
          field: QueryParamsEnums.isValid,
          fieldVal: '1',
          sort: 0,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Credit,
    sourcePath: '',
    status: 1,
    template: '<em class="#level#">#cycle#【#name#】 #count#条记录</em>',
    template2: '#cycle#存在【#name#】案件#count#条记录',
    type: IndicatorTypeEnums.generalItems,
    description: '企业因虚开发票、偷税等税务违法行为被处罚',
  },
  [DimensionLevel2Enums.Bankruptcy]: {
    key: DimensionLevel2Enums.Bankruptcy,
    name: '破产重整',
    strategyModel: {
      boost: 1.0,
      baseScore: 80,
      level: DimensionRiskLevelEnum.High,
      sortField: { field: 'publishdate', order: 'DESC', fieldSnapshot: 'PublishDate' },
      detailsParams: [
        {
          field: QueryParamsEnums.isValid,
          fieldVal: '1', //当前有效
          sort: 0,
        },
        {
          field: QueryParamsEnums.associateObject,
          fieldVal: [
            {
              key: DetailsParamEnums.ActualController,
              keyName: AssociationTypeConst[DetailsParamEnums.ActualController],
              status: 0,
            },
            {
              key: DetailsParamEnums.Benefit,
              keyName: AssociationTypeConst[DetailsParamEnums.Benefit],
              status: 0,
            },
            {
              key: DetailsParamEnums.MajorShareholder,
              keyName: AssociationTypeConst[DetailsParamEnums.MajorShareholder],
              status: 0,
            },
            {
              key: DetailsParamEnums.Shareholder,
              keyName: AssociationTypeConst[DetailsParamEnums.Shareholder],
              status: 0,
            },
          ],
          sort: 1,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Credit,
    sourcePath: '/api/QccSearch/CreditSearch/Credit',
    status: 1,
    template: '<em class="#level#">【#name#】 #count#条记录</em>',
    template2: '已进入【#name#】程序',
    type: IndicatorTypeEnums.generalItems,
    description: '企业及实控人、股东等核心关联方进入破产重整程序',
  },
  [DimensionLevel2Enums.FreezeEquity]: {
    key: DimensionLevel2Enums.FreezeEquity,
    name: '有股权被冻结',
    strategyModel: {
      boost: 1.0,
      baseScore: 10,
      cycle: 3,
      level: DimensionRiskLevelEnum.Medium,
      sortField: { field: 'liandate', order: 'DESC', fieldSnapshot: 'LianDate' },
      detailsParams: [
        {
          field: QueryParamsEnums.equityAmount,
          fieldOperator: OperatorEnums.ge,
          fieldVal: 200000, // 单位 元
          sort: 0,
        },
        {
          field: QueryParamsEnums.isValid,
          fieldVal: '1', //当前有效
          sort: 1,
        },
        {
          field: QueryParamsEnums.associateObject,
          fieldVal: [
            {
              key: DetailsParamEnums.Employ,
              keyName: AssociationTypeConst[DetailsParamEnums.Employ],
              status: 0,
            },
            {
              key: DetailsParamEnums.LegalRepresentative,
              keyName: AssociationTypeConst[DetailsParamEnums.LegalRepresentative],
              status: 0,
            },
            {
              key: DetailsParamEnums.ActualController,
              keyName: AssociationTypeConst[DetailsParamEnums.ActualController],
              status: 0,
            },
            {
              key: DetailsParamEnums.Benefit,
              keyName: AssociationTypeConst[DetailsParamEnums.Benefit],
              status: 0,
            },
            {
              key: DetailsParamEnums.MajorShareholder,
              keyName: AssociationTypeConst[DetailsParamEnums.MajorShareholder],
              status: 0,
            },
            {
              key: DetailsParamEnums.Shareholder,
              keyName: AssociationTypeConst[DetailsParamEnums.Shareholder],
              status: 0,
            },
          ],
          sort: 2,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Credit,
    sourcePath: '',
    status: 1,
    template: '<em class="#level#">【#name#】#cycle# #count#条记录</em><span class="#isHidden#">，冻结股权数额：<em class="#level#">#amountW#</em></span>',
    template2: '【#name#】 #count#条记录<span class="#isHidden#">，冻结股权数额：#amountW#</span>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业及主要人员（董监高法）、核心关联方股权被法院冻结无法进行变更',
  },
  [DimensionLevel2Enums.PersonExecution]: {
    key: DimensionLevel2Enums.PersonExecution,
    name: '被执行人信息',
    strategyModel: {
      boost: 1.0,
      baseScore: 10,
      level: DimensionRiskLevelEnum.Medium,
      sortField: { field: 'liandate', fieldSnapshot: 'LiAnDate', order: 'DESC' },
      detailsParams: [
        {
          field: QueryParamsEnums.executionSum,
          fieldOperator: OperatorEnums.ge,
          fieldVal: 100000, // 金额 元
          sort: 0,
        },
        {
          field: QueryParamsEnums.isValid,
          fieldVal: '1',
          sort: 1,
        },
      ],
    },
    isVirtualDimension: 0,
    // 不使用信用接口原因: 信用数据 未对属于同一司法案件的执行标的做去重处理
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/Court/SearchZhiXing', //'/api/Court/SearchZhiXing',
    status: 1,
    template: '<em class="#level#">【#name#】 #count#条记录</em><span class="#isHidden#">，涉及金额：<em class="#level#">#amountW#</em></span>',
    template2: '【#name#】 #count#条<span class="#isHidden#">，涉及金额：#amountW#</span>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业因未履行法院判决而被强制执行',
  },
  [DimensionLevel2Enums.JudicialAuction]: {
    key: DimensionLevel2Enums.JudicialAuction,
    name: '司法拍卖信息',
    strategyModel: {
      boost: 1.0,
      baseScore: 10,
      level: DimensionRiskLevelEnum.Medium,
      sortField: { field: 'startdate', order: 'DESC', fieldSnapshot: 'StartDate' },
      detailsParams: [
        // 不开放数据范围控制，企业库接口默认是当前有效
        // {
        //   field: QueryParamsEnums.isValid,
        //   fieldVal: '1',
        // },
      ],
      cycle: 3,
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/Court/GetJudicialSaleList',
    status: 1,
    template: '<em class="#level#">【#name#】#cycle#  #count#条记录</em>',
    template2: '【#name#】 #count#条记录',
    type: IndicatorTypeEnums.generalItems,
    description: '企业被法院强制执行拍卖处理资产用于清偿债务',
  },
  [DimensionLevel2Enums.ChattelSeizure]: {
    key: DimensionLevel2Enums.ChattelSeizure,
    name: '动产查封',
    strategyModel: {
      boost: 1.0,
      baseScore: 10,
      level: DimensionRiskLevelEnum.High,
      sortField: { field: 'publishdate', order: 'DESC', fieldSnapshot: 'PublicDate' },
      detailsParams: [
        {
          field: QueryParamsEnums.isValid,
          fieldVal: '1',
          sort: 0,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/Risk/GetMovablePropertySeizures',
    status: 1,
    template: '<em class="#level#">【#name#】 #count#条记录</em>',
    template2: '【#name#】 #count#条记录',
    type: IndicatorTypeEnums.generalItems,
    description: '企业因无法履行义务，导致其持有的动产被查封',
  },
  [DimensionLevel2Enums.ContractBreach]: {
    key: DimensionLevel2Enums.ContractBreach,
    name: '合同违约',
    strategyModel: {
      boost: 1.0,
      baseScore: 10,
      level: DimensionRiskLevelEnum.Medium,
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/ContractBreach/ContractBreachInfo',
    status: 1,
    template: '<em class="#level#">【#name#】次数 #count#次</em>，涉案金额：<em class="#level#">#amountW#</em>，违约等级：<em class="#level#">#degree#</em>',
    template2: '【#name#】次数 #count#次，涉案金额：#amountW#，违约等级：#degree#',
    type: IndicatorTypeEnums.generalItems,
    description: '企业涉及合同违约案件分析结果',
  },
  [DimensionLevel2Enums.CompanyCredit]: {
    key: DimensionLevel2Enums.CompanyCredit,
    name: '被列入严重违法失信企业名录',
    strategyModel: {
      boost: 1.0,
      baseScore: 20,
      level: DimensionRiskLevelEnum.High,
      detailsParams: [
        {
          field: QueryParamsEnums.associateObject,
          fieldVal: [
            {
              key: DetailsParamEnums.ActualController,
              keyName: AssociationTypeConst[DetailsParamEnums.ActualController],
              status: 0,
            },
            {
              key: DetailsParamEnums.MajorShareholder,
              keyName: AssociationTypeConst[DetailsParamEnums.MajorShareholder],
              status: 0,
            },
            {
              key: DetailsParamEnums.Shareholder,
              keyName: AssociationTypeConst[DetailsParamEnums.Shareholder],
              status: 0,
            },
          ],
          sort: 1,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.EnterpriseLib, // 不需要走 credit es 直接用企业库countInfo接口
    sourcePath: '/api/QccSearch/List/SeriousViolation', //'/api/dimension/gov-supervision/get-serious-violation-list',
    status: 1,
    template: '<em class="#level#">【#name#】 #count#条记录</em>',
    template2: '【#name#】 #count#条记录',
    type: IndicatorTypeEnums.generalItems,
    description: '企业及实控人、股东被市场监管部门列入严重违法失信企业名录',
  },
  [DimensionLevel2Enums.CompanyCreditHistory]: {
    key: DimensionLevel2Enums.CompanyCreditHistory,
    name: '被列入严重违法失信企业名录（历史）',
    strategyModel: {
      boost: 1.0,
      baseScore: 10,
      level: DimensionRiskLevelEnum.Medium,
      cycle: 3,
      detailsParams: [
        {
          field: QueryParamsEnums.associateObject,
          fieldVal: [
            {
              key: DetailsParamEnums.ActualController,
              keyName: AssociationTypeConst[DetailsParamEnums.ActualController],
              status: 0,
            },
            {
              key: DetailsParamEnums.MajorShareholder,
              keyName: AssociationTypeConst[DetailsParamEnums.MajorShareholder],
              status: 0,
            },
            {
              key: DetailsParamEnums.Shareholder,
              keyName: AssociationTypeConst[DetailsParamEnums.Shareholder],
              status: 0,
            },
          ],
          sort: 1,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.EnterpriseLib, // 不需要走 credit es 直接用企业库countInfo接口
    sourcePath: '/api/QccSearch/List/SeriousViolation', //'/api/dimension/gov-supervision/get-serious-violation-list',
    status: 1,
    template: '<em class="#level#">【#name#】#cycle#  #count#条记录</em>',
    template2: '【#name#】 #count#条记录',
    type: IndicatorTypeEnums.generalItems,
    description: '企业及实控人、股东曾被市场监管部门列入严重违法失信企业名录',
  },
  [DimensionLevel2Enums.OperationAbnormal]: {
    key: DimensionLevel2Enums.OperationAbnormal,
    name: '被列入经营异常名录（历史）',
    strategyModel: {
      boost: 1.0,
      baseScore: 10,
      level: DimensionRiskLevelEnum.Medium,
      cycle: 3,
      sortField: { field: 'occurrencedate', order: 'DESC', fieldSnapshot: 'CurrenceDate' },
      detailsParams: [
        {
          field: QueryParamsEnums.businessAbnormalType,
          fieldVal: ['0801', '0802', '0803', '0804', '0805', '0806', '0807'],
          sort: 0,
        },
        {
          field: QueryParamsEnums.associateObject,
          fieldVal: [
            {
              key: DetailsParamEnums.ActualController,
              keyName: AssociationTypeConst[DetailsParamEnums.ActualController],
              status: 0,
            },
            {
              key: DetailsParamEnums.MajorShareholder,
              keyName: AssociationTypeConst[DetailsParamEnums.MajorShareholder],
              status: 0,
            },
            {
              key: DetailsParamEnums.Shareholder,
              keyName: AssociationTypeConst[DetailsParamEnums.Shareholder],
              status: 0,
            },
          ],
          sort: 1,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Credit,
    sourcePath: '',
    status: 1,
    template: '<em class="#level#">【#name#】#cycle#  #count#条记录</em>',
    template2: '【#name#】',
    type: IndicatorTypeEnums.generalItems,
    description: '企业及实控人、股东曾被市场监管部门列为经营异常名录',
  },
  [DimensionLevel2Enums.TaxArrearsNotice]: {
    key: DimensionLevel2Enums.TaxArrearsNotice,
    name: '欠税公告',
    strategyModel: {
      boost: 1.0,
      baseScore: 10,
      level: DimensionRiskLevelEnum.Medium,
      sortField: { field: 'liandate', order: 'DESC', fieldSnapshot: 'PublishDate' },
      cycle: 3,
      detailsParams: [
        {
          field: QueryParamsEnums.taxArrearsAmount,
          fieldOperator: OperatorEnums.ge,
          fieldVal: 10000,
          sort: 0,
        },
        {
          field: QueryParamsEnums.isValid,
          fieldVal: '1',
          sort: 1,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/Tax/GetListOfOweNoticeNew',
    status: 1,
    // 近3年欠税公告  XX条记录，欠税余额：XXX万元
    template: '<em class="#level#">【#name#】#cycle#  #count#条记录</em><span class="#isHidden#">，欠税余额：<em class="#level#">#amountW#</em></span>',
    template2: '【#name#】 #count#条记录<span class="#isHidden#">，欠税余额：#amountW#</span>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业未在规定的期限缴纳税款',
  },
  [DimensionLevel2Enums.SpotCheck]: {
    key: DimensionLevel2Enums.SpotCheck,
    name: '抽查检查-不合格',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.Medium,
      cycle: 3,
      sortField: { field: 'publishdate', order: 'DESC', fieldSnapshot: 'PublishDate' },
      detailsParams: [
        {
          field: QueryParamsEnums.isValid,
          fieldVal: '1',
          sort: 0,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Credit,
    sourcePath: '',
    status: 1,
    template: '<em class="#level#">【#name#】#cycle# #count#条记录</em>',
    template2: '【#name#】#cycle# #count#条记录',
    type: IndicatorTypeEnums.generalItems,
    description: '企业在抽查检查中存在不合格记录',
  },
  [DimensionLevel2Enums.AdministrativePenalties2]: {
    key: DimensionLevel2Enums.AdministrativePenalties2,
    name: '涉及商业贿赂、垄断行为或政府采购活动违法行为行政处罚',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.Medium,
      cycle: 3,
      sortField: { field: 'punishdate', order: 'DESC', fieldSnapshot: 'PublishDate' },
      detailsParams: [
        {
          field: QueryParamsEnums.isValid,
          fieldVal: '1',
          sort: 0,
        },
        {
          field: QueryParamsEnums.associateObject,
          fieldVal: [
            {
              key: DetailsParamEnums.ActualController,
              keyName: AssociationTypeConst[DetailsParamEnums.ActualController],
              status: 0,
            },
            {
              key: DetailsParamEnums.MajorShareholder,
              keyName: AssociationTypeConst[DetailsParamEnums.MajorShareholder],
              status: 0,
            },
            {
              key: DetailsParamEnums.Shareholder,
              keyName: AssociationTypeConst[DetailsParamEnums.Shareholder],
              status: 0,
            },
          ],
          sort: 1,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Credit,
    sourcePath: '',
    status: 1,
    template: '<em class="#level#">【#name#】#cycle# #count#条记录</em>',
    template2: '【#name#】#cycle# #count#条记录',
    type: IndicatorTypeEnums.generalItems,
    description: '企业及实控人、股东因涉及商业贿赂、垄断行为或政府采购活动违法行为而被行政处罚',
  },
  [DimensionLevel2Enums.AdministrativePenalties3]: {
    key: DimensionLevel2Enums.AdministrativePenalties3,
    name: '3年前涉及商业贿赂、垄断行为或政府采购活动违法行为行政处罚',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.Medium,
      sortField: { field: 'punishdate', order: 'DESC', fieldSnapshot: 'PublishDate' },
      detailsParams: [
        {
          field: QueryParamsEnums.isValid,
          fieldVal: '1',
          sort: 0,
        },
        {
          field: QueryParamsEnums.associateObject,
          fieldVal: [
            {
              key: DetailsParamEnums.ActualController,
              keyName: AssociationTypeConst[DetailsParamEnums.ActualController],
              status: 0,
            },
            {
              key: DetailsParamEnums.MajorShareholder,
              keyName: AssociationTypeConst[DetailsParamEnums.MajorShareholder],
              status: 0,
            },
            {
              key: DetailsParamEnums.Shareholder,
              keyName: AssociationTypeConst[DetailsParamEnums.Shareholder],
              status: 0,
            },
          ],
          sort: 1,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Credit,
    sourcePath: '',
    status: 1,
    template: '<em class="#level#">【#name#】#cycle# #count#条记录</em>',
    template2: '【#name#】#cycle# #count#条记录',
    type: IndicatorTypeEnums.generalItems,
    description: '企业及实控人、股东3年前曾因涉及涉及商业贿赂、垄断行为或政府采购活动违法行为而被行政处罚',
  },
  [DimensionLevel2Enums.AdministrativePenalties]: {
    key: DimensionLevel2Enums.AdministrativePenalties,
    name: '行政处罚',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.Medium,
      cycle: 3,
      sortField: { field: 'punishdate', order: 'DESC', fieldSnapshot: 'PunishDate' },
      detailsParams: [
        {
          field: QueryParamsEnums.punishReasonType,
          fieldVal: ['201', '202', '203', '301', '0'], //【围串标】、【分包/转包/挂靠】、【虚假材料】、【商业贿赂】、【其他】
          fieldOperator: OperatorEnums.contain,
          sort: 0,
        },
        {
          field: QueryParamsEnums.penaltiesType,
          fieldVal: ['0908', '0911', '0915', '0910', '0914', '0909', '0912', '0913', '0903', '0904', '0905', '0906', '0907', '0901', '0902'],
          fieldOperator: OperatorEnums.contain,
          sort: 1,
        },
        {
          field: QueryParamsEnums.penaltiesAmount,
          fieldVal: 100000,
          fieldOperator: OperatorEnums.ge,
          sort: 2,
        },
        {
          field: QueryParamsEnums.conditionOperator,
          fieldVal: ConditionOperatorEnums.Or,
          sort: 3,
        },
        {
          field: QueryParamsEnums.isValid,
          fieldVal: '1',
          sort: 4,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.SupervisePunish,
    sourcePath: '',
    status: 1,
    template: '<em class="#level#">【#name#】#cycle# #count#条记录</em><span class="#isHidden#">，罚款总金额：<em class="#level#">#amountW#</em></span>',
    template2: '【#name#】#cycle# #count#条记录<span class="#isHidden#">，罚款总金额：#amountW#</span>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业被行政处罚',
  },
  [DimensionLevel2Enums.TaxPenalties]: {
    key: DimensionLevel2Enums.TaxPenalties,
    name: '税务处罚',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.Medium,
      cycle: 3,
      sortField: { field: 'punishdate', order: 'DESC', fieldSnapshot: 'PunishDate' },
      detailsParams: [
        {
          field: QueryParamsEnums.penaltiesType,
          fieldVal: ['0908', '0911', '0915', '0910', '0914', '0909', '0912', '0913', '0903', '0904', '0905', '0906', '0907', '0901', '0902'],
          fieldOperator: OperatorEnums.contain,
          sort: 1,
        },
        {
          field: QueryParamsEnums.penaltiesAmount,
          fieldVal: 100000,
          fieldOperator: OperatorEnums.ge,
          sort: 2,
        },
        {
          field: QueryParamsEnums.isValid,
          fieldVal: '1',
          sort: 3,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.SupervisePunish,
    sourcePath: '',
    status: 1,
    template: '<em class="#level#">【#name#】#cycle# #count#条记录</em><span class="#isHidden#">，罚款总金额：<em class="#level#">#amountW#</em></span>',
    template2: '【#name#】#cycle# #count#条记录<span class="#isHidden#">，罚款总金额：#amountW#</span>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业被税务处罚',
  },
  [DimensionLevel2Enums.EnvironmentalPenalties]: {
    key: DimensionLevel2Enums.EnvironmentalPenalties,
    name: '环保处罚',
    strategyModel: {
      boost: 1.0,
      baseScore: 10,
      level: DimensionRiskLevelEnum.Medium,
      cycle: 3,
      sortField: { field: 'punishdate', order: 'DESC', fieldSnapshot: 'PunishDate' },
      detailsParams: [
        {
          field: QueryParamsEnums.isValid,
          fieldVal: '1',
          sort: 0,
        },
        {
          field: QueryParamsEnums.penaltiesType,
          fieldVal: ['0908', '0911', '0915', '0910', '0914', '0909', '0913', '0903', '0904', '0905', '0906', '0907', '0901', '0902'],
          fieldOperator: OperatorEnums.contain,
          sort: 1,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Credit,
    sourcePath: '',
    status: 1,
    // 近3年环保处罚xx条记录，罚款总金额：
    template: '<em class="#level#">【#name#】#cycle# #count#条记录</em><span class="#isHidden#">，罚款总金额：<em class="#level#">#amountW#</em></span>',
    template2: '【#name#】#cycle# #count#条记录<span class="#isHidden#">，罚款总金额：#amountW#</span>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业因违规排放等问题造成环境污染而收到处罚',
  },
  [DimensionLevel2Enums.TaxCallNotice]: {
    key: DimensionLevel2Enums.TaxCallNotice,
    name: '税务催缴公告',
    strategyModel: {
      boost: 1.0,
      baseScore: 10,
      level: DimensionRiskLevelEnum.Medium,
      cycle: 1,
      sortField: { field: 'publicdate', order: 'DESC', fieldSnapshot: 'publicdate' },
      detailsParams: [],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.TaxAnnouncement,
    sourcePath: '',
    status: 0,
    template: '<em class="#level#">【#name#】#cycle#  #count#条记录</em><span class="#isHidden#">',
    template2: '【#name#】 #count#条记录<span class="#isHidden#">',
    type: IndicatorTypeEnums.generalItems,
    description: '企业因未及时缴纳税款被税务部门公告催缴',
    isHidden: true,
  },
  [DimensionLevel2Enums.TaxReminder]: {
    key: DimensionLevel2Enums.TaxReminder,
    name: '税务催报',
    strategyModel: {
      boost: 1.0,
      baseScore: 10,
      level: DimensionRiskLevelEnum.Medium,
      cycle: 1,
      sortField: { field: 'publishdate', order: 'DESC', fieldSnapshot: 'PublishDate' },
      detailsParams: [
        {
          field: QueryParamsEnums.isValid,
          fieldVal: '1',
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/Risk/GetTaxReminders',
    status: 1,
    template: '<em class="#level#">【#name#】#cycle#  #count#条记录</em><span class="#isHidden#">',
    template2: '【#name#】 #count#条记录<span class="#isHidden#">',
    type: IndicatorTypeEnums.generalItems,
    description: '企业未按照规定的期限办理纳税申报和报送纳税资料，被税务机关催报',
  },
  [DimensionLevel2Enums.TaxCallNoticeV2]: {
    key: DimensionLevel2Enums.TaxCallNoticeV2,
    name: '税务催缴',
    strategyModel: {
      boost: 1.0,
      baseScore: 10,
      level: DimensionRiskLevelEnum.Medium,
      cycle: 1,
      sortField: { field: 'publishdate', order: 'DESC', fieldSnapshot: 'PublishDate' },
      detailsParams: [
        {
          field: QueryParamsEnums.isValid,
          fieldVal: '1',
          sort: 2,
        },
        {
          field: QueryParamsEnums.AmountOwed,
          fieldVal: 100000,
          fieldOperator: OperatorEnums.ge,
          sort: 1,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/Risk/GetTaxReminders',
    status: 1,
    template: '<em class="#level#">【#name#】#cycle#  #count#条记录<span class="#isHidden#">，涉及欠缴金额<em class="#level#">#amountW#</em></span></em>',
    template2: '【#name#】#count#条记录<span class="#isHidden#">，涉及欠缴金额：#amountW#</span>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业因未及时缴纳税款被税务部门公告催缴',
  },
  [DimensionLevel2Enums.BondDefaults]: {
    key: DimensionLevel2Enums.BondDefaults,
    name: '债券违约',
    strategyModel: {
      boost: 1.0,
      baseScore: 60,
      level: DimensionRiskLevelEnum.High,
      sortField: { field: 'publicdate', order: 'DESC', fieldSnapshot: 'MaturityDate' },
      detailsParams: [
        {
          field: QueryParamsEnums.isValid,
          fieldVal: '1',
          sort: 0,
        },
      ],
    },
    isVirtualDimension: 0,
    detailSourcePath: '/api/FinancialInfo/Bond/BondDefaultList',
    // 维度详情使用企业库接口
    // detailSource: DimensionSourceEnums.EnterpriseLib,
    // 排查 切换到 信用接口，
    // 累计违约本金(亿元) ： Amount(亿元) - Amount2（亿元）
    // 累计违约利息(亿元) ： Amount2
    // 债券类型： Casereasontype
    // 首次违约日期：PublishDate
    // 到期日期：LianDate
    // source: DimensionSourceEnums.Credit,
    // sourcePath: '/api/QccSearch/CreditSearch/Credit', //'/api/finance/bond/get-bond-default-list',
    // 信用大数据无法命中，切换到企业库接口，2025-04-08 RA-15283
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/FinancialInfo/Bond/BondDefaultList',
    status: 1,
    // 存在债权违约，累计违约本金：XX万元，累计违约利息：XX万元；目标主体存在偿债风险，建议收紧授信
    template:
      '<em class="#level#">【#name#】#count#条记录</em><span class="#isHiddenY#">，累计违约本金：<em class="#level#">#amountY#</em></span><span class="#isHidden#">，累计违约利息：<em class="#level#">#amount2Y#</em></span>',
    template2:
      '存在【#name#】<span class="#isHiddenY#">，累计违约本金：#amountY#</span><span class="#isHidden#">，累计违约利息：#amount2Y#</span>；目标主体存在偿债风险，建议收紧授信',
    type: IndicatorTypeEnums.generalItems,
    description: '企业有债券到期未偿违约记录',
  },
  [DimensionLevel2Enums.BillDefaults]: {
    key: DimensionLevel2Enums.BillDefaults,
    name: '票据违约',
    strategyModel: {
      boost: 1.0,
      baseScore: 10,
      level: DimensionRiskLevelEnum.High,
      cycle: 3,
      sortField: { field: 'publishdate', order: 'DESC', fieldSnapshot: 'PublishDate' },
      // detailsParams: [
      //   {
      //     field: QueryParamsEnums.isValid,
      //     fieldVal: '1',
      //     sort: 0,
      //   },
      // ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/Risk/BillDefault',
    status: 1,
    // 票据违约  XX条记录
    template: '<em class="#level#">【#name#】#cycle#  #count#条记录</em>',
    template2: '【#name#】 #count#条记录',
    type: IndicatorTypeEnums.generalItems,
    description: '企业存在未能按约定履行支付义务的票据交易记录',
  },
  //持续逾期升级为票据承兑风险
  [DimensionLevel2Enums.PersistentBillOverdue]: {
    key: DimensionLevel2Enums.PersistentBillOverdue,
    name: '票据承兑风险',
    strategyModel: {
      boost: 1.0,
      baseScore: 10,
      level: DimensionRiskLevelEnum.High,
      detailsParams: [
        {
          //1承兑人逾期名单 2持续逾期名单 3信用信息未披露名单 4延迟披露名单
          field: QueryParamsEnums.billAcceptanceRiskStatus,
          fieldVal: [1, 2],
          sort: 0,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Rover,
    sourcePath: '',
    status: 1,
    template: '<em class="#level#">【#name#】 #count#条记录</em>',
    template2: '【#name#】 #count#条记录',
    type: IndicatorTypeEnums.generalItems,
    description: '企业被列入上海票据交易所公布的持续逾期名单/承兑人逾期名单/信用信息未披露名单/延迟披露名单',
  },
  [DimensionLevel2Enums.ChattelMortgage]: {
    key: DimensionLevel2Enums.ChattelMortgage,
    name: '动产抵押',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.Medium,
      sortField: { field: 'liandate', order: 'DESC', fieldSnapshot: 'RegisterDate' },
      detailsParams: [
        {
          field: QueryParamsEnums.isValid,
          fieldVal: '1',
          sort: 0,
        },
        {
          field: QueryParamsEnums.amount,
          fieldVal: 0,
          fieldOperator: OperatorEnums.ge,
          sort: 1,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/QccSearch/List/MPledge',
    status: 0,
    template: '<em class="#level#">【#name#】 #count#条记录</em>',
    template2: '【#name#】 #count#条记录',
    type: IndicatorTypeEnums.generalItems,
    description: '企业将动产抵押获取融资的行为（信息来源于工商公示/中登网）',
    isHidden: true,
  },
  [DimensionLevel2Enums.PledgeMerger]: {
    key: DimensionLevel2Enums.PledgeMerger,
    name: '动产抵押',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.Medium,
      sortField: { field: 'registerstartdate', order: 'DESC', fieldSnapshot: 'RegisterDate' },
      detailsParams: [
        {
          field: QueryParamsEnums.isValid,
          fieldVal: '1',
          sort: 0,
        },
        {
          field: QueryParamsEnums.amount,
          fieldVal: 0,
          fieldOperator: OperatorEnums.ge,
          sort: 1,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.PledgeMergerES, //20250717迭代动产抵押数据源切换融合es
    sourcePath: '',
    status: 1,
    template: '<em class="#level#">【#name#】 #count#条记录</em>',
    template2: '【#name#】 #count#条记录',
    type: IndicatorTypeEnums.generalItems,
    description: '企业将动产抵押获取融资的行为（信息来源于工商公示/中登网）',
  },
  [DimensionLevel2Enums.EquityPledge]: {
    key: DimensionLevel2Enums.EquityPledge,
    name: '股权出质',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.Medium,
      cycle: 3,
      sortField: { field: 'liandate', order: 'DESC', fieldSnapshot: 'RegDate' },
      detailsParams: [
        {
          field: QueryParamsEnums.isValid,
          fieldVal: '1',
          sort: 0,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Pledge,
    status: 1,
    template: '<em class="#level#">【#name#】#cycle#  #count#条记录</em>',
    template2: '【#name#】 #count#条记录',
    type: IndicatorTypeEnums.generalItems,
    description: '企业存在股权出质信息',
  },
  [DimensionLevel2Enums.IPRPledge]: {
    key: DimensionLevel2Enums.IPRPledge,
    name: '知识产权出质',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.Medium,
      cycle: 3,
      sortField: { order: 'DESC', fieldSnapshot: 'PublishDate' },
      detailsParams: [
        {
          field: QueryParamsEnums.isValid,
          fieldVal: '1',
          sort: 0,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/QccSearch/List/IPRPledge',
    status: 1,
    template: '<em class="#level#">【#name#】#cycle#  #count#条记录</em>',
    template2: '【#name#】 #count#条记录',
    type: IndicatorTypeEnums.generalItems,
    description: '企业存在知识产权出质信息',
  },
  [DimensionLevel2Enums.NonPerformingAssets]: {
    key: DimensionLevel2Enums.NonPerformingAssets,
    name: '被列入不良资产',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.Medium,
      cycle: 3,
      sortField: { field: 'publishdate', order: 'DESC', fieldSnapshot: 'publishDate' },
      detailsParams: [
        {
          field: QueryParamsEnums.isValid,
          fieldVal: '1',
          sort: 0,
        },
        {
          field: QueryParamsEnums.nonPerformingAssetAmount,
          fieldVal: 0,
          fieldOperator: OperatorEnums.ge,
          sort: 1,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.NonPerformingAssets,
    sourcePath: '',
    status: 1,
    template: '<em class="#level#">【#name#】 #count#条记录</em><span class="#isHidden#">，资产金额：<em class="#level#">#amountW#</em></span>',
    template2: '【#name#】<span class="#isHidden#">，资产金额：#amountW#</span>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业存在不良资产信息',
  },
  [DimensionLevel2Enums.LandMortgage]: {
    key: DimensionLevel2Enums.LandMortgage,
    name: '土地抵押',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.Medium,
      cycle: 3,
      detailsParams: [
        {
          field: QueryParamsEnums.landMortgageAmount,
          fieldVal: 0,
          fieldOperator: OperatorEnums.ge,
          sort: 0,
        },
        {
          field: QueryParamsEnums.isValid,
          fieldVal: '1',
          sort: 10,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/LandMgmt/GetListOfLandMortgage',
    status: 1,
    template: '<em class="#level#">【#name#】#cycle#  #count#条记录,涉及抵押金额<em class="#level#">#amountW#</em></span></em>',
    template2: '【#name#】#cycle#  #count#条记录<span class="#isHidden#">，涉及抵押金额：#amountW#</span>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业将土地进行抵押获取融资的行为',
  },
  [DimensionLevel2Enums.GuaranteeRisk]: {
    key: DimensionLevel2Enums.GuaranteeRisk,
    name: '担保风险',
    strategyModel: {
      boost: 1.0,
      baseScore: 10,
      level: DimensionRiskLevelEnum.Medium,
      cycle: 3,
      detailsParams: [
        {
          field: QueryParamsEnums.guaranteedprincipal,
          fieldOperator: OperatorEnums.ge,
          fieldVal: 0,
          sort: 0,
        },
        {
          field: QueryParamsEnums.isValid,
          fieldVal: '1',
          sort: 1,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/QccSearch/List/GuarantorRisk',
    status: 1,
    // 担保风险  XX条记录，被保证债权本金：XX万元
    template: '<em class="#level#">【#name#】#cycle#  #count#条记录</em><span class="#isHidden#">，被保证债权本金：<em class="#level#">#amountW#</em></span>',
    template2: '【#name#】#cycle#  #count#条记录<span class="#isHidden#">，被保证债权本金：#amountW#</span>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业存在担保风险信息',
  },
  [DimensionLevel2Enums.GuaranteeInfo]: {
    key: DimensionLevel2Enums.GuaranteeInfo,
    name: '担保信息',
    strategyModel: {
      boost: 1.0,
      baseScore: 10,
      level: DimensionRiskLevelEnum.Medium,
      cycle: 3,
      detailsParams: [
        {
          field: QueryParamsEnums.performanceStatus,
          fieldVal: [0], //0未履行完毕 1.已履行完毕 2.其他
          sort: 0,
        },
        {
          field: QueryParamsEnums.guaranteeAmount,
          fieldOperator: OperatorEnums.ge,
          fieldVal: 0,
          sort: 1,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/QccSearch/List/Guarantor',
    status: 1,
    template: '<em class="#level#">【#name#】 #count#条记录</em>',
    template2: '【#name#】 #count#条记录',
    type: IndicatorTypeEnums.generalItems,
    description: '企业存在担保信息',
  },
  [DimensionLevel2Enums.NoTender]: {
    key: DimensionLevel2Enums.NoTender,
    name: '无招投标记录',
    strategyModel: {
      boost: 1.0,
      baseScore: 2,
      level: DimensionRiskLevelEnum.Medium,
      cycle: 1,
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Tender,
    status: 0,
    template: '<em class="#level#">【#name#】#cycle# </em>',
    template2: '【#name#】',
    type: IndicatorTypeEnums.generalItems,
    description: '未发现企业存在招投标记录',
  },
  [DimensionLevel2Enums.NegativeNewsRecent]: {
    key: DimensionLevel2Enums.NegativeNewsRecent,
    name: '近3年负面新闻',
    strategyModel: {
      boost: 1.0,
      baseScore: 80,
      level: DimensionRiskLevelEnum.Medium,
      sortField: { field: 'publishtime', order: 'DESC', fieldSnapshot: 'publishtime' },
      detailsParams: [
        {
          field: QueryParamsEnums.topics,
          fieldVal: TopicTypes,
          sort: 0,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.NegativeNews,
    status: 1,
    template: '<em class="#level#">【#name#】 #count#条记录</em>',
    template2: '【#name#】 #count#条记录',
    type: IndicatorTypeEnums.generalItems,
    description: '企业因负面信息被新闻报道',
  },
  [DimensionLevel2Enums.NegativeNewsHistory]: {
    key: DimensionLevel2Enums.NegativeNewsHistory,
    name: '3年前负面新闻',
    strategyModel: {
      boost: 1.0,
      baseScore: 60,
      level: DimensionRiskLevelEnum.Medium,
      sortField: { field: 'publishtime', order: 'DESC', fieldSnapshot: 'publishtime' },
      detailsParams: [
        {
          field: QueryParamsEnums.topics,
          fieldVal: TopicTypes,
          sort: 0,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.NegativeNews,
    sourcePath: 'getSimpleCancelDetail',
    status: 0,
    template: '<em class="#level#">【#name#】 #count#条记录</em>',
    template2: '【#name#】 #count#条记录',
    type: IndicatorTypeEnums.generalItems,
    description: '企业因负面信息被新闻报道',
  },
  [DimensionLevel2Enums.CustomerPartnerInvestigation]: {
    key: DimensionLevel2Enums.CustomerPartnerInvestigation,
    name: '与第三方列表企业存在投资任职关联',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.Medium,
      detailsParams: [
        {
          field: QueryParamsEnums.types,
          fieldName: '投资任职关联',
          fieldVal: [
            // {
            //   key: DetailsParamEnums.ShareholdingRelationship,
            //   keyName: RelationTypeConst[DetailsParamEnums.ShareholdingRelationship],
            //   status: 1,
            // },
            {
              key: DetailsParamEnums.InvestorsRelationship,
              keyName: RelationTypeConst[DetailsParamEnums.InvestorsRelationship],
              status: 1,
            },
            {
              key: DetailsParamEnums.EmploymentRelationship,
              keyName: RelationTypeConst[DetailsParamEnums.EmploymentRelationship],
              status: 1,
            },
            // {
            //   key: DetailsParamEnums.HisShareholdingRelationship,
            //   keyName: RelationTypeConst[DetailsParamEnums.HisShareholdingRelationship],
            //   status: 1,
            // },
            {
              key: DetailsParamEnums.HisInvestorsRelationship,
              keyName: RelationTypeConst[DetailsParamEnums.HisInvestorsRelationship],
              status: 1,
            },
            {
              key: DetailsParamEnums.HisLegalAndEmploy,
              keyName: RelationTypeConst[DetailsParamEnums.HisLegalAndEmploy],
              status: 1,
            },
            {
              key: DetailsParamEnums.ActualController,
              keyName: RelationTypeConst[DetailsParamEnums.ActualController],
              status: 1,
            },
            {
              key: DetailsParamEnums.MainInfoUpdateBeneficiary,
              keyName: RelationTypeConst[DetailsParamEnums.MainInfoUpdateBeneficiary],
              status: 1,
            },
            {
              key: DetailsParamEnums.Branch,
              keyName: RelationTypeConst[DetailsParamEnums.Branch],
              status: 1,
            },
            {
              key: DetailsParamEnums.Hold,
              keyName: RelationTypeConst[DetailsParamEnums.Hold],
              status: 1,
            },
            {
              key: DetailsParamEnums.ShareholdingRatio,
              keyName: RelationTypeConst[DetailsParamEnums.ShareholdingRatio],
              value: 0.01,
              status: 1,
            },
          ],
          sort: 0,
        },
        {
          field: QueryParamsEnums.depth,
          fieldName: '关联层级',
          fieldVal: 1,
        },
        {
          field: QueryParamsEnums.excludedTypes,
          fieldVal: [ExcludeNodeTypeEnums.Revocation], // 默认排除已吊销、注销企业
        },
        {
          field: QueryParamsEnums.dataRange,
          fieldVal: [
            {
              key: DetailsParamEnums.Departments,
              keyName: '第三方部门',
              type: 3, // 1按企业所在部门 2不限 3按用户所属部门
              value: [],
              nameList: [], //部门名称
              status: 0,
            },
            {
              key: DetailsParamEnums.Groups,
              keyName: '第三方分组',
              type: 2, //1按企业所在分组 2不限
              value: [],
              nameList: [], //分组名称
              status: 1,
            },
            // {
            //   key: DetailsParamEnums.conditionOperator,
            //   value: ConditionOperatorEnums.And, // 默认并且
            //   isHidden: true,
            //   status: 1,
            // },
            // {
            //   key: DetailsParamEnums.Labels,
            //   keyName: '第三方标签',
            //   type: 2, //1按企业所在标签 2不限
            //   value: [],
            //   nameList: [], //标签名称
            //   isHidden: true,
            //   deprecated: true,//表示废弃
            // },
          ],
          version: 'v2',
          sort: 1,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Rover,
    sourcePath: '',
    status: 0,
    template: '<em class="#level#">【#name#】 #count#条记录</em>',
    template2: '<em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业与第三方列表企业存在股权、人员交叉任职、相同实际控制人等投资任职关联',
  },
  [DimensionLevel2Enums.CustomerSuspectedRelation]: {
    key: DimensionLevel2Enums.CustomerSuspectedRelation,
    name: '与第三方列表企业存在其他关联',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.Medium,
      detailsParams: [
        {
          field: QueryParamsEnums.types,
          fieldVal: [
            {
              key: DetailsParamEnums.Guarantee,
              keyName: RelationTypeConst[DetailsParamEnums.Guarantee],
              status: 1,
            },
            {
              key: DetailsParamEnums.EquityPledgeRelation,
              keyName: RelationTypeConst[DetailsParamEnums.EquityPledgeRelation],
              status: 1,
            },
            {
              key: DetailsParamEnums.HasPhone,
              keyName: RelationTypeConst[DetailsParamEnums.HasPhone],
              status: 1,
            },
            // 暂不支持网址
            // {
            //   key: DetailsParamEnums.WebsiteRelation,
            //   keyName: RelationTypeConst[DetailsParamEnums.WebsiteRelation],
            //   status: 1,
            // },
            {
              key: DetailsParamEnums.HasAddress,
              keyName: RelationTypeConst[DetailsParamEnums.HasAddress],
              status: 1,
            },
            {
              key: DetailsParamEnums.HasEmail,
              keyName: RelationTypeConst[DetailsParamEnums.HasEmail],
              status: 1,
            },
          ],
          sort: 0,
        },
        {
          field: QueryParamsEnums.excludedTypes, // 疑似关系默认排除节点
          fieldVal: [ExcludeNodeTypeEnums.SuspectedBookKeeping],
          isHidden: true,
        },
        {
          field: QueryParamsEnums.dataRange,
          fieldVal: [
            {
              key: DetailsParamEnums.Departments,
              keyName: '第三方部门',
              type: 3, // 1按企业所在部门 2不限 3按用户所属部门
              value: [],
              nameList: [], //部门名称
              status: 0,
            },
            {
              key: DetailsParamEnums.Groups,
              keyName: '第三方分组',
              type: 2, //1按企业所在分组 2不限
              value: [],
              nameList: [], //分组名称
              status: 1,
            },
            // {
            //   key: DetailsParamEnums.conditionOperator,
            //   value: ConditionOperatorEnums.And, // 默认并且
            //   isHidden: true,
            //   status: 1,
            // },
            // {
            //   key: DetailsParamEnums.Labels,
            //   keyName: '第三方标签',
            //   type: 2, //1按企业所在标签 2不限
            //   value: [],
            //   nameList: [], //标签名称
            // },
          ],
          version: 'v2',
          sort: 1,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Rover,
    sourcePath: '',
    status: 0,
    template: '<em class="#level#">【#name#】 #count#条记录</em>',
    template2: '<em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业与第三方列表企业存在相互担保关联、股权出质关联、相同联系方式等其他关联',
  },
  [DimensionLevel2Enums.HitInnerBlackList]: {
    key: DimensionLevel2Enums.HitInnerBlackList,
    name: '被列入内部黑名单',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.High,
      detailsParams: [
        {
          field: QueryParamsEnums.isValid,
          fieldVal: '-1',
          sort: 0,
        },
        {
          field: QueryParamsEnums.dataRange,
          fieldVal: [
            {
              key: DetailsParamEnums.Groups,
              keyName: '黑名单分组',
              value: [],
              nameList: [], //分组名称
            },
            {
              key: DetailsParamEnums.Labels,
              keyName: '黑名单标签',
              value: [],
              nameList: [], //标签名称
            },
            {
              key: DetailsParamEnums.conditionOperator,
              value: ConditionOperatorEnums.Or, // 默认或者
            },
          ],
          sort: 1,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Rover,
    sourcePath: '',
    status: 1,
    template: '<em class="#level#">【被列入内部黑名单】 #count#条记录</em>',
    template2: '被列入内部黑名单#count#条记录',
    type: IndicatorTypeEnums.generalItems,
    description: '企业被列入了内部黑名单列表',
  },
  [DimensionLevel2Enums.EmploymentRelationship]: {
    key: DimensionLevel2Enums.EmploymentRelationship,
    name: '与内部黑名单列表存在人员关联',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.Medium,
      detailsParams: [
        {
          field: QueryParamsEnums.isValid,
          fieldVal: '-1',
          sort: 0,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Rover,
    sourcePath: '',
    status: 0,
    template: '<em class="#level#">【与内部黑名单列表存在人员关联】 #count#条记录</em>',
    template2: '与黑名单（内部）存在人员任职关联#count#条记录',
    type: IndicatorTypeEnums.generalItems,
    description: '目标主体主要任职人员是否被列入内部黑名单',
    isHidden: true,
  },
  [DimensionLevel2Enums.BlacklistPartnerInvestigation]: {
    key: DimensionLevel2Enums.BlacklistPartnerInvestigation,
    name: '与内部黑名单企业存在投资任职关联',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.Medium,
      detailsParams: [
        {
          field: QueryParamsEnums.types,
          fieldName: '投资任职关联',
          fieldVal: [
            {
              key: DetailsParamEnums.ForeignInvestment,
              keyName: RelationTypeConst[DetailsParamEnums.ForeignInvestment],
              status: 1,
            },
            // {
            //   key: DetailsParamEnums.Shareholder,
            //   keyName: RelationTypeConst[DetailsParamEnums.Shareholder],
            //   status: 1,
            // },
            {
              key: DetailsParamEnums.EmploymentRelationship,
              keyName: RelationTypeConst[DetailsParamEnums.EmploymentRelationship],
              status: 1,
            },
            {
              key: DetailsParamEnums.HisForeignInvestment,
              keyName: RelationTypeConst[DetailsParamEnums.HisForeignInvestment],
              status: 1,
            },
            // {
            //   key: DetailsParamEnums.HisShareholder,
            //   keyName: RelationTypeConst[DetailsParamEnums.HisShareholder],
            //   status: 1,
            // },
            {
              key: DetailsParamEnums.HisLegalAndEmploy,
              keyName: RelationTypeConst[DetailsParamEnums.HisLegalAndEmploy],
              status: 1,
            },
            {
              key: DetailsParamEnums.ActualController,
              keyName: RelationTypeConst[DetailsParamEnums.ActualController],
              status: 1,
            },
            {
              key: DetailsParamEnums.MainInfoUpdateBeneficiary,
              keyName: RelationTypeConst[DetailsParamEnums.MainInfoUpdateBeneficiary],
              status: 1,
            },
            {
              key: DetailsParamEnums.Branch,
              keyName: RelationTypeConst[DetailsParamEnums.Branch],
              status: 1,
            },
            {
              key: DetailsParamEnums.Hold,
              keyName: RelationTypeConst[DetailsParamEnums.Hold],
              status: 1,
            },
            {
              key: DetailsParamEnums.ShareholdingRatio,
              keyName: RelationTypeConst[DetailsParamEnums.ShareholdingRatio],
              value: 5,
              status: 1,
            },
          ],
          sort: 0,
        },
        {
          field: QueryParamsEnums.depth,
          fieldName: '关联层级',
          fieldVal: 1,
        },
        {
          field: QueryParamsEnums.excludedTypes,
          fieldVal: [ExcludeNodeTypeEnums.Revocation], // 默认排除已吊销、注销企业
        },
        {
          field: QueryParamsEnums.dataRange,
          fieldVal: [
            {
              key: DetailsParamEnums.Groups,
              keyName: '黑名单分组',
              value: [],
              nameList: [], //分组名称
              status: 1,
            },
            // {
            //   key: DetailsParamEnums.conditionOperator,
            //   value: ConditionOperatorEnums.And, // 默认并且
            //   isHidden: true,
            //   status: 1,
            // },
            // {
            //   key: DetailsParamEnums.Labels,
            //   keyName: '黑名单标签',
            //   value: [],
            //   nameList: [], //标签名称
            // },
          ],
          version: 'v2',
          sort: 1,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Rover,
    sourcePath: '',
    status: 1,
    template: '<em class="#level#">【与内部黑名单企业存在投资任职关联】 #count#条记录</em>',
    template2: '<em class="#level#">【与内部黑名单企业存在投资任职关联】 #count#条记录</em>',
    sort: 7,
    type: IndicatorTypeEnums.generalItems,
    description: '企业与内部黑名单企业存在股权、人员交叉任职、相同实际控制人等投资任职关联',
  },
  [DimensionLevel2Enums.BlacklistSuspectedRelation]: {
    key: DimensionLevel2Enums.BlacklistSuspectedRelation,
    name: '与内部黑名单企业存在其他关联',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.Medium,
      detailsParams: [
        {
          field: QueryParamsEnums.types,
          fieldVal: [
            {
              key: DetailsParamEnums.Guarantee,
              keyName: RelationTypeConst[DetailsParamEnums.Guarantee],
              status: 1,
            },
            {
              key: DetailsParamEnums.EquityPledgeRelation,
              keyName: RelationTypeConst[DetailsParamEnums.EquityPledgeRelation],
              status: 1,
            },
            {
              key: DetailsParamEnums.HasPhone,
              keyName: RelationTypeConst[DetailsParamEnums.HasPhone],
              status: 1,
            },
            // 数据暂不支持
            // {
            //   key: DetailsParamEnums.WebsiteRelation,
            //   keyName: RelationTypeConst[DetailsParamEnums.WebsiteRelation],
            //   status: 1,
            // },
            {
              key: DetailsParamEnums.HasAddress,
              keyName: RelationTypeConst[DetailsParamEnums.HasAddress],
              status: 1,
            },
            {
              key: DetailsParamEnums.HasEmail,
              keyName: RelationTypeConst[DetailsParamEnums.HasEmail],
              status: 1,
            },
          ],
          sort: 0,
        },
        {
          field: QueryParamsEnums.excludedTypes, // 疑似关系默认排除节点
          fieldVal: [ExcludeNodeTypeEnums.SuspectedBookKeeping],
          isHidden: true,
        },
        {
          field: QueryParamsEnums.dataRange,
          fieldVal: [
            {
              key: DetailsParamEnums.Groups,
              keyName: '黑名单分组',
              value: [],
              nameList: [], //分组名称
              status: 1,
            },
            // {
            //   key: DetailsParamEnums.conditionOperator,
            //   value: ConditionOperatorEnums.And, // 默认并且
            //   isHidden: true,
            //   status: 1,
            // },
            // {
            //   key: DetailsParamEnums.Labels,
            //   keyName: '黑名单标签',
            //   value: [],
            //   nameList: [], //标签名称
            // },
          ],
          version: 'v2',
          sort: 1,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Rover,
    sourcePath: '',
    status: 1,
    template: '<em class="#level#">【与内部黑名单企业存在其他关联】 #count#条记录</em>',
    template2: '<em class="#level#">【与内部黑名单企业存在其他关联】 #count#条记录</em>',
    sort: 7,
    type: IndicatorTypeEnums.generalItems,
    description: '企业与内部黑名单企业相互担保关联、股权出质关联、相同联系方式等其他关联',
  },
  [DimensionLevel2Enums.SecurityNotice]: {
    key: DimensionLevel2Enums.SecurityNotice,
    name: '公安通告',
    strategyModel: {
      boost: 1.0,
      baseScore: 15,
      cycle: 3,
      level: DimensionRiskLevelEnum.High,
      sortField: { field: 'UpdateDate', order: 'DESC', fieldSnapshot: 'updateDate' },
      detailsParams: [
        {
          field: QueryParamsEnums.isValid,
          fieldVal: '1',
          sort: 0,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.RiskChange,
    status: 1,
    template: '<em class="#level#">【#name#】#cycle# #count#条记录</em>',
    template2: '【#name#】#cycle# #count#条记录',
    type: IndicatorTypeEnums.generalItems,
    description: '企业因涉嫌非法集资、非法吸收公众存款等问题被公安部门通告',
  },
  [DimensionLevel2Enums.RegulateFinance]: {
    key: DimensionLevel2Enums.RegulateFinance,
    name: '监管处罚',
    strategyModel: {
      boost: 1.0,
      baseScore: 15,
      cycle: 3,
      level: DimensionRiskLevelEnum.Medium,
      sortField: { field: 'punishdate', order: 'DESC', fieldSnapshot: 'PunishDate' },
      detailsParams: [
        {
          field: QueryParamsEnums.isValid,
          fieldVal: '1',
          sort: 0,
        },
        {
          field: QueryParamsEnums.creditType,
          fieldVal: Object.keys(CreditType).filter((c) => c !== 'A199'),
          sort: 1,
        },
        {
          field: QueryParamsEnums.associateObject,
          fieldVal: [
            {
              key: DetailsParamEnums.ActualController,
              keyName: AssociationTypeConst[DetailsParamEnums.ActualController],
              status: 0,
            },
            {
              key: DetailsParamEnums.MajorShareholder,
              keyName: AssociationTypeConst[DetailsParamEnums.MajorShareholder],
              status: 0,
            },
            {
              key: DetailsParamEnums.Shareholder,
              keyName: AssociationTypeConst[DetailsParamEnums.Shareholder],
              status: 0,
            },
          ],
          sort: 2,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Credit,
    status: 1,
    template: '<em class="#level#">【#name#】#cycle# #count#条记录</em>',
    template2: '【#name#】#cycle# #count#条记录',
    type: IndicatorTypeEnums.generalItems,
    description: '企业及实控人、股东存在监管行政处罚信息',
  },
  [DimensionLevel3Enums.CancellationOfFiling]: {
    key: DimensionLevel3Enums.CancellationOfFiling,
    name: '注销备案',
    strategyModel: {
      boost: 1.0,
      baseScore: 80,
      level: DimensionRiskLevelEnum.High,
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/QccSearch/List/Enliq',
    status: 1,
    template: templateString,
    template2: templateString2,
    type: IndicatorTypeEnums.generalItems,
    description: '企业在工商提交注销备案信息',
  },
  [DimensionLevel3Enums.BusinessAbnormal2]: {
    // 目前测试发现 专业版接口 如果命中了 经营状态非存续 就不会命中简易注销
    key: DimensionLevel3Enums.BusinessAbnormal2,
    name: '简易注销',
    strategyModel: {
      boost: 1.0,
      baseScore: 80,
      level: DimensionRiskLevelEnum.High,
      detailsParams: [
        {
          field: QueryParamsEnums.simpleCancellationStep,
          fieldName: '简易注销结果',
          fieldVal: SimpleCancellationTypes,
          sort: 0,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/QccSearch/List/SimpleCancellation',
    status: 1,
    template: '<em class="#level#">【#name#】 #count#条记录</em>',
    template2: templateString2,
    type: IndicatorTypeEnums.generalItems,
    typeCode: '5010',
    description: '企业在工商提交简易注销流程',
  },
  [DimensionLevel3Enums.BusinessAbnormal1]: {
    key: DimensionLevel3Enums.BusinessAbnormal1,
    name: '经营状态非存续',
    strategyModel: {
      boost: 1.0,
      baseScore: 80,
      level: DimensionRiskLevelEnum.High,
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.CompanyDetail,
    status: 1,
    template: templateString,
    template2: templateString2,
    type: IndicatorTypeEnums.generalItems,
    typeCode: '2110',
    description: '企业在工商为“非存续状态”，如“注销、吊销”等',
  },
  [DimensionLevel3Enums.BusinessAbnormal5]: {
    key: DimensionLevel3Enums.BusinessAbnormal5,
    name: '疑似停业歇业停产或被吊销证照', //202503迭代RA13289更换数据源，不再使用信用大数据
    strategyModel: {
      boost: 1.0,
      baseScore: 40,
      level: DimensionRiskLevelEnum.High,
      sortField: { field: 'punishdate', order: 'DESC', fieldSnapshot: 'PunishDate' },
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/QccSearch/List/punishCompany',
    status: 1,
    template: '<em class="#level#">【#name#】 #count#条记录</em>',
    template2: templateString2,
    type: IndicatorTypeEnums.generalItems,
    typeCode: '6314',
    description: '企业疑似停业歇业停产或被吊销证照',
  },
  [DimensionLevel3Enums.BusinessAbnormal7]: {
    key: DimensionLevel3Enums.BusinessAbnormal7,
    name: '无统一社会信用代码',
    strategyModel: {
      boost: 1.0,
      baseScore: 20,
      level: DimensionRiskLevelEnum.Medium,
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.CompanyDetail,
    status: 1,
    template: templateString,
    template2: templateString2,
    type: IndicatorTypeEnums.generalItems,
    description: '企业未取得统一社会信用代码',
  },
  [DimensionLevel3Enums.BusinessAbnormal8]: {
    key: DimensionLevel3Enums.BusinessAbnormal8,
    name: '临近经营期限',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.Medium,
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.CompanyDetail,
    status: 0,
    template: `${templateString}，营业期限：<em class="#level#">#start# 至 #end#</em>`,
    template2: '【#name#】，营业期限：#start# 至 #end#',
    type: IndicatorTypeEnums.generalItems,
    description: '企业工商注册的经营期限临近到期',
    isHidden: true,
  },
  [DimensionLevel3Enums.BusinessAbnormal6]: {
    key: DimensionLevel3Enums.BusinessAbnormal6,
    name: '经营期限已过有效期',
    strategyModel: {
      boost: 1.0,
      baseScore: 20,
      level: DimensionRiskLevelEnum.Medium,
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.CompanyDetail,
    status: 0,
    template: `${templateString}，营业期限：<em class="#level#">#start# 至 #end#</em>`,
    template2: '【#name#】，营业期限：#start# 至 #end#',
    type: IndicatorTypeEnums.generalItems,
    description: '企业工商注册的经营期限已到期',
    isHidden: true,
  },
  [DimensionLevel3Enums.BusinessAbnormal9]: {
    key: DimensionLevel3Enums.BusinessAbnormal9,
    name: '经营期限已到期或临近到期',
    strategyModel: {
      boost: 1.0,
      baseScore: 20,
      level: DimensionRiskLevelEnum.Medium,
      detailsParams: [
        {
          field: QueryParamsEnums.approachingExpiry,
          fieldName: '临近到期',
          fieldVal: 1, //1-近1月，3-近3月，6-近半年
          sort: 0,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.CompanyDetail,
    status: 1,
    template: `${templateString}，营业期限：<em class="#level#">#start# 至 #end#</em>`,
    template2: '【#name#】，营业期限：#start# 至 #end#',
    type: IndicatorTypeEnums.generalItems,
    description: '企业工商注册的经营期限已到期或临近到期',
  },
  [DimensionLevel3Enums.PersonCreditCurrent]: {
    key: DimensionLevel3Enums.PersonCreditCurrent,
    name: '被列入失信被执行人',
    strategyModel: {
      boost: 1.0,
      baseScore: 30,
      level: DimensionRiskLevelEnum.High,
      sortField: { field: 'pubdate', order: 'DESC', fieldSnapshot: 'PublicDate' },
    },
    isVirtualDimension: 0,
    // 不使用信用接口原因: 信用数据 未对属于同一司法案件的执行标的做去重处理
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/Court/SearchShiXin',
    status: 1,
    template: '<em class="#level#">【#name#】#count#条记录</em><span class="#isHidden#">，涉及被执行金额：<em class="#level#">#amountW#</em></span>',
    template2: '【#name#】#count#条记录<span class="#isHidden#">，涉及被执行金额：#amountW#</span>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业被加入失信被执行人名单',
  },
  [DimensionLevel3Enums.RestrictedConsumptionCurrent]: {
    key: DimensionLevel3Enums.RestrictedConsumptionCurrent,
    name: '被列入限制高消费名单',
    strategyModel: {
      boost: 1.0,
      baseScore: 30,
      level: DimensionRiskLevelEnum.High,
      sortField: { field: 'publishdate', order: 'DESC', fieldSnapshot: 'PublishDate' },
      cycle: -1,
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Credit,
    sourcePath: '',
    status: 1,
    template:
      '<em class="#level#">#cycle#</em><em class="#level#">【#name#】 #count#条记录</em><span class="#isHidden#">，涉案总金额：<em class="#level#">#amountW#</em></span>',
    template2: '被列入限制高消费名单',
    type: IndicatorTypeEnums.generalItems,
    description: '企业被加入限制高消费名单',
  },
  [DimensionLevel3Enums.MainMembersPersonCreditCurrent]: {
    key: DimensionLevel3Enums.MainMembersPersonCreditCurrent,
    name: '主要人员及核心关联方被列入失信被执行人',
    strategyModel: {
      boost: 1.0,
      baseScore: 10,
      level: DimensionRiskLevelEnum.High,
      sortField: { field: 'publishdate', order: 'DESC', fieldSnapshot: 'PublishDate' },
      detailsParams: [
        {
          field: QueryParamsEnums.isValid,
          fieldVal: '1',
          sort: 0,
        },
        {
          field: QueryParamsEnums.associateObject,
          fieldVal: [
            {
              key: DetailsParamEnums.Employ,
              keyName: AssociationTypeConst[DetailsParamEnums.Employ],
              status: 1,
            },
            {
              key: DetailsParamEnums.LegalRepresentative,
              keyName: AssociationTypeConst[DetailsParamEnums.LegalRepresentative],
              status: 1,
            },
            {
              key: DetailsParamEnums.ActualController,
              keyName: AssociationTypeConst[DetailsParamEnums.ActualController],
              status: 0,
            },
            {
              key: DetailsParamEnums.Benefit,
              keyName: AssociationTypeConst[DetailsParamEnums.Benefit],
              status: 0,
            },
            {
              key: DetailsParamEnums.MajorShareholder,
              keyName: AssociationTypeConst[DetailsParamEnums.MajorShareholder],
              status: 1,
            },
            {
              key: DetailsParamEnums.Shareholder,
              keyName: AssociationTypeConst[DetailsParamEnums.Shareholder],
              status: 0,
            },
          ],
          sort: 1,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Credit,
    sourcePath: '/api/QccSearch/CreditSearch/Credit',
    status: 1,
    template: '<em class="#level#">【#name#】 #count#条记录</em><span class="#isHidden#">，涉及金额：<em class="#level#">#amountW#</em></span>',
    template2: '【#name#】<span class="#isHidden#">，涉及金额：#amountW#</span>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业的主要人员（董监高法）及核心关联方被列入失信被执行人',
  },
  [DimensionLevel3Enums.MainMembersRestrictedConsumptionCurrent]: {
    key: DimensionLevel3Enums.MainMembersRestrictedConsumptionCurrent,
    name: '主要人员及核心关联方被列入限制高消费',
    strategyModel: {
      boost: 1.0,
      baseScore: 10,
      level: DimensionRiskLevelEnum.High,
      sortField: { field: 'publishdate', order: 'DESC', fieldSnapshot: 'PublishDate' },
      detailsParams: [
        {
          field: QueryParamsEnums.isValid,
          fieldVal: '1',
          sort: 0,
        },
        {
          field: QueryParamsEnums.associateObject,
          fieldVal: [
            {
              key: DetailsParamEnums.Employ,
              keyName: AssociationTypeConst[DetailsParamEnums.Employ],
              status: 1,
            },
            {
              key: DetailsParamEnums.LegalRepresentative,
              keyName: AssociationTypeConst[DetailsParamEnums.LegalRepresentative],
              status: 1,
            },
            {
              key: DetailsParamEnums.ActualController,
              keyName: AssociationTypeConst[DetailsParamEnums.ActualController],
              status: 0,
            },
            {
              key: DetailsParamEnums.Benefit,
              keyName: AssociationTypeConst[DetailsParamEnums.Benefit],
              status: 0,
            },
            {
              key: DetailsParamEnums.MajorShareholder,
              keyName: AssociationTypeConst[DetailsParamEnums.MajorShareholder],
              status: 1,
            },
            {
              key: DetailsParamEnums.Shareholder,
              keyName: AssociationTypeConst[DetailsParamEnums.Shareholder],
              status: 0,
            },
          ],
          sort: 10,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Credit,
    sourcePath: '',
    status: 1,
    template: '<em class="#level#">【#name#】 #count#条记录</em><span class="#isHidden#">，涉案总金额：<em class="#level#">#amountW#</em></span>',
    template2: '主要人员及核心关联方被列入限制高消费',
    type: IndicatorTypeEnums.generalItems,
    description: '企业的主要人员（董监高法）及核心关联方有“限制高消费”',
  },
  [DimensionLevel3Enums.MainMembersRestrictedOutbound]: {
    key: DimensionLevel3Enums.MainMembersRestrictedOutbound,
    name: '主要人员及核心关联方被列入限制出境',
    strategyModel: {
      boost: 1.0,
      baseScore: 10,
      level: DimensionRiskLevelEnum.High,
      sortField: { field: 'publishdate', order: 'DESC', fieldSnapshot: 'PublishDate' },
      detailsParams: [
        {
          field: QueryParamsEnums.associateObject,
          fieldVal: [
            {
              key: DetailsParamEnums.Employ,
              keyName: AssociationTypeConst[DetailsParamEnums.Employ],
              status: 1,
            },
            {
              key: DetailsParamEnums.LegalRepresentative,
              keyName: AssociationTypeConst[DetailsParamEnums.LegalRepresentative],
              status: 1,
            },
            {
              key: DetailsParamEnums.ActualController,
              keyName: AssociationTypeConst[DetailsParamEnums.ActualController],
              status: 0,
            },
            {
              key: DetailsParamEnums.Benefit,
              keyName: AssociationTypeConst[DetailsParamEnums.Benefit],
              status: 0,
            },
            {
              key: DetailsParamEnums.MajorShareholder,
              keyName: AssociationTypeConst[DetailsParamEnums.MajorShareholder],
              status: 1,
            },
            {
              key: DetailsParamEnums.Shareholder,
              keyName: AssociationTypeConst[DetailsParamEnums.Shareholder],
              status: 0,
            },
          ],
          sort: 10,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Credit,
    sourcePath: '/api/QccSearch/CreditSearch/Credit',
    status: 1,
    template: '<em class="#level#">【#name#】 #count#条记录</em>',
    template2: '主要人员及核心关联方被列入限制出境',
    type: IndicatorTypeEnums.generalItems,
    description: '企业主要人员（董监高法）及核心关联方被列入限制出境名单',
  },
  [DimensionLevel3Enums.PersonCreditHistory]: {
    key: DimensionLevel3Enums.PersonCreditHistory,
    name: '历史失信被执行人',
    strategyModel: {
      boost: 1.0,
      baseScore: 15,
      cycle: 3,
      level: DimensionRiskLevelEnum.Medium,
      sortField: { field: 'pubdate', fieldSnapshot: 'PublicDate', order: 'DESC' },
    },
    isVirtualDimension: 0,
    // 不使用信用接口原因: 信用数据 未对属于同一司法案件的执行标的做去重处理
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/Court/SearchShiXin',
    status: 1,
    template: '<em class="#level#">【#name#】#cycle# #count#条记录</em><span class="#isHidden#">，涉及被执行金额：<em class="#level#">#amountW#</em></span>',
    template2: '【#name#】#cycle# #count#条记录<span class="#isHidden#">，涉及被执行金额：#amountW#</span>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业曾被列入失信被执行人名单',
  },
  [DimensionLevel3Enums.RestrictedConsumptionHistory]: {
    key: DimensionLevel3Enums.RestrictedConsumptionHistory,
    name: '历史限制高消费',
    strategyModel: {
      boost: 1.0,
      baseScore: 15,
      level: DimensionRiskLevelEnum.Medium,
      sortField: { field: 'publishdate', order: 'DESC', fieldSnapshot: 'PublishDate' },
      cycle: 3,
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Credit,
    sourcePath: '',
    status: 1,
    template: '<em class="#level#">【#name#】#cycle# #count#条记录</em><span class="#isHidden#">，涉案总金额：<em class="#level#">#amountW#</em></span>',
    template2: '#cycle#曾被列入限制高消费名单',
    type: IndicatorTypeEnums.generalItems,
    description: '企业曾被列入限制高消费名单',
  },
  [DimensionLevel3Enums.CompanyOrMainMembersLitigationInfo]: {
    key: DimensionLevel3Enums.CompanyOrMainMembersLitigationInfo,
    name: '与本司及核心关联方存在涉诉信息',
    strategyModel: {
      boost: 1.0,
      baseScore: 20,
      level: DimensionRiskLevelEnum.Medium,
      cycle: 3,
      sortField: { field: 'LastestDate', order: 'DESC', fieldSnapshot: 'LastestDate' },
      detailsParams: [
        {
          field: QueryParamsEnums.companyInfo,
          fieldVal: {
            companyName: '',
            companyId: '',
          },
        },
        {
          field: QueryParamsEnums.associateObject,
          fieldVal: [
            {
              key: DetailsParamEnums.Employ,
              keyName: AssociationTypeConst[DetailsParamEnums.Employ],
              status: 1,
            },
            {
              key: DetailsParamEnums.LegalRepresentative,
              keyName: AssociationTypeConst[DetailsParamEnums.LegalRepresentative],
              status: 1,
            },
            {
              key: DetailsParamEnums.ActualController,
              keyName: AssociationTypeConst[DetailsParamEnums.ActualController],
              status: 0,
            },
            {
              key: DetailsParamEnums.Benefit,
              keyName: AssociationTypeConst[DetailsParamEnums.Benefit],
              status: 0,
            },
            {
              key: DetailsParamEnums.MajorShareholder,
              keyName: AssociationTypeConst[DetailsParamEnums.MajorShareholder],
              status: 1,
            },
            {
              key: DetailsParamEnums.Shareholder,
              keyName: AssociationTypeConst[DetailsParamEnums.Shareholder],
              status: 0,
            },
            {
              key: DetailsParamEnums.otherAssociateObject,
              keyName: AssociationTypeConst[DetailsParamEnums.otherAssociateObject],
              status: 0,
            },
          ],
        },
        {
          field: QueryParamsEnums.caseIdentity,
          fieldVal: [
            {
              key: DetailsParamEnums.Plaintiff,
              keyName: AssociationTypeConst[DetailsParamEnums.Plaintiff],
              status: 1,
            },
            {
              key: DetailsParamEnums.Defendant,
              keyName: AssociationTypeConst[DetailsParamEnums.Defendant],
              status: 1,
            },
          ],
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Case,
    sourcePath: '',
    status: 0,
    template: '<em class="#level#">【#name#】#cycle# #count#条记录</em>',
    template2: '与本司及核心关联方存在涉诉信息',
    type: IndicatorTypeEnums.generalItems,
    description: '企业与本公司及主要人员（董监高法）、核心关联方存在司法案件信息',
  },
  [DimensionLevel3Enums.CompanyOrMainMembersCriminalOffence]: {
    key: DimensionLevel3Enums.CompanyOrMainMembersCriminalOffence,
    name: '公司及主要人员、核心关联方涉刑事犯罪',
    strategyModel: {
      boost: 1.0,
      baseScore: 20,
      level: DimensionRiskLevelEnum.High,
      cycle: 3,
      sortField: { field: 'LastestDate', order: 'DESC', fieldSnapshot: 'LastestDate' },
      detailsParams: [
        {
          field: QueryParamsEnums.associateObject,
          fieldVal: [
            {
              key: DetailsParamEnums.Employ,
              keyName: AssociationTypeConst[DetailsParamEnums.Employ],
              status: 1,
            },
            {
              key: DetailsParamEnums.LegalRepresentative,
              keyName: AssociationTypeConst[DetailsParamEnums.LegalRepresentative],
              status: 1,
            },
            {
              key: DetailsParamEnums.ActualController,
              keyName: AssociationTypeConst[DetailsParamEnums.ActualController],
              status: 0,
            },
            {
              key: DetailsParamEnums.Benefit,
              keyName: AssociationTypeConst[DetailsParamEnums.Benefit],
              status: 0,
            },
            {
              key: DetailsParamEnums.MajorShareholder,
              keyName: AssociationTypeConst[DetailsParamEnums.MajorShareholder],
              status: 1,
            },
            {
              key: DetailsParamEnums.Shareholder,
              keyName: AssociationTypeConst[DetailsParamEnums.Shareholder],
              status: 0,
            },
          ],
          sort: 10,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Case,
    sourcePath: '',
    status: 1,
    template: '<em class="#level#">【#name#】#cycle# #count#条记录</em>',
    template2: '公司及主要人员、核心关联方涉刑事犯罪',
    type: IndicatorTypeEnums.generalItems,
    description: '企业及主要人员（董监高法）、核心关联方涉及到刑事犯罪相关诉讼',
  },
  [DimensionLevel3Enums.CompanyOrMainMembersCriminalOffenceHistory]: {
    key: DimensionLevel3Enums.CompanyOrMainMembersCriminalOffenceHistory,
    name: '公司及主要人员、核心关联方涉刑事犯罪（3年以上及其他）',
    strategyModel: {
      boost: 1.0,
      baseScore: 20,
      level: DimensionRiskLevelEnum.Medium,
      sortField: { field: 'LastestDate', order: 'DESC', fieldSnapshot: 'LastestDate' },
      detailsParams: [
        {
          field: QueryParamsEnums.associateObject,
          fieldVal: [
            {
              key: DetailsParamEnums.Employ,
              keyName: AssociationTypeConst[DetailsParamEnums.Employ],
              status: 1,
            },
            {
              key: DetailsParamEnums.LegalRepresentative,
              keyName: AssociationTypeConst[DetailsParamEnums.LegalRepresentative],
              status: 1,
            },
            {
              key: DetailsParamEnums.ActualController,
              keyName: AssociationTypeConst[DetailsParamEnums.ActualController],
              status: 0,
            },
            {
              key: DetailsParamEnums.Benefit,
              keyName: AssociationTypeConst[DetailsParamEnums.Benefit],
              status: 0,
            },
            {
              key: DetailsParamEnums.MajorShareholder,
              keyName: AssociationTypeConst[DetailsParamEnums.MajorShareholder],
              status: 1,
            },
            {
              key: DetailsParamEnums.Shareholder,
              keyName: AssociationTypeConst[DetailsParamEnums.Shareholder],
              status: 0,
            },
          ],
          sort: 10,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Case,
    sourcePath: '',
    status: 1,
    template: '<em class="#level#">【#name#】 #count#条记录</em>',
    template2: '公司及主要人员、核心关联方涉刑事犯罪（3年以上及其他）',
    type: IndicatorTypeEnums.generalItems,
    description: '企业及主要人员（董监高法）、核心关联方曾发生过涉及到刑事犯罪相关诉讼',
  },
  [DimensionLevel3Enums.CompanyOrMainMembersCriminalInvolve]: {
    key: DimensionLevel3Enums.CompanyOrMainMembersCriminalInvolve,
    name: '近3年涉贪污受贿裁判相关提及方',
    strategyModel: {
      boost: 1.0,
      baseScore: 30,
      level: DimensionRiskLevelEnum.High,
      sortField: { field: 'courtdate', order: 'DESC', fieldSnapshot: 'courtdate' },
      detailsParams: [
        {
          field: QueryParamsEnums.isValid,
          fieldVal: '-1',
          sort: 0,
        },
        {
          field: QueryParamsEnums.associateObject,
          fieldVal: [
            {
              key: DetailsParamEnums.ActualController,
              keyName: AssociationTypeConst[DetailsParamEnums.ActualController],
              status: 1,
            },
            {
              key: DetailsParamEnums.MajorShareholder,
              keyName: AssociationTypeConst[DetailsParamEnums.MajorShareholder],
              status: 1,
            },
            {
              key: DetailsParamEnums.Shareholder,
              keyName: AssociationTypeConst[DetailsParamEnums.Shareholder],
              status: 0,
            },
          ],
          sort: 10,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Judgement,
    sourcePath: '',
    status: 1,
    template: '<em class="#level#">【#name#】 #count#条记录</em>',
    template2: '近3年涉贪污受贿裁判相关提及方',
    type: IndicatorTypeEnums.generalItems,
    description: '企业及核心关联方涉及到贪污受贿类案件被裁判文书提及',
  },
  [DimensionLevel3Enums.CompanyOrMainMembersCriminalInvolveHistory]: {
    key: DimensionLevel3Enums.CompanyOrMainMembersCriminalInvolveHistory,
    name: '涉贪污受贿裁判相关提及方（3年以上及其他）',
    strategyModel: {
      boost: 1.0,
      baseScore: 30,
      level: DimensionRiskLevelEnum.Medium,
      sortField: { field: 'courtdate', order: 'DESC', fieldSnapshot: 'courtdate' },
      detailsParams: [
        {
          field: QueryParamsEnums.isValid,
          fieldVal: '-1',
          sort: 0,
        },
        {
          field: QueryParamsEnums.associateObject,
          fieldVal: [
            {
              key: DetailsParamEnums.ActualController,
              keyName: AssociationTypeConst[DetailsParamEnums.ActualController],
              status: 1,
            },
            {
              key: DetailsParamEnums.MajorShareholder,
              keyName: AssociationTypeConst[DetailsParamEnums.MajorShareholder],
              status: 1,
            },
            {
              key: DetailsParamEnums.Shareholder,
              keyName: AssociationTypeConst[DetailsParamEnums.Shareholder],
              status: 0,
            },
          ],
          sort: 10,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Judgement,
    sourcePath: '',
    status: 1,
    template: '<em class="#level#">【#name#】 #count#条记录</em>',
    template2: '涉贪污受贿裁判相关提及方（3年以上及其他）',
    type: IndicatorTypeEnums.generalItems,
    description: '企业及核心关联方3年前曾涉及到贪污受贿类案件被裁判文书提及',
  },
  [DimensionLevel3Enums.SalesContractDispute]: {
    key: DimensionLevel3Enums.SalesContractDispute,
    name: '买卖合同纠纷',
    strategyModel: {
      boost: 1.0,
      baseScore: 30,
      level: DimensionRiskLevelEnum.Medium,
      sortField: { field: 'judgedate', order: 'DESC', fieldSnapshot: 'judgedate' },
      cycle: 3,
      detailsParams: [
        {
          field: QueryParamsEnums.percentAsDefendant,
          fieldVal: 50,
          fieldOperator: OperatorEnums.ge,
          sort: 0,
        },
        {
          field: QueryParamsEnums.isValid,
          fieldVal: '1',
          sort: 1,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Judgement,
    sourcePath: '',
    status: 1,
    template: '<em class="#level#">【#name#】#cycle# #count#条记录，作为被告方占比大于等于#percent#%</em>',
    template2: '买卖合同纠纷',
    type: IndicatorTypeEnums.generalItems,
    description: '企业因买卖合同纠纷被诉占比过大',
  },
  [DimensionLevel3Enums.LaborContractDispute]: {
    key: DimensionLevel3Enums.LaborContractDispute,
    name: '劳动纠纷',
    strategyModel: {
      boost: 1.0,
      baseScore: 30,
      level: DimensionRiskLevelEnum.Medium,
      sortField: { field: 'LastestDate', order: 'DESC', fieldSnapshot: 'LastestDate' },
      cycle: 3,
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Case,
    sourcePath: '',
    status: 1,
    template: '<em class="#level#">【#name#】#cycle# #count#条记录</em>',
    template2: '#name#',
    type: IndicatorTypeEnums.keyItems,
    description: '企业存在劳动纠纷案件',
  },
  [DimensionLevel3Enums.MajorDispute]: {
    key: DimensionLevel3Enums.MajorDispute,
    name: '重大纠纷',
    strategyModel: {
      boost: 1.0,
      baseScore: 30,
      level: DimensionRiskLevelEnum.Medium,
      sortField: { field: 'judgedate', order: 'DESC', fieldSnapshot: 'judgedate' },
      cycle: 3,
      detailsParams: [
        {
          field: QueryParamsEnums.amountInvolved,
          fieldVal: 200000,
          fieldOperator: OperatorEnums.ge,
          sort: 0,
        },
        {
          field: QueryParamsEnums.judgementRoleExclude,
          fieldVal: ['prosecutor', 'thirdpartyrole'],
          sort: 1,
        },
        {
          field: QueryParamsEnums.isValid,
          fieldVal: '1',
          sort: 2,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Judgement,
    sourcePath: '',
    status: 1,
    template: '<em class="#level#">【#name#】#cycle# #count#条记录<span class="#isHidden#">，涉及案件金额：<em class="#level#">#amountW#</em></span></em>',
    template2: '重大纠纷',
    type: IndicatorTypeEnums.generalItems,
    description: '企业涉诉案件涉及金额过大',
  },
  [DimensionLevel3Enums.EndExecutionCase]: {
    key: DimensionLevel3Enums.EndExecutionCase,
    name: '终本案件',
    strategyModel: {
      boost: 1.0,
      baseScore: 30,
      level: DimensionRiskLevelEnum.Medium,
      sortField: { field: 'enddate', order: 'DESC', fieldSnapshot: 'EndDate' },
      cycle: 3,
      detailsParams: [
        {
          field: QueryParamsEnums.failure,
          fieldOperator: OperatorEnums.ge,
          fieldVal: 100000, // 单位 元
          sort: 0,
        },
        {
          field: QueryParamsEnums.isValid,
          fieldVal: '1',
          sort: 1,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/Court/GetEndExecutionCaseListByKeyNo',
    status: 1,
    template: '<em class="#level#">【#name#】#cycle#  #count#条记录</em><span class="#isHidden#">，未履行总金额：<em class="#level#">#amountW#</em></span>',
    template2: '<em class="#level#">【#name#】#cycle#  #count#条记录</em><span class="#isHidden#">，未履行总金额：<em class="#level#">#amountW#</em></span>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业涉及法院的执行案件由于被执行人没有可供执行的财产，而裁定终止本次执行程序',
  },
  [DimensionLevel3Enums.BusinessAbnormal4]: {
    key: DimensionLevel3Enums.BusinessAbnormal4,
    name: '被列入非正常户',
    strategyModel: {
      boost: 1.0,
      baseScore: 40,
      cycle: 3,
      level: DimensionRiskLevelEnum.High,
      detailsParams: [
        {
          field: QueryParamsEnums.isValid,
          fieldVal: '1',
          sort: 0,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.CompanyDetail,
    sourcePath: '/api/Risk/GetTaxUnnormals',
    status: 1,
    template: '<em class="#level#">【#name#】#cycle#  #count#条记录</em>',
    template2: '【#name#】 #count#条记录',
    type: IndicatorTypeEnums.generalItems,
    typeCode: '1910',
    description: '企业因未及时申报纳税并无法联系，被税务机关停止办税',
  },
  [DimensionLevel3Enums.BusinessAbnormal3]: {
    key: DimensionLevel3Enums.BusinessAbnormal3,
    name: '被列入经营异常名录',
    strategyModel: {
      boost: 1.0,
      baseScore: 40,
      level: DimensionRiskLevelEnum.High,
      sortField: { field: 'occurrencedate', order: 'DESC', fieldSnapshot: 'CurrenceDate' },
      detailsParams: [
        {
          field: QueryParamsEnums.businessAbnormalType,
          fieldVal: ['0801', '0802', '0803', '0804', '0805', '0806', '0807'],
          sort: 0,
        },
        {
          field: QueryParamsEnums.associateObject,
          fieldVal: [
            {
              key: DetailsParamEnums.ActualController,
              keyName: AssociationTypeConst[DetailsParamEnums.ActualController],
              status: 0,
            },
            {
              key: DetailsParamEnums.MajorShareholder,
              keyName: AssociationTypeConst[DetailsParamEnums.MajorShareholder],
              status: 0,
            },
            {
              key: DetailsParamEnums.Shareholder,
              keyName: AssociationTypeConst[DetailsParamEnums.Shareholder],
              status: 0,
            },
          ],
          sort: 1,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Credit,
    sourcePath: '',
    status: 1,
    template: '<em class="#level#">【#name#】 #count#条记录</em>',
    template2: '【#name#】',
    type: IndicatorTypeEnums.generalItems,
    description: '企业及实控人、股东被市场监管部门列为经营异常名录',
  },
  [DimensionLevel3Enums.ProductQualityProblem6]: {
    key: DimensionLevel3Enums.ProductQualityProblem6,
    name: '未准入境',
    template: '<em class="#level#">【#name#】#cycle# #count#条记录</em>',
    template2: '【#name#】#cycle# #count#条记录',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.Medium,
      sortField: { field: 'publishdate', order: 'DESC', fieldSnapshot: 'PublishDate' },
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Credit,
    sourcePath: '/api/QccSearch/CreditSearch/Credit',
    status: 1,
    type: IndicatorTypeEnums.generalItems,
    description: '企业经营商品未被海关批准入境而被退运或销毁',
  },
  [DimensionLevel3Enums.ProductQualityProblem2]: {
    key: DimensionLevel3Enums.ProductQualityProblem2,
    name: '产品抽查不合格',
    template: '<em class="#level#">【#name#】#cycle# #count#条记录</em>',
    template2: '【#name#】#cycle# #count#条记录',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.Medium,
      sortField: { field: 'publishdate', order: 'DESC', fieldSnapshot: 'PublishDate' },
      cycle: 3,
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Credit,
    sourcePath: '',
    status: 1,
    type: IndicatorTypeEnums.generalItems,
    description: '企业在产品抽查中存在不合格记录',
  },
  [DimensionLevel3Enums.ProductQualityProblem1]: {
    key: DimensionLevel3Enums.ProductQualityProblem1,
    name: '产品召回',
    template: '<em class="#level#">【#name#】#cycle# #count#条记录</em>',
    template2: '【#name#】#cycle# #count#条记录',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      cycle: 3,
      level: DimensionRiskLevelEnum.Medium,
      sortField: { field: 'publishdate', order: 'DESC', fieldSnapshot: 'PublishDate' },
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Credit,
    sourcePath: '',
    status: 1,
    type: IndicatorTypeEnums.generalItems,
    description: '企业发生过产品召回事件',
  },
  [DimensionLevel3Enums.ProductQualityProblem7]: {
    key: DimensionLevel3Enums.ProductQualityProblem7,
    name: '药品抽查检验不合格',
    template: '<em class="#level#">【#name#】 #count#条记录</em>',
    template2: '【#name#】 #count#条记录',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.Medium,
      sortField: { field: 'publishdate', order: 'DESC', fieldSnapshot: 'PublishDate' },
      detailsParams: [
        {
          field: QueryParamsEnums.isValid,
          fieldVal: '1', // 1:当前有效，-1:不限
          sort: 0,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Credit,
    sourcePath: '/api/QccSearch/CreditSearch/Credit',
    status: 1,
    type: IndicatorTypeEnums.generalItems,
    description: '企业药品抽查中存在检验不合格记录',
  },
  [DimensionLevel3Enums.ProductQualityProblem9]: {
    key: DimensionLevel3Enums.ProductQualityProblem9,
    name: '食品安全检查不合格',
    template: '<em class="#level#">【#name#】#cycle# #count#条记录</em>',
    template2: '【#name#】#cycle# #count#条记录',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.Medium,
      sortField: { field: 'batch', order: 'DESC', fieldSnapshot: 'LianDate' },
      cycle: 3,
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/QccSearch/List/FoodSafety',
    status: 1,
    type: IndicatorTypeEnums.generalItems,
    description: '企业食品安全抽查中存在检验不合格记录',
  },
  [DimensionLevel3Enums.MainInfoUpdateRegisteredCapital]: {
    key: DimensionLevel3Enums.MainInfoUpdateRegisteredCapital,
    name: '近期变更注册资本',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.Medium,
      cycle: 1,
      detailsParams: [
        {
          field: QueryParamsEnums.registeredCapitalChangeRatio,
          fieldOperator: OperatorEnums.ge,
          fieldVal: 10, // 单位 %
          sort: 0,
        },
      ],
      sortField: { field: 'ChangeDate', order: 'DESC', fieldSnapshot: 'changeDate' },
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.RiskChange,
    sourcePath: '',
    status: 0,
    template: '<em class="#level#">【#name#】#cycle# #count#条记录</em>',
    template2: '【#name#】#cycle# #count#条记录',
    type: IndicatorTypeEnums.generalItems,
    description: '企业近期注册资本有变更',
  },
  [DimensionLevel3Enums.MainInfoUpdateScope]: {
    key: DimensionLevel3Enums.MainInfoUpdateScope,
    name: '近期变更经营范围',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.Medium,
      cycle: 1,
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/History/GetCoyHistoryInfo',
    status: 0,
    // 近1年经营范围变更 XX记录
    template: '<em class="#level#">【#name#】#cycle# #count#条记录</em>',
    template2: '【#name#】#cycle# #count#条记录',
    type: IndicatorTypeEnums.generalItems,
    description: '企业近期在工商登记的“经营范围”有变更',
  },
  [DimensionLevel3Enums.MainInfoUpdateAddress]: {
    key: DimensionLevel3Enums.MainInfoUpdateAddress,
    name: '近期变更注册地址',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.Medium,
      cycle: 1,
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/History/GetCoyHistoryInfo',
    status: 0,
    template: '<em class="#level#">【#name#】#cycle# #count#条记录</em>',
    template2: '【#name#】#cycle# #count#条记录',
    type: IndicatorTypeEnums.generalItems,
    description: '企业近期在工商登记的“经营地址”有变更',
  },
  [DimensionLevel3Enums.MainInfoUpdateName]: {
    key: DimensionLevel3Enums.MainInfoUpdateName,
    name: '近期变更企业名称',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.Medium,
      cycle: 1,
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/History/GetCoyHistoryInfo',
    status: 0,
    template: '<em class="#level#">【#name#】#cycle# #count#条记录</em>',
    template2: '【#name#】#cycle# #count#条记录',
    type: IndicatorTypeEnums.generalItems,
    description: '企业近期在工商登记的“企业名称”有变更',
  },
  [DimensionLevel3Enums.MainInfoUpdateLegalPerson]: {
    key: DimensionLevel3Enums.MainInfoUpdateLegalPerson,
    name: '近期变更法定代表人',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.Medium,
      cycle: 1,
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/History/GetCoyHistoryInfo',
    status: 0,
    template: '<em class="#level#">【#name#】#cycle# #count#条记录</em>',
    template2: '【#name#】#cycle# #count#条记录',
    type: IndicatorTypeEnums.generalItems,
    description: '企业近期在工商登记的“法定代表人”有变更',
  },
  [DimensionLevel3Enums.MainInfoUpdateHolder]: {
    key: DimensionLevel3Enums.MainInfoUpdateHolder,
    name: '近期变更大股东',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.Medium,
      cycle: 1,
      sortField: { field: 'CreateDate', order: 'DESC' },
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.RiskChange,
    sourcePath: '/api/proxy/risk/dynamic/getDynamicSelfGroupList',
    status: 0,
    template: '<em class="#level#">【#name#】#cycle# #count#条记录</em>',
    template2: '【#name#】#cycle# #count#条记录',
    type: IndicatorTypeEnums.generalItems,
    description: '企业近期大股东有变更',
  },
  [DimensionLevel3Enums.MainHoldAndBeneficiaryUpdate]: {
    key: DimensionLevel3Enums.MainHoldAndBeneficiaryUpdate,
    name: '近期变更实际控制人、受益所有人',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.Medium,
      cycle: 1,
      sortField: { field: 'ChangeDate', order: 'DESC', fieldSnapshot: 'ChangeDate' },
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.RiskChange,
    sourcePath: '/api/proxy/risk/dynamic/getDynamicSelfGroupList',
    status: 0,
    template: '<em class="#level#">【#name#】#cycle# #count#条记录</em>',
    template2: '【#name#】#cycle# #count#条记录',
    type: IndicatorTypeEnums.generalItems,
    description: '企业近期实际控制人或受益所有人（持股>=25%）有变更',
  },
  [DimensionLevel3Enums.MainInfoUpdatePerson]: {
    key: DimensionLevel3Enums.MainInfoUpdatePerson,
    name: '近期变更实际控制人',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.Medium,
      cycle: 1,
      sortField: { field: 'CreateDate', order: 'DESC' },
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.RiskChange,
    sourcePath: '/api/proxy/risk/dynamic/getDynamicSelfGroupList',
    status: 0,
    template: '<em class="#level#">【#name#】#cycle# #count#条记录</em>',
    template2: '【#name#】#cycle# #count#条记录',
    type: IndicatorTypeEnums.generalItems,
    description: '企业近期实际控制人有变更',
    isHidden: true,
  },
  [DimensionLevel3Enums.MainInfoUpdateBeneficiary]: {
    key: DimensionLevel3Enums.MainInfoUpdateBeneficiary,
    name: '近期变更受益所有人',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.Medium,
      cycle: 1,
      sortField: { field: 'CreateDate', order: 'DESC' },
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.RiskChange,
    sourcePath: '/api/proxy/risk/dynamic/getDynamicSelfGroupList',
    status: 0,
    template: '<em class="#level#">【#name#】#cycle# #count#条记录</em>',
    template2: '【#name#】#cycle# #count#条记录',
    type: IndicatorTypeEnums.generalItems,
    description: '企业受益所有人（持股>=25%）有变更',
    isHidden: true,
  },
  [DimensionLevel3Enums.MainInfoUpdateMainPersonnel]: {
    key: DimensionLevel3Enums.MainInfoUpdateMainPersonnel,
    name: '近期变更主要人员',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.Medium,
      cycle: 1,
      sortField: { field: 'ChangeDate', order: 'DESC', fieldSnapshot: 'changeDate' },
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.RiskChange,
    sourcePath: '',
    status: 0,
    template: '<em class="#level#">【#name#】#cycle# #count#条记录</em>',
    template2: '【#name#】#cycle# #count#条记录',
    type: IndicatorTypeEnums.generalItems,
    description: '企业近期主要人员（董监高等）有变更',
  },
  [DimensionLevel3Enums.Liquidation]: {
    key: DimensionLevel3Enums.Liquidation,
    name: '清算信息',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.High,
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/ECILocal/GetLiquidationDetail',
    status: 1,
    template: '<em class="#level#">【#name#】#cycle# #count#条记录</em>',
    template2: '【#name#】#cycle# #count#条记录',
    type: IndicatorTypeEnums.generalItems,
    description: '企业依法解散后清理公司债权债务的行为',
  },
  [DimensionLevel3Enums.StaffWorkingOutsideForeignInvestment]: {
    key: DimensionLevel3Enums.StaffWorkingOutsideForeignInvestment,
    name: '潜在利益冲突',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.High,
      detailsParams: [
        {
          field: QueryParamsEnums.types,
          fieldVal: [
            { key: DetailsParamEnums.Invest, keyName: '自然人股东', status: 1 },
            { key: DetailsParamEnums.LegalAndEmploy, keyName: '董监高/法人', status: 1 },
            {
              key: DetailsParamEnums.ActualController,
              keyName: '实际控制人',
              status: 1,
            },
            { key: DetailsParamEnums.HisInvest, keyName: '自然人股东（历史）', status: 1 },
            { key: DetailsParamEnums.HisLegalAndEmploy, keyName: '董监高/法人（历史）', status: 1 },
            { key: DetailsParamEnums.MainInfoUpdateBeneficiary, keyName: '受益所有人', status: 1 },
          ],
          sort: 0,
        },
        {
          field: QueryParamsEnums.dataRange,
          fieldVal: [
            {
              key: DetailsParamEnums.Groups,
              keyName: '人员分组',
              value: [],
              nameList: [], //分组名称
            },
          ],
          sort: 1,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Rover,
    sourcePath: '',
    status: 1,
    template: '<em class="#level#">【#name#】 #count#条记录</em>',
    template2: '<em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业股东、董监高等与内部员工存在相同',
  },
  [DimensionLevel3Enums.SuspectedInterestConflict]: {
    key: DimensionLevel3Enums.SuspectedInterestConflict,
    name: '疑似潜在利益冲突',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.Medium,
      detailsParams: [
        {
          field: QueryParamsEnums.types,
          fieldVal: [
            {
              key: DetailsParamEnums.SameName,
              keyName: '相同姓名',
              status: 1,
              child: [
                { key: DetailsParamEnums.Invest, keyName: '自然人股东', status: 1 },
                { key: DetailsParamEnums.LegalAndEmploy, keyName: '董监高/法人', status: 1 },
                {
                  key: DetailsParamEnums.ActualController,
                  keyName: '实际控制人',
                  status: 1,
                },
                { key: DetailsParamEnums.HisInvest, keyName: '自然人股东（历史）', status: 1 },
                { key: DetailsParamEnums.HisLegalAndEmploy, keyName: '董监高/法人（历史）', status: 1 },
                { key: DetailsParamEnums.MainInfoUpdateBeneficiary, keyName: '受益所有人', status: 1 },
              ],
            },
            { key: DetailsParamEnums.SameContact, keyName: '相同联系方式（电话、邮箱）', status: 1 },
          ],
          sort: 0,
        },
        {
          field: QueryParamsEnums.dataRange,
          fieldVal: [
            {
              key: DetailsParamEnums.Groups,
              keyName: '人员分组',
              value: [],
              nameList: [], //分组名称
            },
          ],
          sort: 1,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.Rover,
    sourcePath: '',
    status: 1,
    template: '<em class="#level#">【#name#】 #count#条记录</em>',
    template2: '<em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业股东、董监高等与内部员工存在疑似同名/相同联系方式',
  },
  [DimensionLevel3Enums.CapitalReduction]: {
    key: DimensionLevel3Enums.CapitalReduction,
    name: '减资公告',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.Medium,
      sortField: { order: 'DESC', fieldSnapshot: 'NoticeDate' },
      detailsParams: [
        {
          field: QueryParamsEnums.capitalReductionRatio,
          fieldOperator: OperatorEnums.ge,
          fieldVal: 50, // 单位 %
          sort: 0,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.RiskChange,
    sourcePath: '',
    status: 1,
    template: '<em class="#level#">【#name#】 #count#条记录</em>',
    template2: '<em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业公布减资公告(信息来源于工商公示)',
  },
  [DimensionLevel2Enums.HitOuterBlackList]: {
    key: DimensionLevel2Enums.HitOuterBlackList,
    name: '被列入外部黑名单',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.High,
      sortField: { field: 'decisiondate', order: 'DESC', fieldSnapshot: 'Publishdate' },
    },
    isVirtualDimension: 1,
    source: DimensionSourceEnums.OuterBlacklist,
    sourcePath: '',
    status: 1,
    template: '<em class="#level#">【被列入外部黑名单】 #count#条记录</em>',
    template2: '被列入外部黑名单 #count#条记录',
    sort: 1,
    type: IndicatorTypeEnums.generalItems,
    subDimensionList: DefaultOuterBlacklistItems,
  },
  [DimensionLevel3Enums.FinancialHealth]: {
    key: DimensionLevel3Enums.FinancialHealth,
    name: '财务健康度',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.Medium,
      detailsParams: [
        {
          field: QueryParamsEnums.assetLiabilityRatio,
          fieldOperator: OperatorEnums.ge,
          fieldVal: 70, // 单位 %
          sort: 0,
        },
        {
          field: QueryParamsEnums.revenueYearOnYearDeclineRatio,
          fieldOperator: OperatorEnums.ge,
          fieldVal: 30, // 单位 %
          sort: 1,
        },
        {
          field: QueryParamsEnums.netProfitYearOnYearDeclineRatio,
          fieldOperator: OperatorEnums.ge,
          fieldVal: 30, // 单位 %
          sort: 2,
        },
        {
          field: QueryParamsEnums.conditionOperator,
          fieldVal: ConditionOperatorEnums.Or,
          sort: 3,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/EciLocal/Finance/StatementsV2',
    status: 0,
    template: '<em class="#level#">【#name#】 #count#条记录</em>',
    template2: '<em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '根据企业对外公布的最新一期财报数据，评估企业的财务健康度',
  },
  [DimensionLevel3Enums.EmployeeReduction]: {
    key: DimensionLevel3Enums.EmployeeReduction,
    name: '员工人数减少',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.Medium,
      detailsParams: [
        {
          field: QueryParamsEnums.employeeReductionRatio,
          fieldOperator: OperatorEnums.ge,
          fieldVal: 10, // 单位 %
          sort: 0,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/QccSearch/List/SocialSecurityTrend',
    status: 1,
    template: templateString,
    template2: templateString2,
    type: IndicatorTypeEnums.generalItems,
    description: '企业最新参保人数同比上期有所降低',
  },
  [DimensionLevel3Enums.LatestInsuredNumber]: {
    key: DimensionLevel3Enums.LatestInsuredNumber,
    name: '参保人数缺失或过少',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.Medium,
      detailsParams: [
        {
          field: QueryParamsEnums.latestInsuredNumber,
          fieldOperator: OperatorEnums.lt,
          fieldVal: 10,
          sort: 0,
        },
      ],
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/QccSearch/List/SocialSecurityTrend',
    status: 1,
    template: '<em class="#level#">【参保人数缺失或过少】#operator##amountPerson#</em>',
    template2: '最新参保人数#operator##amountPerson#',
    type: IndicatorTypeEnums.generalItems,
    description: '将企业参保人数（来源工商）作为准入门槛，低于设定阈值或缺失即视为触发风险',
  },
  [DimensionLevel3Enums.DebtOverdue]: {
    key: DimensionLevel3Enums.DebtOverdue,
    name: '债务逾期',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.High,
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/Risk/DebtOverdue',
    status: 1,
    template: '<em class="#level#">【#name#】 #count#条记录</em><span class="#isHidden#">，累计逾期金额：<em class="#level#">#amountW#</em></span>',
    template2: '【#name#】<span class="#isHidden#">，累计逾期金额：#amountW#</span>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业存在债务逾期信息',
  },
  [DimensionLevel3Enums.SeparationNotice]: {
    key: DimensionLevel3Enums.SeparationNotice,
    name: '合并/分立公告',
    strategyModel: {
      boost: 1.0,
      baseScore: 20,
      level: DimensionRiskLevelEnum.Medium,
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/QccSearch/List/SeparationNotice',
    status: 1,
    template: templateString,
    template2: templateString2,
    type: IndicatorTypeEnums.generalItems,
    description: '企业在工商官网发布分立/合并公告',
  },
  [DimensionLevel2Enums.StockPledge]: {
    key: DimensionLevel2Enums.StockPledge,
    name: '股权质押',
    strategyModel: {
      boost: 1.0,
      baseScore: 5,
      level: DimensionRiskLevelEnum.Medium,
      detailsParams: [
        {
          field: QueryParamsEnums.pledgeStatus,
          fieldVal: [1, 2, 3], //1.未达预警线 2.已达预警线未达平仓线 3.已达平仓线
          sort: 0,
        },
      ],
      sortField: { field: 'publicdate', order: 'DESC', fieldSnapshot: 'NoticeDate' },
      cycle: 3,
    },
    isVirtualDimension: 0,
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/QccSearch/List/StockPledgeV2',
    status: 1,
    template: '<em class="#level#">【#name#】#cycle#  #count#条记录</em>',
    template2: '<em class="#level#">【#name#】#cycle#  #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业存在股权质押信息',
  },
};

export { DimensionLevel3Enums };
