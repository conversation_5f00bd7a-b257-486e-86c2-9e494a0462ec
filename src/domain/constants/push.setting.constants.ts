import { MonitorPushDimensionType } from '@domain/model/monitor/MonitorPushDimensionType';
import { PushSettingEnum } from '@domain/enums/monitor/PushSettingEnum';
import { DimensionRiskLevelEnum } from '@domain/enums/diligence/DimensionRiskLevelEnum';

export const DefaultMonitorPushSetting: MonitorPushDimensionType = {
  [PushSettingEnum.Groups]: [],

  [PushSettingEnum.RealTimePushContent]: [],

  [PushSettingEnum.NegativeNews]: [
    // { key: NewsSettingEnum.Manually, status: 1 },
    {
      key: DimensionRiskLevelEnum.High,
      status: true,
    },
    { key: DimensionRiskLevelEnum.Medium, status: true },
    { key: DimensionRiskLevelEnum.Alert, status: true },
  ],
  [PushSettingEnum.RiskDynamic]: [
    {
      key: DimensionRiskLevelEnum.High,
      status: true,
    },
    { key: DimensionRiskLevelEnum.Medium, status: true },
    { key: DimensionRiskLevelEnum.Alert, status: true },
  ],

  [PushSettingEnum.PushMethod]: {
    // notifyTimeRange: { startTime: '09:00', endTime: '18:00' },
    system: true,
    mailEnable: false,
    mailAddress: null,
    mobileEnable: false,
    mobileNumber: null,
    // limitSMS: 20,
    notifyTime: '09:00',
    onlyWorkday: true,
    notifyWeekDay: null,
    notifyMonthDay: null,
    interfaceEnable: false,
    interfaceSetting: null,
  },
};

export const DefaultMonitorMultiPushSetting: MonitorPushDimensionType = {
  [PushSettingEnum.Groups]: [],

  [PushSettingEnum.RealTimePushContent]: [],

  [PushSettingEnum.NegativeNews]: [
    {
      key: DimensionRiskLevelEnum.High,
      status: true,
    },
    { key: DimensionRiskLevelEnum.Medium, status: false },
    { key: DimensionRiskLevelEnum.Alert, status: false },
  ],
  [PushSettingEnum.RiskDynamic]: [
    {
      key: DimensionRiskLevelEnum.High,
      status: true,
    },
    { key: DimensionRiskLevelEnum.Medium, status: false },
    { key: DimensionRiskLevelEnum.Alert, status: false },
  ],

  [PushSettingEnum.PushMethod]: {
    // notifyTimeRange: { startTime: '09:00', endTime: '18:00' },
    system: true,
    mailEnable: false,
    mailAddress: null,
    mobileEnable: false,
    mobileNumber: null,
    // limitSMS: 20,
    notifyTime: '09:00',
    onlyWorkday: true,
    notifyWeekDay: null,
    notifyMonthDay: null,
    interfaceEnable: false,
    interfaceSetting: null,
  },
};
