import { DimensionGroupDefinitionTypeV2 } from '@domain/model/diligence/pojo/dimension/group/DimensionGroupDefinitionTypeV2';
import { GroupDimensionVersionEnums } from '@domain/model/diligence/pojo/dimension/group/GroupDimensionVersionEnums';
import { DimensionLevel1Enums } from '@domain/enums/diligence/DimensionLevel1Enums';
import { DimensionLevel2Enums } from '@domain/enums/diligence/DimensionLevel2Enums';
import { DimensionLevel3Enums } from '@domain/enums/diligence/DimensionLevel3Enums';
import { DimensionRiskLevelEnum } from '@domain/enums/diligence/DimensionRiskLevelEnum';
import { DimensionSourceEnums } from '@domain/enums/diligence/DimensionSourceEnums';
import { QueryParamsEnums } from '@domain/model/diligence/pojo/dimension/dimension.filter.params';
import { cloneDeep } from 'lodash';
import { IndicatorTypeEnums } from '@domain/model/settings/IndicatorTypeEnums';
import { DimensionGroupDefinitionTypeV1 } from '@domain/model/diligence/pojo/dimension/group/DimensionGroupDefinitionTypeV1';
import { BaseDimensions, templateString, templateString2 } from './dimension.base.constants';

/**
 * 维度风险样式
 */
export const DimensionLevelClass = ['low', 'medium', 'high'];

/**
 * 模型维度定义 v1, 默认版本
 */
const DefaultDimensionGroupDefinitionsV1: DimensionGroupDefinitionTypeV1 = {
  version: GroupDimensionVersionEnums.V1,
  /**
   * 基础资质
   */
  [DimensionLevel1Enums.Risk_BaseInfo]: {
    name: '基础资质',
    status: 1,
    sort: 1,
    items: [
      { ...BaseDimensions[DimensionLevel3Enums.CancellationOfFiling], sort: 1 },
      { ...BaseDimensions[DimensionLevel3Enums.BusinessAbnormal2], sort: 2 },
      { ...BaseDimensions[DimensionLevel3Enums.BusinessAbnormal1], sort: 3 },
      { ...BaseDimensions[DimensionLevel3Enums.BusinessAbnormal5], sort: 4 },
      { ...BaseDimensions[DimensionLevel3Enums.BusinessAbnormal7], sort: 5 },
      { ...BaseDimensions[DimensionLevel2Enums.FakeSOES], sort: 6 },
      { ...BaseDimensions[DimensionLevel2Enums.NoCapital], sort: 7 },
      { ...BaseDimensions[DimensionLevel2Enums.LowCapital], sort: 8 },
      { ...BaseDimensions[DimensionLevel2Enums.CompanyShell], sort: 9 },
      { ...BaseDimensions[DimensionLevel3Enums.BusinessAbnormal9], sort: 10 },
      { ...BaseDimensions[DimensionLevel2Enums.EstablishedTime], sort: 13 },
      { ...BaseDimensions[DimensionLevel3Enums.LatestInsuredNumber], sort: 17 },
      { ...BaseDimensions[DimensionLevel2Enums.Certification], sort: 15 },
      { ...BaseDimensions[DimensionLevel2Enums.FakeRegister], sort: 16 },
      // 下面是过时的隐藏维度
      { ...BaseDimensions[DimensionLevel3Enums.BusinessAbnormal8], sort: 10 },
      { ...BaseDimensions[DimensionLevel3Enums.BusinessAbnormal6], sort: 11 },
      { ...BaseDimensions[DimensionLevel2Enums.NoQualityCertification], sort: 10000 },
      { ...BaseDimensions[DimensionLevel2Enums.FraudList], sort: 12 },
      { ...BaseDimensions[DimensionLevel2Enums.NoCertification], sort: 14 },
    ],
  },
  /**法律风险 */
  [DimensionLevel1Enums.Risk_Legal]: {
    name: '法律风险',
    status: 1,
    sort: 2,
    items: [
      { ...BaseDimensions[DimensionLevel3Enums.PersonCreditCurrent], sort: 1 },
      { ...BaseDimensions[DimensionLevel3Enums.RestrictedConsumptionCurrent], sort: 2 },
      { ...BaseDimensions[DimensionLevel3Enums.MainMembersPersonCreditCurrent], sort: 3 },
      { ...BaseDimensions[DimensionLevel3Enums.MainMembersRestrictedConsumptionCurrent], sort: 4 },
      { ...BaseDimensions[DimensionLevel3Enums.MainMembersRestrictedOutbound], sort: 5 },
      { ...BaseDimensions[DimensionLevel3Enums.PersonCreditHistory], sort: 6 },
      { ...BaseDimensions[DimensionLevel3Enums.RestrictedConsumptionHistory], sort: 7 },
      { ...BaseDimensions[DimensionLevel3Enums.CompanyOrMainMembersCriminalOffence], sort: 8 },
      { ...BaseDimensions[DimensionLevel3Enums.CompanyOrMainMembersCriminalOffenceHistory], sort: 9 },
      { ...BaseDimensions[DimensionLevel3Enums.CompanyOrMainMembersLitigationInfo], sort: 91 },
      { ...BaseDimensions[DimensionLevel3Enums.CompanyOrMainMembersCriminalInvolve], sort: 100 },
      { ...BaseDimensions[DimensionLevel3Enums.CompanyOrMainMembersCriminalInvolveHistory], sort: 110 },
      { ...BaseDimensions[DimensionLevel2Enums.TaxationOffences], sort: 120 },
      { ...BaseDimensions[DimensionLevel2Enums.Bankruptcy], sort: 130 },
      { ...BaseDimensions[DimensionLevel2Enums.FreezeEquity], sort: 140 },
      { ...BaseDimensions[DimensionLevel2Enums.PersonExecution], sort: 150 },
      { ...BaseDimensions[DimensionLevel2Enums.JudicialAuction], sort: 160 },
      { ...BaseDimensions[DimensionLevel2Enums.ChattelSeizure], sort: 240 },
      { ...BaseDimensions[DimensionLevel2Enums.ContractBreach], sort: 170 },
      { ...BaseDimensions[DimensionLevel3Enums.SalesContractDispute], sort: 180 },
      { ...BaseDimensions[DimensionLevel3Enums.LaborContractDispute], sort: 190 },
      { ...BaseDimensions[DimensionLevel3Enums.MajorDispute], sort: 200 },
      { ...BaseDimensions[DimensionLevel3Enums.NoticeInTimePeriod], sort: 210 },
      { ...BaseDimensions[DimensionLevel3Enums.EndExecutionCase], sort: 220 },
      { ...BaseDimensions[DimensionLevel3Enums.UnfairCompetition], sort: 230 },
    ],
  },
  /**行政监管风险 */
  [DimensionLevel1Enums.Risk_AdministrativeSupervision]: {
    name: '行政监管风险',
    status: 1,
    sort: 3,
    items: [
      { ...BaseDimensions[DimensionLevel2Enums.CompanyCredit], sort: 1 },
      { ...BaseDimensions[DimensionLevel3Enums.BusinessAbnormal4], sort: 2 },
      { ...BaseDimensions[DimensionLevel3Enums.BusinessAbnormal3], sort: 3 },
      { ...BaseDimensions[DimensionLevel2Enums.CompanyCreditHistory], sort: 4 },
      { ...BaseDimensions[DimensionLevel2Enums.OperationAbnormal], sort: 5 },
      { ...BaseDimensions[DimensionLevel2Enums.TaxArrearsNotice], sort: 6 },
      { ...BaseDimensions[DimensionLevel3Enums.ProductQualityProblem6], sort: 7 },
      { ...BaseDimensions[DimensionLevel3Enums.ProductQualityProblem2], sort: 8 },
      { ...BaseDimensions[DimensionLevel3Enums.ProductQualityProblem1], sort: 9 },
      { ...BaseDimensions[DimensionLevel3Enums.ProductQualityProblem7], sort: 10 },
      { ...BaseDimensions[DimensionLevel3Enums.ProductQualityProblem9], sort: 11 },
      { ...BaseDimensions[DimensionLevel2Enums.SpotCheck], sort: 12 },
      { ...BaseDimensions[DimensionLevel2Enums.AdministrativePenalties2], sort: 13 },
      { ...BaseDimensions[DimensionLevel2Enums.AdministrativePenalties3], sort: 14 },
      { ...BaseDimensions[DimensionLevel2Enums.AdministrativePenalties], sort: 15 },
      { ...BaseDimensions[DimensionLevel2Enums.EnvironmentalPenalties], sort: 16 },
      { ...BaseDimensions[DimensionLevel2Enums.TaxReminder], sort: 170 },
      { ...BaseDimensions[DimensionLevel2Enums.TaxCallNoticeV2], sort: 171 },
      { ...BaseDimensions[DimensionLevel2Enums.SecurityNotice], sort: 180 },
      { ...BaseDimensions[DimensionLevel2Enums.RegulateFinance], sort: 190 },
      { ...BaseDimensions[DimensionLevel2Enums.TaxPenalties], sort: 200 },
      { ...BaseDimensions[DimensionLevel2Enums.TaxCallNotice], sort: 172 },
    ],
  },
  /**经营稳定性风险 */
  [DimensionLevel1Enums.Risk_OperateStability]: {
    name: '经营稳定性风险',
    status: 1,
    sort: 4,
    items: [
      { ...BaseDimensions[DimensionLevel2Enums.BondDefaults], sort: 1 },
      { ...BaseDimensions[DimensionLevel2Enums.BillDefaults], sort: 20 },
      { ...BaseDimensions[DimensionLevel2Enums.PersistentBillOverdue], sort: 30 },
      { ...BaseDimensions[DimensionLevel2Enums.ChattelMortgage], sort: 31 },
      { ...BaseDimensions[DimensionLevel2Enums.PledgeMerger], sort: 40 },
      { ...BaseDimensions[DimensionLevel2Enums.StockPledge], sort: 41 },
      { ...BaseDimensions[DimensionLevel2Enums.EquityPledge], sort: 42 },
      { ...BaseDimensions[DimensionLevel2Enums.LandMortgage], sort: 50 },
      { ...BaseDimensions[DimensionLevel2Enums.IPRPledge], sort: 51 },
      { ...BaseDimensions[DimensionLevel2Enums.NonPerformingAssets], sort: 52 },
      { ...BaseDimensions[DimensionLevel2Enums.GuaranteeRisk], sort: 60 },
      { ...BaseDimensions[DimensionLevel2Enums.NoTender], sort: 70 },
      { ...BaseDimensions[DimensionLevel3Enums.MainInfoUpdateScope], sort: 80 },
      { ...BaseDimensions[DimensionLevel3Enums.MainInfoUpdateAddress], sort: 90 },
      { ...BaseDimensions[DimensionLevel3Enums.MainInfoUpdateName], sort: 100 },
      { ...BaseDimensions[DimensionLevel3Enums.MainInfoUpdateLegalPerson], sort: 110 },
      { ...BaseDimensions[DimensionLevel3Enums.MainInfoUpdateHolder], sort: 120 },
      { ...BaseDimensions[DimensionLevel3Enums.MainInfoUpdatePerson], sort: 130 },
      { ...BaseDimensions[DimensionLevel3Enums.MainHoldAndBeneficiaryUpdate], sort: 131 },
      { ...BaseDimensions[DimensionLevel3Enums.MainInfoUpdateBeneficiary], sort: 140 },
      { ...BaseDimensions[DimensionLevel3Enums.MainInfoUpdateMainPersonnel], sort: 141 },
      { ...BaseDimensions[DimensionLevel3Enums.MainInfoUpdateRegisteredCapital], sort: 142 },
      { ...BaseDimensions[DimensionLevel3Enums.Liquidation], sort: 150 },
      { ...BaseDimensions[DimensionLevel2Enums.GuaranteeInfo], sort: 151 },
      { ...BaseDimensions[DimensionLevel3Enums.CapitalReduction], sort: 160 },
      { ...BaseDimensions[DimensionLevel3Enums.FinancialHealth], sort: 170 },
      { ...BaseDimensions[DimensionLevel3Enums.EmployeeReduction], sort: 180 },
      { ...BaseDimensions[DimensionLevel3Enums.DebtOverdue], sort: 190 },
      { ...BaseDimensions[DimensionLevel3Enums.SeparationNotice], sort: 200 },
    ],
  },
  [DimensionLevel1Enums.Risk_NegativeNews]: {
    name: '负面新闻',
    status: 1,
    sort: 5,
    items: [
      { ...BaseDimensions[DimensionLevel2Enums.NegativeNewsRecent], sort: 1 },
      { ...BaseDimensions[DimensionLevel2Enums.NegativeNewsHistory], sort: 2 },
    ],
  },
  /**
   * 交叉重叠关系排查
   */
  [DimensionLevel1Enums.Risk_PartnerInvestigation]: {
    name: '交叉重叠关系排查',
    status: 0,
    sort: 7,
    items: [
      { ...BaseDimensions[DimensionLevel2Enums.CustomerPartnerInvestigation], sort: 1 },
      { ...BaseDimensions[DimensionLevel2Enums.CustomerSuspectedRelation], sort: 2 },
      // 以下hidden
      {
        // 交叉重叠关系排查-投资关联
        key: DimensionLevel2Enums.InvestorsRelationship,
        name: '与第三方列表主体存在投资关联',
        strategyModel: {
          boost: 1.0,
          baseScore: 5,
          level: DimensionRiskLevelEnum.Medium,
          detailsParams: [
            {
              field: QueryParamsEnums.isValid,
              fieldVal: '-1',
              sort: 0,
            },
          ],
        },
        isVirtualDimension: 0,
        source: DimensionSourceEnums.Rover,
        sourcePath: '',
        status: 0,
        template: '<em class="#level#">【#name#】 #count#条记录</em>',
        template2: '与第三方列表主体存在投资关联 #count#条记录',
        sort: 2,
        type: IndicatorTypeEnums.generalItems,
        description: '目标主体投资主体与第三方列表主体投资关联',
        isHidden: true,
      },
      {
        // 交叉重叠关系排查-持股关联
        key: DimensionLevel2Enums.ShareholdingRelationship,
        name: '与第三方列表主体存在持股关联',
        strategyModel: {
          boost: 1.0,
          baseScore: 5,
          level: DimensionRiskLevelEnum.Medium,
          detailsParams: [
            {
              field: QueryParamsEnums.isValid,
              fieldVal: '-1',
              sort: 0,
            },
          ],
        },
        isVirtualDimension: 0,
        source: DimensionSourceEnums.Rover,
        sourcePath: '',
        status: 0,
        template: '<em class="#level#">【#name#】 #count#条记录</em>',
        template2: '与第三方列表主体存在持股关联 #count#条记录',
        sort: 1,
        type: IndicatorTypeEnums.generalItems,
        description: '与第三方列表主体存在持股关联', //'目标主体法人股东主体与客商列表主体持股关联',
        isHidden: true,
      },
      {
        // 交叉重叠关系-人员关联  包括法定代表人、董监高、股东
        key: DimensionLevel2Enums.ServeRelationship,
        name: '与第三方列表主体存在人员关联',
        strategyModel: {
          boost: 1.0,
          baseScore: 5,
          level: DimensionRiskLevelEnum.Medium,
          detailsParams: [
            {
              field: QueryParamsEnums.isValid,
              fieldVal: '-1',
              sort: 0,
            },
          ],
        },
        isVirtualDimension: 0,
        source: DimensionSourceEnums.Rover,
        sourcePath: '',
        status: 0,
        template: '<em class="#level#">【#name#】 #count#条记录</em>',
        template2: '与第三方列表主体存在人员关联 #count#条记录',
        sort: 3,
        type: IndicatorTypeEnums.generalItems,
        description: '目标主体法人股东主体与第三方列表是否存在人员关联',
        isHidden: true,
      },
      {
        key: DimensionLevel2Enums.SameSuspectedActualController,
        name: '与第三方列表主体存在相同实际控制人关联',
        strategyModel: {
          boost: 1.0,
          baseScore: 5,
          level: DimensionRiskLevelEnum.Medium,
        },
        isVirtualDimension: 0,
        source: DimensionSourceEnums.Rover,
        sourcePath: '',
        status: 0,
        template: '<em class="#level#">【#name#】 #count#条记录</em>',
        template2: '与第三方列表主体存在相同实际控制人关联 #count#条记录',
        sort: 5,
        type: IndicatorTypeEnums.generalItems,
        description: '目标主体法人股东主体与第三方列表是否存在相同实际控制人关联',
        isHidden: true,
      },
    ],
  },
  /**
   * 内部黑名单
   */
  [DimensionLevel1Enums.Risk_InnerBlacklist]: {
    name: '内部黑名单',
    status: 1,
    sort: 9,
    items: [
      { ...BaseDimensions[DimensionLevel2Enums.HitInnerBlackList], sort: 1 },
      { ...BaseDimensions[DimensionLevel2Enums.BlacklistPartnerInvestigation], sort: 2 },
      { ...BaseDimensions[DimensionLevel2Enums.BlacklistSuspectedRelation], sort: 3 },
      // 以下hidden
      {
        key: DimensionLevel2Enums.Shareholder,
        name: '参股股东被列入内部黑名单',
        strategyModel: {
          boost: 1.0,
          baseScore: 5,
          level: DimensionRiskLevelEnum.High,
          detailsParams: [
            {
              field: QueryParamsEnums.isValid,
              fieldVal: '-1',
              sort: 0,
            },
          ],
        },
        isVirtualDimension: 0,
        source: DimensionSourceEnums.Rover,
        sourcePath: '',
        status: 0,
        template: '<em class="#level#">【参股股东被列入内部黑名单】 #count#条记录</em>',
        template2: '参股股东被列入内部黑名单#count#条记录',
        sort: 2,
        type: IndicatorTypeEnums.generalItems,
        description: '目标主体持股股东是否被列入内部黑名单',
        isHidden: true,
      },
      {
        key: DimensionLevel2Enums.ForeignInvestment,
        name: '对外投资主体被列入内部黑名单',
        strategyModel: {
          boost: 1.0,
          baseScore: 5,
          level: DimensionRiskLevelEnum.High,
          detailsParams: [
            {
              field: QueryParamsEnums.isValid,
              fieldVal: '-1',
              sort: 0,
            },
          ],
        },
        isVirtualDimension: 0,
        source: DimensionSourceEnums.Rover,
        sourcePath: '',
        status: 0,
        template: '<em class="#level#">【#name#】 #count#条记录</em>',
        template2: '对外投资主体被列入内部黑名单#count#条记录',
        sort: 3,
        type: IndicatorTypeEnums.generalItems,
        description: '目标主体对外投资主体是否被列入内部黑名单',
        isHidden: true,
      },
      { ...BaseDimensions[DimensionLevel2Enums.EmploymentRelationship], sort: 4 },
      {
        key: DimensionLevel2Enums.BlacklistSameSuspectedActualController,
        name: '与内部黑名单列表存在相同实际控制人关联',
        strategyModel: {
          boost: 1.0,
          baseScore: 5,
          level: DimensionRiskLevelEnum.High,
        },
        isVirtualDimension: 0,
        source: DimensionSourceEnums.Rover,
        sourcePath: '',
        status: 0,
        template: '<em class="#level#">【#name#】 #count#条记录</em>',
        template2: '与黑名单（内部）存在相同实际控制人关联#count#条记录',
        sort: 6,
        type: IndicatorTypeEnums.generalItems,
        description: '目标主体与内部黑名单存在相同实际控制人关联',
        isHidden: true,
      },
    ],
  },
  /**
   * 外部黑名单
   */
  [DimensionLevel1Enums.Risk_OuterBlacklist]: {
    name: '外部黑名单',
    status: 1,
    sort: 11,
    // 说明： 外部黑名单 直接将子维度开放为设置维度
    // 排查时候再将子维度移动 到 DimensionLevel2Enums.HitOuterBlackList的 subDimensionList 中
    //  代码实现在 settingService.getDimensionGroupDefinition 方法中
    // items: DefaultOuterBlacklistItems,
    items: [{ ...BaseDimensions[DimensionLevel2Enums.HitOuterBlackList], sort: 1 }],
  },
  /**
   * 潜在利益冲突
   */
  [DimensionLevel1Enums.Risk_InterestConflict]: {
    name: '潜在利益冲突',
    status: 0,
    sort: 13,
    items: [
      { ...BaseDimensions[DimensionLevel3Enums.StaffWorkingOutsideForeignInvestment], sort: 1 },
      { ...BaseDimensions[DimensionLevel3Enums.SuspectedInterestConflict], sort: 2 },
      // 以下hidden
      {
        key: DimensionLevel3Enums.StaffWorkingOutside,
        name: '疑似潜在利益冲突-在外任职',
        strategyModel: {
          boost: 1.0,
          baseScore: 5,
          level: DimensionRiskLevelEnum.Medium,
          detailsParams: [
            {
              field: QueryParamsEnums.isValid,
              fieldVal: '-1', //当前有效
              sort: 0,
            },
          ],
        },
        isVirtualDimension: 0,
        source: DimensionSourceEnums.Rover,
        sourcePath: '',
        status: 0,
        template: '<em class="#level#">【疑似潜在利益冲突-在外任职】 #count#条记录</em>',
        template2: '疑似潜在利益冲突-在外任职 #count#条记录',
        sort: 1,
        type: IndicatorTypeEnums.generalItems,
        description: '目标主体法定代表人、董监高等是否与内部员工存在疑似同名',
        isHidden: true,
      },
      {
        key: DimensionLevel3Enums.StaffForeignInvestment,
        name: '疑似潜在利益冲突-对外投资',
        strategyModel: {
          boost: 1.0,
          baseScore: 5,
          level: DimensionRiskLevelEnum.Medium,
          detailsParams: [
            {
              field: QueryParamsEnums.isValid,
              fieldVal: '-1', //当前有效
              sort: 0,
            },
          ],
        },
        isVirtualDimension: 0,
        source: DimensionSourceEnums.Rover,
        sourcePath: '',
        status: 0,
        template: '<em class="#level#">【疑似潜在利益冲突-对外投资】 #count#条记录</em>',
        template2: '潜在利益冲突-对外投资 #count#条记录',
        sort: 2,
        type: IndicatorTypeEnums.generalItems,
        description: '目标主体自然人股东是否与内部员工存在疑似同名',
        isHidden: true,
      },
      {
        key: DimensionLevel3Enums.SamePhone,
        name: '疑似潜在利益冲突-相同联系方式',
        strategyModel: {
          boost: 1.0,
          baseScore: 5,
          level: DimensionRiskLevelEnum.Medium,
        },
        isVirtualDimension: 0,
        source: DimensionSourceEnums.Rover,
        sourcePath: '',
        status: 0,
        template: '<em class="#level#">【#name#】 #count#条记录</em>',
        template2: '疑似潜在利益冲突-相同联系方式 #count#条记录',
        sort: 3,
        type: IndicatorTypeEnums.generalItems,
        description: '目标主体联系方式与企业相关联系方式相同',
        isHidden: true,
      },
    ],
  },
};

/**
 * 模型维度定义 v2, 蔡司版本
 */
const DefaultDimensionGroupDefinitionsV2: DimensionGroupDefinitionTypeV2 = {
  version: GroupDimensionVersionEnums.V2,
  /**
   * 交叉重叠关系
   */
  [DimensionLevel1Enums.Risk_PartnerInvestigation]: {
    name: '交叉重叠关系',
    status: 0,
    sort: 1,
    items: [
      {
        // 交叉重叠关系-投资关联
        key: DimensionLevel2Enums.InvestorsRelationship,
        name: '与第三方列表主体存在投资关联',
        strategyModel: {
          boost: 1.0,
          baseScore: 5,
          level: DimensionRiskLevelEnum.Medium,
          detailsParams: [
            {
              field: QueryParamsEnums.isValid,
              fieldVal: '-1',
              sort: 0,
            },
          ],
        },
        isVirtualDimension: 0,
        source: DimensionSourceEnums.Rover,
        sourcePath: '',
        status: 0,
        template: '<em class="#level#">【#name#】 #count#条记录</em>',
        template2: '与第三方列表主体投资关联 #count#条记录',
        sort: 2,
        type: IndicatorTypeEnums.generalItems,
        description: '目标主体投资主体与第三方列表主体投资关联',
        isHidden: true,
      },
      {
        // 交叉重叠关系-持股关联
        key: DimensionLevel2Enums.ShareholdingRelationship,
        name: '与第三方列表主体存在持股关联',
        strategyModel: {
          boost: 1.0,
          baseScore: 5,
          level: DimensionRiskLevelEnum.Medium,
          detailsParams: [
            {
              field: QueryParamsEnums.isValid,
              fieldVal: '-1',
              sort: 0,
            },
          ],
        },
        isVirtualDimension: 0,
        source: DimensionSourceEnums.Rover,
        sourcePath: '',
        status: 0,
        template: '<em class="#level#">【与第三方列表主体持股关联】 #count#条记录</em>',
        template2: '与第三方列表主体持股关联 #count#条记录',
        sort: 1,
        type: IndicatorTypeEnums.generalItems,
        description: '目标主体与第三方列表主体持股关联', //'目标主体法人股东主体与第三方列表主体持股关联',
        isHidden: true,
      },
      {
        // 交叉重叠关系-人员关联  包括法定代表人、董监高、股东
        key: DimensionLevel2Enums.ServeRelationship,
        name: '与第三方列表主体存在人员关联',
        strategyModel: {
          boost: 1.0,
          baseScore: 5,
          level: DimensionRiskLevelEnum.Medium,
          detailsParams: [
            {
              field: QueryParamsEnums.isValid,
              fieldVal: '-1',
              sort: 0,
            },
          ],
        },
        isVirtualDimension: 0,
        source: DimensionSourceEnums.Rover,
        sourcePath: '',
        status: 0,
        template: '<em class="#level#">【与第三方列表主体存在人员关联】 #count#条记录</em>',
        template2: '与第三方列表主体存在人员关联 #count#条记录',
        sort: 3,
        type: IndicatorTypeEnums.generalItems,
        description: '目标主体法人股东主体与第三方列表是否存在人员关联',
        isHidden: true,
      },
      {
        key: DimensionLevel2Enums.SameSuspectedActualController,
        name: '与第三方列表主体存在相同实际控制人关联',
        strategyModel: {
          boost: 1.0,
          baseScore: 5,
          level: DimensionRiskLevelEnum.Medium,
        },
        isVirtualDimension: 0,
        source: DimensionSourceEnums.Rover,
        sourcePath: '',
        status: 0,
        template: '<em class="#level#">【#name#】 #count#条记录</em>',
        template2: '与第三方列表主体存在相同实际控制人关联 #count#条记录',
        sort: 5,
        type: IndicatorTypeEnums.generalItems,
        description: '目标主体法人股东主体与第三方列表是否存在相同实际控制人关联',
        isHidden: true,
      },
      { ...BaseDimensions[DimensionLevel2Enums.CustomerPartnerInvestigation], sort: 5 },
      { ...BaseDimensions[DimensionLevel2Enums.CustomerSuspectedRelation], sort: 6 },
    ],
  },
  /**
   * 潜在利益冲突
   */
  [DimensionLevel1Enums.Risk_InterestConflict]: {
    name: '潜在利益冲突',
    status: 0,
    sort: 2,
    items: [
      {
        key: DimensionLevel3Enums.StaffWorkingOutside,
        name: '疑似潜在利益冲突-在外任职',
        strategyModel: {
          boost: 1.0,
          baseScore: 5,
          level: DimensionRiskLevelEnum.Medium,
          detailsParams: [
            {
              field: QueryParamsEnums.isValid,
              fieldVal: '-1', //当前有效
              sort: 0,
            },
          ],
        },
        isVirtualDimension: 0,
        source: DimensionSourceEnums.Rover,
        sourcePath: '',
        status: 0,
        template: '<em class="#level#">【疑似潜在利益冲突-在外任职】 #count#条记录</em>',
        template2: '疑似潜在利益冲突-在外任职 #count#条记录',
        sort: 1,
        type: IndicatorTypeEnums.generalItems,
        description: '目标主体法定代表人、董监高等是否与内部员工存在疑似同名',
        isHidden: true,
      },
      {
        key: DimensionLevel3Enums.StaffForeignInvestment,
        name: '疑似潜在利益冲突-对外投资',
        strategyModel: {
          boost: 1.0,
          baseScore: 5,
          level: DimensionRiskLevelEnum.Medium,
          detailsParams: [
            {
              field: QueryParamsEnums.isValid,
              fieldVal: '-1', //当前有效
              sort: 0,
            },
          ],
        },
        isVirtualDimension: 0,
        source: DimensionSourceEnums.Rover,
        sourcePath: '',
        status: 0,
        template: '<em class="#level#">【疑似潜在利益冲突-对外投资】 #count#条记录</em>',
        template2: '潜在利益冲突-对外投资 #count#条记录',
        sort: 2,
        type: IndicatorTypeEnums.generalItems,
        description: '目标主体自然人股东是否与内部员工存在疑似同名',
        isHidden: true,
      },
      {
        key: DimensionLevel3Enums.SamePhone,
        name: '疑似潜在利益冲突-相同联系方式',
        strategyModel: {
          boost: 1.0,
          baseScore: 5,
          level: DimensionRiskLevelEnum.Medium,
        },
        isVirtualDimension: 0,
        source: DimensionSourceEnums.Rover,
        sourcePath: '',
        status: 0,
        template: '<em class="#level#">【#name#】 #count#条记录</em>',
        template2: '疑似潜在利益冲突-相同联系方式 #count#条记录',
        sort: 3,
        type: IndicatorTypeEnums.generalItems,
        description: '目标主体联系方式与企业相关联系方式相同',
        isHidden: true,
      },
      { ...BaseDimensions[DimensionLevel3Enums.StaffWorkingOutsideForeignInvestment], sort: 4 },
      { ...BaseDimensions[DimensionLevel3Enums.SuspectedInterestConflict], sort: 5 },
    ],
  },
  /**
   * 新成立第三方
   */
  [DimensionLevel1Enums.Risk_NewlyEstablished]: {
    name: '新成立第三方',
    status: 1,
    sort: 3,
    items: [{ ...BaseDimensions[DimensionLevel2Enums.EstablishedTime], sort: 1 }],
  },

  /**
   * 被处罚员工
   */
  [DimensionLevel1Enums.Risk_PunishedEmployees]: {
    name: '被处罚员工',
    status: 1,
    sort: 4,
    items: [
      {
        key: DimensionLevel3Enums.PunishedEmployeesWorkingOutside,
        name: '疑似被处罚员工-在外任职',
        strategyModel: {
          boost: 1.0,
          baseScore: 5,
          level: DimensionRiskLevelEnum.Medium,
          detailsParams: [
            {
              field: QueryParamsEnums.isValid,
              fieldVal: '1', //当前有效
              sort: 0,
            },
          ],
        },
        isVirtualDimension: 0,
        source: DimensionSourceEnums.Rover,
        sourcePath: '',
        status: 1,
        template: '<em class="#level#">【#name#】 #count#条记录</em>',
        template2: '疑似被处罚员工-在外任职 #count#条记录',
        sort: 1,
        type: IndicatorTypeEnums.generalItems,
        description: '疑似被处罚员工在企业任职董监高',
      },
      {
        key: DimensionLevel3Enums.PunishedEmployeesForeignInvestment,
        name: '疑似被处罚员工-对外投资',
        strategyModel: {
          boost: 1.0,
          baseScore: 5,
          level: DimensionRiskLevelEnum.Medium,
          detailsParams: [
            {
              field: QueryParamsEnums.isValid,
              fieldVal: '1', //当前有效
              sort: 0,
            },
          ],
        },
        isVirtualDimension: 0,
        source: DimensionSourceEnums.Rover,
        sourcePath: '',
        status: 1,
        template: '<em class="#level#">【#name#】 #count#条记录</em>',
        template2: '疑似被处罚员工-对外投资 #count#条记录',
        sort: 2,
        type: IndicatorTypeEnums.generalItems,
        description: '疑似被处罚员工在企业有投资股份',
      },
    ],
  },
  /**
   * 负面信息
   */
  [DimensionLevel1Enums.Risk_NegativeOpinion]: {
    name: '负面信息',
    status: 1,
    sort: 5,
    items: [
      {
        key: DimensionLevel1Enums.Risk_BaseInfo,
        name: '基础资质',
        isVirtualDimension: 1,
        status: 1,
        template: templateString,
        template2: templateString2,
        sort: 1,
        type: IndicatorTypeEnums.generalItems,
        subDimensionList: [
          { ...BaseDimensions[DimensionLevel3Enums.BusinessAbnormal1], sort: 1 },
          { ...BaseDimensions[DimensionLevel3Enums.CancellationOfFiling], sort: 2 },
          { ...BaseDimensions[DimensionLevel3Enums.BusinessAbnormal2], sort: 3 },
          { ...BaseDimensions[DimensionLevel3Enums.BusinessAbnormal5], sort: 4 },
          { ...BaseDimensions[DimensionLevel2Enums.FakeSOES], sort: 5 },
          { ...BaseDimensions[DimensionLevel2Enums.CompanyShell], sort: 6 },
          { ...BaseDimensions[DimensionLevel3Enums.BusinessAbnormal9], sort: 7 },
          { ...BaseDimensions[DimensionLevel2Enums.NoCapital], sort: 9 },
          { ...BaseDimensions[DimensionLevel3Enums.BusinessAbnormal7], sort: 10 },
          { ...BaseDimensions[DimensionLevel2Enums.LowCapital], sort: 11 },
          { ...BaseDimensions[DimensionLevel2Enums.FakeRegister], sort: 12 },
          // 以下维度已隐藏
          { ...BaseDimensions[DimensionLevel3Enums.BusinessAbnormal8], sort: 7 },
          { ...BaseDimensions[DimensionLevel3Enums.BusinessAbnormal6], sort: 8 },
          { ...BaseDimensions[DimensionLevel2Enums.FraudList], sort: 13 },
          { ...BaseDimensions[DimensionLevel2Enums.Certification], sort: 16 },
          { ...BaseDimensions[DimensionLevel3Enums.LatestInsuredNumber], sort: 17 },
          // { ...BaseDimensions[DimensionLevel2Enums.NoQualityCertification], sort: 14 },
          // { ...BaseDimensions[DimensionLevel2Enums.NoCertification], sort: 15 },
        ],
      },
      {
        key: DimensionLevel1Enums.Risk_Legal,
        name: '法律风险',
        isVirtualDimension: 1,
        status: 1,
        template: templateString,
        template2: templateString2,
        sort: 2,
        type: IndicatorTypeEnums.generalItems,
        subDimensionList: [
          { ...BaseDimensions[DimensionLevel3Enums.PersonCreditCurrent], sort: 1 },
          { ...BaseDimensions[DimensionLevel2Enums.TaxationOffences], sort: 4 },
          { ...BaseDimensions[DimensionLevel3Enums.RestrictedConsumptionCurrent], sort: 2 },
          { ...BaseDimensions[DimensionLevel3Enums.RestrictedConsumptionHistory], sort: 8 },
          { ...BaseDimensions[DimensionLevel2Enums.Bankruptcy], sort: 5 },
          { ...BaseDimensions[DimensionLevel3Enums.PersonCreditHistory], sort: 9 },
          { ...BaseDimensions[DimensionLevel2Enums.FreezeEquity], sort: 18 },
          { ...BaseDimensions[DimensionLevel2Enums.PersonExecution], sort: 14 },
          { ...BaseDimensions[DimensionLevel2Enums.ChattelSeizure], sort: 24 },
          { ...BaseDimensions[DimensionLevel2Enums.ContractBreach], sort: 915 },
          { ...BaseDimensions[DimensionLevel2Enums.JudicialAuction], sort: 19 },
          { ...BaseDimensions[DimensionLevel3Enums.MainMembersPersonCreditCurrent], sort: 5 },
          { ...BaseDimensions[DimensionLevel3Enums.MainMembersRestrictedConsumptionCurrent], sort: 6 },
          { ...BaseDimensions[DimensionLevel3Enums.MainMembersRestrictedOutbound], sort: 7 },
          { ...BaseDimensions[DimensionLevel3Enums.CompanyOrMainMembersCriminalOffence], sort: 10 },
          { ...BaseDimensions[DimensionLevel3Enums.CompanyOrMainMembersCriminalOffenceHistory], sort: 11 },
          { ...BaseDimensions[DimensionLevel3Enums.CompanyOrMainMembersLitigationInfo], sort: 25 },
          { ...BaseDimensions[DimensionLevel3Enums.SalesContractDispute], sort: 16 },
          { ...BaseDimensions[DimensionLevel3Enums.LaborContractDispute], sort: 17 },
          { ...BaseDimensions[DimensionLevel3Enums.MajorDispute], sort: 18 },
          { ...BaseDimensions[DimensionLevel3Enums.CompanyOrMainMembersCriminalInvolve], sort: 19 },
          { ...BaseDimensions[DimensionLevel3Enums.CompanyOrMainMembersCriminalInvolveHistory], sort: 20 },
          { ...BaseDimensions[DimensionLevel3Enums.NoticeInTimePeriod], sort: 21 },
          { ...BaseDimensions[DimensionLevel3Enums.EndExecutionCase], sort: 22 },
          { ...BaseDimensions[DimensionLevel3Enums.UnfairCompetition], sort: 23 },
        ],
      },
      {
        key: DimensionLevel1Enums.Risk_AdministrativeSupervision,
        name: '行政监管风险',
        isVirtualDimension: 1,
        status: 1,
        template: templateString,
        template2: templateString2,
        sort: 3,
        type: IndicatorTypeEnums.generalItems,
        subDimensionList: [
          { ...BaseDimensions[DimensionLevel2Enums.CompanyCredit], sort: 1 },
          { ...BaseDimensions[DimensionLevel2Enums.CompanyCreditHistory], sort: 4 },
          { ...BaseDimensions[DimensionLevel3Enums.BusinessAbnormal3], sort: 2 },
          { ...BaseDimensions[DimensionLevel2Enums.OperationAbnormal], sort: 9 },
          { ...BaseDimensions[DimensionLevel2Enums.TaxArrearsNotice], sort: 11 },
          { ...BaseDimensions[DimensionLevel3Enums.BusinessAbnormal4], sort: 3 },
          { ...BaseDimensions[DimensionLevel3Enums.ProductQualityProblem1], sort: 12 },
          { ...BaseDimensions[DimensionLevel3Enums.ProductQualityProblem2], sort: 13 },
          { ...BaseDimensions[DimensionLevel3Enums.ProductQualityProblem6], sort: 14 },
          { ...BaseDimensions[DimensionLevel3Enums.ProductQualityProblem7], sort: 15 },
          { ...BaseDimensions[DimensionLevel3Enums.ProductQualityProblem9], sort: 16 },
          { ...BaseDimensions[DimensionLevel2Enums.AdministrativePenalties], sort: 8 },
          { ...BaseDimensions[DimensionLevel2Enums.TaxPenalties], sort: 17 },
          { ...BaseDimensions[DimensionLevel2Enums.EnvironmentalPenalties], sort: 7 },
          { ...BaseDimensions[DimensionLevel2Enums.AdministrativePenalties2], sort: 5 },
          { ...BaseDimensions[DimensionLevel2Enums.AdministrativePenalties3], sort: 6 },
          { ...BaseDimensions[DimensionLevel2Enums.SpotCheck], sort: 10 },
          { ...BaseDimensions[DimensionLevel2Enums.TaxReminder], sort: 180 },
          { ...BaseDimensions[DimensionLevel2Enums.TaxCallNoticeV2], sort: 181 },
          { ...BaseDimensions[DimensionLevel2Enums.TaxCallNotice], sort: 182 },
          { ...BaseDimensions[DimensionLevel2Enums.SecurityNotice], sort: 190 },
          { ...BaseDimensions[DimensionLevel2Enums.RegulateFinance], sort: 200 },
        ],
      },
      {
        key: DimensionLevel1Enums.Risk_OperateStability,
        name: '经营稳定性风险',
        isVirtualDimension: 1,
        status: 1,
        template: templateString,
        template2: templateString2,
        sort: 4,
        type: IndicatorTypeEnums.generalItems,
        subDimensionList: [
          { ...BaseDimensions[DimensionLevel2Enums.BondDefaults], sort: 10 },
          { ...BaseDimensions[DimensionLevel2Enums.GuaranteeRisk], sort: 20 },
          { ...BaseDimensions[DimensionLevel2Enums.PledgeMerger], sort: 30 },
          { ...BaseDimensions[DimensionLevel2Enums.StockPledge], sort: 40 },
          { ...BaseDimensions[DimensionLevel2Enums.EquityPledge], sort: 41 },
          { ...BaseDimensions[DimensionLevel2Enums.IPRPledge], sort: 50 },
          { ...BaseDimensions[DimensionLevel2Enums.NonPerformingAssets], sort: 51 },
          { ...BaseDimensions[DimensionLevel2Enums.LandMortgage], sort: 60 },
          { ...BaseDimensions[DimensionLevel2Enums.NoTender], sort: 70 },
          { ...BaseDimensions[DimensionLevel3Enums.MainInfoUpdateScope], sort: 80 },
          { ...BaseDimensions[DimensionLevel3Enums.MainInfoUpdateAddress], sort: 81 },
          { ...BaseDimensions[DimensionLevel3Enums.MainInfoUpdateName], sort: 90 },
          { ...BaseDimensions[DimensionLevel3Enums.MainInfoUpdateLegalPerson], sort: 100 },
          { ...BaseDimensions[DimensionLevel3Enums.MainInfoUpdateHolder], sort: 110 },
          { ...BaseDimensions[DimensionLevel3Enums.MainInfoUpdatePerson], sort: 120 },
          { ...BaseDimensions[DimensionLevel3Enums.MainHoldAndBeneficiaryUpdate], sort: 121 },
          { ...BaseDimensions[DimensionLevel3Enums.MainInfoUpdateBeneficiary], sort: 140 },
          { ...BaseDimensions[DimensionLevel3Enums.MainInfoUpdateMainPersonnel], sort: 141 },
          { ...BaseDimensions[DimensionLevel3Enums.MainInfoUpdateRegisteredCapital], sort: 142 },
          { ...BaseDimensions[DimensionLevel2Enums.BillDefaults], sort: 160 },
          { ...BaseDimensions[DimensionLevel2Enums.PersistentBillOverdue], sort: 170 },
          { ...BaseDimensions[DimensionLevel3Enums.Liquidation], sort: 180 },
          { ...BaseDimensions[DimensionLevel2Enums.GuaranteeInfo], sort: 181 },
          { ...BaseDimensions[DimensionLevel3Enums.CapitalReduction], sort: 190 },
          { ...BaseDimensions[DimensionLevel3Enums.FinancialHealth], sort: 200 },
          { ...BaseDimensions[DimensionLevel3Enums.EmployeeReduction], sort: 210 },
          { ...BaseDimensions[DimensionLevel3Enums.DebtOverdue], sort: 220 },
          { ...BaseDimensions[DimensionLevel3Enums.SeparationNotice], sort: 230 },
        ],
      },
      {
        key: DimensionLevel1Enums.Risk_NegativeNews,
        name: '负面新闻',
        isVirtualDimension: 1,
        status: 1,
        template: templateString,
        template2: templateString2,
        sort: 5,
        type: IndicatorTypeEnums.generalItems,
        subDimensionList: [
          { ...BaseDimensions[DimensionLevel2Enums.NegativeNewsRecent], sort: 1 },
          { ...BaseDimensions[DimensionLevel2Enums.NegativeNewsHistory], sort: 2 },
        ],
      },
    ],
  },
  /**
   * 第三方黑名单 (内部+外部）
   */
  [DimensionLevel1Enums.Risk_Blacklist]: {
    name: '第三方黑名单',
    status: 1,
    sort: 6,
    items: [
      {
        key: DimensionLevel1Enums.Risk_InnerBlacklist,
        name: '内部黑名单',
        strategyModel: {
          boost: 1.0,
          baseScore: 5,
          level: DimensionRiskLevelEnum.High,
        },
        isVirtualDimension: 1,
        source: DimensionSourceEnums.Rover,
        sourcePath: '',
        status: 1,
        template: '',
        template2: '',
        sort: 1,
        type: IndicatorTypeEnums.generalItems,
        subDimensionList: [
          { ...BaseDimensions[DimensionLevel2Enums.HitInnerBlackList], sort: 1 },
          {
            key: DimensionLevel2Enums.Shareholder,
            name: '参股股东被列入内部黑名单',
            strategyModel: {
              boost: 1.0,
              baseScore: 5,
              level: DimensionRiskLevelEnum.High,
              detailsParams: [
                {
                  field: QueryParamsEnums.isValid,
                  fieldVal: '-1',
                  sort: 0,
                },
              ],
            },
            isVirtualDimension: 0,
            source: DimensionSourceEnums.Rover,
            sourcePath: '',
            status: 0,
            template: '<em class="#level#">【参股股东被列入内部黑名单】 #count#条记录</em>',
            template2: '参股股东被列入内部黑名单#count#条记录',
            sort: 2,
            type: IndicatorTypeEnums.generalItems,
            description: '目标主体持股股东是否被列入内部黑名单',
            isHidden: true,
          },
          {
            key: DimensionLevel2Enums.ForeignInvestment,
            name: '对外投资主体被列入内部黑名单',
            strategyModel: {
              boost: 1.0,
              baseScore: 5,
              level: DimensionRiskLevelEnum.High,
              detailsParams: [
                {
                  field: QueryParamsEnums.isValid,
                  fieldVal: '-1',
                  sort: 0,
                },
              ],
            },
            isVirtualDimension: 0,
            source: DimensionSourceEnums.Rover,
            sourcePath: '',
            status: 0,
            template: '<em class="#level#">【#name#】 #count#条记录</em>',
            template2: '对外投资主体被列入内部黑名单#count#条记录',
            sort: 3,
            type: IndicatorTypeEnums.generalItems,
            description: '目标主体对外投资主体是否被列入内部黑名单',
            isHidden: true,
          },
          { ...BaseDimensions[DimensionLevel2Enums.EmploymentRelationship], sort: 4 },
          {
            key: DimensionLevel2Enums.BlacklistSameSuspectedActualController,
            name: '与内部黑名单列表存在相同实际控制人关联',
            strategyModel: {
              boost: 1.0,
              baseScore: 5,
              level: DimensionRiskLevelEnum.High,
            },
            isVirtualDimension: 0,
            source: DimensionSourceEnums.Rover,
            sourcePath: '',
            status: 0,
            template: '<em class="#level#">【#name#】 #count#条记录</em>',
            template2: '与黑名单（内部）存在相同实际控制人关联#count#条记录',
            sort: 6,
            type: IndicatorTypeEnums.generalItems,
            description: '目标主体与内部黑名单存在相同实际控制人关联',
            isHidden: true,
          },
          { ...BaseDimensions[DimensionLevel2Enums.BlacklistPartnerInvestigation], sort: 7 },
          { ...BaseDimensions[DimensionLevel2Enums.BlacklistSuspectedRelation], sort: 8 },
        ],
      },
      {
        key: DimensionLevel1Enums.Risk_OuterBlacklist,
        name: '外部黑名单',
        isVirtualDimension: 1,
        status: 1,
        template: templateString,
        template2: templateString2,
        sort: 2,
        type: IndicatorTypeEnums.generalItems,
        subDimensionList: [
          { ...BaseDimensions[DimensionLevel2Enums.HitOuterBlackList], sort: 1 },
          // {
          //   key: DimensionLevel2Enums.HitOuterBlackList,
          //   name: '被列入外部黑名单',
          //   strategyModel: {
          //     boost: 1.0,
          //     baseScore: 5,
          //     level: DimensionRiskLevelEnum.High,
          //     sortField: { field: 'decisiondate', order: 'DESC', fieldSnapshot: 'Publishdate' },
          //   },
          //   isVirtualDimension: 1,
          //   source: DimensionSourceEnums.OuterBlacklist,
          //   sourcePath: '',
          //   status: 1,
          //   template: '<em class="#level#">【被列入外部黑名单】 #count#条记录</em>',
          //   template2: '被列入外部黑名单 #count#条记录',
          //   sort: 2,
          //   type: IndicatorTypeEnums.generalItems,
          //   subDimensionList: DefaultOuterBlacklistItems,
          // },
        ],
      },
    ],
  },
};

export const getDefaultDimensionGroupDefinition = (version: 'v1' | 'v2'): DimensionGroupDefinitionTypeV1 | DimensionGroupDefinitionTypeV2 => {
  if (version === 'v1') {
    return cloneDeep(DefaultDimensionGroupDefinitionsV1);
  } else if (version === 'v2') {
    return cloneDeep(DefaultDimensionGroupDefinitionsV2);
  } else {
    throw new Error(`getDefaultDimensionGroupDefinition() unknown version=${version}`);
  }
};
