import { DimensionLevel2Enums } from '@domain/enums/diligence/DimensionLevel2Enums';
import { DimensionLevel3Enums } from '@domain/enums/diligence/DimensionLevel3Enums';
import { DimensionTypeEnums } from '@domain/enums/diligence/DimensionTypeEnums';

export class CreditEsMappingModelField {
  sourceName: string;
  returnName: string;
  mapping?: object;
  desc: string;
  isShow?: boolean;
  isHighlight?: boolean = false;
  highlightTo?: string[];
}

export class CreditEsMappingModel {
  type: string;
  desc: string;
  dimensionType: DimensionTypeEnums[]; //如果length大于1， 0: 全部， 1： 当前有效，2： 历史有效, 3: 主要人员
  fieldList: CreditEsMappingModelField[]; // 可以暂时为空，如果需要获取详情，需要配置
}

/**
 * 行政处罚-处罚类型 publishtype
 */
export const PenaltiesType = {
  '0908': { esCode: 'A008', desc: '吊销许可证件（含执照）', sort: 1 },
  '0911': { esCode: 'A011', desc: '责令关闭', sort: 2 },
  '0915': { esCode: 'A015', desc: '移送司法机关', sort: 3 },
  '0910': { esCode: 'A010', desc: '责令停产停业', sort: 4 },
  '0914': { esCode: 'A014', desc: '行政拘留', sort: 5 },
  '0909': { esCode: 'A009', desc: '限制开展生产经营活动', sort: 6 },
  '0912': { esCode: 'A012', desc: '限制申请行政许可', sort: 7 },
  '0913': { esCode: 'A013', desc: '限制从业', sort: 8 },
  '0903': { esCode: 'A003', desc: '罚款', sort: 9 },
  '0904': { esCode: 'A004', desc: '没收违法所得', sort: 10 },
  '0905': { esCode: 'A005', desc: '没收非法财物', sort: 11 },
  '0906': { esCode: 'A006', desc: '暂扣许可证件（含执照）', sort: 12 },
  '0907': { esCode: 'A007', desc: '降低资质等级', sort: 13 },
  '0901': { esCode: 'A001', desc: '警告', sort: 14 },
  '0902': { esCode: 'A002', desc: '通报批评', sort: 15 },
  '0999': { esCode: 'A099', desc: '其他行政处罚', sort: 16 },
  '0916': { esCode: 'A016', desc: '不予处罚', sort: 17 },
  // '0912': { esCode: 'A012', desc: '限制申请行政许可' },
};

export const CreditType = {
  A112: { esCode: 'A112', desc: '暂停相关业务', sort: 1 },
  A111: { esCode: 'A111', desc: '暂停或者限制交易权限', sort: 2 },
  A110: { esCode: 'A110', desc: '严重警告', sort: 3 },
  A113: { esCode: 'A113', desc: '责令改正', sort: 4 },
  A101: { esCode: 'A101', desc: '公开谴责', sort: 5 },
  A104: { esCode: 'A104', desc: '监管警示', sort: 6 },
  A102: { esCode: 'A102', desc: '监管关注', sort: 7 },
  A103: { esCode: 'A103', desc: '监管函', sort: 8 },
  A105: { esCode: 'A105', desc: '诫勉谈话', sort: 9 },
  A117: { esCode: 'A117', desc: '警告', sort: 10 },
  A106: { esCode: 'A106', desc: '警示函', sort: 11 },
  A107: { esCode: 'A107', desc: '内部批评', sort: 12 },
  A109: { esCode: 'A109', desc: '书面警示', sort: 13 },
  A116: { esCode: 'A116', desc: '通报批评', sort: 14 },
  A108: { esCode: 'A108', desc: '认定不适当人选', sort: 15 },
  A114: { esCode: 'A114', desc: '责令致歉', sort: 16 },
  A115: { esCode: 'A115', desc: '自律管理', sort: 17 },
  A199: { esCode: 'A199', desc: '其他处理措施', sort: 18 },
};

/**
 * 人员身份
 */
export const OperType = {
  '1': '法定代表人',
  '2': '执行事务合伙人',
  '3': '负责人',
  '4': '经营者',
  '5': '投资人',
  '6': '董事长',
  '7': '理事长',
  '8': '代表人',
};
/**
 * 经营异常-异常类型 publishtype
 */
export const BusinessAbnormalType = {
  '0803': { esCode: '3', desc: '公示信息隐瞒真实情况/弄虚作假' },
  '0801': { esCode: '1', desc: '登记的住所/经营场所无法联系企业' },
  '0805': { esCode: '5', desc: '未在规定期限公示年度报告' },
  '0802': { esCode: '2', desc: '未按规定公示企业信息' },
  '0804': { esCode: '4', desc: '未在登记所从事经营活动' },
  '0806': { esCode: '6', desc: '商事主体名称不适宜' },
  '0807': { esCode: '7', desc: '其他原因' },
};

export const creditSearchSourceMappings: CreditEsMappingModel[] = [
  {
    type: '100',
    desc: '破产重整',
    dimensionType: [DimensionLevel2Enums.Bankruptcy],
    fieldList: [
      {
        sourceName: 'id',
        returnName: 'id',
        desc: '主键',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'name',
        returnName: 'respondent',
        desc: '被申请人',
        isShow: true,
        isHighlight: true,
        highlightTo: ['name', 'name.text'],
      },
      {
        sourceName: 'pername',
        returnName: 'pername',
        desc: 'pername',
        isShow: false,
        isHighlight: true,
        highlightTo: ['pername'],
      },
      {
        sourceName: 'originalname',
        returnName: 'originalname',
        desc: '曾用名',
        isHighlight: true,
        highlightTo: ['originalname', 'originalname.text'],
      },
      {
        sourceName: 'nameandkeyno',
        returnName: 'nameandkeyno',
        desc: '主体KeyNo',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'type',
        returnName: 'type',
        desc: '信用类型',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'casereasontype',
        returnName: 'casereasontype',
        desc: '破产类型',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'publishdate',
        returnName: 'publicTime',
        desc: '公开时间',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'court',
        returnName: 'court',
        desc: '法院',
        isHighlight: false,
        highlightTo: [],
      },

      {
        sourceName: 'caseno',
        returnName: 'caseno',
        desc: '案号',
        isShow: true,
        isHighlight: true,
        highlightTo: ['caseno'],
      },
      {
        sourceName: 'riskid',
        returnName: 'riskid',
        desc: '风险id',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'applicant',
        returnName: 'applicant',
        desc: '申请人',
        isShow: true,
        isHighlight: true,
        highlightTo: ['applicant', 'p_applicant'],
      },
      {
        sourceName: 'nameandkeyno',
        returnName: 'nameandkeyno',
        desc: 'applicantKeyNo',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'datastatus',
        returnName: 'datastatus',
        desc: 'datastatus',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'hasimage',
        returnName: 'hasimage',
        desc: 'hasimage',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'stockinfo',
        returnName: 'stockinfo',
        desc: '股票',
        isHighlight: true,
        highlightTo: ['stockinfo'],
      },
      {
        sourceName: 'product',
        returnName: 'product',
        desc: '产品',
        isHighlight: true,
        highlightTo: ['product'],
      },
      {
        sourceName: 'punishgovcode',
        returnName: 'punishgovcode',
        desc: '处罚机构code',
        isHighlight: false,
        highlightTo: [],
      },
    ],
  },
  {
    type: '2',
    desc: '失信被执行人',
    dimensionType: [
      DimensionLevel3Enums.PersonCreditCurrent, // DimensionLevel3Enums.PersonCreditCurrent,
      DimensionLevel3Enums.PersonCreditHistory, // DimensionLevel3Enums.PersonCreditHistory,
      DimensionLevel3Enums.MainMembersPersonCreditCurrent,
    ], //确保current在前，history在后
    fieldList: [
      {
        sourceName: 'id',
        returnName: 'id',
        desc: '主键',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'name',
        returnName: 'executee',
        desc: '被执行人',
        isShow: true,
        isHighlight: true,
        highlightTo: ['name', 'name.text'],
      },
      {
        sourceName: 'pername',
        returnName: 'pername',
        desc: 'pername',
        isShow: false,
        isHighlight: true,
        highlightTo: ['pername'],
      },
      {
        sourceName: 'originalname',
        returnName: 'originalname',
        desc: '曾用名',
        isHighlight: true,
        highlightTo: ['originalname', 'originalname.text'],
      },
      {
        sourceName: 'nameandkeyno',
        returnName: 'nameandkeyno',
        desc: '主体KeyNo',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'type',
        returnName: 'type',
        desc: '信用类型',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'publishdate',
        returnName: 'releaseTime',
        desc: '发布时间',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'liandate',
        returnName: 'filingTime',
        desc: '立案时间',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'amount',
        returnName: 'involvedAmount',
        desc: '涉案金额',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'court',
        returnName: 'court',
        desc: '执行法院',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'caseno',
        returnName: 'caseno',
        desc: '案号',
        isShow: true,
        isHighlight: true,
        highlightTo: ['caseno'],
      },
      {
        sourceName: 'orgno',
        returnName: 'executeDocNum',
        desc: '执行依据文号',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'actionremark',
        returnName: 'dishonestBehavio',
        desc: '失信行为',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'executestatus',
        returnName: 'performance',
        desc: '履行情况',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'riskid',
        returnName: 'riskid',
        desc: '风险id',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'datastatus',
        returnName: 'datastatus',
        desc: 'datastatus',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'hasimage',
        returnName: 'hasimage',
        desc: 'hasimage',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'stockinfo',
        returnName: 'stockinfo',
        desc: '股票',
        isHighlight: true,
        highlightTo: ['stockinfo'],
      },
      {
        sourceName: 'product',
        returnName: 'product',
        desc: '产品',
        isHighlight: true,
        highlightTo: ['product'],
      },
      {
        sourceName: 'punishgovcode',
        returnName: 'punishgovcode',
        desc: '处罚机构code',
        isHighlight: false,
        highlightTo: [],
      },
    ],
  },
  {
    type: '3',
    desc: '限制高消费',
    dimensionType: [
      DimensionLevel3Enums.RestrictedConsumptionCurrent,
      DimensionLevel3Enums.RestrictedConsumptionHistory,
      DimensionLevel3Enums.MainMembersRestrictedConsumptionCurrent,
    ],
    fieldList: [
      {
        sourceName: 'id',
        returnName: 'id',
        desc: '主键',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'name',
        returnName: 'restrictedObjects',
        desc: '限消令对象',
        isShow: true,
        isHighlight: true,
        highlightTo: ['name', 'name.text'],
      },
      {
        sourceName: 'pername',
        returnName: 'pername',
        desc: 'pername',
        isShow: false,
        isHighlight: true,
        highlightTo: ['pername'],
      },
      {
        sourceName: 'originalname',
        returnName: 'originalname',
        desc: '曾用名',
        isShow: false,
        isHighlight: true,
        highlightTo: ['originalname', 'originalname.text'],
      },
      {
        sourceName: 'nameandkeyno',
        returnName: 'nameandkeyno',
        desc: '主体KeyNo',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'type',
        returnName: 'type',
        desc: '信用类型',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'publishdate',
        returnName: 'releaseTime',
        desc: '发布时间',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'liandate',
        returnName: 'FilingTime',
        desc: '立案时间',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'caseno',
        returnName: 'caseno',
        desc: '案号',
        isShow: true,
        isHighlight: true,
        highlightTo: ['caseno'],
      },
      {
        sourceName: 'actionremark',
        returnName: 'noticeStatus',
        desc: '是否有详情,1-存在详情，0-不存在详情',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'osskey',
        returnName: 'fileUrl',
        desc: '附件',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'riskid',
        returnName: 'riskid',
        desc: '风险id',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'pledgor',
        returnName: 'associatedObject',
        desc: '关联对象',
        isShow: true,
        isHighlight: true,
        highlightTo: ['pledgor', 'p_pledgor'],
      },
      {
        sourceName: 'nameandkeyno',
        returnName: 'nameandkeyno',
        desc: 'pledgorKeyNo',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'applicant',
        returnName: 'applicant',
        desc: '申请人',
        isShow: true,
        isHighlight: true,
        highlightTo: ['applicant', 'p_applicant'],
      },
      {
        sourceName: 'nameandkeyno',
        returnName: 'nameandkeyno',
        desc: 'applicantKeyNo',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'datastatus',
        returnName: 'datastatus',
        desc: 'datastatus',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'hasimage',
        returnName: 'hasimage',
        desc: 'hasimage',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'stockinfo',
        returnName: 'stockinfo',
        desc: '股票',
        isHighlight: true,
        highlightTo: ['stockinfo'],
      },
      {
        sourceName: 'product',
        returnName: 'product',
        desc: '产品',
        isHighlight: true,
        highlightTo: ['product'],
      },
      {
        sourceName: 'punishgovcode',
        returnName: 'punishgovcode',
        desc: '处罚机构code',
        isHighlight: false,
        highlightTo: [],
      },
    ],
  },
  {
    type: '4',
    desc: '限制出境',
    dimensionType: [DimensionLevel2Enums.RestrictedOutbound, DimensionLevel3Enums.MainMembersRestrictedOutbound],
    fieldList: [
      {
        sourceName: 'id',
        returnName: 'id',
        desc: '主键',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'name',
        returnName: 'restrictedOutbound',
        desc: '限制出境对象',
        isShow: true,
        isHighlight: false,
        highlightTo: ['name', 'name.text'],
      },
      {
        sourceName: 'pername',
        returnName: 'pername',
        desc: 'pername',
        isShow: false,
        isHighlight: true,
        highlightTo: ['pername'],
      },
      {
        sourceName: 'originalname',
        returnName: 'originalname',
        desc: '曾用名',
        isShow: false,
        isHighlight: true,
        highlightTo: ['originalname', 'originalname.text'],
      },
      {
        sourceName: 'nameandkeyno',
        returnName: 'nameandkeyno',
        desc: '主体KeyNo',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'type',
        returnName: 'type',
        desc: '信用类型',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'publishdate',
        returnName: 'releaseDate',
        desc: '发布日期',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'amount',
        returnName: 'executionSubject',
        desc: '执行标的',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'court',
        returnName: 'court',
        desc: '法院',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'caseno',
        returnName: 'caseno',
        desc: '案号',
        isShow: true,
        isHighlight: true,
        highlightTo: ['caseno'],
      },
      {
        sourceName: 'riskid',
        returnName: 'riskid',
        desc: '风险id',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'pledgor',
        returnName: 'executedPerson',
        desc: '被执行人',
        isShow: true,
        isHighlight: true,
        highlightTo: ['pledgor', 'p_pledgor'],
      },
      {
        sourceName: 'nameandkeyno',
        returnName: 'nameandkeyno',
        desc: 'pledgorKeyNo',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'address',
        returnName: 'address',
        desc: '被执行人地址',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'applicant',
        returnName: 'applicant',
        desc: '申请执行人',
        isShow: true,
        isHighlight: true,
        highlightTo: ['applicant', 'p_applicant'],
      },
      {
        sourceName: 'nameandkeyno',
        returnName: 'nameandkeyno',
        desc: 'applicantKeyNo',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'datastatus',
        returnName: 'datastatus',
        desc: 'datastatus',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'hasimage',
        returnName: 'hasimage',
        desc: 'hasimage',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'stockinfo',
        returnName: 'stockinfo',
        desc: '股票',
        isHighlight: true,
        highlightTo: ['stockinfo'],
      },
      {
        sourceName: 'product',
        returnName: 'product',
        desc: '产品',
        isHighlight: true,
        highlightTo: ['product'],
      },
      {
        sourceName: 'punishgovcode',
        returnName: 'punishgovcode',
        desc: '处罚机构code',
        isHighlight: false,
        highlightTo: [],
      },
    ],
  },
  {
    type: '5',
    desc: '被执行人',
    dimensionType: [DimensionLevel2Enums.PersonExecution],
    fieldList: [
      {
        sourceName: 'id',
        returnName: 'id',
        desc: '主键',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'name',
        returnName: 'executedPerson',
        desc: '被执行人',
        isShow: true,
        isHighlight: true,
        highlightTo: ['name', 'name.text'],
      },
      {
        sourceName: 'pername',
        returnName: 'pername',
        desc: 'pername',
        isShow: false,
        isHighlight: true,
        highlightTo: ['pername'],
      },
      {
        sourceName: 'originalname',
        returnName: 'originalname',
        desc: '曾用名',
        isShow: false,
        isHighlight: true,
        highlightTo: ['originalname', 'originalname.text'],
      },
      {
        sourceName: 'nameandkeyno',
        returnName: 'nameandkeyno',
        desc: '主体KeyNo',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'type',
        returnName: 'type',
        desc: '信用类型',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'publishdate',
        returnName: 'filingDate',
        desc: '立案日期',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'amount',
        returnName: 'executionSubject',
        desc: '执行标的',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'court',
        returnName: 'court',
        desc: '执行法院',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'caseno',
        returnName: 'caseno',
        desc: '案号',
        isShow: true,
        isHighlight: true,
        highlightTo: ['caseno'],
      },
      {
        sourceName: 'orgno',
        returnName: 'executedPersonNo',
        desc: '被执行人身份证号/组织机构代码',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'riskid',
        returnName: 'riskid',
        desc: '风险id',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'address',
        returnName: 'suspectedExecutor',
        desc: '疑似申请执行人',
        isHighlight: false,
        highlightTo: [],
      },

      {
        sourceName: 'datastatus',
        returnName: 'datastatus',
        desc: 'datastatus',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'hasimage',
        returnName: 'hasimage',
        desc: 'hasimage',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'stockinfo',
        returnName: 'stockinfo',
        desc: '股票',
        isHighlight: true,
        highlightTo: ['stockinfo'],
      },
      {
        sourceName: 'product',
        returnName: 'product',
        desc: '产品',
        isHighlight: true,
        highlightTo: ['product'],
      },
      {
        sourceName: 'punishgovcode',
        returnName: 'punishgovcode',
        desc: '处罚机构code',
        isHighlight: false,
        highlightTo: [],
      },
    ],
  },
  {
    type: '6',
    desc: '终本案件',
    dimensionType: null,
    fieldList: [
      {
        sourceName: 'id',
        returnName: 'id',
        desc: '主键',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'name',
        returnName: 'executedPerson',
        desc: '被执行人',
        isShow: true,
        isHighlight: true,
        highlightTo: ['name', 'name.text'],
      },
      {
        sourceName: 'pername',
        returnName: 'pername',
        desc: 'pername',
        isShow: false,
        isHighlight: true,
        highlightTo: ['pername'],
      },
      {
        sourceName: 'originalname',
        returnName: 'originalname',
        desc: '曾用名',
        isShow: false,
        isHighlight: true,
        highlightTo: ['originalname', 'originalname.text'],
      },
      {
        sourceName: 'nameandkeyno',
        returnName: 'nameandkeyno',
        desc: '主体KeyNo',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'type',
        returnName: 'type',
        desc: '信用类型',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'publishdate',
        returnName: 'endDate',
        desc: '终本日期',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'liandate',
        returnName: 'filingDate',
        desc: '立案日期',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'amount',
        returnName: 'executionSubject',
        desc: '执行标的',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'amount2',
        returnName: 'nonPerAmount',
        desc: '未履行金额',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'court',
        returnName: 'court',
        desc: '执行法院',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'caseno',
        returnName: 'caseno',
        desc: '案号',
        isShow: true,
        isHighlight: true,
        highlightTo: ['caseno'],
      },
      {
        sourceName: 'riskid',
        returnName: 'riskid',
        desc: '风险id',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'orgno',
        returnName: 'suspectedExecutor',
        desc: '被执行人身份证号/组织机构代码',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },

      {
        sourceName: 'datastatus',
        returnName: 'datastatus',
        desc: 'datastatus',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'hasimage',
        returnName: 'hasimage',
        desc: 'hasimage',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'stockinfo',
        returnName: 'stockinfo',
        desc: '股票',
        isHighlight: true,
        highlightTo: ['stockinfo'],
      },
      {
        sourceName: 'product',
        returnName: 'product',
        desc: '产品',
        isHighlight: true,
        highlightTo: ['product'],
      },
      {
        sourceName: 'punishgovcode',
        returnName: 'punishgovcode',
        desc: '处罚机构code',
        isHighlight: false,
        highlightTo: [],
      },
    ],
  },
  {
    type: '700',
    desc: '股权冻结',
    dimensionType: [DimensionLevel2Enums.FreezeEquity],
    fieldList: [
      {
        sourceName: 'id',
        returnName: 'id',
        desc: '主键',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'pername',
        returnName: 'pername',
        desc: 'pername',
        isShow: false,
        isHighlight: true,
        highlightTo: ['pername'],
      },
      {
        sourceName: 'name',
        returnName: 'executedPerson',
        desc: '被执行人',
        isShow: true,
        isHighlight: true,
        highlightTo: ['name', 'name.text'],
      },
      { sourceName: 'keyno', returnName: 'keyNo', desc: '被执行人keyno' },
      {
        sourceName: 'originalname',
        returnName: 'originalname',
        desc: '曾用名',
        isShow: false,
        isHighlight: true,
        highlightTo: ['originalname', 'originalname.text'],
      },
      {
        sourceName: 'nameandkeyno',
        returnName: 'nameandkeyno',
        desc: '主体KeyNo',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'type',
        returnName: 'type',
        desc: '信用类型',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'publishdate',
        returnName: 'publishdate',
        desc: '公式时间',
        isHighlight: false,
        highlightTo: [],
      },

      {
        sourceName: 'liandate',
        returnName: 'freezeStartDate',
        desc: '冻结开始时间',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'actionremark',
        returnName: 'freezeEndDate',
        desc: '冻结结束时间',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'amount',
        returnName: 'amount',
        desc: '金额',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'amountdesc',
        returnName: 'amountdesc',
        desc: '股权数额（描述，非数值）',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'court',
        returnName: 'court',
        desc: '执行法院',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'caseno',
        returnName: 'executNoticeNo',
        desc: '执行通知书文号',
        isShow: true,
        isHighlight: true,
        highlightTo: ['caseno'],
      },
      {
        sourceName: 'executestatus',
        returnName: 'executestatus',
        desc: '状态',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'riskid',
        returnName: 'riskid',
        desc: '风险id',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'pledgor',
        returnName: 'pledgor',
        desc: '股权冻结企业',
        isShow: true,
        isHighlight: true,
        highlightTo: ['pledgor', 'p_pledgor'],
      },

      {
        sourceName: 'datastatus',
        returnName: 'datastatus',
        desc: 'datastatus',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'hasimage',
        returnName: 'hasimage',
        desc: 'hasimage',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'stockinfo',
        returnName: 'stockinfo',
        desc: '股票',
        isHighlight: true,
        highlightTo: ['stockinfo'],
      },
      {
        sourceName: 'product',
        returnName: 'product',
        desc: '产品',
        isHighlight: true,
        highlightTo: ['product'],
      },
      {
        sourceName: 'orgno',
        returnName: 'orgno',
        desc: 'orgno',
        isHighlight: true,
        highlightTo: ['product'],
      },
      {
        sourceName: 'punishgovcode',
        returnName: 'punishgovcode',
        desc: '处罚机构code',
        isHighlight: false,
        highlightTo: [],
      },
    ],
  },
  {
    type: '8',
    desc: '经营异常',
    dimensionType: [DimensionLevel3Enums.BusinessAbnormal3, DimensionLevel2Enums.OperationAbnormal],
    fieldList: [
      {
        sourceName: 'id',
        returnName: 'id',
        desc: '主键',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'name',
        returnName: 'listedEnterprises',
        desc: '被列入企业',
        isShow: true,
        isHighlight: true,
        highlightTo: ['name', 'name.text'],
      },
      {
        sourceName: 'pername',
        returnName: 'pername',
        desc: 'pername',
        isShow: false,
        isHighlight: true,
        highlightTo: ['pername'],
      },
      {
        sourceName: 'originalname',
        returnName: 'originalname',
        desc: '曾用名',
        isShow: false,
        isHighlight: true,
        highlightTo: ['originalname', 'originalname.text'],
      },
      {
        sourceName: 'nameandkeyno',
        returnName: 'nameandkeyno',
        desc: '主体KeyNo',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'type',
        returnName: 'type',
        desc: '信用类型',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'removereason',
        returnName: 'removereason',
        desc: '移除原因',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'casereason',
        returnName: 'casereason',
        desc: '列入原因',
        isShow: true,
        isHighlight: true,
        highlightTo: ['casereason'],
      },
      {
        sourceName: 'publishdate',
        returnName: 'inclusionDate',
        desc: '列入日期',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'liandate',
        returnName: 'removeDate',
        desc: '移除日期',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'court',
        returnName: 'court',
        desc: '列入决定机关',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'actionremark',
        returnName: 'actionremark',
        desc: '移除机关',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'riskid',
        returnName: 'riskid',
        desc: '风险id',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'datastatus',
        returnName: 'datastatus',
        desc: 'datastatus',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'hasimage',
        returnName: 'hasimage',
        desc: 'hasimage',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'stockinfo',
        returnName: 'stockinfo',
        desc: '股票',
        isHighlight: true,
        highlightTo: ['stockinfo'],
      },
      {
        sourceName: 'product',
        returnName: 'product',
        desc: '产品',
        isHighlight: true,
        highlightTo: ['product'],
      },
      {
        sourceName: 'punishgovcode',
        returnName: 'punishgovcode',
        desc: '处罚机构code',
        isHighlight: false,
        highlightTo: [],
      },
    ],
  },
  // {
  //   type: '31',
  //   desc: '行政处罚',
  //   dimensionType: [DimensionLevel2Enums.AdministrativePenalties],
  //   fieldList: [
  //     {
  //       sourceName: 'id',
  //       returnName: 'id',
  //       desc: '主键',
  //       isHighlight: false,
  //       highlightTo: [],
  //     },
  //     {
  //       sourceName: 'publishtype',
  //       returnName: 'publishtype',
  //       desc: '处罚类型',
  //       mapping: {
  //         '0901': { esCode: 'A001', desc: '警告' },
  //         '0902': { esCode: 'A002', desc: '通报批评' },
  //         '0903': { esCode: 'A003', desc: '罚款' },
  //         '0904': { esCode: 'A004', desc: '没收违法所得' },
  //         '0905': { esCode: 'A005', desc: '没收非法财物' },
  //         '0906': { esCode: 'A006', desc: '暂扣许可证件' },
  //         '0907': { esCode: 'A007', desc: '降低资质等级' },
  //         '0908': { esCode: 'A008', desc: '吊销许可证/执照' },
  //         '0909': { esCode: 'A009', desc: '限制开展生产经营活动' },
  //         '0910': { esCode: 'A010', desc: '责令停产停业' },
  //         '0911': { esCode: 'A011', desc: '责令关闭' },
  //         '0912': { esCode: 'A012', desc: '限制申请行政许可' },
  //         '0913': { esCode: 'A013', desc: '限制从业' },
  //         '0914': { esCode: 'A014', desc: '行政拘留' },
  //         '0915': { esCode: 'A015', desc: '移送司法机关' },
  //         '0916': { esCode: 'A016', desc: '不予处罚' },
  //         '0999': { esCode: 'A099', desc: '其他行政处罚' },
  //       },
  //       isHighlight: false,
  //       highlightTo: [],
  //     },
  //     {
  //       sourceName: 'title',
  //       returnName: 'punishResult',
  //       desc: '处罚结果',
  //       isShow: true,
  //       isHighlight: true,
  //       highlightTo: ['title'],
  //     },
  //     {
  //       sourceName: 'name',
  //       returnName: 'penaltyObject',
  //       desc: '处罚对象',
  //       isShow: true,
  //       isHighlight: true,
  //       highlightTo: ['name', 'name.text'],
  //     },
  //     {
  //       sourceName: 'pername',
  //       returnName: 'pername',
  //       desc: 'pername',
  //       isShow: false,
  //       isHighlight: true,
  //       highlightTo: ['pername'],
  //     },
  //     {
  //       sourceName: 'originalname',
  //       returnName: 'originalname',
  //       desc: '曾用名',
  //       isShow: false,
  //       isHighlight: true,
  //       highlightTo: ['originalname', 'originalname.text'],
  //     },
  //     {
  //       sourceName: 'nameandkeyno',
  //       returnName: 'nameandkeyno',
  //       desc: '主体KeyNo',
  //       isHighlight: false,
  //       highlightTo: [],
  //     },
  //     {
  //       sourceName: 'type',
  //       returnName: 'type',
  //       desc: '信用类型',
  //       isHighlight: false,
  //       highlightTo: [],
  //     },
  //     {
  //       sourceName: 'casereason',
  //       returnName: 'punishReason',
  //       desc: '处罚事由',
  //       isShow: true,
  //       isHighlight: false,
  //       highlightTo: [],
  //     },
  //     {
  //       sourceName: 'publishdate',
  //       returnName: 'publishdate',
  //       desc: '发布日期',
  //       isShow: true,
  //       isHighlight: false,
  //       highlightTo: [],
  //     },
  //     {
  //       sourceName: 'punishdate',
  //       returnName: 'punishdate',
  //       desc: '处罚日期',
  //       isShow: true,
  //       isHighlight: false,
  //       highlightTo: [],
  //     },
  //     {
  //       sourceName: 'court',
  //       returnName: 'publishUnit',
  //       desc: '处罚单位',
  //       isShow: true,
  //       isHighlight: false,
  //       highlightTo: [],
  //     },
  //     {
  //       sourceName: 'caseno',
  //       returnName: 'caseno',
  //       desc: '决定文书号',
  //       isShow: true,
  //       isHighlight: true,
  //       highlightTo: ['caseno'],
  //     },
  //     {
  //       sourceName: 'riskid',
  //       returnName: 'riskid',
  //       desc: '风险id',
  //       isHighlight: false,
  //       highlightTo: [],
  //     },
  //     {
  //       sourceName: 'datastatus',
  //       returnName: 'datastatus',
  //       desc: 'datastatus',
  //       isHighlight: false,
  //       highlightTo: [],
  //     },
  //     {
  //       sourceName: 'hasimage',
  //       returnName: 'hasimage',
  //       desc: 'hasimage',
  //       isHighlight: false,
  //       highlightTo: [],
  //     },
  //     {
  //       sourceName: 'stockinfo',
  //       returnName: 'stockinfo',
  //       desc: '股票',
  //       isHighlight: true,
  //       highlightTo: ['stockinfo'],
  //     },
  //     {
  //       sourceName: 'product',
  //       returnName: 'product',
  //       desc: '产品',
  //       isHighlight: true,
  //       highlightTo: ['product'],
  //     },
  //     {
  //       sourceName: 'punishgovcode',
  //       returnName: 'punishgovcode',
  //       desc: '处罚机构code',
  //       isHighlight: false,
  //       highlightTo: [],
  //     },
  //     {
  //       sourceName: 'osskey',
  //       returnName: 'fileUrl',
  //       desc: '附件',
  //       isHighlight: false,
  //       highlightTo: [],
  //     },
  //     {
  //       sourceName: 'isshow',
  //       returnName: 'isshow',
  //       desc: 'isshow',
  //       isHighlight: false,
  //       highlightTo: [],
  //     },
  //     {
  //       sourceName: 'groupcount',
  //       returnName: 'groupcount',
  //       desc: 'groupcount',
  //       isHighlight: false,
  //       highlightTo: [],
  //     },
  //     {
  //       sourceName: 'groupid',
  //       returnName: 'groupid',
  //       desc: 'groupid',
  //       isHighlight: false,
  //       highlightTo: [],
  //     },
  //   ],
  // },
  {
    type: '31',
    desc: '涉及商业贿赂、垄断行为或政府采购活动违法行为行政处罚',
    dimensionType: [DimensionLevel2Enums.AdministrativePenalties2],
    fieldList: [
      {
        sourceName: 'id',
        returnName: 'id',
        desc: '主键',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'publishtype',
        returnName: 'publishtype',
        desc: '处罚类型',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'title',
        returnName: 'punishResult',
        desc: '处罚结果',
        isShow: true,
        isHighlight: true,
        highlightTo: ['title'],
      },
      {
        sourceName: 'name',
        returnName: 'penaltyObject',
        desc: '处罚对象',
        isShow: true,
        isHighlight: true,
        highlightTo: ['name', 'name.text'],
      },
      {
        sourceName: 'pername',
        returnName: 'pername',
        desc: 'pername',
        isShow: false,
        isHighlight: true,
        highlightTo: ['pername'],
      },
      {
        sourceName: 'originalname',
        returnName: 'originalname',
        desc: '曾用名',
        isShow: false,
        isHighlight: true,
        highlightTo: ['originalname', 'originalname.text'],
      },
      {
        sourceName: 'nameandkeyno',
        returnName: 'nameandkeyno',
        desc: '主体KeyNo',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'type',
        returnName: 'type',
        desc: '信用类型',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'casereason',
        returnName: 'punishReason',
        desc: '处罚事由',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'publishdate',
        returnName: 'publishdate',
        desc: '发布日期',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'punishdate',
        returnName: 'punishdate',
        desc: '处罚日期',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'court',
        returnName: 'publishUnit',
        desc: '处罚单位',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'caseno',
        returnName: 'caseno',
        desc: '决定文书号',
        isShow: true,
        isHighlight: true,
        highlightTo: ['caseno'],
      },
      {
        sourceName: 'riskid',
        returnName: 'riskid',
        desc: '风险id',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'datastatus',
        returnName: 'datastatus',
        desc: 'datastatus',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'hasimage',
        returnName: 'hasimage',
        desc: 'hasimage',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'stockinfo',
        returnName: 'stockinfo',
        desc: '股票',
        isHighlight: true,
        highlightTo: ['stockinfo'],
      },
      {
        sourceName: 'product',
        returnName: 'product',
        desc: '产品',
        isHighlight: true,
        highlightTo: ['product'],
      },
      {
        sourceName: 'punishgovcode',
        returnName: 'punishgovcode',
        desc: '处罚机构code',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'osskey',
        returnName: 'fileUrl',
        desc: '附件',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'isshow',
        returnName: 'isshow',
        desc: 'isshow',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'groupcount',
        returnName: 'groupcount',
        desc: 'groupcount',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'groupid',
        returnName: 'groupid',
        desc: 'groupid',
        isHighlight: false,
        highlightTo: [],
      },
    ],
  },
  // {
  //   type: '31',
  //   desc: '涉围串标等投标处罚记录',
  //   dimensionType: [DimensionLevel2Enums.BidAdministrativePenalties],
  //   fieldList: [],
  // },
  {
    type: '31',
    desc: '3年前涉及商业贿赂、垄断行为或政府采购活动违法行为行政处罚',
    dimensionType: [DimensionLevel2Enums.AdministrativePenalties3],
    fieldList: [
      {
        sourceName: 'id',
        returnName: 'id',
        desc: '主键',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'publishtype',
        returnName: 'publishtype',
        desc: '处罚类型',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'title',
        returnName: 'punishResult',
        desc: '处罚结果',
        isShow: true,
        isHighlight: true,
        highlightTo: ['title'],
      },
      {
        sourceName: 'name',
        returnName: 'penaltyObject',
        desc: '处罚对象',
        isShow: true,
        isHighlight: true,
        highlightTo: ['name', 'name.text'],
      },
      {
        sourceName: 'pername',
        returnName: 'pername',
        desc: 'pername',
        isShow: false,
        isHighlight: true,
        highlightTo: ['pername'],
      },
      {
        sourceName: 'originalname',
        returnName: 'originalname',
        desc: '曾用名',
        isShow: false,
        isHighlight: true,
        highlightTo: ['originalname', 'originalname.text'],
      },
      {
        sourceName: 'nameandkeyno',
        returnName: 'nameandkeyno',
        desc: '主体KeyNo',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'type',
        returnName: 'type',
        desc: '信用类型',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'casereason',
        returnName: 'punishReason',
        desc: '处罚事由',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'publishdate',
        returnName: 'publishdate',
        desc: '发布日期',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'punishdate',
        returnName: 'punishdate',
        desc: '处罚日期',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'court',
        returnName: 'publishUnit',
        desc: '处罚单位',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'caseno',
        returnName: 'caseno',
        desc: '决定文书号',
        isShow: true,
        isHighlight: true,
        highlightTo: ['caseno'],
      },
      {
        sourceName: 'riskid',
        returnName: 'riskid',
        desc: '风险id',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'datastatus',
        returnName: 'datastatus',
        desc: 'datastatus',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'hasimage',
        returnName: 'hasimage',
        desc: 'hasimage',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'stockinfo',
        returnName: 'stockinfo',
        desc: '股票',
        isHighlight: true,
        highlightTo: ['stockinfo'],
      },
      {
        sourceName: 'product',
        returnName: 'product',
        desc: '产品',
        isHighlight: true,
        highlightTo: ['product'],
      },
      {
        sourceName: 'punishgovcode',
        returnName: 'punishgovcode',
        desc: '处罚机构code',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'osskey',
        returnName: 'fileUrl',
        desc: '附件',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'isshow',
        returnName: 'isshow',
        desc: 'isshow',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'groupcount',
        returnName: 'groupcount',
        desc: 'groupcount',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'groupid',
        returnName: 'groupid',
        desc: 'groupid',
        isHighlight: false,
        highlightTo: [],
      },
    ],
  },
  {
    type: '31',
    desc: '疑似停业歇业停产或被吊销证照',
    dimensionType: [DimensionLevel3Enums.BusinessAbnormal5],
    fieldList: [
      {
        sourceName: 'id',
        returnName: 'id',
        desc: '主键',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'publishtype',
        returnName: 'publishtype',
        desc: '处罚类型',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'title',
        returnName: 'punishResult',
        desc: '处罚结果',
        isShow: true,
        isHighlight: true,
        highlightTo: ['title'],
      },
      {
        sourceName: 'name',
        returnName: 'penaltyObject',
        desc: '处罚对象',
        isShow: true,
        isHighlight: true,
        highlightTo: ['name', 'name.text'],
      },
      {
        sourceName: 'pername',
        returnName: 'pername',
        desc: 'pername',
        isShow: false,
        isHighlight: true,
        highlightTo: ['pername'],
      },
      {
        sourceName: 'originalname',
        returnName: 'originalname',
        desc: '曾用名',
        isShow: false,
        isHighlight: true,
        highlightTo: ['originalname', 'originalname.text'],
      },
      {
        sourceName: 'nameandkeyno',
        returnName: 'nameandkeyno',
        desc: '主体KeyNo',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'type',
        returnName: 'type',
        desc: '信用类型',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'casereason',
        returnName: 'punishReason',
        desc: '处罚事由',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'publishdate',
        returnName: 'publishdate',
        desc: '发布日期',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'punishdate',
        returnName: 'punishdate',
        desc: '处罚日期',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'court',
        returnName: 'publishUnit',
        desc: '处罚单位',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'caseno',
        returnName: 'caseno',
        desc: '决定文书号',
        isShow: true,
        isHighlight: true,
        highlightTo: ['caseno'],
      },
      {
        sourceName: 'riskid',
        returnName: 'riskid',
        desc: '风险id',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'datastatus',
        returnName: 'datastatus',
        desc: 'datastatus',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'hasimage',
        returnName: 'hasimage',
        desc: 'hasimage',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'stockinfo',
        returnName: 'stockinfo',
        desc: '股票',
        isHighlight: true,
        highlightTo: ['stockinfo'],
      },
      {
        sourceName: 'product',
        returnName: 'product',
        desc: '产品',
        isHighlight: true,
        highlightTo: ['product'],
      },
      {
        sourceName: 'punishgovcode',
        returnName: 'punishgovcode',
        desc: '处罚机构code',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'osskey',
        returnName: 'fileUrl',
        desc: '附件',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'isshow',
        returnName: 'isshow',
        desc: 'isshow',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'groupcount',
        returnName: 'groupcount',
        desc: 'groupcount',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'groupid',
        returnName: 'groupid',
        desc: 'groupid',
        isHighlight: false,
        highlightTo: [],
      },
    ],
  },
  {
    type: '121', //121 有 1018988 条记录， 12 有 396620 条记录
    desc: '环保处罚',
    dimensionType: [DimensionLevel2Enums.EnvironmentalPenalties],
    fieldList: [
      {
        sourceName: 'id',
        returnName: 'id',
        desc: '主键',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'title',
        returnName: 'punishResult',
        desc: '处罚结果',
        isShow: true,
        isHighlight: true,
        highlightTo: ['title'],
      },
      {
        sourceName: 'name',
        returnName: 'penaltyObject',
        desc: '处罚对象',
        isShow: true,
        isHighlight: true,
        highlightTo: ['name', 'name.text'],
      },
      {
        sourceName: 'pername',
        returnName: 'pername',
        desc: 'pername',
        isShow: false,
        isHighlight: true,
        highlightTo: ['pername'],
      },
      {
        sourceName: 'originalname',
        returnName: 'originalname',
        desc: '曾用名',
        isShow: false,
        isHighlight: true,
        highlightTo: ['originalname', 'originalname.text'],
      },
      {
        sourceName: 'nameandkeyno',
        returnName: 'nameandkeyno',
        desc: '主体KeyNo',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'type',
        returnName: 'type',
        desc: '信用类型',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'casereason',
        returnName: 'punishReason',
        desc: '处罚事由',
        isShow: true,
        isHighlight: true,
        highlightTo: ['casereason'],
      },
      {
        sourceName: 'publishdate',
        returnName: 'punishDate',
        desc: '处罚日期',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'court',
        returnName: 'publishUnit',
        desc: '处罚单位',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'caseno',
        returnName: 'caseno',
        desc: '决定文书号',
        isShow: true,
        isHighlight: true,
        highlightTo: ['caseno'],
      },
      {
        sourceName: 'riskid',
        returnName: 'riskid',
        desc: '风险id',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'executestatus',
        returnName: 'implementation',
        desc: '执行情况',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'datastatus',
        returnName: 'datastatus',
        desc: 'datastatus',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'hasimage',
        returnName: 'hasimage',
        desc: 'hasimage',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'stockinfo',
        returnName: 'stockinfo',
        desc: '股票',
        isHighlight: true,
        highlightTo: ['stockinfo'],
      },
      {
        sourceName: 'product',
        returnName: 'product',
        desc: '产品',
        isHighlight: true,
        highlightTo: ['product'],
      },
      {
        sourceName: 'punishgovcode',
        returnName: 'punishgovcode',
        desc: '处罚机构code',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'osskey',
        returnName: 'fileUrl',
        desc: '附件',
        isHighlight: false,
        highlightTo: [],
      },
    ],
  },
  {
    type: '13',
    desc: '欠税公告',
    dimensionType: [DimensionLevel2Enums.TaxArrearsNotice],
    fieldList: [
      {
        sourceName: 'id',
        returnName: 'id',
        desc: '主键',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'name',
        returnName: 'owingTaxes',
        desc: '欠税企业',
        isShow: true,
        isHighlight: true,
        highlightTo: ['name', 'name.text'],
      },
      {
        sourceName: 'pername',
        returnName: 'pername',
        desc: 'pername',
        isShow: false,
        isHighlight: true,
        highlightTo: ['pername'],
      },
      {
        sourceName: 'originalname',
        returnName: 'originalname',
        desc: '曾用名',
        isShow: false,
        isHighlight: true,
        highlightTo: ['originalname', 'originalname.text'],
      },
      {
        sourceName: 'nameandkeyno',
        returnName: 'nameandkeyno',
        desc: '主体KeyNo',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'type',
        returnName: 'type',
        desc: '信用类型',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'casereasontype',
        returnName: 'TaxArrears',
        desc: '欠税税种',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'publishdate',
        returnName: 'releaseDate',
        desc: '发布日期',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'amount',
        returnName: 'owingTaxesBalance',
        desc: '欠税余额',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'amount2',
        returnName: 'addOwingTaxesBalance',
        desc: '新增欠税金额',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'court',
        returnName: 'issuedUnit',
        desc: '发布单位',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'caseno',
        returnName: 'caseno',
        desc: '案号',
        isHighlight: true,
        highlightTo: ['caseno'],
      },
      {
        sourceName: 'riskid',
        returnName: 'riskid',
        desc: '风险id',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'datastatus',
        returnName: 'datastatus',
        desc: 'datastatus',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'hasimage',
        returnName: 'hasimage',
        desc: 'hasimage',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'stockinfo',
        returnName: 'stockinfo',
        desc: '股票',
        isHighlight: true,
        highlightTo: ['stockinfo'],
      },
      {
        sourceName: 'product',
        returnName: 'product',
        desc: '产品',
        isHighlight: true,
        highlightTo: ['product'],
      },
      {
        sourceName: 'punishgovcode',
        returnName: 'punishgovcode',
        desc: '处罚机构code',
        isHighlight: false,
        highlightTo: [],
      },
    ],
  },
  {
    type: '14',
    desc: '严重违法',
    dimensionType: [DimensionLevel2Enums.CompanyCredit, DimensionLevel2Enums.CompanyCreditHistory], //严重违法 ===  被列入严重违法失信企业名录？
    fieldList: [
      {
        sourceName: 'id',
        returnName: 'id',
        desc: '主键',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'name',
        returnName: 'listedEnterprises',
        desc: '被列入企业',
        isShow: true,
        isHighlight: true,
        highlightTo: ['name', 'name.text'],
      },
      {
        sourceName: 'pername',
        returnName: 'pername',
        desc: 'pername',
        isShow: false,
        isHighlight: true,
        highlightTo: ['pername'],
      },
      {
        sourceName: 'originalname',
        returnName: 'originalname',
        desc: '曾用名',
        isShow: false,
        isHighlight: true,
        highlightTo: ['originalname', 'originalname.text'],
      },
      {
        sourceName: 'nameandkeyno',
        returnName: 'nameandkeyno',
        desc: '主体KeyNo',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'type',
        returnName: 'type',
        desc: '信用类型',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'removereason',
        returnName: 'removereason',
        desc: '移出原因',
        isHighlight: true,
        highlightTo: ['removereason'],
      },
      {
        sourceName: 'casereason',
        returnName: 'inclusionReason',
        desc: '列入原因',
        isShow: true,
        isHighlight: true,
        highlightTo: ['casereason'],
      },
      {
        sourceName: 'publishdate',
        returnName: 'inclusionDate',
        desc: '列入日期',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'liandate',
        returnName: 'removeDate',
        desc: '移除日期',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'court',
        returnName: 'includedOrgan',
        desc: '列入决定机关',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'actionremark',
        returnName: 'actionremark',
        desc: '移除机关',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'caseno',
        returnName: 'caseno',
        desc: '案号',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'riskid',
        returnName: 'riskid',
        desc: '风险id',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'datastatus',
        returnName: 'datastatus',
        desc: 'datastatus',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'hasimage',
        returnName: 'hasimage',
        desc: 'hasimage',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'stockinfo',
        returnName: 'stockinfo',
        desc: '股票',
        isHighlight: true,
        highlightTo: ['stockinfo'],
      },
      {
        sourceName: 'product',
        returnName: 'product',
        desc: '产品',
        isHighlight: true,
        highlightTo: ['product'],
      },
      {
        sourceName: 'punishgovcode',
        returnName: 'punishgovcode',
        desc: '处罚机构code',
        isHighlight: false,
        highlightTo: [],
      },
    ],
  },
  {
    type: '151',
    desc: '黑名单',
    dimensionType: [
      DimensionLevel2Enums.HitOuterBlackList,
      DimensionLevel3Enums.MigrantWorkers,
      DimensionLevel3Enums.GovernmentPurchaseIllegal,
      DimensionLevel3Enums.GovProcurementIllegal,
      DimensionLevel3Enums.ArmyProcurementIllegal,
      DimensionLevel3Enums.ArmyProcurementSuspended,
      DimensionLevel3Enums.AIIBBlackList,
    ],
    fieldList: [
      {
        sourceName: 'id',
        returnName: 'id',
        desc: '主键',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'title',
        returnName: 'blacklistTitle',
        desc: '黑名单标题',
        isShow: true,
        isHighlight: true,
        highlightTo: ['title'],
      },
      {
        sourceName: 'name',
        returnName: 'listedEnterprises',
        desc: '被列入企业',
        isShow: true,
        isHighlight: true,
        highlightTo: ['name', 'name.text'],
      },
      {
        sourceName: 'pername',
        returnName: 'pername',
        desc: 'pername',
        isShow: false,
        isHighlight: true,
        highlightTo: ['pername'],
      },
      {
        sourceName: 'originalname',
        returnName: 'originalname',
        desc: '曾用名',
        isShow: false,
        isHighlight: true,
        highlightTo: ['originalname', 'originalname.text'],
      },
      {
        sourceName: 'nameandkeyno',
        returnName: 'nameandkeyno',
        desc: '主体KeyNo',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'type',
        returnName: 'type',
        desc: '信用类型',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'casereasontype',
        returnName: 'disciplinaryType',
        desc: '惩戒类型',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'publishdate',
        returnName: 'inclusionDate',
        desc: '列入日期',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'liandate',
        returnName: 'removeDate',
        desc: '移出日期',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'court',
        returnName: 'listedOrgan',
        desc: '列入机关',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'orgno',
        returnName: 'orgno',
        desc: '新闻id',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'actionremark',
        returnName: 'actionremark',
        desc: '来源',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'riskid',
        returnName: 'riskid',
        desc: '风险id',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'datastatus',
        returnName: 'datastatus',
        desc: 'datastatus',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'hasimage',
        returnName: 'hasimage',
        desc: 'hasimage',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'stockinfo',
        returnName: 'stockinfo',
        desc: '股票',
        isHighlight: true,
        highlightTo: ['stockinfo'],
      },
      {
        sourceName: 'product',
        returnName: 'product',
        desc: '产品',
        isHighlight: true,
        highlightTo: ['product'],
      },
      {
        sourceName: 'punishgovcode',
        returnName: 'punishgovcode',
        desc: '处罚机构code',
        isHighlight: false,
        highlightTo: [],
      },
    ],
  },
  {
    type: '16',
    desc: '产品召回',
    dimensionType: [DimensionLevel3Enums.ProductQualityProblem1],
    fieldList: [
      {
        sourceName: 'id',
        returnName: 'id',
        desc: '主键',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'title',
        returnName: 'recallProducts',
        desc: '召回产品',
        isShow: true,
        isHighlight: true,
        highlightTo: ['title'],
      },
      {
        sourceName: 'name',
        returnName: 'recallEnterprise',
        desc: '召回企业',
        isShow: true,
        isHighlight: false,
        highlightTo: ['name', 'name.text'],
      },
      {
        sourceName: 'pername',
        returnName: 'pername',
        desc: 'pername',
        isShow: false,
        isHighlight: true,
        highlightTo: ['pername'],
      },
      {
        sourceName: 'originalname',
        returnName: 'originalname',
        desc: '曾用名',
        isShow: false,
        isHighlight: true,
        highlightTo: ['originalname', 'originalname.text'],
      },
      {
        sourceName: 'nameandkeyno',
        returnName: 'nameandkeyno',
        desc: '主体KeyNo',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'type',
        returnName: 'type',
        desc: '信用类型',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'publishdate',
        returnName: 'releaseDate',
        desc: '发布日期',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'riskid',
        returnName: 'riskid',
        desc: '风险id',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'datastatus',
        returnName: 'datastatus',
        desc: 'datastatus',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'hasimage',
        returnName: 'hasimage',
        desc: 'hasimage',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'stockinfo',
        returnName: 'stockinfo',
        desc: '股票',
        isHighlight: true,
        highlightTo: ['stockinfo'],
      },
      {
        sourceName: 'product',
        returnName: 'product',
        desc: '产品',
        isHighlight: true,
        highlightTo: ['product'],
      },
      {
        sourceName: 'punishgovcode',
        returnName: 'punishgovcode',
        desc: '处罚机构code',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'osskey',
        returnName: 'fileUrl',
        desc: '附件',
        isHighlight: false,
        highlightTo: [],
      },
    ],
  },
  {
    type: '17',
    desc: '食品安全',
    dimensionType: [DimensionLevel3Enums.ProductQualityProblem9],
    fieldList: [
      {
        sourceName: 'id',
        returnName: 'id',
        desc: '主键',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'title',
        returnName: 'foodName',
        desc: '食品名称',
        isShow: true,
        isHighlight: true,
        highlightTo: ['title'],
      },
      {
        sourceName: 'name',
        returnName: 'sampledEnterprises',
        desc: '被抽检企业',
        isShow: true,
        isHighlight: true,
        highlightTo: ['name', 'name.text'],
      },
      {
        sourceName: 'pername',
        returnName: 'pername',
        desc: 'pername',
        isShow: false,
        isHighlight: true,
        highlightTo: ['pername'],
      },
      {
        sourceName: 'originalname',
        returnName: 'originalname',
        desc: '曾用名',
        isShow: false,
        isHighlight: true,
        highlightTo: ['originalname', 'originalname.text'],
      },
      {
        sourceName: 'nameandkeyno',
        returnName: 'nameandkeyno',
        desc: '主体KeyNo',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'type',
        returnName: 'type',
        desc: '信用类型',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'publishdate',
        returnName: 'updateDate',
        desc: '更新日期',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'liandate',
        returnName: 'productionDate',
        desc: '生产日期',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'amount2',
        returnName: 'samplingTimes',
        desc: '抽检次数',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'amountdesc',
        returnName: 'noticeStatus',
        desc: '是否有详情,1-存在详情，0-不存在详情',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'orgno',
        returnName: 'samplingNo',
        desc: '抽检编号',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'actionremark',
        returnName: 'trademark',
        desc: '商标',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'executestatus',
        returnName: 'samplingResults',
        desc: '抽检结果',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'riskid',
        returnName: 'riskid',
        desc: '风险id',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'address',
        returnName: 'businessAddress',
        desc: '标称生产企业地址',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'applicant',
        returnName: 'nominalManufacturer',
        desc: '标称生产企业',
        isShow: true,
        isHighlight: true,
        highlightTo: ['applicant', 'p_applicant'],
      },
      {
        sourceName: 'nameandkeyno',
        returnName: 'nameandkeyno',
        desc: 'applicantKeyNo',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'datastatus',
        returnName: 'datastatus',
        desc: 'datastatus',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'hasimage',
        returnName: 'hasimage',
        desc: 'hasimage',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'stockinfo',
        returnName: 'stockinfo',
        desc: '股票',
        isHighlight: true,
        highlightTo: ['stockinfo'],
      },
      {
        sourceName: 'product',
        returnName: 'product',
        desc: '产品',
        isHighlight: true,
        highlightTo: ['product'],
      },
      {
        sourceName: 'punishgovcode',
        returnName: 'punishgovcode',
        desc: '处罚机构code',
        isHighlight: false,
        highlightTo: [],
      },
    ],
  },
  {
    type: '33',
    desc: '药品抽检',
    dimensionType: [DimensionLevel3Enums.ProductQualityProblem7],
    fieldList: [],
  },
  {
    type: '19',
    desc: '税收违法',
    dimensionType: [DimensionLevel2Enums.TaxationOffences],
    fieldList: [
      {
        sourceName: 'id',
        returnName: 'id',
        desc: '主键',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'title',
        returnName: 'punishment',
        desc: '处罚情况',
        isShow: true,
        isHighlight: true,
        highlightTo: ['title'],
      },
      {
        sourceName: 'name',
        returnName: 'taxpayer',
        desc: '纳税人',
        isShow: true,
        isHighlight: true,
        highlightTo: ['name', 'name.text'],
      },
      {
        sourceName: 'pername',
        returnName: 'pername',
        desc: 'pername',
        isShow: false,
        isHighlight: true,
        highlightTo: ['pername'],
      },
      {
        sourceName: 'originalname',
        returnName: 'originalname',
        desc: '曾用名',
        isShow: false,
        isHighlight: true,
        highlightTo: ['originalname', 'originalname.text'],
      },
      {
        sourceName: 'nameandkeyno',
        returnName: 'nameandkeyno',
        desc: '主体KeyNo',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'type',
        returnName: 'type',
        desc: '信用类型',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'casereason',
        returnName: 'illegalFacts',
        desc: '主要违法事实',
        isShow: true,
        isHighlight: true,
        highlightTo: ['casereason'],
      },
      {
        sourceName: 'casereasontype',
        returnName: 'casereasontype',
        desc: '案件性质',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'publishdate',
        returnName: 'releaseDate',
        desc: '发布日期',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'court',
        returnName: 'taxAuthority',
        desc: '所属税务机关',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'actionremark',
        returnName: 'actionremark',
        desc: '案件性质',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'riskid',
        returnName: 'riskid',
        desc: '风险id',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'datastatus',
        returnName: 'datastatus',
        desc: 'datastatus',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'hasimage',
        returnName: 'hasimage',
        desc: 'hasimage',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'stockinfo',
        returnName: 'stockinfo',
        desc: '股票',
        isHighlight: true,
        highlightTo: ['stockinfo'],
      },
      {
        sourceName: 'product',
        returnName: 'product',
        desc: '产品',
        isHighlight: true,
        highlightTo: ['product'],
      },
      {
        sourceName: 'punishgovcode',
        returnName: 'punishgovcode',
        desc: '处罚机构code',
        isHighlight: false,
        highlightTo: [],
      },
    ],
  },
  {
    type: '21',
    desc: '双随机抽查',
    dimensionType: null,
    fieldList: [
      {
        sourceName: 'id',
        returnName: 'id',
        desc: '主键',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'title',
        returnName: 'taskName',
        desc: '任务名称',
        isShow: true,
        isHighlight: true,
        highlightTo: ['title'],
      },
      {
        sourceName: 'name',
        returnName: 'enterpriseName',
        desc: '企业名称',
        isShow: true,
        isHighlight: true,
        highlightTo: ['name', 'name.text'],
      },
      {
        sourceName: 'pername',
        returnName: 'pername',
        desc: 'pername',
        isShow: false,
        isHighlight: true,
        highlightTo: ['pername'],
      },
      {
        sourceName: 'originalname',
        returnName: 'originalname',
        desc: '曾用名',
        isShow: false,
        isHighlight: true,
        highlightTo: ['originalname', 'originalname.text'],
      },
      {
        sourceName: 'nameandkeyno',
        returnName: 'nameandkeyno',
        desc: '主体KeyNo',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'type',
        returnName: 'type',
        desc: '信用类型',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'casereasontype',
        returnName: 'productType',
        desc: '产品类型',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'publishdate',
        returnName: 'completionDate',
        desc: '完成日期',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'court',
        returnName: 'court',
        desc: '抽查机关',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'orgno',
        returnName: 'taskNo',
        desc: '任务编号',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'riskid',
        returnName: 'riskid',
        desc: '风险id',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'datastatus',
        returnName: 'datastatus',
        desc: 'datastatus',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'hasimage',
        returnName: 'hasimage',
        desc: 'hasimage',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'stockinfo',
        returnName: 'stockinfo',
        desc: '股票',
        isHighlight: true,
        highlightTo: ['stockinfo'],
      },
      {
        sourceName: 'product',
        returnName: 'product',
        desc: '产品',
        isHighlight: true,
        highlightTo: ['product'],
      },
      {
        sourceName: 'punishgovcode',
        returnName: 'punishgovcode',
        desc: '处罚机构code',
        isHighlight: false,
        highlightTo: [],
      },
    ],
  },
  {
    type: '22',
    dimensionType: [DimensionLevel3Enums.ProductQualityProblem6], //TODO
    desc: '未准入境',
    fieldList: [
      {
        sourceName: 'id',
        returnName: 'id',
        desc: '主键',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'title',
        returnName: 'productName',
        desc: '产品名称',
        isShow: true,
        isHighlight: true,
        highlightTo: ['title'],
      },
      {
        sourceName: 'name',
        returnName: 'enterpriseName',
        desc: '公司名称',
        isShow: true,
        isHighlight: true,
        highlightTo: ['name', 'name.text'],
      },
      {
        sourceName: 'pername',
        returnName: 'pername',
        desc: 'pername',
        isShow: false,
        isHighlight: true,
        highlightTo: ['pername'],
      },
      {
        sourceName: 'originalname',
        returnName: 'originalname',
        desc: '曾用名',
        isShow: false,
        isHighlight: true,
        highlightTo: ['originalname', 'originalname.text'],
      },
      {
        sourceName: 'nameandkeyno',
        returnName: 'nameandkeyno',
        desc: '主体KeyNo',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'type',
        returnName: 'type',
        desc: '信用类型',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'casereasontype',
        returnName: 'casereasontype',
        desc: '产品类型',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'publishdate',
        returnName: 'releaseDate',
        desc: '发布日期',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'liandate',
        returnName: 'submissionTime',
        desc: '报送时间',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'amountdesc',
        returnName: 'amountdesc',
        desc: '数/重量',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'actionremark',
        returnName: 'actionreReason',
        desc: '原因',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'riskid',
        returnName: 'riskid',
        desc: '风险id',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'applicant',
        returnName: 'brand',
        desc: '生产企业信息/品牌',
        isHighlight: true,
        highlightTo: ['applicant', 'p_applicant'],
      },
      {
        sourceName: 'nameandkeyno',
        returnName: 'nameandkeyno',
        desc: 'applicantKeyNo',
        isHighlight: false,
        highlightTo: [],
      },

      {
        sourceName: 'datastatus',
        returnName: 'datastatus',
        desc: 'datastatus',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'hasimage',
        returnName: 'hasimage',
        desc: 'hasimage',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'stockinfo',
        returnName: 'stockinfo',
        desc: '股票',
        isHighlight: true,
        highlightTo: ['stockinfo'],
      },
      {
        sourceName: 'product',
        returnName: 'product',
        desc: '产品',
        isHighlight: true,
        highlightTo: ['product'],
      },
      {
        sourceName: 'punishgovcode',
        returnName: 'punishgovcode',
        desc: '处罚机构code',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'osskey',
        returnName: 'fileUrl',
        desc: '附件',
        isHighlight: false,
        highlightTo: [],
      },
    ],
  },
  {
    type: '231',
    desc: '简易注销',
    dimensionType: [],
    fieldList: [
      {
        sourceName: 'id',
        returnName: 'id',
        desc: '主键',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'name',
        returnName: 'announcementName',
        desc: '公告名称',
        isShow: true,
        isHighlight: true,
        highlightTo: ['name', 'name.text'],
      },
      {
        sourceName: 'pername',
        returnName: 'pername',
        desc: 'pername',
        isShow: false,
        isHighlight: true,
        highlightTo: ['pername'],
      },
      {
        sourceName: 'originalname',
        returnName: 'originalname',
        desc: '曾用名',
        isShow: false,
        isHighlight: true,
        highlightTo: ['originalname', 'originalname.text'],
      },
      {
        sourceName: 'nameandkeyno',
        returnName: 'nameandkeyno',
        desc: '主体KeyNo',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'type',
        returnName: 'type',
        desc: '信用类型',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'publishdate',
        returnName: 'startTime',
        desc: '公示开始时间',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'liandate',
        returnName: 'endTime',
        desc: '公示结束时间',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'executestatus',
        returnName: 'logoutResults',
        desc: '注销结果',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'riskid',
        returnName: 'riskid',
        desc: '风险id',
        isHighlight: false,
        highlightTo: [],
      },

      {
        sourceName: 'datastatus',
        returnName: 'datastatus',
        desc: 'datastatus',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'hasimage',
        returnName: 'hasimage',
        desc: 'hasimage',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'stockinfo',
        returnName: 'stockinfo',
        desc: '股票',
        isHighlight: true,
        highlightTo: ['stockinfo'],
      },
      {
        sourceName: 'product',
        returnName: 'product',
        desc: '产品',
        isHighlight: true,
        highlightTo: ['product'],
      },
      {
        sourceName: 'punishgovcode',
        returnName: 'punishgovcode',
        desc: '处罚机构code',
        isHighlight: false,
        highlightTo: [],
      },
    ],
  },
  {
    type: '24',
    desc: '注销备案',
    dimensionType: [DimensionLevel3Enums.CancellationOfFiling],
    fieldList: [
      {
        sourceName: 'id',
        returnName: 'id',
        desc: '主键',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'name',
        returnName: 'cancelRecordCorp',
        desc: '注销备案企业',
        isShow: true,
        isHighlight: true,
        highlightTo: ['name', 'name.text'],
      },
      {
        sourceName: 'pername',
        returnName: 'pername',
        desc: 'pername',
        isShow: false,
        isHighlight: true,
        highlightTo: ['pername'],
      },
      {
        sourceName: 'originalname',
        returnName: 'originalname',
        desc: '曾用名',
        isShow: false,
        isHighlight: true,
        highlightTo: ['originalname', 'originalname.text'],
      },
      {
        sourceName: 'nameandkeyno',
        returnName: 'nameandkeyno',
        desc: '主体KeyNo',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'type',
        returnName: 'type',
        desc: '信用类型',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'removereason',
        returnName: 'cancelReason',
        desc: '注销原因',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'liandate',
        returnName: 'liquidationDate',
        desc: '清算组成立日期',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'beiandate',
        returnName: 'recordDate',
        desc: '清算组备案日期',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'publishdate',
        returnName: 'publicStartDate',
        desc: '公告日期',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'actionremark',
        returnName: 'publicEndDate',
        desc: '公告结束日期',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'court',
        returnName: 'court',
        desc: '登记机关',
        isHighlight: false,
        highlightTo: [],
      },
      // {
      //     "sourceName":"noticeDate",
      //     "returnName":"noticeDate",
      //     "desc":"公告期",
      //     "isShow":true,
      //     "isHighlight":false,
      //     "highlightTo":[]
      // },
      {
        sourceName: 'caseno',
        returnName: 'telephone',
        desc: '债权申报联系电话',
        isHighlight: true,
        highlightTo: ['caseno'],
      },
      {
        sourceName: 'executestatus',
        returnName: 'announcementStatus',
        desc: '公告状态',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'riskid',
        returnName: 'riskid',
        desc: '风险id',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'address',
        returnName: 'address',
        desc: '债权申报地址',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'datastatus',
        returnName: 'datastatus',
        desc: 'datastatus',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'hasimage',
        returnName: 'hasimage',
        desc: 'hasimage',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'stockinfo',
        returnName: 'stockinfo',
        desc: '股票',
        isHighlight: true,
        highlightTo: ['stockinfo'],
      },
      {
        sourceName: 'product',
        returnName: 'product',
        desc: '产品',
        isHighlight: true,
        highlightTo: ['product'],
      },
      {
        sourceName: 'punishgovcode',
        returnName: 'punishgovcode',
        desc: '处罚机构code',
        isHighlight: false,
        highlightTo: [],
      },
    ],
  },
  {
    type: '25',
    desc: '软件违规',
    dimensionType: null,
    fieldList: [
      {
        sourceName: 'id',
        returnName: 'id',
        desc: '主键',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'title',
        returnName: 'appName',
        desc: '应用名称',
        isShow: true,
        isHighlight: true,
        highlightTo: ['title'],
      },
      {
        sourceName: 'name',
        returnName: 'enterpriseName',
        desc: '被列入企业',
        isShow: true,
        isHighlight: true,
        highlightTo: ['name', 'name.text'],
      },
      {
        sourceName: 'pername',
        returnName: 'pername',
        desc: 'pername',
        isShow: false,
        isHighlight: true,
        highlightTo: ['pername'],
      },
      {
        sourceName: 'casereason',
        returnName: 'actionremark',
        desc: '所涉问题',
        isShow: true,
        isHighlight: true,
        highlightTo: ['casereason'],
      },
      {
        sourceName: 'nameandkeyno',
        returnName: 'nameandkeyno',
        desc: '主体KeyNo',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'type',
        returnName: 'type',
        desc: '信用类型',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'publishdate',
        returnName: 'releaseDate',
        desc: '发布日期',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'orgno',
        returnName: 'caseno',
        desc: '版本号',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'actionremark',
        returnName: 'source',
        desc: '来源',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'riskid',
        returnName: 'riskid',
        desc: '风险id',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'datastatus',
        returnName: 'datastatus',
        desc: 'datastatus',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'hasimage',
        returnName: 'hasimage',
        desc: 'hasimage',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'stockinfo',
        returnName: 'stockinfo',
        desc: '股票',
        isHighlight: true,
        highlightTo: ['stockinfo'],
      },
      {
        sourceName: 'product',
        returnName: 'product',
        desc: '产品',
        isHighlight: true,
        highlightTo: ['product'],
      },
      {
        sourceName: 'punishgovcode',
        returnName: 'punishgovcode',
        desc: '处罚机构code',
        isHighlight: false,
        highlightTo: [],
      },
    ],
  },
  {
    type: '26',
    desc: '政府约谈',
    dimensionType: null,
    fieldList: [
      {
        sourceName: 'id',
        returnName: 'id',
        desc: '主键',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'name',
        returnName: 'respondent',
        desc: '被列入企业',
        isShow: true,
        isHighlight: true,
        highlightTo: ['name', 'name.text'],
      },
      {
        sourceName: 'pername',
        returnName: 'pername',
        desc: 'pername',
        isShow: false,
        isHighlight: true,
        highlightTo: ['pername'],
      },
      {
        sourceName: 'nameandkeyno',
        returnName: 'nameandkeyno',
        desc: '主体KeyNo',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'title',
        returnName: 'newsTitle',
        desc: '新闻标题',
        isShow: true,
        isHighlight: true,
        highlightTo: ['title'],
      },
      {
        sourceName: 'type',
        returnName: 'type',
        desc: '信用类型',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'casereason',
        returnName: 'interviewQuestion',
        desc: '约谈问题',
        isShow: true,
        isHighlight: true,
        highlightTo: ['casereason'],
      },
      {
        sourceName: 'publishdate',
        returnName: 'publishdate',
        desc: '发布日期',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'liandate',
        returnName: 'interviewDate',
        desc: '约谈日期',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'court',
        returnName: 'court',
        desc: '约谈机关',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },

      {
        sourceName: 'datastatus',
        returnName: 'datastatus',
        desc: 'datastatus',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'hasimage',
        returnName: 'hasimage',
        desc: 'hasimage',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'stockinfo',
        returnName: 'stockinfo',
        desc: '股票',
        isHighlight: true,
        highlightTo: ['stockinfo'],
      },
      {
        sourceName: 'product',
        returnName: 'product',
        desc: '产品',
        isHighlight: true,
        highlightTo: ['product'],
      },
      {
        sourceName: 'punishgovcode',
        returnName: 'punishgovcode',
        desc: '处罚机构code',
        isHighlight: false,
        highlightTo: [],
      },
    ],
  },
  {
    type: '27',
    desc: '税务评级',
    dimensionType: null,
    fieldList: [],
  },
  {
    type: '201',
    desc: '抽样抽查',
    dimensionType: [DimensionLevel2Enums.SpotCheck],
    fieldList: [
      {
        sourceName: 'court',
        returnName: 'court',
        desc: '检查实施机关',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'casereasontype',
        returnName: 'casereasontype',
        desc: '类型',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'publishdate',
        returnName: 'publicTime',
        desc: '日期',
        isShow: true,
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'title',
        returnName: 'title',
        desc: '结果',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'idnum',
        returnName: 'idnum',
        desc: 'idnum',
        isHighlight: false,
        highlightTo: [],
      },
    ],
  },
  {
    type: '32',
    desc: '非正常户',
    dimensionType: [DimensionLevel3Enums.BusinessAbnormal4],
    fieldList: [],
  },
  {
    type: '34',
    desc: '产品抽查',
    dimensionType: [DimensionLevel3Enums.ProductQualityProblem2],
    fieldList: [],
  },
  {
    type: '28',
    desc: '环保评级',
    dimensionType: null,
    fieldList: [],
  },
  {
    type: '35',
    desc: '行政许可',
    dimensionType: null,
    fieldList: [],
  },
  {
    type: '39',
    desc: '劳动仲裁开庭',
    dimensionType: null,
    fieldList: [],
  },
  {
    type: '40',
    desc: '劳动仲裁送达',
    dimensionType: null,
    fieldList: [],
  },
  {
    type: '29',
    desc: '其他评级',
    dimensionType: null,
    fieldList: [],
  },
  {
    type: '41',
    desc: '司法案件',
    dimensionType: null,
    fieldList: [],
  },
  {
    type: '37',
    desc: '公安通报',
    dimensionType: null,
    fieldList: [],
  },
  {
    type: '38',
    desc: '合同违约',
    dimensionType: [DimensionLevel2Enums.ContractBreach],
    fieldList: [],
  },
  {
    type: '30',
    desc: '债券违约',
    dimensionType: [DimensionLevel2Enums.BondDefaults],
    fieldList: [],
  },
  {
    type: '36',
    desc: '假冒化妆品',
    dimensionType: [DimensionLevel3Enums.ProductQualityProblem8],
    fieldList: [],
  },
  {
    type: '42',
    desc: '监管处罚',
    dimensionType: [DimensionLevel2Enums.RegulateFinance],
    fieldList: [
      {
        sourceName: 'Id',
        returnName: 'Id',
        desc: '主键',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'KeyNo',
        returnName: 'KeyNo',
        desc: 'keyNo',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'NameAndKeyNo',
        returnName: 'NameAndKeyNo',
        desc: '跳转企业信息',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'CompanyName',
        returnName: 'CompanyName',
        desc: '名称',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'DocNo',
        returnName: 'DocNo',
        desc: '决定书文号',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'PunishReason',
        returnName: 'PunishReason',
        desc: '处罚原因',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'PunishReasonClean',
        returnName: 'PunishReasonClean',
        desc: '处罚原因',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'PunishYear',
        returnName: 'PunishYear',
        desc: '处罚年份',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'PunishDate',
        returnName: 'PunishDate',
        desc: '处罚日期',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'PunishOffice',
        returnName: 'PunishOffice',
        desc: '处罚单位',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'PunishOfficeType',
        returnName: 'PunishOfficeType',
        desc: '处罚机关类型',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'PunishCategory',
        returnName: 'PunishCategory',
        desc: '处罚种类',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'PunishResult',
        returnName: 'PunishResult',
        desc: '处罚内容',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'ResultClean',
        returnName: 'ResultClean',
        desc: '处罚内容',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'SourceName',
        returnName: 'SourceName',
        desc: '数据来源',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'DataStatus',
        returnName: 'DataStatus',
        desc: '数据状态',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'PenaltyMoney',
        returnName: 'PenaltyMoney',
        desc: '处罚金额',
        isHighlight: false,
        highlightTo: [],
      },
      {
        sourceName: 'OssKey',
        returnName: 'OssKey',
        desc: '附件地址',
        isHighlight: false,
        highlightTo: [],
      },
    ],
  },
];
