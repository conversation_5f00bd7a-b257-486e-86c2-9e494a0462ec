import {
  BasicQualificationCheckItemEnum,
  ComplianceScreeningCheckItemEnum,
  DataSourceEnum,
  MobileBiddingSettingGroupEnum,
  RelationshipCheckGroupEnum,
  RelationshipCheckItemEnum,
  RiskLevelEnum,
} from '@domain/enums/mobile-bidding/mobile.enums';

export const MobileBiddingSetting = {
  version: 1,
  name: '移动端招标排查',
  description: '移动端招标排查',
  groups: [
    // 关联关系检查
    {
      key: MobileBiddingSettingGroupEnum.RelationshipCheck,
      name: '关联关系排查',
      description: '关联关系排查',
      items: [
        {
          key: RelationshipCheckGroupEnum.ParentSubsidiary,
          name: '母子公司与控制关系',
          description: '母子公司与控制关系',
          riskLevel: RiskLevelEnum.High,
          checkItems: [
            { key: RelationshipCheckItemEnum.DirectEquity, name: '直接持股', description: '直接持股', riskLevel: RiskLevelEnum.High },
            { key: RelationshipCheckItemEnum.IndirectEquity, name: '间接持股', description: '间接持股', riskLevel: RiskLevelEnum.High },
            { key: RelationshipCheckItemEnum.SingleControlPath, name: '单一控制路径', description: '单一控制路径', riskLevel: RiskLevelEnum.High },
            { key: RelationshipCheckItemEnum.MultipleControlPath, name: '多重控制路径', description: '多重控制路径', riskLevel: RiskLevelEnum.High },
            { key: RelationshipCheckItemEnum.MajorShareholder, name: '大股东', description: '大股东', riskLevel: RiskLevelEnum.High },
          ],
          sort: 0,
          batchOrder: 0,
        },
        {
          key: RelationshipCheckGroupEnum.HeadBranch,
          name: '总分公司关系',
          description: '总分公司关系',
          riskLevel: RiskLevelEnum.High,
          checkItems: [
            { key: RelationshipCheckItemEnum.HeadBranch, name: '总公司与分公司', description: '总公司与分公司', riskLevel: RiskLevelEnum.High },
            { key: RelationshipCheckItemEnum.SameHeadCompany, name: '隶属同一总公司', description: '隶属同一总公司', riskLevel: RiskLevelEnum.High },
          ],
          sort: 10,
          batchOrder: 0,
        },
        {
          key: RelationshipCheckGroupEnum.SameControlPerson,
          name: '有相同实际控制人',
          description: '有相同实际控制人',
          riskLevel: RiskLevelEnum.High,
          checkItems: [
            { key: RelationshipCheckItemEnum.SameFinalControlPerson, name: '有相同实际控制人', description: '有相同实际控制人', riskLevel: RiskLevelEnum.High },
          ],
          sort: 20,
          batchOrder: 0,
        },
        {
          key: RelationshipCheckGroupEnum.SameParentCompany,
          name: '同一母公司',
          description: '同一母公司',
          riskLevel: RiskLevelEnum.High,
          checkItems: [
            { key: RelationshipCheckItemEnum.DirectControl, name: '直接控制', description: '直接控制', riskLevel: RiskLevelEnum.High },
            { key: RelationshipCheckItemEnum.IndirectControl, name: '间接控制', description: '间接控制', riskLevel: RiskLevelEnum.High },
          ],
          sort: 30,
          batchOrder: 1,
        },
        {
          key: RelationshipCheckGroupEnum.SameGroupCompany,
          name: '隶属同一集团',
          description: '隶属同一集团',
          riskLevel: RiskLevelEnum.High,
          checkItems: [{ key: RelationshipCheckItemEnum.SameGroupCompany, name: '隶属同一集团', description: '隶属同一集团', riskLevel: RiskLevelEnum.High }],
          sort: 40,
          batchOrder: 2,
        },
        {
          key: RelationshipCheckGroupEnum.PartnerInvestment,
          name: '股权投资关联',
          description: '股权投资关联',
          riskLevel: RiskLevelEnum.Medium,
          checkItems: [
            { key: RelationshipCheckItemEnum.DirectEquity, name: '直接持股', description: '直接持股', riskLevel: RiskLevelEnum.Medium },
            { key: RelationshipCheckItemEnum.IndirectEquity, name: '间接持股', description: '间接持股', riskLevel: RiskLevelEnum.Medium },
            { key: RelationshipCheckItemEnum.HistoricalEquity, name: '历史持股', description: '历史持股', riskLevel: RiskLevelEnum.Medium },
          ],
          sort: 50,
          batchOrder: 2,
        },
        {
          key: RelationshipCheckGroupEnum.SameLegal,
          name: '法定代表人为同一人',
          description: '法定代表人为同一人',
          riskLevel: RiskLevelEnum.Medium,
          checkItems: [
            { key: RelationshipCheckItemEnum.CurrentSameLegal, name: '当前法人相同', description: '当前法人相同', riskLevel: RiskLevelEnum.High },
            {
              key: RelationshipCheckItemEnum.CurrentAndHistoricalLegal,
              name: '当前与历史法人相同',
              description: 'A公司当前法定代表人与B公司历史法定代表人（或反之）为同一自然人（一边有历史就算历史）',
              riskLevel: RiskLevelEnum.Medium,
            },
            {
              key: RelationshipCheckItemEnum.HistoricalSameLegal,
              name: '历史法人相同',
              description: 'A公司与B公司的历史法定代表人曾为同一自然人（一边有历史就算历史）',
              riskLevel: RiskLevelEnum.Medium,
            },
          ],
          sort: 60,
          batchOrder: 0,
        },
        {
          key: RelationshipCheckGroupEnum.SameEmployee,
          name: '管理层有相同主要人员',
          description: '管理层有相同主要人员',
          riskLevel: RiskLevelEnum.Medium,
          checkItems: [
            {
              key: RelationshipCheckItemEnum.CurrentSameKeyPersonnel,
              name: '当前主要人员相同',
              description: '当前主要人员相同',
              riskLevel: RiskLevelEnum.High,
            },
            {
              key: RelationshipCheckItemEnum.CurrentAndHistoricalKeyPersonnel,
              name: '当前与历史主要人员相同',
              description: 'A公司当前主要人员与B公司历史主要人员（或反之）为同一自然人（一边有历史就算历史）',
              riskLevel: RiskLevelEnum.Medium,
            },
            {
              key: RelationshipCheckItemEnum.HistoricalSameKeyPersonnel,
              name: '历史主要人员相同',
              description: 'A公司与B公司的历史主要人员曾为同一自然人（一边有历史就算历史）',
              riskLevel: RiskLevelEnum.Medium,
            },
          ],
          sort: 70,
          batchOrder: 3,
        },
        {
          key: RelationshipCheckGroupEnum.SameEmployeeInvestor,
          name: '管理层共同任职/投资',
          description: '管理层共同任职/投资',
          riskLevel: RiskLevelEnum.Medium,
          checkItems: [{ key: RelationshipCheckItemEnum.CommonInvestment, name: '共同投资', description: '共同投资', riskLevel: RiskLevelEnum.Medium }],
          sort: 80,
          batchOrder: 3,
        },
        {
          key: RelationshipCheckGroupEnum.SameContactWay,
          name: '联系方式相同',
          description: '联系方式相同',
          riskLevel: RiskLevelEnum.Medium,
          checkItems: [
            { key: RelationshipCheckItemEnum.SamePhoneNumber, name: '联系电话相同', description: '相同联系电话', riskLevel: RiskLevelEnum.Medium },
            { key: RelationshipCheckItemEnum.SameEmail, name: '邮箱相同', description: '相同邮箱', riskLevel: RiskLevelEnum.Medium },
            { key: RelationshipCheckItemEnum.SameWebsite, name: '网址相同', description: '相同域名', riskLevel: RiskLevelEnum.Medium },
          ],
          sort: 90,
          batchOrder: 3,
        },
        {
          key: RelationshipCheckGroupEnum.SameAddress,
          name: '地址信息相同或高度相似',
          description: '地址信息相同或高度相似',
          riskLevel: RiskLevelEnum.Medium,
          checkItems: [
            {
              key: RelationshipCheckItemEnum.CurrentAddressSame,
              name: '当前地址完全相同',
              description: '当前地址完全相同',
              riskLevel: RiskLevelEnum.Medium,
            },
            {
              key: RelationshipCheckItemEnum.CurrentAddressSimilar,
              name: '当前地址高度相似',
              description: '当前地址高度相似',
              riskLevel: RiskLevelEnum.Medium,
            },
            {
              key: RelationshipCheckItemEnum.HistoricalAddressSame,
              name: '历史地址相同',
              description: '历史地址相同',
              riskLevel: RiskLevelEnum.Medium,
            },
          ],
          sort: 100,
          batchOrder: 3,
        },
        {
          key: RelationshipCheckGroupEnum.Guarantor,
          name: '存在资金担保关系',
          description: '存在资金担保关系',
          riskLevel: RiskLevelEnum.Medium,
          checkItems: [
            { key: RelationshipCheckItemEnum.ExistsGuarantee, name: '存在资金担保关系', description: '存在资金担保关系', riskLevel: RiskLevelEnum.Medium },
          ],
          sort: 110,
          batchOrder: 3,
        },
        {
          key: RelationshipCheckGroupEnum.EquityPledge,
          name: '股权出质',
          description: '股权出质',
          riskLevel: RiskLevelEnum.Medium,
          checkItems: [{ key: RelationshipCheckItemEnum.EquityPledge, name: '股权出质', description: '股权出质', riskLevel: RiskLevelEnum.Medium }],
          sort: 120,
          batchOrder: 3,
        },
        {
          key: RelationshipCheckGroupEnum.ChattelMortgage,
          name: '动产抵押',
          description: '动产抵押',
          riskLevel: RiskLevelEnum.Medium,
          checkItems: [{ key: RelationshipCheckItemEnum.ChattelMortgage, name: '动产抵押', description: '动产抵押', riskLevel: RiskLevelEnum.Medium }],
          sort: 130,
          batchOrder: 3,
        },
        {
          key: RelationshipCheckGroupEnum.SameInvestCompany,
          name: '共同投资同一公司',
          description: '共同投资同一公司',
          riskLevel: RiskLevelEnum.Medium,
          checkItems: [
            {
              key: RelationshipCheckItemEnum.CommonInvestmentCompany,
              name: '共同投资同一公司',
              description: '共同投资同一公司',
              riskLevel: RiskLevelEnum.Medium,
            },
            {
              key: RelationshipCheckItemEnum.CommonHisInvestmentCompany,
              name: '共同历史投资同一公司',
              description: '共同历史投资同一公司',
              riskLevel: RiskLevelEnum.Medium,
            },
          ],
          sort: 140,
          batchOrder: 3,
        },
        {
          key: RelationshipCheckGroupEnum.SameLegalInvestor,
          name: '有共同股东',
          description: '有共同股东',
          riskLevel: RiskLevelEnum.Medium,
          checkItems: [
            { key: RelationshipCheckItemEnum.CommonLegalShareholder, name: '共同法人股东', description: '共同法人股东', riskLevel: RiskLevelEnum.Medium },
            {
              key: RelationshipCheckItemEnum.CommonHisLegalShareholder,
              name: '共同历史法人股东',
              description: '共同历史法人股东',
              riskLevel: RiskLevelEnum.Medium,
            },
          ],
          sort: 150,
          batchOrder: 3,
        },
        {
          key: RelationshipCheckGroupEnum.IndirectComplex,
          name: '间接复杂或隐蔽关联',
          description: '间接复杂或隐蔽关联',
          riskLevel: RiskLevelEnum.Low,
          checkItems: [
            {
              key: RelationshipCheckItemEnum.EnterpriseRelationship,
              name: '企业间关联关系',
              description: '复杂关联穿透',
              riskLevel: RiskLevelEnum.Medium,
            },
            {
              key: RelationshipCheckItemEnum.OverlapCustomerSupplier,
              name: '主要客户或供应商高度重叠',
              description: '主要客户或供应商高度重叠',
              riskLevel: RiskLevelEnum.Low,
            },
            { key: RelationshipCheckItemEnum.CommonPatent, name: '共同申请知识产权', description: '共同申请知识产权', riskLevel: RiskLevelEnum.Low },
            { key: RelationshipCheckItemEnum.PatentTransfer, name: '专利转让', description: '专利转让', riskLevel: RiskLevelEnum.Low },
            // { key: RelationshipCheckItemEnum.BidFraud, name: '曾有围串标', description: '曾有围串标', riskLevel: RiskLevelEnum.Medium }, // 来源行政处罚和黑名单
          ],
          sort: 160,
          batchOrder: 4,
        },
      ],
    },
    // 资格检查
    {
      key: MobileBiddingSettingGroupEnum.BasicQualification,
      name: '基础资格',
      description: '基础资格',
      items: [
        {
          key: BasicQualificationCheckItemEnum.RegistrationStatus,
          name: '登记状态',
          description: '登记状态',
          riskLevel: RiskLevelEnum.High,
          dataSource: DataSourceEnum.CompanyDetail,
          sort: 0,
        },
        {
          key: BasicQualificationCheckItemEnum.IsBranchCompany,
          name: '独立法人资格',
          description: '独立法人资格',
          riskLevel: RiskLevelEnum.Medium,
          dataSource: DataSourceEnum.CompanyDetail,
          sort: 10,
        },
        {
          key: BasicQualificationCheckItemEnum.TaxpayerType,
          name: '一般纳税人',
          description: '一般纳税人',
          riskLevel: RiskLevelEnum.Low,
          dataSource: DataSourceEnum.CompanyDetail,
          sort: 20,
        },
        {
          key: BasicQualificationCheckItemEnum.BusinessStatus,
          name: '经营异常',
          description: '经营异常',
          riskLevel: RiskLevelEnum.High,
          dataSource: DataSourceEnum.CreditEs,
          sort: 30,
        },
        {
          key: BasicQualificationCheckItemEnum.FakeStateOwnedEnterprise,
          name: '假冒国企',
          description: '假冒国企',
          riskLevel: RiskLevelEnum.High,
          dataSource: DataSourceEnum.CompanyDetail,
          typeCode: '623',
          sort: 40,
        },
        {
          key: BasicQualificationCheckItemEnum.StopProduction,
          name: '被责令停产停业',
          description: '被责令停产停业',
          riskLevel: RiskLevelEnum.Medium,
          dataSource: DataSourceEnum.DataInterface,
          sort: 50,
        },
        {
          key: BasicQualificationCheckItemEnum.Bankruptcy,
          name: '破产重整',
          description: '破产重整',
          riskLevel: RiskLevelEnum.High,
          dataSource: DataSourceEnum.CreditEs,
          sort: 60,
        },
      ],
    },
    {
      key: MobileBiddingSettingGroupEnum.ComplianceCheck,
      name: '合规检查事项',
      description: '合规检查事项',
      items: [
        {
          key: ComplianceScreeningCheckItemEnum.SeriousViolation,
          name: '被列入严重违法失信企业名单',
          description: '被列入严重违法失信企业名单',
          riskLevel: RiskLevelEnum.High,
          dataSource: DataSourceEnum.DataInterface,
          sort: 0,
        },
        // {
        //   key: ComplianceScreeningCheckItemEnum.FraudulentEnterprise,
        //   name: '涉行贿犯罪记录',
        //   description: '涉行贿犯罪记录',
        //   riskLevel: RiskLevelEnum.High,
        //   dataSource: DataSourceEnum.JudgementEs,
        //   sort: 10,
        // },
        {
          key: ComplianceScreeningCheckItemEnum.ShixinRen,
          name: '失信被执行人',
          description: '失信被执行人',
          riskLevel: RiskLevelEnum.High,
          dataSource: DataSourceEnum.DataInterface,
          path: '/api/Court/SearchShiXin', // 使用 C 端接口，包含涉案总金额数据
          sort: 20,
        },
        {
          key: ComplianceScreeningCheckItemEnum.LimitHighConsumption,
          name: '限制高消费',
          description: '限制高消费',
          riskLevel: RiskLevelEnum.High,
          dataSource: DataSourceEnum.DataInterface,
          path: '/api/Court/GetSumptuaryListByType', // 使用 C 端接口，包含涉案总金额数据
          sort: 30,
        },
        {
          key: ComplianceScreeningCheckItemEnum.MajorTaxFraud,
          name: '重大税收违法',
          description: '重大税收违法',
          riskLevel: RiskLevelEnum.High,
          dataSource: DataSourceEnum.DataInterface,
          path: '/api/Tax/GetIllegalList', // 使用 C 端接口，包含涉案总金额数据
          sort: 40,
        },
        {
          key: ComplianceScreeningCheckItemEnum.TaxNonNormal,
          name: '被列入税务非正常户',
          description: '被列入税务非正常户',
          riskLevel: RiskLevelEnum.Medium,
          dataSource: DataSourceEnum.DataInterface,
          path: '/api/Risk/GetTaxUnnormals',
          sort: 50,
        },
        {
          key: ComplianceScreeningCheckItemEnum.DebtRecord,
          name: '欠税记录',
          description: '欠税记录',
          riskLevel: RiskLevelEnum.Medium,
          dataSource: DataSourceEnum.DataInterface,
          path: '/api/Tax/GetListOfOweNoticeNew',
          sort: 60,
        },
        {
          key: ComplianceScreeningCheckItemEnum.BlacklistForUnpaidWorkers,
          name: '被列入拖欠农民工工资黑名单',
          description: '被列入拖欠农民工工资黑名单',
          riskLevel: RiskLevelEnum.High,
          dataSource: DataSourceEnum.BlacklistEs,
          child: ['5'],
          sort: 70,
        },
        {
          key: ComplianceScreeningCheckItemEnum.StatisticalSerious,
          name: '统计领域严重失信企业及其有关人员',
          description: '统计领域严重失信企业及其有关人员',
          riskLevel: RiskLevelEnum.High,
          dataSource: DataSourceEnum.BlacklistEs,
          child: ['3'],
          sort: 80,
        },
        {
          key: ComplianceScreeningCheckItemEnum.SafetyProduction,
          name: '安全生产领域失信生产经营单位',
          description: '安全生产领域失信生产经营单位',
          riskLevel: RiskLevelEnum.High,
          dataSource: DataSourceEnum.BlacklistEs,
          child: ['2'],
          sort: 90,
        },
        {
          key: ComplianceScreeningCheckItemEnum.OtherIndustryRegulatoryBlacklist,
          name: '其他行业领域监管黑名单',
          description: '其他行业领域监管黑名单',
          riskLevel: RiskLevelEnum.Medium,
          dataSource: DataSourceEnum.BlacklistEs,
          child: [
            1, 28, 33, 34, 69, 39, 40, 41, 68, 56, 46, 97, 4, 54, 24, 38, 55, 69, 11, 8, 16, 29, 30, 32, 35, 36, 37, 44, 47, 48, 49, 50, 51, 52, 53, 98, 99,
          ],
          sort: 100,
        },
        {
          key: ComplianceScreeningCheckItemEnum.GovernmentProcurementSeriousFraud,
          name: '列入政府采购严重违法失信名单',
          description: '列入政府采购严重违法失信名单',
          riskLevel: RiskLevelEnum.Medium,
          dataSource: DataSourceEnum.BlacklistEs,
          child: ['25'],
          sort: 110,
        },
        {
          key: ComplianceScreeningCheckItemEnum.MilitaryProcurementFraud,
          name: '军队采购失信名单',
          description: '军队采购失信名单',
          riskLevel: RiskLevelEnum.Medium,
          dataSource: DataSourceEnum.BlacklistEs,
          child: ['75', '104'],
          sort: 120,
        },
        {
          key: ComplianceScreeningCheckItemEnum.MilitaryProcurementSuspensionSupplier,
          name: '军队采购暂停供应商资格名单',
          description: '军队采购暂停供应商资格名单',
          riskLevel: RiskLevelEnum.Medium,
          dataSource: DataSourceEnum.BlacklistEs,
          child: ['76'],
          sort: 130,
        },
        {
          key: ComplianceScreeningCheckItemEnum.StateOwnedEnterprisePurchaseBlacklist,
          name: '国央企采购黑名单',
          description: '国央企采购黑名单',
          riskLevel: RiskLevelEnum.Medium,
          dataSource: DataSourceEnum.BlacklistEs,
          child: ['74', '77', '78'],
          sort: 140,
        },
        // {
        //   key: ComplianceScreeningCheckItemEnum.PurchasePunishment,
        //   name: '涉采购处罚',
        //   description: '涉采购处罚',
        //   riskLevel: RiskLevelEnum.Medium,
        //   dataSource: DataSourceEnum.PunishmentEs,
        //   sort: 150,
        // },
        // {
        //   key: ComplianceScreeningCheckItemEnum.BidFraud,
        //   name: '涉诉围串标记录',
        //   description: '涉诉围串标记录',
        //   riskLevel: RiskLevelEnum.Medium,
        //   dataSource: DataSourceEnum.JudgementEs,
        //   sort: 160,
        // },
      ],
    },
  ],
};
