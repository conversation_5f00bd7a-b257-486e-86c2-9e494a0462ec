import { BatchBusinessTypeEnums } from '@domain/enums/batch/BatchBusinessTypeEnums';

export const BatchConstants = {
  Consumer: {
    // per pod
    Diligence: process.env.BATCH_DD_CONSUMER ? parseInt(process.env.BATCH_DD_CONSUMER) : 1,
    Snapshot: process.env.BATCH_DD_CONSUMER ? parseInt(process.env.BATCH_DD_CONSUMER) : 1,
    Others: 1,
    Monitor: 1,
  },
  JobConcurrency: {
    // Bluebird.map中的concurrency或者批量保存数据库时候的chunkSize
    Customer: 5,
    Diligence: 2,
    Person: 5,
    InnerBlacklist: 5,
  },
  Threshold: {
    Batch: 3 * 60 * 60 * 1000, //ms
    Job: 3 * 60 * 60 * 1000, //ms  job超时时间改为跟batch相同，防止一开始创建的job排队比较靠后，会在排队中超时
  },
  getBatchTimeoutHours(businessType: BatchBusinessTypeEnums, recordCount: number) {
    let hours = Math.ceil(recordCount / this.getMaxItemsPerHour(businessType)) + 1; // 多加1个小时，防止scanJob超时造成一进入Monitor就超时
    if (!hours || hours < 1) {
      hours = 1;
    } else if (hours > 24) {
      hours = 24;
    }
    return hours; // 最少是1小时
  },
  getMaxItemsPerHour(businessType: BatchBusinessTypeEnums) {
    return 60 * (60000 / this.getMonitorInterval(businessType)) * this.getJobItemSize(businessType) * this.getScanJobSize(businessType);
  },
  getJobItemSize(businessType: BatchBusinessTypeEnums) {
    switch (businessType) {
      case BatchBusinessTypeEnums.Specific_Async_Diligence: {
        return 5;
      }
      case BatchBusinessTypeEnums.Potentail_Batch_Customer:
      case BatchBusinessTypeEnums.Potential_Batch_Excel:
      case BatchBusinessTypeEnums.Potential_Batch_Data: {
        return 5;
      }
      case BatchBusinessTypeEnums.Diligence_File:
      case BatchBusinessTypeEnums.Diligence_Customer:
      case BatchBusinessTypeEnums.Diligence_ID: {
        return 5;
      }
      case BatchBusinessTypeEnums.Monitor_File:
      case BatchBusinessTypeEnums.Customer_File:
      case BatchBusinessTypeEnums.InnerBlacklist_File:
      case BatchBusinessTypeEnums.Person_File: {
        return 50;
      }
      case BatchBusinessTypeEnums.Diligence_Batch_Detail: {
        return 50;
      }
      case BatchBusinessTypeEnums.Diligence_Customer_Analyze:
      case BatchBusinessTypeEnums.Bidding_Diligence_File:
      case BatchBusinessTypeEnums.Specific_Diligence_File: {
        return 1;
      }
      case BatchBusinessTypeEnums.Bidding_Diligence: {
        return 1;
      }
      default:
        return 5;
    }
  },
  getScanJobSize(businessType: BatchBusinessTypeEnums) {
    //结合 getMonitorInterval 和 getJobItemSize ， 每次分钟扫描的次数 * 每次扫描出来的job的数量 * 每个job包含的item的数量 = 每分钟最大能处理的item 的数量
    switch (businessType) {
      case BatchBusinessTypeEnums.Bidding_Diligence_File:
      case BatchBusinessTypeEnums.Diligence_File:
      case BatchBusinessTypeEnums.Diligence_Customer:
      case BatchBusinessTypeEnums.Diligence_Customer_Analyze:
      case BatchBusinessTypeEnums.Diligence_ID: {
        return 10; // 结合上面的规则， 每分钟最大处理 60/30 * 5 * 5 = 50个尽调
      }
      default: {
        return 10;
      }
    }
  },
  getMonitorInterval(businessType: BatchBusinessTypeEnums) {
    if (process?.env?.JEST_WORKER_ID) {
      return 1000; //ms 单元测试的时候，提高扫描的频率，方便测试
    }
    //规则：根据 pod数量*consumer数量*jobConcurrency 计算出一批次最多处理的记录数量，再结合一个批次消耗的时间，可以得出一分钟最多处理的的记录数量，然后再根据期望的一分钟最大的处理数量，计算出monitor 的interval，确保一个monitor中扫描出来的一批job，最低需要 interval的时间。
    //以batch diligence为例： 有两个pod，一个pod中的consumer数量是1，jobItemSize是20， jobConcurrency是5，monitor中一次最多扫描10条消息并存入消息队列中。
    // 这样一次并发可以处理  2 条消息， 40个尽调。 40个尽调会按照并发10(pod数量*consumer数量*jobConcurrency)处理，一次消耗大概2s(生产环境).处理两条消息需要12s,10条消息就需要40s
    // 考虑到 尽调时间可能不同以及服务器负载，我们可以把 interval 时间设置为60s, 效果是 每60s内处理的尽职调查不超过 200(10*jobItemSize)
    //同时侧重考虑同时消费多个用户的批量消息，也避免一个消息中jobItemSize过大更容易失败（数量多执行时间太久，jobConcurrency过大，可能也容易单个job更容易失败），我们可以适当增大consumer的数量，降低jobConcurrency的数量。 比如 实际并发=pod数量*consumer数量*jobConcurrency ，在pod 一直的情况下 1*4*2 和 1*2*4 结果一样，但是1*4*2的容错性会相对较高
    switch (businessType) {
      case BatchBusinessTypeEnums.Bidding_Diligence_File:
      case BatchBusinessTypeEnums.Diligence_File:
      case BatchBusinessTypeEnums.Diligence_Customer:
      case BatchBusinessTypeEnums.Diligence_Customer_Analyze:
      case BatchBusinessTypeEnums.Diligence_ID: {
        return 10 * 1000; // ms
      }
      default: {
        return 10 * 1000; //ms
      }
    }
  },
};
