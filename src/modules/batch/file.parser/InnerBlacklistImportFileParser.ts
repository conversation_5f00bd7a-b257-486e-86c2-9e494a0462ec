import { RoverExceptions } from '@commons/constants/exceptionConstants';
import { GROUP_LIMIT_COUNT, SPEC_GROUP_LIMIT_COUNT, specialOrgIds } from '@domain/constants/common';
import { FieldParseErrorTypeEnums } from '@domain/enums/batch/FieldParseErrorTypeEnums';
import { ExcelParserSettingPO } from '@domain/model/batch/po/parse/ExcelParserSettingPO';
import { FileParseResult } from '@domain/model/batch/po/parse/FileParseResult';
import { InnerBlacklistImportExcelRecord, ParseErrorItem } from '@domain/model/batch/po/parse/ParsedRecordBase';
import { RoverUserModel } from '@domain/model/RoverUserModel';
import { getComplexExceptionDescription } from '@domain/utils/diligence/diligence.utils';
import { RoverBundleEntityConfig, RoverBundleLimitationType, RoverBundleService } from '@kezhaozhao/saas-bundle-service';
import { BadRequestException, Injectable } from '@nestjs/common';
import { parseDurationChinese } from '../common/file.export.template';
import { FileParserAbstract } from './FileParserAbstract';

@Injectable()
export class InnerBlacklistImportFileParser extends FileParserAbstract {
  constructor(protected readonly bundleService: RoverBundleService) {
    super(bundleService);
  }

  async getFileLimit(currentUser: RoverUserModel): Promise<number> {
    const userBundle: RoverBundleEntityConfig = await this.bundleService.getBundle(currentUser);
    return userBundle[RoverBundleLimitationType.BatchInnerBlacklistUploadQuantity]?.value || this.getParseSetting().limit;
  }

  async parseFile(currentUser: RoverUserModel, filePath: string): Promise<FileParseResult> {
    const success: InnerBlacklistImportExcelRecord[] = [];
    const error: ParseErrorItem[] = [];
    const nameSet = new Set<string>();
    const rows = await this.parse(currentUser, filePath);
    rows.forEach((rowData) => {
      const label = this.parseRowString(rowData, 2);
      const departmentNames = this.parseRowString(rowData, 7);
      const blacklist = Object.assign(new InnerBlacklistImportExcelRecord(), {
        companyName: this.parseRowString(rowData, 0, true),
        group: this.parseRowString(rowData, 1), //分组
        label: label ? label.split(',') : undefined, //标签，标签如果是填写多个的话用中文"，"逗号隔开，例如：「优质客户，普通客户」
        reason: this.parseRowString(rowData, 3),
        joinDate: this.parseRowDate(rowData, 4, ''),
        duration: parseDurationChinese(rowData[5]),
        expiredDate: this.parseRowDate(rowData, 6, ''), //截止日期
        departmentNames: departmentNames ? departmentNames.split(',') : undefined, //部门
        comment: this.parseRowString(rowData, 8),
      });

      if (!blacklist.companyName) {
        error.push({
          data: blacklist,
          errorType: FieldParseErrorTypeEnums.Required,
        });
        return;
      }
      if (nameSet.has(blacklist.companyName)) {
        // 有重复数据
        error.push({
          data: blacklist,
          errorType: FieldParseErrorTypeEnums.Duplicated,
        });
        return;
      } else {
        nameSet.add(blacklist.companyName);
      }
      success.push(blacklist);
    });
    return { succeedItems: success, failedItems: error };
  }

  getParseSetting(): ExcelParserSettingPO {
    return {
      explainRows: 3,
      limit: 1000,
      sheetName: '内部黑名单-批量添加',
      templateTitle: [
        '企业名称/统一社会信用代码',
        '企业分组（选填）',
        '标签（选填）',
        '列入原因（选填）',
        '列入时间（选填）',
        '有效期（选填）',
        '截止日期（选填）',
        '来源部门（选填）',
        '备注（选填）',
      ],
      exportExcelColumns: {
        resultDes: { header: '导入结果', width: 30 },
        companyName: { header: '企业名称/统一社会信用代码', width: 50 },
        group: { header: '企业分组（选填）', width: 30 },
        label: { header: '标签（选填）', width: 30 },
        reason: { header: '列入原因（选填）', width: 30 },
        joinDate: { header: '列入时间（选填）', width: 30 },
        duration: { header: '有效期（选填）', width: 30 },
        expiredDate: { header: '截止日期（选填）', width: 30 },
        departmentNames: { header: '来源部门（选填）', width: 30 },
        comment: { header: '备注（选填）', width: 30 },
      },
    };
  }

  /**
   * 预校验分组和部门数量限制
   * 统计Excel中每个分组、部门的数量，检查是否超过限制
   * @param currentUser 当前用户
   * @param validData Excel中的有效数据行
   */
  protected async validateGroupAndDepartmentLimits(currentUser: RoverUserModel, validData: any[][]): Promise<void> {
    // 统计分组数量
    const groupCounts = new Map<string, number>();
    // 统计部门数量
    const departmentCounts = new Map<string, number>();

    // 获取当前org的限制数量
    const limitCount = specialOrgIds.includes(currentUser.currentOrg) ? SPEC_GROUP_LIMIT_COUNT : GROUP_LIMIT_COUNT;

    validData.forEach((rowData) => {
      // 解析分组信息（第2列，索引1）
      const groupName = this.parseRowString(rowData, 1);
      if (groupName) {
        groupCounts.set(groupName, (groupCounts.get(groupName) || 0) + 1);
      }

      // 解析部门信息（第8列，索引7），可能有多个部门用逗号分隔
      const departmentNames = this.parseRowString(rowData, 7);
      if (departmentNames) {
        const departments = departmentNames
          .split(',')
          .map((name) => name.trim())
          .filter(Boolean);
        departments.forEach((dept) => {
          departmentCounts.set(dept, (departmentCounts.get(dept) || 0) + 1);
        });
      }
    });

    // 检查分组数量限制
    for (const [groupName, count] of groupCounts.entries()) {
      if (count > limitCount) {
        const err = { ...RoverExceptions.Import.GroupLimited };
        err.error = getComplexExceptionDescription(err.error, { groupName, count, limit: limitCount });
        throw new BadRequestException(err);
      }
    }

    // 检查部门数量限制
    for (const [departmentName, count] of departmentCounts.entries()) {
      if (count > limitCount) {
        const err = { ...RoverExceptions.Import.DepartmentLimited };
        err.error = getComplexExceptionDescription(err.error, { departmentName, count, limit: limitCount });
        throw new BadRequestException(err);
      }
    }
  }
}
