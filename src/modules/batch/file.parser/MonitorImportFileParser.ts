import { FileParserAbstract } from './FileParserAbstract';
import { RoverBundleService } from '@kezhaozhao/saas-bundle-service';
import { FileParseResult } from '@domain/model/batch/po/parse/FileParseResult';
import { InnerBlacklistImportExcelRecord, ParseErrorItem } from '@domain/model/batch/po/parse/ParsedRecordBase';
import { FieldParseErrorTypeEnums } from '@domain/enums/batch/FieldParseErrorTypeEnums';
import { ExcelParserSettingPO } from '@domain/model/batch/po/parse/ExcelParserSettingPO';
import { Injectable } from '@nestjs/common';
import { RoverUserModel } from '@domain/model/RoverUserModel';

@Injectable()
export class MonitorImportFileParser extends FileParserAbstract {
  constructor(protected readonly bundleService: RoverBundleService) {
    super(bundleService);
  }

  async getFileLimit(currentUser: RoverUserModel): Promise<number> {
    return this.getParseSetting().limit;
  }

  async parseFile(currentUser: RoverUserModel, filePath: string): Promise<FileParseResult> {
    const success: InnerBlacklistImportExcelRecord[] = [];
    const error: ParseErrorItem[] = [];
    const nameSet = new Set<string>();
    const rows = await this.parse(currentUser, filePath);
    rows.forEach((rowData) => {
      const blacklist = Object.assign(new InnerBlacklistImportExcelRecord(), {
        companyName: this.parseRowString(rowData, 0, true),
        group: this.parseRowString(rowData, 1), //监控分组
      });

      if (!blacklist.companyName) {
        error.push({
          data: blacklist,
          errorType: FieldParseErrorTypeEnums.Required,
        });
        return;
      }
      if (nameSet.has(blacklist.companyName)) {
        // 有重复数据
        error.push({
          data: blacklist,
          errorType: FieldParseErrorTypeEnums.Duplicated,
        });
        return;
      } else {
        nameSet.add(blacklist.companyName);
      }
      if (!blacklist.group) {
        blacklist.group = '默认分组';
      }
      success.push(blacklist);
    });
    return { succeedItems: success, failedItems: error };
  }

  getParseSetting(): ExcelParserSettingPO {
    return {
      explainRows: 3,
      limit: 5000,
      sheetName: '监控列表-导入',
      templateTitle: ['企业名称/统一社会信用代码', '所属分组（选填）'],
      exportExcelColumns: {
        resultDes: { header: '导入结果', width: 30 },
        companyName: { header: '企业名称/统一社会信用代码', width: 50 },
        group: { header: '所属分组（选填）', width: 30 },
      },
    };
  }
}
