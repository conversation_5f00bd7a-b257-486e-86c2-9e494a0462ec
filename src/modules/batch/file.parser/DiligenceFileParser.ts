import { FileParserAbstract } from './FileParserAbstract';
import { Injectable } from '@nestjs/common';
import { BatchDiligenceExcelRecord, ParseErrorItem } from '@domain/model/batch/po/parse/ParsedRecordBase';
import { ExcelParserSettingPO } from '@domain/model/batch/po/parse/ExcelParserSettingPO';
import { FieldParseErrorTypeEnums } from '@domain/enums/batch/FieldParseErrorTypeEnums';
import { FileParseResult } from '@domain/model/batch/po/parse/FileParseResult';
import { RoverBundleEntityConfig, RoverBundleLimitationType, RoverBundleService } from '@kezhaozhao/saas-bundle-service';
import { RoverUserModel } from '@domain/model/RoverUserModel';

@Injectable()
export class DiligenceFileParser extends FileParserAbstract {
  constructor(protected readonly bundleService: RoverBundleService) {
    super(bundleService);
  }
  async getFileLimit(currentUser: RoverUserModel): Promise<number> {
    const userBundle: RoverBundleEntityConfig = await this.bundleService.getBundle(currentUser);
    return userBundle[RoverBundleLimitationType.BatchDiligenceUploadQuantity]?.value || this.getParseSetting().limit;
  }

  async parseFile(currentUser: RoverUserModel, filePath: string): Promise<FileParseResult> {
    const success: BatchDiligenceExcelRecord[] = [];
    const error: ParseErrorItem[] = [];
    const nameSet = new Set<string>();
    const rows = await this.parse(currentUser, filePath);
    rows.forEach((rowData) => {
      const companyName = this.parseRowString(rowData, 0, true);

      if (!companyName) {
        //TODO 空行是直接跳过还是要提示用户？
        // error.push({
        //   data: null,
        //   errorType: FieldParseErrorTypeEnums.Required,
        // });
        return;
      }
      if (nameSet.has(companyName)) {
        // 有重复数据
        error.push({
          data: { companyName },
          errorType: FieldParseErrorTypeEnums.Duplicated,
        });
        return;
      } else {
        nameSet.add(companyName);
        success.push({ companyName });
      }
    });
    return { succeedItems: success, failedItems: error };
  }

  getParseSetting(): ExcelParserSettingPO {
    return {
      explainRows: 3,
      limit: 5000,
      sheetName: '批量风险排查-导入',
      templateTitle: ['企业名称/统一社会信用代码'],
      exportExcelColumns: {
        resultDes: { header: '导入结果', width: 30 },
        companyName: { header: '企业名称/统一社会信用代码', width: 50 },
      },
    };
  }
}
