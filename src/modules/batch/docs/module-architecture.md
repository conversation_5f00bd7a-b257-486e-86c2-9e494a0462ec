# 批量处理模块架构设计

## 📖 文档导航

- **[模块概览](../README.md)** | **当前：架构设计** | **[业务流程](workflow.md)** | **[开发指南](development-guide.md)**

## 🏗️ 核心架构

批量处理模块采用 **Facade（门面）模式** 作为统一入口，结合 **Strategy（策略）模式** 和 **Observer（观察者）模式** 处理不同类型的批量任务。架构设计重点在于解耦任务接收与任务处理，通过消息队列实现异步处理，保证系统的高吞吐量和稳定性。

### 架构分层视图

```mermaid
graph TD
    Client[客户端/前端] --> Controller[Controller Layer]
    
    subgraph "Facade Layer (门面层)"
        Controller --> BatchFacade[BatchFacadeService]
        BatchFacade --> CreationService[BatchCreationService]
        BatchFacade --> QueryService[BatchQueryService]
        BatchFacade --> StatisticService[BatchStatisticService]
    end
    
    subgraph "Core Service Layer (核心服务层)"
        CreationService --> CheckService[BatchCheckService]
        CreationService --> FileParser[FileParserService]
        FileParser --> Parsers[Specific Parsers (Diligence/Customer...)]
    end
    
    subgraph "Async Processing Layer (异步处理层)"
        CreationService --"Send Message"--> MQ[Message Queue]
        MQ --> MessageHandler[Message Handler Layer]
        
        MessageHandler --> DefaultHandler[BatchMessageHandlerDefault]
        MessageHandler --> DiligenceHandler[BatchMessageHandlerDiligence]
        MessageHandler --> ExportHandler[BatchMessageHandlerExport]
        
        DefaultHandler --> ImportProcessor[Import Processors]
        DiligenceHandler --> DiligenceProcessor[Diligence Processors]
        ExportHandler --> ExportProcessor[Export Processors]
    end
    
    subgraph "Data Layer (数据层)"
        ImportProcessor --> Repos[Repositories]
        DiligenceProcessor --> Repos
        ExportProcessor --> Repos
    end
```

## 📊 数据模型

批量任务的核心实体关系如下：

```mermaid
erDiagram
    BatchEntity ||--o{ BatchJobEntity : "包含多个子任务"
    BatchEntity {
        int id PK
        int orgId
        int userId
        int businessType "业务类型"
        int status "批次状态"
        string fileName "源文件名"
        json statisticsInfo "统计信息"
    }
    
    BatchJobEntity {
        int id PK
        int batchId FK
        string uniqueId "任务唯一标识"
        int status "任务状态"
        json inputData "输入数据"
        json resultData "结果数据"
    }
    
    BatchEntity ||--o{ BatchResultEntity : "包含结果"
    BatchEntity ||--o{ BatchDiligenceEntity : "关联尽调记录"
```

## 🔧 关键设计决策

### 1. 异步任务处理机制

- **设计背景**: 批量导入和排查任务通常涉及大量数据处理（如解析 5000 条 Excel 数据并调用外部 API），同步处理会导致 HTTP 请求超时且阻塞服务器资源。
- **解决方案**: 采用 "任务创建-消息投递-异步消费" 的模式。
  1.  **创建阶段**: 解析文件，创建 `BatchEntity` 和 `BatchJobEntity` 记录，状态为 "Pending"。
  2.  **投递阶段**: 将任务 ID 发送到消息队列。
  3.  **消费阶段**: `MessageHandler` 监听消息，调用具体的 `Processor` 逐条或批量处理任务，更新 Job 状态，最后更新 Batch 状态。
- **权衡考量**: 增加了架构复杂度，但极大地提升了系统的响应速度和并发处理能力，避免了长连接超时问题。

### 2. 处理器策略模式 (Processor Strategy)

- **设计背景**: 批量任务类型繁多（客商导入、黑名单导入、招投标排查、尽调导出等），每种任务的处理逻辑差异巨大，如果堆砌在 Service 中会难以维护。
- **解决方案**: 定义统一的 `Processor` 接口，不同业务类型实现各自的 Processor（如 `CustomerImportProcessor`, `DiligenceExportProcessor`）。通过 `MessageHandler` 根据 `businessType` 动态分发到对应的 Processor。
- **权衡考量**: 这是一个典型的策略模式应用，符合开闭原则，新增业务类型只需新增 Processor 类，无需修改原有逻辑。

### 3. 文件解析抽象层

- **设计背景**: 不同业务的导入模板格式不同（列名、数据类型、校验规则）。
- **解决方案**: 设计 `FileParserAbstract` 基类，各业务实现具体的 Parser（如 `CustomerImportFileParser`）。基类负责通用的 Excel 读取和基础校验，子类负责具体的列映射和业务校验。
- **权衡考量**: 提高了代码复用性，统一了文件解析的标准和错误处理机制。

---

**⚠️ 注意**:
- 本架构设计基于实际代码中的 `facade.service`, `message.handler` 和 `file.parser` 目录结构分析得出。
- 异步处理依赖于系统的消息队列服务（Kafka/Redis），开发时需确保消息服务的可用性。
