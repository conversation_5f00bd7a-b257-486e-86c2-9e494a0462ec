# 批量处理模块开发指南

## 📖 文档导航

- **[模块概览](../README.md)** | **[架构设计](module-architecture.md)** | **[业务流程](workflow.md)** | **当前：开发指南**

## 🎯 开发关注点

### 1. 新增批量业务类型

如果你需要增加一种新的批量任务（例如"供应商批量导入"），请遵循以下步骤：

1.  **定义枚举**: 在 `BatchBusinessTypeEnums` 中添加新的类型枚举值。
2.  **实现 FileParser**: 继承 `FileParserAbstract`，实现 `SupplierImportFileParser`，定义 Excel 列映射和校验规则。
3.  **注册 Parser**: 在 `FileParserService` 或 `BatchModule` 中注册新的 Parser。
4.  **实现 Processor**: 实现新的 Processor 类（如 `SupplierImportProcessor`），处理单条数据的业务逻辑。
5.  **注册 Processor**: 在 `BatchMessageHandlerDefault` (如果是导入) 或其他 Handler 中注册该 Processor，使其能被正确路由。

### 2. 处理大文件与内存管理

- **流式处理**: 在导出大量数据时，**严禁**一次性将所有数据加载到内存中。请使用 TypeORM 的 `stream()` 方法或分页查询（Cursor Pagination）来获取数据。
- **Excel 生成**: 使用 `exceljs` 的流式写入功能（WorkbookWriter），边查询边写入，防止 Node.js 进程 OOM（内存溢出）。

### 3. 异步任务的异常捕获

- **Global Catch**: 在 Processor 的 `process` 方法中，必须包裹 try-catch 块。
- **状态更新**: 即使发生未知异常，也**必须**将 Job 状态更新为 `Error`，并记录错误信息，否则任务会一直处于 `Processing` 状态卡死。
- **日志记录**: 使用 `this.logger.error` 记录完整的堆栈信息，方便排查。

## ⚠️ 关键开发陷阱

### 1. 事务与长耗时任务

- **陷阱**: 在一个大事务中处理所有批量数据。
- **后果**: 数据库锁等待超时，阻塞其他业务操作。
- **正确做法**: 批量任务**不应该**开启大事务。应该单条处理单条提交，或者分小批（如 100 条）提交。如果某条失败，仅该条标记为失败，不回滚已成功的记录。

### 2. 消息队列的幂等性

- **陷阱**: 假设消息只会被消费一次。
- **后果**: 网络抖动可能导致消息重复投递，导致数据重复插入。
- **正确做法**: 在 Processor 中实现幂等逻辑。例如在插入前检查 `uniqueId` 或 `batchId + companyId` 是否已存在。

### 3. Excel 模板的列名匹配

- **陷阱**: 硬编码列索引（如 `row[0]`, `row[1]`）。
- **后果**: 用户上传的 Excel 列顺序稍有变化（如调整了列位置），解析就会出错。
- **正确做法**: 必须根据列头名称（Header Name）来匹配数据。`FileParserAbstract` 已提供基础支持，请确保子类正确配置列名映射。

---

**⚠️ 注意**:
- 修改 `BatchEntity` 或 `BatchJobEntity` 结构时，请务必评估对历史数据的影响，因为批量任务表的数据量通常非常大。
