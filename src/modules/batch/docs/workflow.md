# 批量处理模块业务流程

## 📖 文档导航

- **[模块概览](../README.md)** | **[架构设计](module-architecture.md)** | **当前：业务流程** | **[开发指南](development-guide.md)**

## 🔄 核心业务流程

### 1. 批量导入/匹配流程

此流程适用于客商导入、黑名单导入、监控导入等场景。

```mermaid
sequenceDiagram
    participant User as 用户
    participant Controller as BatchController
    participant Service as BatchFacadeService
    participant Parser as FileParserService
    participant DB as Database
    participant MQ as MessageQueue
    participant Handler as MessageHandler

    User->>Controller: 上传文件 (createBatchByFile)
    Controller->>Service: 调用 createBatchByFile
    Service->>Parser: 解析文件 (getParseResult)
    Parser-->>Service: 返回解析结果 (Parsed Records)
    Service->>DB: 创建 BatchEntity (Status: Pending)
    Service->>DB: 创建 BatchMatchCompanyItemEntity (待匹配项)
    Service->>MQ: 发送任务消息
    Service-->>User: 返回 Batch ID

    Note right of MQ: 异步处理开始

    MQ->>Handler: 消费消息
    Handler->>Handler: 匹配公司信息 (Match Company)
    Handler->>DB: 更新 BatchMatchCompanyItemEntity (匹配结果)
    Handler->>DB: 更新 BatchEntity (Status: Done/Error)
```

### 2. 批量尽调排查流程

此流程适用于对已匹配或已存在的企业列表发起批量风险排查。

```mermaid
sequenceDiagram
    participant User as 用户
    participant Controller as BatchDiligenceController
    participant Service as BatchCreationService
    participant DB as Database
    participant MQ as MessageQueue
    participant Processor as DiligenceProcessor
    participant DiligenceMod as DiligenceModule

    User->>Controller: 发起批量排查 (createBatchDiligenceTask)
    Controller->>Service: 校验配额 (Check Quota)
    Service->>DB: 创建 BatchEntity & BatchJobEntity
    Service->>MQ: 发送排查任务消息
    Service-->>User: 返回任务已创建

    MQ->>Processor: 消费消息
    loop 遍历每个 Job
        Processor->>DiligenceMod: 调用单条排查服务 (analyze)
        DiligenceMod-->>Processor: 返回排查结果
        Processor->>DB: 更新 BatchJobEntity 状态
    end
    Processor->>DB: 更新 BatchEntity 统计信息和状态
```

### 3. 批量数据导出流程

此流程用于异步导出大量数据，避免阻塞 Web 请求。

```mermaid
sequenceDiagram
    participant User as 用户
    participant Controller as BatchExportController
    participant Service as BatchCreationService
    participant MQ as MessageQueue
    participant Processor as ExportProcessor
    participant OSS as ObjectStorage

    User->>Controller: 请求导出 (createExportBatchEntity)
    Controller->>Service: 创建导出任务
    Service->>MQ: 发送导出指令
    Service-->>User: 返回任务 ID

    MQ->>Processor: 消费导出指令
    Processor->>DB: 分页查询业务数据
    Processor->>Processor: 生成 Excel/CSV 文件
    Processor->>OSS: 上传文件
    OSS-->>Processor: 返回下载链接
    Processor->>DB: 更新任务状态和下载链接
    Processor->>User: 发送完成通知 (邮件/站内信)
```

## 📋 关键业务规则

### 1. 配额限制规则
- **批量排查配额**: 在创建批量尽调任务前，必须校验用户/组织的剩余配额。如果配额不足，拒绝创建任务。
- **并发限制**: 同一用户或组织在同一时间段内允许运行的批量任务数量有限制，防止资源耗尽。

### 2. 文件解析规则
- **模板校验**: 上传文件必须符合预定义的模板格式（列头名称必须匹配）。
- **数据行数限制**: 单次上传的文件行数通常限制在 5000-10000 行，超过限制需分批上传。

### 3. 幂等性与重试
- **任务重试**: 失败的任务支持手动重试。重试时，仅重新处理状态为 "Failed" 的子任务，跳过已成功的任务。
- **状态流转**: 任务状态流转必须严格遵守 `Pending -> Processing -> Done/Error` 的顺序，不可逆。

---

**⚠️ 注意**:
- 流程图中的消息队列 (MQ) 处理是异步的，用户在前端看到的进度条是通过轮询 `BatchEntity` 的状态和统计字段实现的。
- 所有的批量操作都必须记录操作日志 (OpLog)，以便审计。
