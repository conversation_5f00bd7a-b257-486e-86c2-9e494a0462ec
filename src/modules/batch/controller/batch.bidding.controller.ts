import { BadRequestException, Body, Controller, Post, Query, Request, UploadedFile, UseFilters, UseGuards, UseInterceptors } from '@nestjs/common';
import { ApiCookieAuth, ApiOperation, ApiParam, ApiTags } from '@nestjs/swagger';
import { BatchFacadeService } from '../facade.service/batch.facade.service';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { FileInterceptor } from '@nestjs/platform-express';
import { defaultFileUploadOptions } from '@commons/file/config';
import { BatchBusinessTypeEnums } from '@domain/enums/batch/BatchBusinessTypeEnums';
import { FileSizeLimitExceptionFilter } from '@commons/exceptions/FileSizeLimitExceptionFilter';
import { SearchBiddingBatchResultRequest } from '@domain/dto/batch/request/SearchBiddingBatchResultRequest';
import { RoverSessionGuard } from '@core/guards/RoverSession.guard';
import { RoverRolesGuard } from '@core/guards/rover.roles.guard';
import { RoverUserModel } from '@domain/model/RoverUserModel';
import { ExportConditionRequest } from '@domain/dto/batch/export/ExportConditionRequest';
import { ExportBatchReportRequest } from '@domain/dto/batch/export/ExportBatchReportRequest';
import { SearchDiligenceBiddingRequest } from '@domain/dto/bidding/SearchDiligenceBiddingRequest';
import { RoverExceptions } from '@commons/constants/exceptionConstants';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import Redis from 'ioredis';

@Controller('batch')
@ApiTags('批量招标排查')
@ApiCookieAuth()
@UseGuards(RoverSessionGuard, RoverRolesGuard)
export class BatchBiddingController {
  private readonly logger: Logger = QccLogger.getLogger(BatchBiddingController.name);
  private readonly redisClient: Redis;
  constructor(private readonly batchFacadeService: BatchFacadeService, private readonly redisService: RedisService) {
    this.redisClient = this.redisService.getClient();
  }

  // ************************************************** 导入类型 start **************************************************
  @Post('/import/bidding/excel')
  @UseFilters(FileSizeLimitExceptionFilter)
  @UseInterceptors(FileInterceptor('file', { ...defaultFileUploadOptions }))
  @ApiParam({ name: 'file', type: 'file', description: 'excel上传' })
  @ApiOperation({ summary: '上传excel文件并创建 批量招标排查任务' })
  async createBatchBiddingJob(@UploadedFile() file, @Request() req, @Query('fileName') fileName: string, @Query('settingId') settingId?: number) {
    return this.batchFacadeService.createBatchByFile(
      req.user,
      file.path,
      fileName || file.originalname,
      BatchBusinessTypeEnums.Bidding_Diligence_File,
      settingId,
    );
  }

  @Post('/import/bidding/diligence_check')
  @UseFilters(FileSizeLimitExceptionFilter)
  @UseInterceptors(FileInterceptor('file', { ...defaultFileUploadOptions }))
  @ApiParam({ name: 'file', type: 'file', description: 'excel上传' })
  @ApiOperation({ summary: '批量招标排查任务的额度检查' })
  async checkBatchBiddingJob(@UploadedFile() file, @Request() req, @Query('settingId') settingId?: number) {
    return this.batchFacadeService.checkBatchBiddingDiligenceLimit(req.user, file.path, settingId);
  }

  @Post('/import/bidding/statistics')
  @ApiOperation({ summary: '查询批量招标排查统计信息' })
  async searchBiddingStatistics(@Request() req, @Body() data: SearchBiddingBatchResultRequest) {
    return this.batchFacadeService.getBiddingStatistics(req.user, data);
  }

  @Post('/import/bidding/detail')
  @ApiOperation({ summary: '查询批量招标排查详情' })
  async searchBiddingDetail(@Request() req, @Body() data: SearchBiddingBatchResultRequest) {
    return this.batchFacadeService.searchBiddingDetail(req.user, data);
  }

  // ************************************************** 导入类型 end **************************************************

  // ************************************************** 导出类型 start **************************************************

  @Post('export/tender_report_pdf')
  @ApiOperation({ description: '导出招标排查PDF' })
  async tenderExport(@Query('id') id: number, @Request() req) {
    const currentUser: RoverUserModel = req.user;
    const condition: ExportConditionRequest = { diligenceId: id };
    const cacheKey = `request:exportBiddingPdf:${currentUser.userId}:${id}`;
    const cacheValue = await this.redisClient.get(cacheKey);
    if (!cacheValue) {
      // 网关默认超时重试时间为 31s, 设置35s过期时间，防止网关超时重试时
      await this.redisClient.setex(cacheKey, 35, id.toString());
    }
    if (cacheValue == id.toString()) {
      // 重复请求
      throw new BadRequestException(RoverExceptions.Common.Request.Duplicated);
    }
    return this.batchFacadeService.createExportBatchEntity(currentUser, BatchBusinessTypeEnums.Tender_Report_Export, condition);
  }

  @Post('export/tender_diligence_record')
  @ApiOperation({ description: '导出批量批量招标排查统计以及列表' })
  async batchTenderExportDiligenceRecord(@Request() req, @Body() data: ExportBatchReportRequest) {
    const currentUser: RoverUserModel = req.user;
    Object.assign(data, { orgId: currentUser.currentOrg });
    return this.batchFacadeService.createExportBatchEntity(currentUser, BatchBusinessTypeEnums.Tender_Diligence_Record_Export, data);
  }

  @Post('export/tender_dimension_detail')
  @ApiOperation({ description: '导出批量招标排查维度详情' })
  async tenderExportDimensionDetail(@Request() req, @Body() data: SearchDiligenceBiddingRequest) {
    const currentUser: RoverUserModel = req.user;
    Object.assign(data, { orgId: currentUser.currentOrg });
    return this.batchFacadeService.createExportBatchEntity(currentUser, BatchBusinessTypeEnums.Tender_Dimension_Detail_Export, data);
  }

  @Post('export/tender_all_dimension_detail')
  @ApiOperation({ description: '导出批量招标排查维度详情' })
  async tenderExportAllDimensionDetail(@Request() req, @Body() data: SearchDiligenceBiddingRequest) {
    const currentUser: RoverUserModel = req.user;
    Object.assign(data, { orgId: currentUser.currentOrg });
    return this.batchFacadeService.createExportBatchEntity(currentUser, BatchBusinessTypeEnums.Tender_All_Dimension_Detail_Export, data);
  }

  @Post('export/tender_diligence_history')
  @ApiOperation({ description: '导出招标排查历史记录列表' })
  async tenderExportDiligenceHistory(@Request() req, @Body() data: SearchDiligenceBiddingRequest) {
    const currentUser: RoverUserModel = req.user;
    Object.assign(data, { orgId: currentUser.currentOrg });
    return this.batchFacadeService.createExportBatchEntity(currentUser, BatchBusinessTypeEnums.Tender_Diligence_History_Export, data);
  }

  // ************************************************** 导出类型 end **************************************************
}
