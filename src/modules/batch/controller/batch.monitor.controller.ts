import { Body, Controller, ParseBoolPipe, ParseIntPipe, Post, Put, Query, Request, UploadedFile, UseFilters, UseGuards, UseInterceptors } from '@nestjs/common';
import { ApiCookieAuth, ApiOperation, ApiParam, ApiTags } from '@nestjs/swagger';
import { BatchFacadeService } from '../facade.service/batch.facade.service';
import { FileInterceptor } from '@nestjs/platform-express';
import { defaultFileUploadOptions } from '@commons/file/config';
import { BatchBusinessTypeEnums } from '@domain/enums/batch/BatchBusinessTypeEnums';
import { FileSizeLimitExceptionFilter } from '@commons/exceptions/FileSizeLimitExceptionFilter';
import { SearchMatchCompanyRequest } from '@domain/dto/batch/request/SearchMatchCompanyRequest';
import { UpdateMatchCompanyRequest } from '@domain/dto/batch/request/UpdateMatchCompanyRequest';
import { BatchRemoveRequest } from '@domain/dto/batch/request/BatchRemoveRequest';
import { RoverSessionGuard } from '@core/guards/RoverSession.guard';
import { RoverRolesGuard } from '@core/guards/rover.roles.guard';
import { RiskDynamicsSearchRequest } from '@domain/model/monitor/request/RiskDynamicsSearchRequest';
import { RoverUserModel } from '@domain/model/RoverUserModel';
import { SearchCustomerNewsRequest } from '@domain/dto/customer/SearchCustomerNewsRequest';
import { SearchCompanyRequest } from '@domain/model/monitor/request/SearchCompanyRequest';

@Controller('batch')
@ApiTags('批量导入处理')
@ApiCookieAuth()
@UseGuards(RoverSessionGuard, RoverRolesGuard)
export class BatchMonitorController {
  constructor(private readonly batchFacadeService: BatchFacadeService) {}

  // ************************************************** 导入类型 start **************************************************
  @Post('/import/monitor/excel')
  @UseFilters(FileSizeLimitExceptionFilter)
  @UseInterceptors(FileInterceptor('file', { ...defaultFileUploadOptions }))
  @ApiParam({ name: 'file', type: 'file', description: 'excel上传' })
  @ApiOperation({ summary: '上传excel文件并创建 合作监控导入任务' })
  @ApiTags('合作监控导入任务')
  async createBatchMonitorJob(@UploadedFile() file, @Request() req, @Query('fileName') fileName: string) {
    return this.batchFacadeService.createBatchByFile(req.user, file.path, fileName || file.originalname, BatchBusinessTypeEnums.Monitor_File);
  }

  @Post('/import/monitor/excel/parse')
  @UseFilters(FileSizeLimitExceptionFilter)
  @UseInterceptors(FileInterceptor('file', { ...defaultFileUploadOptions }))
  @ApiParam({ name: 'file', type: 'file', description: 'excel上传' })
  @ApiOperation({ summary: '上传合作监控excel文件，匹配公司' })
  async matchBatchMonitorCompany(@UploadedFile() file, @Request() req, @Query('fileName') fileName: string) {
    return this.batchFacadeService.matchBatchCompany(req.user, file.path, fileName || file.originalname, BatchBusinessTypeEnums.Monitor_File);
  }

  @Post('/import/monitor/excel/item/search')
  @ApiOperation({ summary: '分页查询上传合作监控excel文件匹配的公司' })
  async searchMonitorMatchCompany(@Request() req, @Body() body: SearchMatchCompanyRequest) {
    return this.batchFacadeService.searchMatchCompanyRequest(req.user, body);
  }

  @Post('/import/monitor/excel/item')
  @ApiOperation({ summary: 'post 删除上传合作监控excel文件匹配的公司' })
  async deleteMatchMonitorCompany(@Request() req, @Body() data: BatchRemoveRequest) {
    return this.batchFacadeService.deleteMatchCompany(req.user, data);
  }

  @Put('/import/monitor/excel/item')
  @ApiOperation({ summary: '更新上传合作监控excel文件匹配的公司' })
  async updateMatchMonitorCompany(@Request() req, @Query('itemId', ParseIntPipe) itemId: number, @Body() body: UpdateMatchCompanyRequest) {
    return this.batchFacadeService.updateMatchCompany(req.user, itemId, body);
  }

  @Post('/import/monitor/excel/execute')
  @ApiOperation({ summary: '上传合作监控excel文件，创建合作监控导入任务' })
  async executeBatchMonitorCompany(@Request() req, @Query('batchId', ParseIntPipe) batchId: number, @Query('isUpdate', ParseBoolPipe) isUpdate: boolean) {
    return this.batchFacadeService.executeBatchImport(req.user, batchId, BatchBusinessTypeEnums.Monitor_File, isUpdate);
  }
  // ************************************************** 导入类型 end **************************************************

  // ************************************************** 导出类型 start **************************************************

  @Post('export/risk')
  @ApiOperation({ summary: '风险动态列表导出' })
  async getRiskExport(@Request() req, @Body() data: RiskDynamicsSearchRequest) {
    const currentUser: RoverUserModel = req.user;
    return this.batchFacadeService.createExportBatchEntity(currentUser, BatchBusinessTypeEnums.Risk_Export, data);
  }

  @Post('export/sentiment')
  @ApiOperation({ summary: '舆情动态列表导出' })
  async getSentimentExport(@Request() req, @Body() data: SearchCustomerNewsRequest) {
    const currentUser: RoverUserModel = req.user;
    return this.batchFacadeService.createExportBatchEntity(currentUser, BatchBusinessTypeEnums.Sentiment_Export, data);
  }

  @Post('export/monitor/companyList')
  @ApiOperation({ summary: '监控企业列表导出' })
  async getCompanyListExport(@Request() req, @Body() data: SearchCompanyRequest) {
    const currentUser: RoverUserModel = req.user;
    return this.batchFacadeService.createExportBatchEntity(currentUser, BatchBusinessTypeEnums.Monitor_Company_Export, data);
  }
  // ************************************************** 导出类型 end **************************************************
}
