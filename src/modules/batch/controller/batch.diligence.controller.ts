import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Request,
  UploadedFile,
  UseFilters,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiCookieAuth, ApiOkResponse, ApiOperation, ApiParam, ApiTags } from '@nestjs/swagger';
import { BatchFacadeService } from '../facade.service/batch.facade.service';
import { FileInterceptor } from '@nestjs/platform-express';
import { defaultFileUploadOptions } from '@commons/file/config';
import { BatchBusinessTypeEnums } from '@domain/enums/batch/BatchBusinessTypeEnums';
import { BatchDiligenceRequest } from '@domain/model/batch/po/parse/ParsedRecordBase';
import { FileSizeLimitExceptionFilter } from '@commons/exceptions/FileSizeLimitExceptionFilter';
import { SearchMatchCompanyRequest } from '@domain/dto/batch/request/SearchMatchCompanyRequest';
import { UpdateMatchCompanyRequest } from '@domain/dto/batch/request/UpdateMatchCompanyRequest';
import { BatchTypeEnums } from '@domain/enums/batch/BatchTypeEnums';
import { BatchRemoveRequest } from '@domain/dto/batch/request/BatchRemoveRequest';
import { RoverSessionGuard } from '@core/guards/RoverSession.guard';
import { RoverRolesGuard } from '@core/guards/rover.roles.guard';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { SearchBatchResponseItemDTO } from '@domain/dto/batch/response/SearchBatchResponseItemDTO';
import { Cacheable } from '@type-cacheable/core';
import { useIoRedisAdapter } from 'type-cacheable';
import { createHash } from 'node:crypto';
import { reduce, keys } from 'lodash';
import { SearchBatchResultRequest } from '@domain/dto/batch/request/SearchBatchResultRequest';
import { RoverUserModel } from '@domain/model/RoverUserModel';
import { SearchCustomerDiligenceRequest } from '@domain/model/customer/analyze/SearchCustomerDiligenceRequest';
import { ExportConditionRequest } from '@domain/dto/batch/export/ExportConditionRequest';
import { DiligenceBatchExportRequest } from '@domain/model/monitor/request/DiligenceBatchExportRequest';
import { ExportBatchReportRequest } from '@domain/dto/batch/export/ExportBatchReportRequest';
import { DiligenceHistoryRequest } from '@domain/model/diligence/pojo/history/DiligenceHistoryRequest';
import Redis from 'ioredis';
import { RoverExceptions } from '@commons/constants/exceptionConstants';

@Controller('batch/')
@ApiTags('批量导入处理')
@ApiCookieAuth()
@UseGuards(RoverSessionGuard, RoverRolesGuard)
export class BatchDiligenceController {
  private readonly redisClient: Redis;

  constructor(private readonly batchFacadeService: BatchFacadeService, private readonly redisService: RedisService) {
    //@ts-ignore
    useIoRedisAdapter(this.redisService.getClient());
    this.redisClient = this.redisService.getClient();
  }

  // ************************************************** 导入类型 start **************************************************
  @Post('import/detail')
  @ApiOperation({ summary: '获取指定批量任务详情' })
  @ApiOkResponse({ type: SearchBatchResponseItemDTO })
  @ApiTags('准入排查/批量-获取指定批量任务详情')
  @Cacheable({
    cacheKey: (args): string => {
      const body = args[1];
      const cacheKey = reduce(keys(body).sort(), (acc, key) => acc.concat([`${key}:${body[key]}`]), []);
      const hashKey = createHash('md5').update(cacheKey.join('-')).digest('hex');
      return `cache:batch:import:detail:${hashKey}`;
    },
    ttlSeconds: 60 * 60 * 24, // 缓存24小时
  })
  async getBatch(@Request() req, @Body() data: SearchBatchResultRequest) {
    return this.batchFacadeService.getBatch(req.user, data);
  }

  @Post('import/detail/company')
  @ApiOperation({ summary: '获取批量任务详情公司命中详情' })
  @ApiOkResponse({ type: SearchBatchResponseItemDTO })
  @ApiTags('准入排查/批量-获取批量任务详情公司命中详情')
  async getCompanyDimensionDetail(@Request() req, @Body() data: SearchBatchResultRequest) {
    return this.batchFacadeService.getCompanyDimensionDetail(req.user.currentOrg, data);
  }

  @Post('import/diligence/excel')
  @UseFilters(FileSizeLimitExceptionFilter)
  @UseInterceptors(FileInterceptor('file', { ...defaultFileUploadOptions }))
  @ApiParam({ name: 'file', type: 'file', description: 'excel上传' })
  @ApiOperation({ summary: '批量排查，上传excel文件，匹配公司' })
  async matchBatchDiligenceCompany(@UploadedFile() file, @Request() req, @Query('fileName') fileName: string) {
    return this.batchFacadeService.matchBatchDiligenceCompany(req.user, file.path, fileName || file.originalname);
  }

  @Post('import/diligence/excel/item/search')
  @ApiOperation({ summary: '分页查询上传excel文件匹配的公司' })
  async searchMatchCompany(@Request() req, @Body() body: SearchMatchCompanyRequest) {
    return this.batchFacadeService.searchMatchCompanyRequest(req.user, body);
  }

  @Delete('import/diligence/excel/item')
  @ApiOperation({ summary: '删除上传excel文件匹配的公司' })
  async deleteMatchCompany(@Request() req, @Body() data: BatchRemoveRequest) {
    return this.batchFacadeService.deleteMatchCompany(req.user, data);
  }

  @Post('import/diligence/excel/item')
  @ApiOperation({ summary: 'post 删除上传excel文件匹配的公司' })
  async deleteMatchCompany2(@Request() req, @Body() data: BatchRemoveRequest) {
    return this.batchFacadeService.deleteMatchCompany(req.user, data);
  }

  @Put('import/diligence/excel/item')
  @ApiOperation({ summary: '更新上传excel文件匹配的公司' })
  async updateMatchCompany(@Request() req, @Query('itemId', ParseIntPipe) itemId: number, @Body() body: UpdateMatchCompanyRequest) {
    return this.batchFacadeService.updateMatchCompany(req.user, itemId, body);
  }

  @Post('import/diligence/excel/execute')
  @ApiOperation({ summary: '批量排查，上传excel文件，执行排查' })
  async executeBatchDiligenceCompany(@Request() req, @Query('batchId', ParseIntPipe) batchId: number, @Query('settingId') settingId?: number) {
    return this.batchFacadeService.executeBatchByBatchMatchCompanyId(req.user, batchId, BatchBusinessTypeEnums.Diligence_File, settingId);
  }

  @Post('import/diligence/data')
  @ApiOperation({ summary: '通过公司ID和名称创建 批量尽职调查任务' })
  @ApiTags('准入排查/批量')
  async createDiligenceCompanyIdJob(@Body() body: BatchDiligenceRequest, @Request() req) {
    const { settingId } = body;
    return this.batchFacadeService.createBatchDiligenceTask(req.user, body.data, BatchBusinessTypeEnums.Diligence_ID, BatchTypeEnums.Import, { settingId });
  }

  @Post('import/diligence/customer')
  @ApiOperation({ summary: '客商列表里选择公司ID创建 批量尽职调查任务' })
  @ApiTags('准入排查/批量')
  async createDiligenceCustomerIdJob(@Body() body: BatchDiligenceRequest, @Request() req) {
    const { settingId } = body;
    return this.batchFacadeService.createBatchDiligenceTask(req.user, body.data, BatchBusinessTypeEnums.Diligence_Customer, BatchTypeEnums.Import, {
      settingId,
    });
  }
  // ************************************************** 导入类型 end **************************************************

  // ************************************************** 导出类型 start **************************************************

  @Post('export/diligence_batch_detail')
  @ApiOperation({ summary: '导出批量任务详情' })
  async getDetailExport(@Request() req, @Body() data: SearchBatchResultRequest) {
    const currentUser: RoverUserModel = req.user;
    return this.batchFacadeService.createExportBatchEntity(currentUser, BatchBusinessTypeEnums.Diligence_Batch_Detail, data);
  }

  @Post('export/dimension_detail')
  @ApiOperation({ summary: '导出批量任务维度详情' })
  async getDimensionDetailExport(@Request() req, @Body() data: SearchBatchResultRequest) {
    const currentUser: RoverUserModel = req.user;
    return this.batchFacadeService.createExportBatchEntity(currentUser, BatchBusinessTypeEnums.Dimension_Detail_Export, data);
  }

  @Post('export/analyze_record')
  @ApiOperation({ summary: '导出风险巡检结果' })
  async getAnalyzeRecordExport(@Request() req, @Body() data: SearchCustomerDiligenceRequest) {
    const currentUser: RoverUserModel = req.user;
    return this.batchFacadeService.createExportBatchEntity(currentUser, BatchBusinessTypeEnums.Analyze_Record_Export, data);
  }

  @Post('export/analyze_dimension_detail')
  @ApiOperation({ summary: '导出巡检批量任务维度详情' })
  async getAnalyzeDimensionDetailExport(@Request() req, @Body() data: SearchBatchResultRequest) {
    const currentUser: RoverUserModel = req.user;
    return this.batchFacadeService.createExportBatchEntity(currentUser, BatchBusinessTypeEnums.Analyze_Dimension_Detail, data);
  }

  @Post('export/diligence_record')
  @ApiOperation({ summary: '排查记录导出' })
  async getDiligenceExport(@Request() req, @Body() data: DiligenceHistoryRequest) {
    const currentUser: RoverUserModel = req.user;
    return this.batchFacadeService.createExportBatchEntity(currentUser, BatchBusinessTypeEnums.Diligence_Record, data);
  }

  @Post('export/diligence_pdf/:diligenceId')
  @ApiOperation({ description: '排查报告导出pdf任务' })
  async diligenceExport(@Param('diligenceId', ParseIntPipe) diligenceId: number, @Request() req) {
    const currentUser: RoverUserModel = req.user;

    // 检查排查是否已生成快照
    const isGenneratedSnapshot = await this.batchFacadeService.isGenneratedSnapshot(currentUser, diligenceId);
    if (!isGenneratedSnapshot) {
      throw new BadRequestException(RoverExceptions.Diligence.Snapshot.NotGenerated);
    }
    const cacheKey = `request:exportDiligencePdf:${currentUser.userId}:${diligenceId}`;
    const cacheValue = await this.redisClient.get(cacheKey);
    if (!cacheValue) {
      // 网关默认超时重试时间为 31s, 设置35s过期时间，防止网关超时重试时
      await this.redisClient.setex(cacheKey, 35, diligenceId.toString());
    }
    if (cacheValue == diligenceId.toString()) {
      // 重复请求
      throw new BadRequestException(RoverExceptions.Common.Request.Duplicated);
    }
    const condition: ExportConditionRequest = { diligenceId };
    try {
      return this.batchFacadeService.createExportBatchEntity(currentUser, BatchBusinessTypeEnums.Diligence_Report_Export, condition);
    } catch (error) {
      if (error instanceof BadRequestException && error.message == RoverExceptions.Diligence.Snapshot.NotGenerated.error) {
        // 快照尚未生成，导出任务未创建，删除任务已提交缓存
        await this.redisClient.del(cacheKey);
      }
      throw error;
    }
  }

  @Post('export/batch_diligence_pdfs')
  @ApiOperation({ description: '排查报告批量导出pdf任务,并生成zip压缩包' })
  async diligenceBatchExport(@Body() data: DiligenceBatchExportRequest, @Request() req) {
    const currentUser: RoverUserModel = req.user;
    return this.batchFacadeService.createExportBatchEntity(
      currentUser,
      BatchBusinessTypeEnums.Diligence_Report_Batch_Export,
      Object.assign(new ExportBatchReportRequest(), data),
    );
  }

  // ************************************************** 导出类型 end **************************************************
}
