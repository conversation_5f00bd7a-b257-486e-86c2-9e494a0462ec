/**
 * @file batchExportQueue 队列消息处理
 */
import { BadRequestException, Injectable } from '@nestjs/common';
import { KzzConsumerConfig, RabbitMQ } from '@kezhaozhao/message-queue';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { InjectRepository } from '@nestjs/typeorm';
import { BatchEntity } from '@domain/entities/BatchEntity';
import { In, Repository } from 'typeorm';
import { BatchResultExportService } from '../../facade.service/service/batch.result.export.service';
import { CounterOperation } from '@domain/constants/common';
import { BatchConstants } from '../../common/batch.constants';
import { JobProcessCostPO } from '@domain/model/batch/po/JobProcessCostPO';
import * as Bluebird from 'bluebird';
import { BatchStatusEnums } from '@domain/enums/batch/BatchStatusEnums';
import { BatchBusinessMessageTitle, BatchBusinessTypeEnums } from '@domain/enums/batch/BatchBusinessTypeEnums';
import { MessageBody } from '@domain/model/message/MessageBody';
import { MsgType } from '@domain/model/message/MessageRequest';
import { BatchExportMessagePO, BatchMessageCondition } from '@domain/model/batch/po/message/BatchExportMessagePO';
import { tmpdir } from 'os';
import { BatchStatisticsBasePO } from '@domain/model/batch/po/BatchStatisticsBasePO';
import * as moment from 'moment';
import * as fs from 'fs';
import * as path from 'path';
import { RoverBundleCounterType, RoverBundleLimitationType } from '@kezhaozhao/saas-bundle-service';
import { BatchBundleService } from '../../facade.service/service/batch.bundle.service';
import { random } from 'lodash';
import { DiligenceHistoryEntity } from '@domain/entities/DiligenceHistoryEntity';
import { SnapshotStatus } from '@domain/model/diligence/pojo/model/SnapshotDetail';
import axios from 'axios';
import NameCounter from '@commons/utils/name-counter';
import * as archiver from 'archiver';
import { CustomerExportProcessor } from './processors/customer.export.processor';
import { DiligenceExportProcessor } from './processors/diligence.export.processor';
import { DimensionDetailsExportProcessor } from './processors/dimension.details.export.processor';
import { InnerBlacklistExportProcessor } from './processors/inner.blacklist.export.processor';
import { PersonExportProcessor } from './processors/person.export.processor';
import { RiskExportProcessor } from './processors/risk.export.processor';
import { SentimentExportProcessor } from './processors/sentiment.export.processor';
import { TenderExportProcessor } from './processors/tender.export.processor';
import { ExportProcessorBase } from './export.processor.base';
import { BatchMessageHandlerAbstract } from '../batch.message.handler.abstract';
import { QueueService } from '@core/config/queue.service';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { ExportConditionBase } from '@domain/model/batch/export/ExportRecordModel';
import { SpecificExportProcessor } from './processors/specific.export.processor';
import { TenderDimensionExportProcessor } from './processors/tender.dimension.export.processor';
import { BundleConsumeDetailExportProcessor } from './processors/bundle.consume.detail.export.processor';
import { RoverUserModel } from '@domain/model/RoverUserModel';
import { MonitorExportProcessor } from './processors/monitor.export.processor';

@Injectable()
export class BatchMessageHandlerExport extends BatchMessageHandlerAbstract {
  public batchExportQueue: RabbitMQ;
  protected readonly logger: Logger = QccLogger.getLogger(BatchMessageHandlerExport.name);

  constructor(
    protected readonly queueService: QueueService,
    protected readonly redisService: RedisService,
    @InjectRepository(DiligenceHistoryEntity) private readonly diligenceHistoryRepo: Repository<DiligenceHistoryEntity>,
    protected readonly resultExportService: BatchResultExportService,
    private readonly batchBundleService: BatchBundleService,
    private readonly customerProcessor: CustomerExportProcessor,
    private readonly diligenceProcessor: DiligenceExportProcessor,
    private readonly dimensionDetailProcessor: DimensionDetailsExportProcessor,
    private readonly innerBlacklistProcessor: InnerBlacklistExportProcessor,
    private readonly personProcessor: PersonExportProcessor,
    private readonly riskProcessor: RiskExportProcessor,
    private readonly sentimentProcessor: SentimentExportProcessor,
    private readonly tenderProcessor: TenderExportProcessor,
    private readonly tenderDimensionProcessor: TenderDimensionExportProcessor,
    private readonly specificProcessor: SpecificExportProcessor,
    private readonly bundleConsumeDetailExportProcessor: BundleConsumeDetailExportProcessor,
    private readonly monitorExportProcessor: MonitorExportProcessor,
  ) {
    super(queueService, redisService);
    // 注册处理器
    Array.prototype.push.apply(this.allProcessor, [
      this.customerProcessor,
      this.diligenceProcessor,
      this.dimensionDetailProcessor,
      this.innerBlacklistProcessor,
      this.personProcessor,
      this.riskProcessor,
      this.sentimentProcessor,
      this.tenderProcessor,
      this.tenderDimensionProcessor,
      this.specificProcessor,
      this.bundleConsumeDetailExportProcessor,
      this.monitorExportProcessor,
    ]);
    this.batchExportQueue = this.queueService.batchExportQueue;
    this.batchExportQueue
      .consume(this.handleJobMessage.bind(this), Object.assign(new KzzConsumerConfig(), { concurrency: BatchConstants.Consumer.Others }))
      .catch((err) => this.logger.error(err));
    this.batchExportQueue.on('failed', this.onPulsarQueueError.bind(this));
  }

  getProperlyProcessor(businessType: BatchBusinessTypeEnums): ExportProcessorBase {
    return super.getProperlyProcessor(businessType) as ExportProcessorBase;
  }

  // async onPulsarQueueError(messageData: BatchExportMessagePO, error) {
  //   const { batchId, targetId, jobId } = messageData;
  //   this.logger.error(`onBatchExportQueueError(): batchId=${batchId} ,targetId=${targetId},jobId=${jobId} ,error=${error.message}`);
  //   this.logger.error(error);
  //   captureException(new PulsarError(`onBatchExportQueueError() error:${error?.message}`, error), {
  //     extra: {
  //       messageData,
  //     },
  //   });
  // }

  async processExportTask(message: BatchExportMessagePO<BatchMessageCondition>) {
    const { batchId, businessType, jobId, condition, orgId, operatorId } = message;
    //1. 标记job 处理中
    try {
      // 标记batch 完成，统计最新的信息
      const statisticsInfo = new BatchStatisticsBasePO();
      const partialUpdate: any = { statisticsInfo };
      this.logger.info(`export processJobMessage: batchId=${batchId},jobId=${jobId}`);
      const jobCost: JobProcessCostPO = new JobProcessCostPO();
      const jobStartTime = Date?.now();
      // 1.判断当前关联的batch是否存在或者已经失败 2.判断当前job是否还在等待中, 防止重复处理
      const [job, batchEntity] = await Bluebird.all([this.batchJobRepo.findOne({ where: { jobId } }), this.batchRepo.findOne({ where: { batchId } })]);
      if (!batchEntity || batchEntity.status === BatchStatusEnums.Error) {
        await this.batchJobRepo.update(jobId, {
          status: BatchStatusEnums.Error,
          comment: 'batch不存在或者已经失败',
          errorDate: new Date(),
        });
        return '';
      }

      // 导出排查报告，如果快照数据未生成，进行重试或直接失败
      switch (businessType) {
        case BatchBusinessTypeEnums.Diligence_Report_Export: {
          const { diligenceId } = condition;
          this.logger.info(`Diligence_Report_Export:diligenceId:${diligenceId},batchId:${batchId},jobId:${jobId},orgId:${orgId}`);
          const dbDiligence = await this.diligenceHistoryRepo.findOne({ where: { id: diligenceId, orgId } });
          if (dbDiligence?.snapshotDetails?.status !== SnapshotStatus.SUCCESS) {
            this.logger.info(`export processJobMessage: batchId=${batchId},jobId=${jobId} diligence snapshot not prepared`);
            if (Date?.now() - batchEntity.createDate.getTime() > 120 * 1000) {
              throw new BadRequestException('snapshot not prepared');
            }
            await this.batchExportQueue.sendMessageV2(message, { ttl: 30 * 1000, retries: 1 }); // 重试
            return;
          }
          break;
        }
        // case BatchBusinessTypeEnums.Tender_Report_Export: {
        //   const pdfRequest = condition as ExportConditionRequest;
        //   this.logger.info(`招标排查结果PDF导出 export processJobMessage: batchId=${batchId},jobId=${jobId},orgId=${orgId},id=${pdfRequest.diligenceId}`);
        //   await this.biddingUpdateService.searchDetail(orgId, [pdfRequest.diligenceId]);
        //   break;
        // }
      }

      // 待导出的数据数量
      const exportDataCount = batchEntity.statisticsInfo.recordCount;
      statisticsInfo.successCount = exportDataCount;
      statisticsInfo.recordCount = exportDataCount;
      //导出是导出所有，所以分页参数要设置
      condition.pageSize = exportDataCount;
      condition.pageIndex = 1;
      if (!job) {
        this.logger.error(`export processJobMessage: batchId=${batchId},jobId=${jobId} job is null,throw exception!`);
        // 这种情况应该标记任务失败
        throw new BadRequestException(`job is null: batchId=${batchId},jobId=${jobId} `);
      }
      if (job.status == BatchStatusEnums.Processing) {
        this.logger.error(`export processJobMessage: batchId=${batchId},jobId=${jobId} job is Processing,skip!`);
        return '';
      }
      await this.batchJobRepo.update(jobId, { status: BatchStatusEnums.Processing, startDate: new Date() });
      //查询出要导出的列表信息并生成Excel
      const { fileUrl, fileName, previewUrl } = await this.searchDataAndGeneratorFile(condition, businessType);
      partialUpdate.detailFile = fileUrl;
      partialUpdate.fileName = fileName;
      partialUpdate.previewUrl = previewUrl;
      partialUpdate.endDate = new Date();
      partialUpdate.status = BatchStatusEnums.Done;

      const partial: any = { status: BatchStatusEnums.Done, endDate: new Date() };
      jobCost.jobTotalTime = Date?.now() - jobStartTime;
      Object.assign(partial, { comment: JSON.stringify(jobCost) });
      if (partial.status === BatchStatusEnums.Error) {
        partial.errorDate = new Date();
      }
      await this.batchJobRepo.update(jobId, partial);
      await this.batchRepo.update(batchId, partialUpdate);
      if (!(businessType === BatchBusinessTypeEnums.Diligence_Report_Export && message.condition?.isSub)) {
        // 非批量准入排查导出任务的子任务，需要发送任务结果消息
        await this.sendMessage(batchId, businessType);
      }
      this.logger.info(`export processJobMessage: batchId=${batchId},jobId=${jobId} Finished!`);
    } catch (e) {
      this.logger.error(`export processJobMessage error! batchId=${batchId},jobId=${jobId} error=${JSON.stringify(e)}`);
      this.logger.error(e);
      //清除job 对应的result 以及 batchDiligence
      const comment = e?.response
        ? `${e?.response?.error}-${e.response.message}-${e?.response?.internalMessage}-${e?.response?.errorExtraData?.httpRequest}`.substr(0, 1000)
        : e?.message?.substr(0, 1000);
      await this.setJobError([jobId], comment);
      await this.batchFail(operatorId, orgId, batchId, businessType, comment);
    }
  }

  async zipFiles(sources: string[], dest: string) {
    return new Promise((resolve, reject) => {
      let done = false;
      const output = fs.createWriteStream(dest);
      const archive = archiver('zip', {
        zlib: { level: 5 }, // Sets the compression level.
      });
      archive.on('error', (err) => {
        this.logger.error('archive', err);
        if (!done) {
          reject(err);
          done = true;
        }
      });
      archive.pipe(output);

      output.on('close', () => {
        if (!done) {
          resolve(0);
          done = true;
        }
      });

      archive.on('warning', (err) => {
        if (err.code === 'ENOENT') {
          this.logger.warn('archive', err);
        } else {
          this.logger.error('archive', err);
          if (!done) {
            reject(err);
            done = true;
          }
        }
      });
      sources.forEach((f) => archive.file(f, { name: path.basename(f) }));
      archive.finalize();
    });
  }

  async downloadFile(url: string, dest: string) {
    url = url.replace('.oss-cn-hangzhou.aliyuncs.com/', '.oss-cn-hangzhou-internal.aliyuncs.com/');
    const response = await axios({
      url: url,
      method: 'GET',
      responseType: 'stream',
    });

    const writer = fs.createWriteStream(dest);

    response.data.pipe(writer);

    return new Promise((resolve, reject) => {
      writer.on('finish', () => resolve(undefined));
      writer.on('error', reject);
    });
  }

  async processBundleTask(message: BatchExportMessagePO<BatchMessageCondition>) {
    try {
      if (!message.condition?.subBatchIds) {
        this.logger.info('ignore invalid bundle task: ' + message.batchId);
        return;
      }
      const subBatches = await this.batchRepo.findByIds(message.condition.subBatchIds);
      // 检查所有子任务是否都已经完成
      const date = new Date();
      for (const subBatch of subBatches) {
        if (subBatch.status !== BatchStatusEnums.Done && subBatch.status !== BatchStatusEnums.Error) {
          if (subBatch.createDate.getTime() + 30 * 60 * 1000 < date.getTime()) {
            await this.batchRepo.update(subBatch.batchId, { status: BatchStatusEnums.Error, comment: '任务超时' });
          } else {
            this.logger.debug('持续检查');
            // 发送自消息持续检查
            await this.batchExportQueue.sendMessageV2(message, { ttl: 30 * 1000, retries: 1 });
          }
          return;
        }
      }

      // 下载所有文件到本地
      const workDir = path.join(tmpdir(), message.batchId.toString() + '-' + random(1000, 9999));
      fs.mkdirSync(workDir);
      const fileNames = [];
      const nameCounter = new NameCounter();
      let doneCounter = 0;
      for (const subBatch of subBatches) {
        if (subBatch.status === BatchStatusEnums.Done) {
          doneCounter++;
          const count = nameCounter.inc(subBatch.fileName);
          let dest;
          if (count === 1) {
            dest = path.join(workDir, `${subBatch.fileName}.pdf`);
          } else {
            dest = path.join(workDir, `${subBatch.fileName}(${count}).pdf`);
          }
          await this.downloadFile(subBatch.detailFile, dest);
          fileNames.push(dest);
        }
      }
      // 合并文件
      const zipFile = path.join(workDir, 'result.zip');

      await this.zipFiles(fileNames, zipFile);

      const fileName = `report_batch_export_${date.getTime()}.zip`;
      const exportUrl = await this.resultExportService.putFileToOss(zipFile, fileName, `风险排查-批量生成报告 ${moment(date).format('YYYYMMDDHHmmss')}.zip`);
      const batch = await this.batchRepo.findOne({ where: { batchId: message.batchId } });
      if (!batch) return;
      batch.detailFile = exportUrl;
      batch.fileName = '风险排查-批量生成报告';
      batch.endDate = date;
      batch.status = BatchStatusEnums.Done;
      batch.statisticsInfo.successCount = doneCounter;
      batch.statisticsInfo.errorCount = batch.statisticsInfo.recordCount - doneCounter;
      await this.batchRepo.save(batch);
      await this.sendSysMessageBatchExport(batch);
    } catch (e) {
      this.logger.error(`processBundleTask error! batchId=${message.batchId} error=${e}`);
      await this.setJobError(message.condition.subBatchIds, e?.message);
      await this.batchFail(message.operatorId, message.orgId, message.batchId, message.businessType, e.message);
    }
    return '';
  }

  /**
   * 发送消息 批量导出报告
   * @param finishedBatch
   * @private
   */
  private async sendSysMessageBatchExport(finishedBatch: BatchEntity) {
    const statisticsInfo = finishedBatch.statisticsInfo;
    const messageBody: MessageBody = {
      title: BatchBusinessMessageTitle[BatchBusinessTypeEnums.Diligence_Report_Batch_Export],
      content: `您的批量生成报告已完成，成功生成 <em class="success">${statisticsInfo?.successCount || 0}</em> 条，下载查看结果`,
      userId: finishedBatch.creatorId,
      msgType: MsgType.TaskMsg,
      objectId: finishedBatch.batchId + '',
      url: `/tasklist/report-task-list?batchId=${finishedBatch.batchId}`,
    };
    await this.messageService.addMessage(messageBody);
  }

  /**
   * 处理具体的导出任务
   * job 的 items 不能超过200条
   * @param message
   */
  async handleJobMessage(message: BatchExportMessagePO<ExportConditionBase>) {
    const { targetId, condition, orgId, operatorId } = message;
    condition.targetId = targetId;
    condition.orgId = orgId;
    condition.userId = operatorId;
    // condition['targetId'] = targetId;
    // condition['orgId'] = orgId;
    // condition['userId'] = operatorId;
    if (message.businessType === BatchBusinessTypeEnums.Diligence_Report_Batch_Export) {
      return this.processBundleTask(message);
    }
    return this.processExportTask(message);
  }

  async setJobError(jobIds: number[], comment: string) {
    await Bluebird.all([
      this.batchResultRepo.delete({ jobId: In(jobIds) }),
      this.batchJobRepo.update(
        { jobId: In(jobIds) },
        {
          status: BatchStatusEnums.Error,
          comment,
          errorDate: new Date(),
        },
      ),
    ]);
  }

  /**
   * 查询要批量排查的结果并生成文件
   * @param condition targetId指的是在批量排查中的当前的batchId，仅在批量排查生成文件时生效
   * @param businessType
   */
  public async searchDataAndGeneratorFile(condition: ExportConditionBase, businessType: BatchBusinessTypeEnums) {
    const { targetId, orgId, userId } = condition;
    const user: RoverUserModel = await this.userService.getRoverUser(userId, orgId);
    this.logger.info(`searchDataAndGeneratorFile businessType: ${businessType} ,targetId: ${targetId} `);
    const processor = this.getProperlyProcessor(businessType);
    const fileResult = await processor.searchDataAndGeneratorFile(condition, user, businessType);
    //TODO 处理 处理了位置类型导出没有生成文件的情况
    //new code
    return fileResult;
  }

  /**
   * 发送消息
   * @param batchId
   * @param businessType
   * @param successResult
   * @private
   */
  private async sendMessage(batchId: number, businessType: BatchBusinessTypeEnums, successResult = true) {
    const finishedBatch = await this.batchRepo.findOne({ where: { batchId } });
    const statisticsInfo = finishedBatch.statisticsInfo;

    const messageBody: MessageBody = {
      title: BatchBusinessMessageTitle[businessType],
      content: `您的导出文件任务已经完成，导出成功 <em class="success">${statisticsInfo?.successCount || 0}</em> 条，下载文件查看导出结果`,
      userId: finishedBatch.creatorId,
      msgType: MsgType.DownloadMsg,
      objectId: finishedBatch.batchId + '',
      url: `/tasklist/export-task-list?batchId=${batchId}`,
    };
    if (
      [
        BatchBusinessTypeEnums.Diligence_Report_Export,
        BatchBusinessTypeEnums.Tender_Detail_Export,
        BatchBusinessTypeEnums.Tender_Report_Export,
        BatchBusinessTypeEnums.Specific_Report_Export,
      ].includes(businessType)
    ) {
      messageBody.title = finishedBatch.fileName;
      messageBody.url = `/tasklist/report-task-list?batchId=${batchId}`;
      messageBody.content = successResult ? '报告生成成功' : '报告生成失败，请重新生成！';
    }
    await this.messageService.addMessage(messageBody);
  }

  /**
   * 变更 batch 为失败
   * @param operatorId
   * @param orgId
   * @param batchId
   * @param businessType
   * @param failedComment
   */
  private async batchFail(operatorId: number, orgId: number, batchId: number, businessType: BatchBusinessTypeEnums, failedComment: string) {
    try {
      const user = await this.userService.getRoverUser(operatorId, orgId);
      switch (businessType) {
        case BatchBusinessTypeEnums.Diligence_Batch_Detail:
        case BatchBusinessTypeEnums.Diligence_Record:
        case BatchBusinessTypeEnums.Analyze_Record_Export:
          await this.batchBundleService.useOrglimitationCounter(user, RoverBundleLimitationType.DiligenceExportDailyQuantity, CounterOperation.Decrease, 1);
          break;
        case BatchBusinessTypeEnums.Diligence_Report_Export:
        case BatchBusinessTypeEnums.Tender_Report_Export:
        case BatchBusinessTypeEnums.Specific_Report_Export:
          await this.batchBundleService.bundleCounter(user, RoverBundleCounterType.DiligenceReportQuantity, CounterOperation.Decrease, 1);
          break;
        default:
          break;
      }
    } catch (e) {
      this.logger.error(`batchFail decrease bundleCounterError, batchId=${batchId} error=${e.message}`);
      this.logger.error(e);
    }
    const batchEntity = await this.batchRepo.findOne({ where: { batchId } });
    if (!batchEntity) {
      return null;
    }
    // 标记任务状态失败
    await this.batchRepo.update(batchId, {
      status: BatchStatusEnums.Error,
      comment: failedComment,
      endDate: new Date(),
    });

    this.logger.info(`Batch Failed! businessType: ${businessType} ,batchId: ${batchId} `);
    await this.sendMessage(batchId, businessType, false);
  }

  getTargetQueue(businessType?: BatchBusinessTypeEnums): RabbitMQ {
    return this.batchExportQueue;
  }
}
