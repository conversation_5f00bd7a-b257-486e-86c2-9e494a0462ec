import { ExportProcessorBase } from '../export.processor.base';
import { FileResultModel } from '@domain/model/batch/export/FileResultModel';
import { BatchBusinessTypeEnums } from '@domain/enums/batch/BatchBusinessTypeEnums';
import { Injectable } from '@nestjs/common';
import { SearchBatchResultRequest } from '@domain/dto/batch/request/SearchBatchResultRequest';
import { ExportEnums } from '@domain/enums/batch/ExportEnums';
import { BatchFacadeService } from '../../../facade.service/batch.facade.service';
import { BatchEntity } from '@domain/entities/BatchEntity';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { DimensionLevel3Enums } from '@domain/enums/diligence/DimensionLevel3Enums';
import { DimensionLevel1Enums } from '@domain/enums/diligence/DimensionLevel1Enums';
import { ExcelFileName, SheetKeyName } from '../../../common/file.export.template';
import { DimensionDefinitionPO } from '@domain/model/diligence/pojo/dimension/DimensionDefinitionPO';
import { DimensionLevel2Enums } from '@domain/enums/diligence/DimensionLevel2Enums';
import { cloneDeep, compact, intersectionBy, isNumber, uniq, uniqBy, xor } from 'lodash';
import * as Bluebird from 'bluebird';
import { DimensionTypeEnums } from '@domain/enums/diligence/DimensionTypeEnums';
import { Workbook, Worksheet } from 'exceljs';
import { DiligenceSnapshotHelper } from '@modules/risk-assessment/diligence/snapshot/diligence.snapshot.helper';
import { DimensionDetailExportRecordItemPO, ExportConditionBase, ExportRecordModel } from '@domain/model/batch/export/ExportRecordModel';
import { AnalyzedCompanySearchedRequest } from '@domain/model/diligence/pojo/analyze/AnalyzedCompanySearchedRequest';
import { DiligenceAnalyzeService } from '@modules/risk-assessment/diligence/analyze/diligence.analyze.service';
import { DiligenceAnalyzeResponseItemPO } from '@domain/model/diligence/pojo/analyze/DiligenceAnalyzeResponseItemPO';
import { fetchAll, formatMoney } from '@commons/utils/utils';
import { DiligenceHistoryEntity } from '@domain/entities/DiligenceHistoryEntity';
import { generateControlPaths, processRelationTypes, processSuspectRelationTypes } from './DimensionDetailValFormatUtil';
import { DimensionDetailCommonFields } from '@domain/utils/excel/excel-template.util';
import { RoverUserModel } from '@domain/model/RoverUserModel';
import { SortFieldPO } from '@domain/model/diligence/pojo/dimension/DimensionStrategyPO';
import { BaseDimensions } from '@domain/constants/dimension.base.constants';

@Injectable()
export class DimensionDetailsExportProcessor extends ExportProcessorBase {
  protected logger = QccLogger.getLogger('DimensionDetailsExportProcessor');

  constructor(
    private readonly batchService: BatchFacadeService,
    private readonly snapshotHelper: DiligenceSnapshotHelper,
    public readonly diligenceAnalyzeService: DiligenceAnalyzeService,
  ) {
    super();
  }

  async searchDataAndGeneratorFile(condition: ExportConditionBase, user: RoverUserModel, businessType: BatchBusinessTypeEnums): Promise<FileResultModel> {
    condition.targetId = condition['batchId'];
    const { targetId, orgId } = condition;
    const searchCondition = condition as AnalyzedCompanySearchedRequest;
    const { dimensionLevel1, dimensionLevel2, batchIdCurrent, diligenceIds } = searchCondition;
    const fileResult = { recordCount: 0, fileUrl: '', fileName: '', previewUrl: '' };
    try {
      let diligenceList: DiligenceHistoryEntity[] = [];
      //let dimensionHits: DimensionTypeEnums[] = [];
      switch (businessType) {
        case BatchBusinessTypeEnums.Analyze_Dimension_Detail:
        case BatchBusinessTypeEnums.Dimension_Detail_Export: {
          condition.pageSize = 100;
          const analyzeData: DiligenceAnalyzeResponseItemPO[] = await fetchAll(
            this.diligenceAnalyzeService.analyze.bind(this.diligenceAnalyzeService),
            [],
            condition as AnalyzedCompanySearchedRequest,
            orgId,
            true,
          );
          const analyzeResponse = await this.diligenceAnalyzeService.analyze(condition as AnalyzedCompanySearchedRequest, orgId);
          compact(
            Object.keys(analyzeResponse.aggs)
              .filter((m) => isNumber(analyzeResponse.aggs[m]) && m !== 'allDimensions')
              .map((m) => m as DimensionTypeEnums),
          );
          analyzeData.forEach((d) => {
            if (d.diligenceInfo) {
              d.diligenceInfo.creditcode = d?.companyDetail?.creditcode;
              diligenceList.push(d.diligenceInfo);
            }
          });
          break;
        }
        default: {
          this.logger.warn(`searchDataAndGeneratorFile unknown batch business type: ${businessType}`);
        }
      }
      if (diligenceList?.length) {
        //维度快照ids
        let snapShotIds = [];
        diligenceList.forEach((d) => {
          d?.details?.dimensionScoreDetails?.forEach((e) => {
            d[e.groupKey] = e.totalHits;
          });
          if (dimensionLevel2 && d?.snapshotDetails?.successHits.includes(dimensionLevel2)) {
            snapShotIds.push({
              diligenceId: d.id,
              snapshotId: d.snapshotId,
              companyName: d.name,
              creditcode: d.creditcode,
              dimensionHits: d?.details.dimensionHits,
            });
          }
        });
        if (dimensionLevel1) {
          diligenceList = diligenceList.filter((d) => d?.[dimensionLevel1] > 0);
          snapShotIds = [];
          diligenceList.forEach((d) => {
            snapShotIds.push({
              diligenceId: d.id,
              snapshotId: d.snapshotId,
              companyName: d.name,
              creditcode: d.creditcode,
              dimensionHits: d?.details.dimensionHits, //只取 aggs 中的维度
            });
          });
        }
        if (diligenceIds?.length > 0) {
          snapShotIds = uniqBy(snapShotIds, (s) => s.diligenceId).filter((s) => diligenceIds.includes(s.diligenceId));
        }
        this.logger.info(`generate dimension details businessType: ${businessType} ,targetId: ${targetId}`);
        const { fileUrl, fileName } = await this.generateResultFile(condition, businessType, { [ExportEnums.DimensionDetail]: snapShotIds });
        fileResult.fileUrl = fileUrl;
        fileResult.fileName = fileName;
        fileResult.recordCount = diligenceList?.length;
      }
    } catch (error) {
      this.logger.error(`searchDataAndGeneratorFile error batchId: ${batchIdCurrent}, businessType: ${businessType} ,condition: ${JSON.stringify(condition)}}`);
      this.logger.error(error);
      throw error;
    }
    return fileResult;
  }

  getBusinessType(): BatchBusinessTypeEnums[] {
    //巡检维度详情和批量排查维度详情共用
    return [BatchBusinessTypeEnums.Dimension_Detail_Export, BatchBusinessTypeEnums.Analyze_Dimension_Detail];
  }

  async applyDataToFile(businessType: BatchBusinessTypeEnums, record: ExportRecordModel, workbook: Workbook, condition: ExportConditionBase): Promise<string> {
    let exportFileName = '';
    const dimensionKV = await this.settingService.getDimensionKV(condition.orgId);
    const orgSetting = await this.processorHelper.getOrgSetting(condition.orgId, condition?.targetId);
    const setting = orgSetting?.content;
    const worksheets = workbook.worksheets;
    switch (businessType) {
      case BatchBusinessTypeEnums.Analyze_Dimension_Detail:
      case BatchBusinessTypeEnums.Dimension_Detail_Export: {
        const searchRequest = condition as SearchBatchResultRequest;
        const { dimensionLevel1, dimensionLevel2 } = searchRequest;
        let baseName = '批量排查维度详情报告';
        if (businessType == BatchBusinessTypeEnums.Analyze_Dimension_Detail) {
          baseName = '风险巡检维度详情报告';
        }
        exportFileName = baseName;
        const records: DimensionDetailExportRecordItemPO[] = record[ExportEnums.DimensionDetail];
        const workSheetNames = worksheets.map((w) => w.name);
        const longSheetNameDimensions: DimensionTypeEnums[] = [
          DimensionLevel3Enums.MainMembersRestrictedConsumptionCurrent,
          DimensionLevel3Enums.CompanyOrMainMembersCriminalOffence,
          DimensionLevel3Enums.CompanyOrMainMembersCriminalOffenceHistory,
          DimensionLevel3Enums.CompanyOrMainMembersCriminalInvolve,
          DimensionLevel3Enums.CompanyOrMainMembersCriminalInvolveHistory,
          DimensionLevel3Enums.CompanyOrMainMembersLitigationInfo,
          DimensionLevel3Enums.StaffWorkingOutsideForeignInvestment,
        ];
        const specialSheetNames = longSheetNameDimensions.map((l) => dimensionKV[l]);
        const usedSheetName = [];
        if (
          dimensionLevel1 &&
          dimensionLevel2 &&
          dimensionLevel2 !== DimensionLevel1Enums.Risk_InnerBlacklist &&
          dimensionLevel2 !== DimensionLevel1Enums.Risk_OuterBlacklist
        ) {
          //对sheetName过长的sheet需要特殊处理  主要人员限制高消费（当前有效）
          let useSheet = worksheets.find((w) => w.name === dimensionLevel2);
          exportFileName = `${baseName}-${ExcelFileName[dimensionLevel1]}`;
          if (dimensionLevel1 === DimensionLevel1Enums.Risk_Legal || dimensionLevel1 === DimensionLevel1Enums.Risk_InterestConflict) {
            Array.prototype.push.apply(workSheetNames, longSheetNameDimensions);
            const currentDimensionName = specialSheetNames.find((s) => s === dimensionKV[dimensionLevel2]);
            useSheet = worksheets.find((w) => w.name === dimensionLevel2 || w.name === currentDimensionName);
            if (longSheetNameDimensions.includes(dimensionLevel2)) {
              usedSheetName.push(dimensionKV[dimensionLevel2]);
            }
          }
          usedSheetName.push(dimensionLevel2);
          await this.handleDimensionDetailRecord(records, useSheet, dimensionLevel2);
        } else {
          //获取子维度
          const childDimensions: DimensionDefinitionPO[] = setting[dimensionLevel1]?.items;
          if (!childDimensions?.length) {
            return;
          }
          await Bluebird.map(
            worksheets,
            async (worksheet) => {
              if (dimensionLevel1) {
                if (dimensionLevel2) {
                  if (dimensionLevel2 === DimensionLevel1Enums.Risk_InnerBlacklist) {
                    if (
                      [
                        DimensionLevel2Enums.HitInnerBlackList,
                        DimensionLevel2Enums.BlacklistPartnerInvestigation,
                        DimensionLevel1Enums.Risk_OuterBlacklist,
                      ].includes(worksheet.name)
                    ) {
                      usedSheetName.push(worksheet.name);
                      await this.handleDimensionDetailRecord(records, worksheet, worksheet.name);
                    }
                  } else if (dimensionLevel2 === DimensionLevel1Enums.Risk_OuterBlacklist) {
                    if (worksheet.name === DimensionLevel2Enums.HitOuterBlackList) {
                      usedSheetName.push(DimensionLevel2Enums.HitOuterBlackList);
                      await this.handleDimensionDetailRecord(records, worksheet, DimensionLevel2Enums.HitOuterBlackList);
                    }
                  }
                } else {
                  exportFileName = `${baseName}-${ExcelFileName[dimensionLevel1]}`;
                  switch (dimensionLevel1) {
                    case DimensionLevel1Enums.Risk_BaseInfo:
                    case DimensionLevel1Enums.Risk_OuterBlacklist:
                    case DimensionLevel1Enums.Risk_AdministrativeSupervision:
                    case DimensionLevel1Enums.Risk_OperateStability:
                    case DimensionLevel1Enums.Risk_NegativeNews: {
                      const sn = childDimensions.find((c) => c.key === worksheet.name)?.key;
                      if (sn) {
                        const usefulSheetNames = await this.handleDimensionDetailRecord(records, worksheet, sn);
                        Array.prototype.push.apply(usedSheetName, usefulSheetNames);
                      }
                      break;
                    }
                    case DimensionLevel1Enums.Risk_Legal: {
                      let sn = childDimensions.find((c) => c.key === worksheet.name)?.key;
                      if (sn) {
                        const usefulSheetNames = await this.handleDimensionDetailRecord(records, worksheet, sn);
                        Array.prototype.push.apply(usedSheetName, usefulSheetNames);
                      } else {
                        const currentDimensionName = specialSheetNames.find((s) => s === worksheet.name);
                        sn = childDimensions.find((c) => c.name === currentDimensionName)?.key;
                        if (sn) {
                          const usefulSheetNames = await this.handleDimensionDetailRecord(records, worksheet, sn);
                          if (usefulSheetNames?.length && usefulSheetNames.includes(sn)) {
                            Array.prototype.push.apply(usedSheetName, [currentDimensionName]);
                          }
                          Array.prototype.push.apply(usedSheetName, usefulSheetNames);
                        }
                      }
                      break;
                    }
                    case DimensionLevel1Enums.Risk_PartnerInvestigation: {
                      if ([DimensionLevel2Enums.CustomerPartnerInvestigation, DimensionLevel2Enums.CustomerSuspectedRelation].includes(worksheet.name)) {
                        const uns = await this.handleDimensionDetailRecord(records, worksheet, worksheet.name);
                        //获取 uns 和 dimensionLevel2Arr的交集把，交集添加到 usedSheetName
                        const intersection = intersectionBy(uns, [
                          DimensionLevel2Enums.CustomerPartnerInvestigation,
                          DimensionLevel2Enums.CustomerSuspectedRelation,
                        ]);
                        usedSheetName.push(...intersection);
                      }
                      break;
                    }
                    case DimensionLevel1Enums.Risk_Blacklist: {
                      const dimensionLevel2Arr = [
                        DimensionLevel2Enums.HitInnerBlackList,
                        DimensionLevel2Enums.HitOuterBlackList,
                        DimensionLevel2Enums.BlacklistPartnerInvestigation,
                      ];
                      if (dimensionLevel2Arr.includes(worksheet.name)) {
                        const uns = await this.handleDimensionDetailRecord(records, worksheet, worksheet.name);
                        const intersection = intersectionBy(uns, dimensionLevel2Arr);
                        usedSheetName.push(...intersection);
                      }
                      break;
                    }
                    case DimensionLevel1Enums.Risk_InnerBlacklist: {
                      const dimensionLevel2Arr = [
                        DimensionLevel2Enums.HitInnerBlackList,
                        DimensionLevel2Enums.BlacklistPartnerInvestigation,
                        DimensionLevel2Enums.BlacklistSuspectedRelation,
                      ];
                      if (dimensionLevel2Arr.includes(worksheet.name)) {
                        const uns = await this.handleDimensionDetailRecord(records, worksheet, worksheet.name);
                        const intersection = intersectionBy(uns, dimensionLevel2Arr);
                        usedSheetName.push(...intersection);
                      }
                      break;
                    }
                    case DimensionLevel1Enums.Risk_InterestConflict: {
                      const currentDimensionName = specialSheetNames.find((s) => s === dimensionKV[DimensionLevel3Enums.StaffWorkingOutsideForeignInvestment]);
                      if (worksheet.name === DimensionLevel1Enums.Risk_InterestConflict || worksheet.name == currentDimensionName) {
                        usedSheetName.push(dimensionKV[DimensionLevel3Enums.StaffWorkingOutsideForeignInvestment]);
                        await this.handleDimensionDetailRecord(records, worksheet, DimensionLevel3Enums.StaffWorkingOutsideForeignInvestment);
                      }
                      if (worksheet.name === DimensionLevel3Enums.SuspectedInterestConflict) {
                        usedSheetName.push(DimensionLevel3Enums.SuspectedInterestConflict);
                        await this.handleDimensionDetailRecord(records, worksheet, DimensionLevel3Enums.SuspectedInterestConflict);
                      }
                      break;
                    }
                    default: {
                      const msg = `unsupported dimensionLevel1: ${dimensionLevel1}`;
                      this.logger.warn(msg);
                    }
                  }
                }
              }
            },
            { concurrency: 3 },
          );
        }
        const unusedSheet = xor(usedSheetName, workSheetNames);
        unusedSheet.forEach((u) => workbook.removeWorksheet(u));
        workbook.worksheets.forEach((worksheet) => {
          worksheet.name = SheetKeyName[worksheet.name] || dimensionKV[worksheet.name] || worksheet.name;
        });
        break;
      }
      default: {
        this.logger.warn(`applyDataToFile unknown batch business type: ${businessType}`);
      }
    }
    return exportFileName;
  }

  private async handleDimensionDetailRecord(records: Record<string, any>[], worksheet: Worksheet, dimension: DimensionTypeEnums | string) {
    const usedSheetNames = [];
    const addRow = (record, dataDetail = {}, fields = []) => {
      const row = {
        ...dataDetail,
        companyName: record?.companyName || '-',
        creditcode: record?.creditcode || '-',
      };
      fields.forEach((field) => {
        row[field.key] = field.format ? field.format(dataDetail) : dataDetail[field.key] || '-';
      });
      worksheet.addRow(row).commit();
    };
    for (const record of records) {
      if (!record?.dimensionHits?.some((d) => d === dimension)) {
        continue;
      }
      const dimensionSortField = BaseDimensions[dimension]?.strategyModel?.sortField as SortFieldPO;
      const sortField = dimensionSortField?.fieldSnapshot || dimensionSortField?.field;
      const sortOrder = dimensionSortField?.order || 'DESC';
      const snapshotRes = await this.snapshotHelper.getSnapshotHitDetails({
        snapshotId: record.snapshotId,
        dimensionKey: [dimension as DimensionTypeEnums],
        pagination: {
          pageSize: 99,
          pageIndex: 1,
        },
        sortField,
        sortOrder,
      });
      const dimensionData = snapshotRes.Result;
      if (dimensionData?.length) {
        usedSheetNames.push(dimension);
        if (dimension === DimensionLevel2Enums.CompanyShell) {
          addRow(record, {
            RegistCapi: formatMoney(dimensionData[0]?.value),
            RecCap: formatMoney(dimensionData[1]?.value),
          });
          continue;
        }
        //维度命中数据
        const dimensionDataDetails = cloneDeep(dimensionData);
        const matchedCommonField = DimensionDetailCommonFields.find((f) => f.dimensions.map((d) => d.toString()).includes(dimension));
        if (matchedCommonField) {
          dimensionDataDetails.forEach((dataDetail) => {
            addRow(record, dataDetail, matchedCommonField.fields);
          });
        } else {
          dimensionDataDetails.forEach((dataDetail) => {
            if (dimension === DimensionLevel2Enums.FakeSOES) {
              const items: any[] = [];
              let pathDetail = '-';
              if (dataDetail?.relations?.relations2?.length) {
                pathDetail = generateControlPaths(dataDetail?.relations?.relations2).join('\n');
              }

              items.push({
                companyName: record?.companyName || '-',
                creditcode: record?.creditcode || '-',
                riskCompanyName: dataDetail?.riskCompanyName,
                pathDetail,
              });
              if (items.length) {
                items.forEach((item) => {
                  worksheet.addRow({ ...item }).commit();
                });
              }
            }
            if (
              dimension === DimensionLevel2Enums.BlacklistPartnerInvestigation ||
              dimension === DimensionLevel2Enums.CustomerPartnerInvestigation ||
              dimension === DimensionLevel2Enums.CustomerSuspectedRelation ||
              dimension === DimensionLevel2Enums.BlacklistSuspectedRelation
            ) {
              let items: any[];
              if (dimension === DimensionLevel2Enums.CustomerSuspectedRelation || dimension === DimensionLevel2Enums.BlacklistSuspectedRelation) {
                items = processSuspectRelationTypes(dataDetail);
              } else {
                items = processRelationTypes(dataDetail);
              }
              if (items.length) {
                items.forEach((item) => {
                  item['creditcode'] = record?.creditcode || '-';
                  worksheet.addRow({ ...item }).commit();
                });
              }
            } else {
              const msg = `handleDimensionDetailRecord unknown dimension type: ${dimension}`;
              this.logger.warn(msg);
            }
          });
        }
      }
    }
    if (
      [
        DimensionLevel2Enums.CustomerPartnerInvestigation,
        DimensionLevel2Enums.BlacklistPartnerInvestigation,
        DimensionLevel2Enums.CustomerSuspectedRelation,
        DimensionLevel2Enums.BlacklistSuspectedRelation,
      ].some((d) => d === dimension)
    ) {
      this.resultExportService.mergeCell(worksheet, 3, 1, 3);
    }
    if ([DimensionLevel3Enums.StaffWorkingOutsideForeignInvestment, DimensionLevel3Enums.SuspectedInterestConflict].some((d) => d === dimension)) {
      this.resultExportService.mergeCell(worksheet, 3, 1, 2);
    }
    return uniq(usedSheetNames);
  }

  async onJobError(jobIds: number[], batchId: number, businessType: BatchBusinessTypeEnums): Promise<any> {
    return Promise.resolve(undefined);
  }

  async onBatchSuccess(batchEntity: BatchEntity): Promise<any> {
    return Promise.resolve(undefined);
  }

  async onBatchError(batchEntity: BatchEntity): Promise<any> {
    return Promise.resolve(undefined);
  }
}
