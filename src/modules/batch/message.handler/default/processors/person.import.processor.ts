import { RoverExceptions } from '@commons/constants/exceptionConstants';
import { BatchEntity } from '@domain/entities/BatchEntity';
import { GroupsEntity } from '@domain/entities/GroupsEntity';
import { PersonEntity } from '@domain/entities/PersonEntity';
import { BatchBusinessTypeEnums } from '@domain/enums/batch/BatchBusinessTypeEnums';
import { BatchJobResultTypeEnums } from '@domain/enums/batch/BatchJobResultTypeEnums';
import { BatchStatusEnums } from '@domain/enums/batch/BatchStatusEnums';
import { PersonRelationshipEnums } from '@domain/enums/person/PersonRelationshipEnums';
import { BatchResultPO } from '@domain/model/batch/po/BatchResultPO';
import { BatchJobMessagePO } from '@domain/model/batch/po/message/BatchJobMessagePO';
import { PersonImportExcelRecord } from '@domain/model/batch/po/parse/ParsedRecordBase';
import { RelationshipConst } from '@domain/model/batch/po/RelationshipConst';
import { GroupType } from '@domain/model/element/CreateGroupModel';
import { CreatePersonModel } from '@domain/model/person/CreatePersonModel';
import { RoverUserModel } from '@domain/model/RoverUserModel';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { RoverBundleCounterType } from '@kezhaozhao/saas-bundle-service';
import { processBatchJobFailed } from '@modules/batch/common/batch-job.utils';
import { CompanySearchService } from '@modules/company-management/company/company-search.service';
import { PersonHelperService } from '@modules/company-management/person/helper/person.helper.service';
import { PersonService } from '@modules/company-management/person/person.service';
import { PersonCheckService } from '@modules/company-management/person/service/person.check.service';
import { InjectRepository } from '@nestjs/typeorm';
import * as Bluebird from 'bluebird';
import { validate } from 'class-validator';
import { trim } from 'lodash';
import { Not, Repository } from 'typeorm';
import { BatchBundleService } from '../../../facade.service/service/batch.bundle.service';
import { DefaultProcessorBase } from '../default.processor.base';

export class PersonImportProcessor extends DefaultProcessorBase {
  protected readonly logger = QccLogger.getLogger(PersonImportProcessor.name);

  constructor(
    @InjectRepository(GroupsEntity) private readonly groupsRepo: Repository<GroupsEntity>,
    private readonly personService: PersonService,
    @InjectRepository(PersonEntity) private readonly personRepo: Repository<PersonEntity>,
    @InjectRepository(BatchEntity) private readonly batchRepo: Repository<BatchEntity>,
    private readonly companyService: CompanySearchService,
    private readonly batchBundleService: BatchBundleService,
    private readonly personHelperService: PersonHelperService,
    private readonly personCheckService: PersonCheckService,
  ) {
    super();
  }

  getBusinessType(): BatchBusinessTypeEnums[] {
    return [BatchBusinessTypeEnums.Person_File];
  }

  async processJobMessage(message: BatchJobMessagePO): Promise<any> {
    const { batchId, items, orgId, operatorId, jobId } = message;
    this.logger.info(`processJobMessage batchCreatePerson: batchId=${batchId},jobId=${jobId},items=${JSON.stringify(items)}`);
    const currentUser = await this.userService.getRoverUser(operatorId, orgId);
    //获取所有分组
    const data: PersonImportExcelRecord[] = items;
    if (data?.length) {
      //分组匹配校验
      const groupKV = {};
      const groupsEntities = await this.groupsRepo.find({ where: { groupType: GroupType.PersonGroup, orgId } });
      groupsEntities.forEach((group) => (groupKV[group.name] = group.groupId));
      const selfData = data.filter((d) => d.relationship === RelationshipConst[PersonRelationshipEnums.self]);
      await this.savePersons(selfData, message, groupKV, currentUser);
      const relatives = data.filter((d) => d.relationship !== RelationshipConst[PersonRelationshipEnums.self]);
      await this.savePersons(relatives, message, groupKV, currentUser);
    }
    this.logger.info(`processJobMessage batchCreatePerson Finished: batchId=${batchId},jobId=${jobId} `);
  }

  private async savePersons(data: PersonImportExcelRecord[], message: BatchJobMessagePO, groupKV: any, currentUser: RoverUserModel) {
    const { batchId, orgId, operatorId, jobId } = message;
    //const personRelationMap = groupBy(data, (e) => e.personNo);
    const names: string[] = data.map((d) => d.companyName).filter((e) => e);
    const companyInfos = [];
    if (names?.length) {
      const { matchedCompanyInfos } = await this.companyService.matchCompanyInfoV2(names, false, true);
      Array.prototype.push.apply(companyInfos, matchedCompanyInfos);
    }
    await Bluebird.map(
      data,
      async (currentPerson) => {
        //const selfPerson = personRelationMap[currentPerson.personNo]?.find((p) => p.relationship === RelationshipConst[PersonRelationshipEnums.self]);
        const batchResult: BatchResultPO = {
          batchId,
          jobId,
          resultType: BatchJobResultTypeEnums.SUCCEED_PAID,
          resultInfo: currentPerson,
          resultHashkey: currentPerson.recordHashkey,
        };
        const dbPerson = await this.personRepo.findOne({
          where: {
            orgId,
            personNo: trim(currentPerson.personNo),
            active: 1,
          },
        });
        let relationPersonId = -1;
        let relationGroupId = -1;
        if (dbPerson) {
          if (currentPerson.relationship !== RelationshipConst[PersonRelationshipEnums.self]) {
            //非本人，表示为亲属类型人员，
            if (dbPerson) {
              relationPersonId = dbPerson.id;
              relationGroupId = dbPerson.groupId;
            }
          }
        } else if (currentPerson.relationship !== RelationshipConst[PersonRelationshipEnums.self]) {
          //该personNo查不到数据且非本人数据时
          batchResult.resultType = BatchJobResultTypeEnums.FAILED_VERIFICATION;
          batchResult.comment = '未找到关联人员';
          batchResult.resultInfo = currentPerson;
          await this.batchHelperService.handleJobResult([batchResult]);
          return;
        }
        let companyId: string;
        let keyNo: string;
        if (currentPerson.companyName) {
          //1.先根据人员和公司名称查询公司companyId
          const company = companyInfos.find((i) => this.batchHelperService.matchCompany(currentPerson.companyName, i));
          //2.匹配到公司列表了，通过人员姓名和公司keyNo获取人员keyNo
          if (company) {
            const companyKeyNo = company.id;
            const personList = await this.personService.matchCompanyPerson(currentPerson.name, companyKeyNo);
            if (personList?.length) {
              companyId = companyKeyNo;
              keyNo = personList[0].keyNo;
              currentPerson.companyName = company.name;
            }
          }
        }
        const personModel = Object.assign(new CreatePersonModel(), {
          orgId,
          createBy: operatorId,
          batchId: batchId,
          status: BatchStatusEnums.Processing, // 1: 处理中， 2: 处理完成
          name: currentPerson.name,
          personNo:
            currentPerson.relationship === RelationshipConst[PersonRelationshipEnums.self]
              ? currentPerson.personNo
              : `${currentPerson.personNo}_${currentPerson.relationship}_${currentPerson.name}`,
          birthDay: currentPerson.birthDay,
          cardId: currentPerson.cardId?.trim() ? currentPerson.cardId.trim() : null,
          phone: currentPerson.phone,
          email: currentPerson?.email,
          relationship: currentPerson?.relationship,
          relationPersonId,
          companyId,
          keyNo,
          companyName: companyId ? currentPerson.companyName : null,
        });
        batchResult.resultInfo = currentPerson;

        //获取分组id
        if (currentPerson.groupName) {
          personModel.groupId = groupKV[currentPerson.groupName];
        }
        // 如果是近亲属，亲属的分组id必须和本人的相同
        if (currentPerson.relationship !== RelationshipConst[PersonRelationshipEnums.self]) {
          personModel.groupId = relationGroupId;
        }
        //获取地区
        if (currentPerson.birthPlace) {
          const birthPlaceList = currentPerson.birthPlace.split('/');
          if (birthPlaceList?.length > 0) {
            personModel.province = birthPlaceList[0] || '';
            personModel.city = birthPlaceList[1] || '';
            personModel.district = birthPlaceList[2] || '';
          }
        }

        // 保存人员信息
        try {
          const err = await validate(personModel);
          if (err?.length) {
            if (err[0].constraints?.matches) {
              batchResult.resultType = BatchJobResultTypeEnums.FAILED_VERIFICATION;
              batchResult.comment = err[0].constraints?.matches;
            } else {
              batchResult.resultType = BatchJobResultTypeEnums.FAILED_UNMATCHED;
              batchResult.comment = JSON.stringify(err);
            }
          } else {
            const postData = this.personHelperService.postDataProcess(personModel);
            await this.personCheckService.createPersonCheck(currentUser, postData);
            await this.personService.create(currentUser, postData, true);
          }
        } catch (error) {
          this.logger.error(
            `processJobMessage batchCreatePerson createPerson error: ${error} userId=${currentUser.userId}, personModel=${JSON.stringify(
              personModel,
            )},jobId=${jobId}`,
          );
          // 可更新异常分类
          processBatchJobFailed(error, batchResult);
          // 不同异常分类处理
          await this.processError(error, batchResult, currentUser, personModel);
        }

        // 记录处理结果
        await this.batchHelperService.handleJobResult([batchResult]);
      },
      { concurrency: 1 },
    );
  }

  /**
   * 异常情况处理
   * @param error
   * @param batchResult
   * @param currentUser
   * @param personModel
   */
  async processError(error: any, batchResult: BatchResultPO, currentUser: RoverUserModel, personModel: CreatePersonModel) {
    // 可更新异常分类处理
    if (batchResult.resultType === BatchJobResultTypeEnums.UPDATE_DUPLICATED) {
      try {
        if (error.message.code === RoverExceptions.GroupRelated.Person.DuplicatedPersonNoError.code) {
          //只有编号重复的情况下，才更新数据
          await this.personService.updateFromExcel(currentUser, personModel);
          batchResult.comment = '更新成功';
        }
      } catch (ue) {
        // 其他异常分类处理
        this.processUpdateError(ue, batchResult);
        this.logger.error(`update person error: ${ue} userId=${currentUser.userId}, personModel=${JSON.stringify(personModel)}`);
      }
    } else {
      // 其他异常分类处理
      this.processUpdateError(error, batchResult);
    }
  }

  /**
   * 其他异常分类处理
   * @param error
   * @param batchResult
   */
  processUpdateError(error: any, batchResult: BatchResultPO) {
    if (
      error.message.code === RoverExceptions.GroupRelated.Person.DuplicatedPhoneError.code ||
      error.response.code === RoverExceptions.GroupRelated.Person.DuplicatedPhoneError.code
    ) {
      batchResult.resultType = BatchJobResultTypeEnums.FAILED_VERIFICATION;
      batchResult.comment = '手机号码重复';
    } else if (
      error.message.code === RoverExceptions.GroupRelated.Person.DuplicatedCardIdError.code ||
      error.response.code === RoverExceptions.GroupRelated.Person.DuplicatedCardIdError.code
    ) {
      batchResult.resultType = BatchJobResultTypeEnums.FAILED_VERIFICATION;
      batchResult.comment = '证件号码重复';
    } else if (
      error.message.code === RoverExceptions.GroupRelated.Person.PersonDuplicateError.code ||
      error.response.code === RoverExceptions.GroupRelated.Person.PersonDuplicateError.code
    ) {
      batchResult.resultType = BatchJobResultTypeEnums.FAILED_VERIFICATION;
      batchResult.comment = '该企业已关联到其他同名人员';
    } else {
      batchResult.resultType = BatchJobResultTypeEnums.FAILED_CODE;
    }
  }

  async onJobError(jobIds: number[], batchId: number, businessType: BatchBusinessTypeEnums): Promise<any> {
    const deleteResult = await this.personRepo.delete({ batchId, status: Not(BatchStatusEnums.Done) });
    if (deleteResult) {
      await this.batchBundleService.handleBatchCounterEvent(batchId, RoverBundleCounterType.PersonQuantity, -deleteResult.affected);
    }
  }

  async onBatchSuccess(batchEntity: BatchEntity): Promise<any> {
    return this.personRepo.update(
      {
        batchId: batchEntity.batchId,
        status: BatchStatusEnums.Processing,
      },
      { status: BatchStatusEnums.Done },
    );
  }

  async onBatchError(batchEntity: BatchEntity): Promise<any> {
    return Promise.resolve(undefined);
  }

  // // 单测使用;
  // async processBatchJoMessage(jobId: number) {
  //   const job = await this.batchJobRepo.findOne({ where: { jobId } });
  //   const bactch = await this.batchRepo.findOne({ where: { batchId: job.batchId } });
  //   const basePo = {
  //     batchId: job.batchId,
  //     batchType: bactch.batchType,
  //     businessType: bactch.businessType,
  //     startDate: Date.now(),
  //     operatorId: bactch.creatorId,
  //     orgId: bactch.orgId,
  //   };
  //   const po: BatchJobMessagePO = Object.assign(new BatchJobMessagePO(), basePo, {
  //     items: job.jobInfo.items?.map((i: RecordBasePO) => {
  //       if (!i.recordHashkey) {
  //         i.recordHashkey = v4();
  //       }
  //       return i;
  //     }),
  //     jobId: job.jobId,
  //   });
  //   await this.processJobMessage(po);
  // }
}
