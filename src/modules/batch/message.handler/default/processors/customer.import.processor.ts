import { CreateCustomerModelRequest } from '@domain/dto/customer/CreateCustomerModelRequest';
import { BatchEntity } from '@domain/entities/BatchEntity';
import { CustomerEntity } from '@domain/entities/CustomerEntity';
import { GroupsEntity } from '@domain/entities/GroupsEntity';
import { LabelEntity } from '@domain/entities/LabelEntity';
import { BatchBusinessTypeEnums } from '@domain/enums/batch/BatchBusinessTypeEnums';
import { BatchJobResultTypeEnums } from '@domain/enums/batch/BatchJobResultTypeEnums';
import { BatchStatusEnums } from '@domain/enums/batch/BatchStatusEnums';
import { StreamOperationEnum } from '@domain/enums/data/StreamOperationEnum';
import { StreamTableEnums } from '@domain/enums/data/StreamTableEnums';
import { BatchResultPO } from '@domain/model/batch/po/BatchResultPO';
import { BatchJobMessagePO } from '@domain/model/batch/po/message/BatchJobMessagePO';
import { CustomerImportExcelRecord } from '@domain/model/batch/po/parse/ParsedRecordBase';
import { GroupType } from '@domain/model/element/CreateGroupModel';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { RoverBundleCounterType } from '@kezhaozhao/saas-bundle-service';
import { processBatchJobFailed } from '@modules/batch/common/batch-job.utils';
import { CompanySearchService } from '@modules/company-management/company/company-search.service';
import { CustomerService } from '@modules/company-management/customer/customer.service';
import { RoverGraphService } from '@modules/data-processing/data/source/rover.graph.service';
import { InjectRepository } from '@nestjs/typeorm';
import * as Bluebird from 'bluebird';
import { validate } from 'class-validator';
import { flatten } from 'lodash';
import { Logger } from 'log4js';
import { In, Not, Repository } from 'typeorm';
import { BatchBundleService } from '../../../facade.service/service/batch.bundle.service';
import { DefaultProcessorBase } from '../default.processor.base';

export class CustomerImportProcessor extends DefaultProcessorBase {
  protected readonly logger: Logger = QccLogger.getLogger(CustomerImportProcessor.name);

  constructor(
    private readonly companyService: CompanySearchService,
    private readonly roverGraphService: RoverGraphService,
    @InjectRepository(GroupsEntity) private readonly groupsRepo: Repository<GroupsEntity>,
    @InjectRepository(LabelEntity) private readonly labelRepo: Repository<LabelEntity>,
    private readonly customerService: CustomerService,
    @InjectRepository(CustomerEntity) private readonly customerRepo: Repository<CustomerEntity>,
    private readonly batchBundleService: BatchBundleService,
  ) {
    super();
  }

  getBusinessType(): BatchBusinessTypeEnums[] {
    return [BatchBusinessTypeEnums.Customer_File];
  }

  async processJobMessage(message: BatchJobMessagePO): Promise<any> {
    const { batchId, items, jobId, orgId, operatorId, isUpdate } = message;
    this.logger.info(`processJobMessage batchCreateCustomer batchId: ${batchId}, jobId: ${jobId} , items: ${JSON.stringify(items)}`);
    const currentUser = await this.userService.getRoverUser(operatorId, orgId);
    const data: CustomerImportExcelRecord[] = items;

    // 直接处理所有数据，不进行预校验（校验在create阶段进行，确保原子性）
    this.logger.info(`开始处理job数据orgId: ${orgId}, 数据量: ${data.length}条`);

    // 通过name字段匹配公司，匹配不上的，标记错误
    const names: string[] = data.map((d) => d.companyName);
    const { matchedCompanyInfos, unmatchedNames, matchedNames, unsupported } = await this.companyService.matchCompanyInfoV2(names, true, true);
    // 对匹配到的公司数据进行处理，保存客商列表
    if (matchedCompanyInfos?.length > 0) {
      //生成标签KV
      const labels: string[][] = data.map((d) => d.label);
      const labelKV = {};
      //TODO labelName 如果不存在，是否需要自动创建label
      const labelNames = flatten(labels.filter((ls) => ls)).filter((l) => l);
      const labelEntities = await this.labelRepo.find({ where: { orgId, name: In(labelNames), labelType: 1 } });
      labelEntities?.forEach((d) => {
        labelKV[d.name] = d.labelId;
      });

      // 生成分组KV
      const groupKV = {};
      const groupEntities = await this.groupsRepo.find({ where: { orgId, groupType: GroupType.CustomerGroup } });
      groupEntities?.forEach((d) => {
        groupKV[d.name] = d.groupId;
      });
      groupKV['未分组'] = -1;

      // 对同一companyId的更新操作进行去重，只保留最后一个
      const updateRecordMap = new Map<string, CustomerImportExcelRecord>();
      const createRecords: CustomerImportExcelRecord[] = [];

      data.forEach((record) => {
        const company = matchedCompanyInfos.find((i) => this.batchHelperService.matchCompany(record.companyName, i));
        if (company) {
          // 如果是更新模式且可能重复，先收集到map中进行去重
          if (isUpdate) {
            updateRecordMap.set(company.id, record);
          } else {
            createRecords.push(record);
          }
        }
      });

      // 将去重后的更新记录合并到处理列表中
      const finalRecords = isUpdate ? [...createRecords, ...Array.from(updateRecordMap.values())] : data;

      // 记录去重日志
      if (isUpdate && updateRecordMap.size > 0) {
        const originalUpdateCount = data.length - createRecords.length;
        const deduplicatedUpdateCount = updateRecordMap.size;
        this.logger.info(
          `批量更新去重完成orgId: ${orgId}, 原始更新记录${originalUpdateCount}条, 去重后${deduplicatedUpdateCount}条, 去重${
            originalUpdateCount - deduplicatedUpdateCount
          }条`,
        );
      }

      const customerIds = (
        await Bluebird.map(
          finalRecords,
          async (excelRecord: CustomerImportExcelRecord) => {
            const company = matchedCompanyInfos.find((i) => this.batchHelperService.matchCompany(excelRecord.companyName, i));
            if (company) {
              const customerObj = Object.assign(new CreateCustomerModelRequest(), {
                orgId,
                createBy: operatorId,
                batchId: batchId,
                status: BatchStatusEnums.Processing, // 1: 处理中， 2: 处理完成
                name: company.name,
                companyId: company.id,
                startDate: excelRecord.startDate || '',
                endDate: excelRecord.endDate || '',
                principal: excelRecord?.principal,
                customerDepartment: excelRecord?.customerDepartment || '',
                departmentNames: excelRecord?.departmentNames,
                creditQuota: excelRecord?.creditQuota,
                contactQuota: excelRecord?.contactQuota,
                cost: excelRecord?.cost,
                userUpdateDate: new Date(),
              });
              const unMatchedLabelName: string[] = [];
              let unMatchedGroup: string;

              //绑定labelIds
              const labelIds = [];
              excelRecord?.label?.forEach((label) => {
                labelKV[label] ? labelIds.push(labelKV[label]) : unMatchedLabelName.push(label);
              });
              customerObj.labelIds = labelIds?.length ? labelIds : undefined;

              //绑定groupId
              if (excelRecord?.group) {
                if (groupKV[excelRecord.group]) {
                  customerObj.groupId = groupKV[excelRecord.group];
                } else {
                  // error中标记该分组不存在
                  unMatchedGroup = excelRecord.group;
                }
              } else {
                customerObj.groupId = groupKV['未分组'];
              }
              this.logger.info(
                `processJobMessage batchCreateCustomer before create batchId: ${batchId}, jobId: ${jobId}, customerObj=${JSON.stringify(customerObj)}`,
              );
              const batchResult: BatchResultPO = {
                batchId,
                jobId,
                resultType: BatchJobResultTypeEnums.SUCCEED_PAID,
                resultInfo: excelRecord,
                resultHashkey: excelRecord.recordHashkey,
              };
              let customerId = 0;
              try {
                //验证客商参数
                const err = await validate(customerObj);
                if (err?.length) {
                  batchResult.resultType = BatchJobResultTypeEnums.FAILED_UNMATCHED;
                  batchResult.comment = JSON.stringify(err.map((e) => e.constraints));
                } else {
                  //保存客商列表（现在会在create中进行实时校验，确保数据一致性）
                  const dbCustomer = await this.customerService.create(currentUser, customerObj);
                  // 如果group和label不存在，记录错误记录
                  if (unMatchedGroup || unMatchedLabelName?.length > 0) {
                    batchResult.resultType = BatchJobResultTypeEnums.SUCCEED_IGNORE;
                  }
                  customerId = dbCustomer.customerId;
                }
              } catch (error) {
                processBatchJobFailed(error, batchResult);
                //isUpdate=true，覆盖更新，否则报错
                if (batchResult.resultType == BatchJobResultTypeEnums.UPDATE_DUPLICATED && isUpdate) {
                  //重复，判断有没有更新数据的权限
                  try {
                    const duplicateCustomer = await this.customerService.updateFromExcel(currentUser, customerObj);
                    if (duplicateCustomer) {
                      customerId = duplicateCustomer.customerId;
                    } else {
                      batchResult.resultType = BatchJobResultTypeEnums.FAILED_VERIFICATION;
                      batchResult.comment = '该企业已存在';
                    }
                  } catch (ue) {
                    batchResult.resultType = BatchJobResultTypeEnums.FAILED_CODE;
                    this.logger.error(`update customer error: ${ue} user=${currentUser.userId}, customerObj=${JSON.stringify(customerObj)}`);
                  }
                } else if (batchResult.resultType == BatchJobResultTypeEnums.UPDATE_DUPLICATED && !isUpdate) {
                  //选择不更新企业，则修改 batchResult.resultType为成功忽略
                  batchResult.resultType = BatchJobResultTypeEnums.SUCCEED_UPDATE_IGNORE;
                }
                this.logger.error(
                  `processJobMessage batchCreateCustomer createCustomer error: ${error} userId=${currentUser.userId}, customerObj=${JSON.stringify(
                    customerObj,
                  )}`,
                );
              }
              await this.batchHelperService.handleJobResult([batchResult]);
              return customerId;
            }
          },
          { concurrency: 3 },
        )
      ).filter((t) => t);
      if (customerIds.length > 0) {
        await this.roverGraphService.syncToNebula(orgId, customerIds, StreamTableEnums.Customer, StreamOperationEnum.INSERT);
      }
    }

    // 对未匹配到数据保
    if (unmatchedNames?.length) {
      // 保存未匹配上的公司
      await this.batchHelperService.saveUnMatchedCompanyBatch(unmatchedNames, batchId, jobId);
    }

    if (unsupported?.length) {
      // 通过name字段匹配公司，公司类型不支持的
      await this.batchHelperService.saveUnsupportedCompanyBatch(unsupported, batchId, jobId);
    }
    this.logger.info(
      `processJobMessage batchCreateCustomer Finished: batchId=${batchId},jobId=${jobId},matchedNames=${matchedNames},unmatchedNames=${unmatchedNames}`,
    );
  }

  async onJobError(jobIds: number[], batchId: number, businessType: BatchBusinessTypeEnums): Promise<any> {
    const deleteResult = await this.customerRepo.delete({ batchId, status: Not(BatchStatusEnums.Done) });
    if (deleteResult) {
      await this.batchBundleService.handleBatchCounterEvent(batchId, RoverBundleCounterType.ThirdPartyQuantity, -deleteResult.affected);
    }
  }

  async onBatchSuccess(batchEntity: BatchEntity): Promise<any> {
    await this.customerRepo.update(
      {
        batchId: batchEntity.batchId,
        status: BatchStatusEnums.Processing,
      },
      { status: BatchStatusEnums.Done },
    );
    //更新redis计数器
    await this.customerService.refreshCustomerRedisCounters(batchEntity.orgId);
  }

  async onBatchError(batchEntity: BatchEntity): Promise<any> {
    return Promise.resolve(undefined);
  }
}
