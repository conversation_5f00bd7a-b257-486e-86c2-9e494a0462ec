import { BatchEntity } from '@domain/entities/BatchEntity';
import { MonitorGroupCompanyEntity } from '@domain/entities/MonitorGroupCompanyEntity';
import { MonitorGroupEntity } from '@domain/entities/MonitorGroupEntity';
import { BatchBusinessTypeEnums } from '@domain/enums/batch/BatchBusinessTypeEnums';
import { BatchJobResultTypeEnums } from '@domain/enums/batch/BatchJobResultTypeEnums';
import { BatchStatusEnums } from '@domain/enums/batch/BatchStatusEnums';
import { BatchResultPO } from '@domain/model/batch/po/BatchResultPO';
import { BatchJobMessagePO } from '@domain/model/batch/po/message/BatchJobMessagePO';
import { MonitorImportExcelRecord } from '@domain/model/batch/po/parse/ParsedRecordBase';
import { AddCompanyRequest } from '@domain/model/monitor/request/AddCompanyRequest';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { RoverBundleCounterType } from '@kezhaozhao/saas-bundle-service';
import { processBatchJobFailed } from '@modules/batch/common/batch-job.utils';
import { CompanySearchService } from '@modules/company-management/company/company-search.service';
import { RoverGraphService } from '@modules/data-processing/data/source/rover.graph.service';
import { MonitorCompanyService } from '@modules/risk-assessment/monitor/service/monitor.company.service';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as Bluebird from 'bluebird';
import { validate } from 'class-validator';
import { Logger } from 'log4js';
import { In, Not, Repository } from 'typeorm';
import { BatchBundleService } from '../../../facade.service/service/batch.bundle.service';
import { DefaultProcessorBase } from '../default.processor.base';

@Injectable()
export class BatchMonitorProcessor extends DefaultProcessorBase {
  constructor(
    private readonly companyService: CompanySearchService,
    @InjectRepository(MonitorGroupEntity) private readonly monitorGroupRepo: Repository<MonitorGroupEntity>,
    @InjectRepository(MonitorGroupCompanyEntity) private readonly monitorRepo: Repository<MonitorGroupCompanyEntity>,
    private readonly monitorService: MonitorCompanyService,
    private readonly roverGraphService: RoverGraphService,
    private readonly batchBundleService: BatchBundleService,
  ) {
    super();
  }

  protected readonly logger: Logger = QccLogger.getLogger(BatchMonitorProcessor.name);

  getBusinessType(): BatchBusinessTypeEnums[] {
    return [BatchBusinessTypeEnums.Monitor_File];
  }

  async processJobMessage(message: BatchJobMessagePO): Promise<any> {
    const { batchId, items, orgId, operatorId, jobId, isUpdate } = message;
    this.logger.info(`processJobMessage batchCreateMonitor batchId: ${batchId}, jobId: ${jobId} , items: ${JSON.stringify(items)}`);
    const currentUser = await this.userService.getRoverUser(operatorId, orgId);
    const data: MonitorImportExcelRecord[] = items;
    // 通过name字段匹配公司，匹配不上的，标记错误
    const names: string[] = data.map((d) => d.companyName);
    const { matchedCompanyInfos, unmatchedNames, matchedNames, unsupported } = await this.companyService.matchCompanyInfoV2(names, true);
    // 对匹配到的公司数据进行处理，保存合作监控列表
    if (matchedCompanyInfos?.length > 0) {
      //生成分组KV
      const groupKV = {};
      const groups: string[] = data.map((d) => d.group);
      if (groups?.length > 0) {
        //监控列表分组
        const groupsEntities = await this.monitorGroupRepo.find({
          where: {
            name: In(groups),
            orgId,
          },
        });
        groupsEntities.forEach((group) => (groupKV[group.name] = group.id));
      }

      await Bluebird.map(
        data,
        async (excelRecord) => {
          const company = matchedCompanyInfos.find((i) => this.batchHelperService.matchCompany(excelRecord.companyName, i));
          if (company) {
            const monitorCompany = Object.assign(new AddCompanyRequest(), {
              orgId,
              createBy: operatorId,
              batchId: batchId,
              status: BatchStatusEnums.Processing,
              companyName: company.name,
              companyId: company.id,
            });
            let unMatchedGroup: string;

            //绑定groupId
            if (excelRecord?.group) {
              if (groupKV[excelRecord.group]) {
                monitorCompany.groupId = groupKV[excelRecord.group];
              } else {
                // error中标记该分组不存在
                unMatchedGroup = excelRecord.group;
              }
            }
            // 记录处理结果
            const batchResult: BatchResultPO = {
              batchId,
              jobId,
              resultType: unMatchedGroup ? BatchJobResultTypeEnums.FAILED_VERIFICATION : BatchJobResultTypeEnums.SUCCEED_PAID,
              resultInfo: excelRecord,
              resultHashkey: excelRecord.recordHashkey,
            };
            let monitorCompanyId = 0;
            if (unMatchedGroup) {
              batchResult.comment = '分组不存在';
              await this.batchHelperService.handleJobResult([batchResult]);
              return null;
            }
            //保存监控列表
            try {
              const err = await validate(monitorCompany);
              if (err?.length) {
                batchResult.resultType = BatchJobResultTypeEnums.FAILED_UNMATCHED;
                batchResult.comment = JSON.stringify(err);
              } else {
                const r = await this.monitorService.create(currentUser, [monitorCompany]);
                monitorCompanyId = r[0].id;
              }
            } catch (error) {
              processBatchJobFailed(error, batchResult);
              if (batchResult.resultType == BatchJobResultTypeEnums.UPDATE_DUPLICATED && isUpdate) {
                try {
                  await this.monitorService.createFromExcel(currentUser, [monitorCompany], true);
                } catch (ue) {
                  batchResult.resultType = BatchJobResultTypeEnums.FAILED_CODE;
                  this.logger.error(`update monitorCompany error: ${ue} user=${currentUser.userId}, monitorCompany=${JSON.stringify(monitorCompany)}`);
                }
              } else if (batchResult.resultType == BatchJobResultTypeEnums.UPDATE_DUPLICATED && !isUpdate) {
                //选择不更新企业，则修改 batchResult.resultType为成功忽略
                batchResult.resultType = BatchJobResultTypeEnums.SUCCEED_UPDATE_IGNORE;
              }
              this.logger.error(
                `processJobMessage batchCreateMonitor create monitorCompany error: ${error} userId=${currentUser?.userId}, monitorCompany=${JSON.stringify(
                  monitorCompany,
                )}`,
              );
            }
            await this.batchHelperService.handleJobResult([batchResult]);
            return monitorCompanyId;
          }
        },
        { concurrency: 3 },
      );
    }

    // 对未匹配到数据保
    if (unmatchedNames?.length) {
      await this.batchHelperService.saveUnMatchedCompanyBatch(unmatchedNames, batchId, jobId);
    }
    if (unsupported?.length) {
      // 通过name字段匹配公司，公司类型不支持的
      await this.batchHelperService.saveUnsupportedCompanyBatch(unsupported, batchId, jobId);
    }
    this.logger.info(
      `processJobMessage batchCreateMonitor Finished: batchId=${batchId},jobId=${jobId},matchedNames=${matchedNames},unmatchedNames=${unmatchedNames}`,
    );
  }

  async onJobError(jobIds: number[], batchId: number, businessType: BatchBusinessTypeEnums): Promise<any> {
    const deleteResult = await this.monitorRepo.delete({ batchId, status: Not(BatchStatusEnums.Done) });
    if (deleteResult) {
      await this.batchBundleService.handleBatchCounterEvent(batchId, RoverBundleCounterType.MonitorCompanyQuantity, -deleteResult.affected);
    }
  }

  async onBatchSuccess(batchEntity: BatchEntity): Promise<any> {
    return this.monitorRepo.update(
      {
        batchId: batchEntity.batchId,
        status: BatchStatusEnums.Processing,
      },
      { status: BatchStatusEnums.Done },
    );
  }

  async onBatchError(batchEntity: BatchEntity): Promise<any> {
    return Promise.resolve(undefined);
  }
}
