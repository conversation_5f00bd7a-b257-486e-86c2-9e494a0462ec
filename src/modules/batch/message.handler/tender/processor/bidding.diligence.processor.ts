import { TenderProcessorBase } from '../tender.processor.base';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { BatchBusinessTypeEnums } from '@domain/enums/batch/BatchBusinessTypeEnums';
import { BatchJobMessagePO } from '@domain/model/batch/po/message/BatchJobMessagePO';
import { BiddingDiligenceRecord } from '@domain/model/batch/po/parse/ParsedRecordBase';
import { BatchJobResultTypeEnums } from '@domain/enums/batch/BatchJobResultTypeEnums';
import { DiligenceBiddingRequest } from '@domain/dto/bidding/DiligenceBiddingRequest';
import * as Bluebird from 'bluebird';
import { BatchEntity } from '@domain/entities/BatchEntity';
import { BatchResultPO } from '@domain/model/batch/po/BatchResultPO';
import { BatchBaseHelper } from '../../../facade.service/helper/batch.base.helper';
import { DimensionLevel2Enums } from '@domain/enums/diligence/DimensionLevel2Enums';
import { HitDetailsBidBaseQueryParams } from '@domain/model/diligence/pojo/req&res/details/request/HitDetailsBidBaseQueryParams';
import { DimensionLevel1Enums } from '@domain/enums/diligence/DimensionLevel1Enums';
import { BiddingDiligenceRelationshipHelper } from '@modules/relationship-investigation/bidding/helper/bidding.diligence.relationship.helper';
import { BiddingDiligenceService } from '@modules/relationship-investigation/bidding/service/bidding.diligence.service';
import { Injectable } from '@nestjs/common';
import { BiddingDimensionHitsDetails } from '@domain/dto/bidding/DiligenceBiddingResponse';
import { DimensionDefinitionPO } from '@domain/model/diligence/pojo/dimension/DimensionDefinitionPO';
import { pick, sum } from 'lodash';
import { UserService } from '@modules/user-access-management/user/user.service';
import { BiddingDiligenceHelper } from '@modules/relationship-investigation/bidding/helper/bidding.diligence.helper';
import { processBatchJobFailed } from '@modules/batch/common/batch-job.utils';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { BatchResultEntity } from '@domain/entities/BatchResultEntity';
import { RoverDimensionResponse, RoverResponse, StatusCodeEnums } from '@domain/model/RoverDimensionResponse';
import { HttpQueryException } from '@commons/exceptions/HttpQueryException';

/**
 * 单个异步招标排查消息处理器
 */
@Injectable()
export class BiddingDiligenceProcessor extends TenderProcessorBase {
  protected readonly logger = QccLogger.getLogger(TenderProcessorBase.name);

  constructor(
    private readonly biddingDiligenceService: BiddingDiligenceService,
    private readonly biddingRelationshipHelper: BiddingDiligenceRelationshipHelper,
    private readonly biddingDiligenceHelper: BiddingDiligenceHelper,
    private readonly batchHelperService: BatchBaseHelper,
    private readonly userService: UserService,
    @InjectRepository(BatchEntity) protected readonly batchRepo: Repository<BatchEntity>,
    @InjectRepository(BatchResultEntity) protected readonly batchResultRepo: Repository<BatchResultEntity>,
  ) {
    super();
  }

  getBusinessType(): BatchBusinessTypeEnums[] {
    return [BatchBusinessTypeEnums.Bidding_Diligence];
  }

  async processJobMessage(message: BatchJobMessagePO): Promise<any> {
    const { batchId, items, orgId, jobId } = message;
    this.logger.info(`processJobMessage BiddingDiligence batchId:${batchId}, jobId:${jobId}, length:${items?.length}, items:${JSON.stringify(items)}`);
    const data: BiddingDiligenceRecord[] = items as BiddingDiligenceRecord[];
    await Bluebird.map(
      data,
      async (record: BiddingDiligenceRecord) => {
        const { diligenceId, dimensionKey, dimensionDefinition, keyNos } = record;
        const result: BatchResultPO = {
          batchId,
          jobId,
          resultInfo: record,
          resultHashkey: record.recordHashkey,
          resultType: BatchJobResultTypeEnums.SUCCEED_UNPAID,
          result: null,
        };
        try {
          if (dimensionKey == DimensionLevel2Enums.BiddingCompanyRelation) {
            // 关系排查
            const response = await this.biddingRelationshipHelper.getBiddingCompanyRelationsResponse(
              [dimensionDefinition],
              Object.assign(new DiligenceBiddingRequest(), { keyNos, orgId, diligenceId }),
            );
            result.result = response;
          } else {
            const batchEntity = await this.batchRepo.findOne({
              where: {
                batchId,
                orgId,
              },
            });
            const params: DiligenceBiddingRequest = batchEntity.batchInfo?.params;
            const detailParams: HitDetailsBidBaseQueryParams = Object.assign(new HitDetailsBidBaseQueryParams(), {
              orgId,
              pageIndex: 1,
              pageSize: 500,
              keyNoAndNames: params.keyNoAndNames,
              keyNos: params.keyNoAndNames?.map((e) => e.companyId),
            });
            switch (dimensionKey) {
              // 共同投标分析
              case DimensionLevel2Enums.JointBiddingAnalysis: {
                const response = await this.biddingDiligenceHelper.getJointBiddingAnalysisResponse([dimensionDefinition], detailParams);
                result.result = response;
                break;
              }
              // 内部黑名单排查
              case DimensionLevel1Enums.Risk_InnerBlacklist: {
                const response = await this.biddingDiligenceHelper.getInnerBlackListResponse([dimensionDefinition], detailParams);
                result.result = response;
                break;
              }
              // 潜在利益冲突
              case DimensionLevel1Enums.Risk_InterestConflict: {
                const response = await this.biddingDiligenceHelper.getInterestConflictResponse([dimensionDefinition], detailParams);
                result.result = response;
                break;
              }
              // 涉采购不良行为
              case DimensionLevel2Enums.PurchaseIllegal: {
                const response = await this.biddingDiligenceHelper.getPurchaseIllegalResponse([dimensionDefinition], detailParams);
                result.result = response;
                break;
              }
              // 资质筛查
              case DimensionLevel2Enums.BiddingCompanyCertification:
                const response = await this.biddingDiligenceHelper.getCertificationV2Response(
                  [dimensionDefinition],
                  detailParams,
                  params.certification,
                  diligenceId,
                  orgId,
                );
                result.result = response;
                break;
              default:
                break;
            }
          }
          if (result.result['statusCode'] !== StatusCodeEnums.SUCCESS) {
            // 排查异常
            throw new HttpQueryException(result.result['message']);
          }
          result.resultType = BatchJobResultTypeEnums.SUCCEED_PAID;
          await this.batchHelperService.handleJobResult([result]);
        } catch (e) {
          //出错后保存到数据库中
          this.logger.error(`Bidding_Diligence error: batchId=${batchId},jobId=${jobId},param=${JSON.stringify(record)},error=${e}`);
          this.logger.error(e);
          processBatchJobFailed(e, result);
          await this.batchHelperService.handleJobResult([result]);
        }
      },
      { concurrency: 5 },
    );
    this.logger.info(`processJobMessage Bidding_Diligence Finished: batchId=${batchId},jobId=${jobId}`);
  }

  async onJobError(jobIds: number[], batchId: number, businessType: BatchBusinessTypeEnums): Promise<any> {
    return Promise.resolve(undefined);
  }

  async onBatchSuccess(batchEntity: BatchEntity): Promise<any> {
    const hitDetails: BiddingDimensionHitsDetails[] = [];
    const directRelationList = [];
    const contactRelationList = [];
    const suspectRelationList = [];
    const batchResults = await this.batchResultRepo.find({
      where: {
        batchId: batchEntity.batchId,
      },
    });

    const dataList = batchResults.map((result) => {
      return {
        ...result.result,
        jobId: result.jobId,
      };
    }) as RoverDimensionResponse<any>[];

    const directSet = new Set<string>();
    const contactSet = new Set<string>();
    const suspectSet = new Set<string>();
    dataList.forEach((response) => {
      const result = response.data;
      if (result) {
        if (result?.key !== DimensionLevel2Enums.BiddingCompanyRelation) {
          hitDetails.push(result);
        } else if (result.totalHits > 0) {
          const directRelation = result.subDimension.find((x) => x.key == DimensionLevel2Enums.BiddingCompanyRelationship);
          const contactRelationship = result.subDimension.find((x) => x.key == DimensionLevel2Enums.ContactRelationship);
          const suspectRelation = result.subDimension.find((x) => x.key == DimensionLevel2Enums.BiddingCompanyRelationship2);
          if (directRelation?.data?.length > 0) {
            directRelation.data.forEach((x) => {
              const pairKey = [x.startCompanyKeyno, x.endCompanyKeyno].sort().join('-');
              if (!directSet.has(pairKey)) {
                directSet.add(pairKey);
              }
              directRelationList.push(x);
            });
          }

          if (contactRelationship?.data?.length > 0) {
            contactRelationship.data.forEach((x) => {
              const pairKey = [x.startCompanyKeyno, x.endCompanyKeyno].sort().join('-');
              if (!contactSet.has(pairKey)) {
                contactSet.add(pairKey);
                contactRelationList.push(x);
              }
            });
          }
          if (suspectRelation?.data?.length > 0) {
            suspectRelation.data.forEach((x) => {
              const pairKey = [x.startCompanyKeyno, x.endCompanyKeyno, x.type].sort().join('-');
              if (!suspectSet.has(pairKey)) {
                suspectSet.add(pairKey);
                suspectRelationList.push(x);
              }
            });
          }
        }
      }
    });
    const dimensionDefinition = batchResults.find((r) => (r.result as RoverDimensionResponse<any>).dimensionKey == DimensionLevel2Enums.BiddingCompanyRelation)
      .resultInfo['dimensionDefinition'];
    const relationHitsDetails = this.getBiddingCompanyRelations(
      dimensionDefinition,
      directRelationList,
      contactRelationList,
      suspectRelationList,
      directSet,
      contactSet,
    );
    hitDetails.push(relationHitsDetails);

    const { params, diligenceId } = batchEntity.batchInfo;

    const hitDetailsResponse = new RoverResponse();
    hitDetailsResponse.statusCode = StatusCodeEnums.SUCCESS;
    hitDetailsResponse.dataList = dataList;
    hitDetailsResponse.data = hitDetails;

    await this.biddingDiligenceService.diligenceSuccess(diligenceId, params, hitDetailsResponse);
    return Promise.resolve(undefined);
  }

  /**
   * 关系排查结果处理
   * @param dimensionDefinition
   * @param relations1
   * @param relations2
   * @returns
   */
  private getBiddingCompanyRelations(
    dimensionDefinition: DimensionDefinitionPO,
    directRelationList: any[],
    contactRelationList: any[],
    suspectRelationList: any[],
    directSet: Set<string>,
    contactSet: Set<string>,
  ) {
    const result = Object.assign(new BiddingDimensionHitsDetails(), pick(dimensionDefinition, ['key', 'name', 'status', 'sort']), { totalHits: 0 });
    if (dimensionDefinition.status == 0) {
      return result;
    }
    result.subDimension = [];

    // 投标公司直接关系排查
    const relationShipDim = dimensionDefinition.subDimensionList.find((x) => x.status == 1 && x.key == DimensionLevel2Enums.BiddingCompanyRelationship);
    if (relationShipDim) {
      const hitDetail = Object.assign(new BiddingDimensionHitsDetails(), pick(relationShipDim, ['key', 'name', 'status', 'sort']), {
        totalHits: 0,
        data: [],
      });
      if (directRelationList?.length > 0) {
        hitDetail.totalHits = directRelationList.length;
        hitDetail.data = directRelationList;
      }
      result.subDimension.push(hitDetail);
    }

    // 联系方式关联排查
    const contactRelationShipDim = dimensionDefinition.subDimensionList.find((x) => x.status == 1 && x.key == DimensionLevel2Enums.ContactRelationship);
    if (contactRelationShipDim) {
      const hitDetail = Object.assign(new BiddingDimensionHitsDetails(), pick(contactRelationShipDim, ['key', 'name', 'status', 'sort']), {
        totalHits: 0,
        data: [],
      });
      if (contactRelationList?.length > 0) {
        hitDetail.totalHits = contactRelationList.length;
        hitDetail.data = contactRelationList;
      }
      result.subDimension.push(hitDetail);
    }

    // 投标公司疑似关系排查
    const relationShip2Dim = dimensionDefinition.subDimensionList.find((x) => x.status == 1 && x.key == DimensionLevel2Enums.BiddingCompanyRelationship2);
    if (relationShip2Dim) {
      const hitDetail = Object.assign(new BiddingDimensionHitsDetails(), pick(relationShip2Dim, ['key', 'name', 'status', 'sort']), {
        totalHits: 0,
        data: [],
      });
      const filteredSuspectRelationList = suspectRelationList.filter((x) => {
        const pairKey = [x.startCompanyKeyno, x.endCompanyKeyno].sort().join('-');
        if (directSet.has(pairKey) || contactSet.has(pairKey)) {
          return false;
        }
        return true;
      });

      if (filteredSuspectRelationList?.length > 0) {
        hitDetail.totalHits = filteredSuspectRelationList.length;
        hitDetail.data = filteredSuspectRelationList;
      }
      result.subDimension.push(hitDetail);
    }
    result.totalHits = sum(result.subDimension.map((x) => x.totalHits));
    return result;
  }

  async onBatchError(batchEntity: BatchEntity): Promise<any> {
    const { diligenceId } = batchEntity.batchInfo;
    const currentUser = await this.userService.getRoverUser(batchEntity.creatorId, batchEntity.orgId);
    await this.biddingDiligenceService.diligenceError(currentUser, diligenceId);
    return Promise.resolve(undefined);
  }
}
