/**
 * @file batchJobMonitorQueue batch主任务循环监控处理
 */
import { BadRequestException, Injectable } from '@nestjs/common';
import { BatchJobMessagePO } from '@domain/model/batch/po/message/BatchJobMessagePO';
import { BatchStatusEnums } from '@domain/enums/batch/BatchStatusEnums';
import { BatchBusinessTypeEnums } from '@domain/enums/batch/BatchBusinessTypeEnums';
import { JobMonitorMessagePO, JobMonitorMessageTypeEnums } from '@domain/model/batch/po/message/JobMonitorMessagePO';
import * as Bluebird from 'bluebird';
import { KzzConsumerConfig, RabbitMQ } from '@kezhaozhao/message-queue';
import { Logger } from 'log4js';
import { ContextManager, QccLogger } from '@kezhaozhao/qcc-logger';
import { InjectRepository } from '@nestjs/typeorm';
import { BatchEntity } from '@domain/entities/BatchEntity';
import { Brackets, In, Repository } from 'typeorm';
import { BatchJobEntity } from '@domain/entities/BatchJobEntity';
import { BatchResultEntity } from '@domain/entities/BatchResultEntity';
import { BatchConstants } from '../common/batch.constants';
import { QueueService } from '@core/config/queue.service';
import { BatchMessageHandlerDiligence } from './diligence/batch.message.handler.diligence';
import { BatchMessageHandlerDefault } from './default/batch.message.handler.default';
import { BatchMessageHandlerExport } from './export/batch.message.handler.export';
import { BatchMessageHandlerTender } from './tender/batch.message.handler.tender';
import { BatchMessageHandlerAbstract } from './batch.message.handler.abstract';
import { RecordBasePO } from '@domain/model/batch/po/parse/ParsedRecordBase';
import { v4 } from 'uuid';
import { BatchBaseHelper } from '../facade.service/helper/batch.base.helper';
import { BatchJobResultTypeEnums } from '@domain/enums/batch/BatchJobResultTypeEnums';
import { BatchCreatorHelperBase } from '../facade.service/helper/batch.creator.helper.base';
import { RoverExceptions } from '@commons/constants/exceptionConstants';
import { captureException } from '@sentry/node';
import { PulsarError } from '@kezhaozhao/message-queue/dist/exceptions/PulsarError';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { BatchProgressLock } from '@commons/distributed_lock/BatchProgressLock';
import { BatchTenderDiligenceEntity } from '@domain/entities/BatchTenderDiligenceEntity';
import { BiddingDiligenceService } from '@modules/relationship-investigation/bidding/service/bidding.diligence.service';
import { DiligenceTenderHistoryEntity } from '@domain/entities/DiligenceTenderHistoryEntity';
import { BatchMessageHandlerSpecific } from './specific/batch.message.handler.specific';
import { BatchSpecificDiligenceEntity } from '@domain/entities/BatchSpecificDiligenceEntity';
import { SpecificFacadeService } from '@modules/relationship-investigation/specific/specific.facade.service';
import { BatchMessageHandlerPotential } from './potential/batch.message.handler.potential';
import { BatchTypeEnums } from '@domain/enums/batch/BatchTypeEnums';
import { RoverUserModel } from '@domain/model/RoverUserModel';
import { BatchCheckService } from '../facade.service/service/batch.check.service';

@Injectable()
export class BatchMessageService {
  public batchJobMonitorQueue: RabbitMQ;
  private readonly logger: Logger = QccLogger.getLogger(BatchMessageService.name);
  private allHandlers: BatchMessageHandlerAbstract[] = [];

  constructor(
    @InjectRepository(BatchEntity) private readonly batchRepo: Repository<BatchEntity>,
    @InjectRepository(BatchJobEntity) private readonly batchJobRepo: Repository<BatchJobEntity>,
    @InjectRepository(BatchResultEntity) private readonly batchResultRepo: Repository<BatchResultEntity>,
    private readonly queueService: QueueService,
    batchMessageHandlerPotential: BatchMessageHandlerPotential,
    batchMessageHandlerDiligence: BatchMessageHandlerDiligence,
    private readonly batchMessageHandlerDefault: BatchMessageHandlerDefault,
    batchMessageHandlerExport: BatchMessageHandlerExport,
    batchMessageHandlerTender: BatchMessageHandlerTender,
    batchMessageHandlerSpecific: BatchMessageHandlerSpecific,
    private readonly batchHelperService: BatchBaseHelper,
    private readonly batchCreatorHelperService: BatchCreatorHelperBase,
    private readonly redisService: RedisService,
    @InjectRepository(BatchTenderDiligenceEntity) private readonly batchTenderDiligenceRepo: Repository<BatchTenderDiligenceEntity>,
    @InjectRepository(BatchSpecificDiligenceEntity) private readonly batchSpecificDiligenceRepo: Repository<BatchSpecificDiligenceEntity>,
    private readonly biddingServiceV2: BiddingDiligenceService,
    private readonly specificFacadeService: SpecificFacadeService,
    private readonly batchCheckService: BatchCheckService,
  ) {
    Array.prototype.push.apply(this.allHandlers, [
      batchMessageHandlerDefault,
      batchMessageHandlerDiligence,
      batchMessageHandlerExport,
      batchMessageHandlerTender,
      batchMessageHandlerSpecific,
      batchMessageHandlerPotential,
    ]);

    this.batchJobMonitorQueue = this.queueService.batchJobMonitorQueue;
    this.batchJobMonitorQueue
      .consume(this.processJobMonitorMessage.bind(this), Object.assign(new KzzConsumerConfig(), { concurrency: BatchConstants.Consumer.Monitor }))
      .catch((err) => this.logger.error(err));
    this.batchJobMonitorQueue.on('failed', this.handleBatchJobMonitorQueueError.bind(this));
  }

  async handleBatchJobMonitorQueueError(messageData: JobMonitorMessagePO, error) {
    const { batchId } = messageData;
    this.logger.error(
      `handleBatchJobMonitorQueueError() error，标记整个批量任务失败, batchId=${batchId}, messageData=${JSON.stringify(messageData)}, error=${error?.message}`,
    );
    this.logger.error(error);
    captureException(new PulsarError(`onBatchExportQueueError() error:${error?.message}`, error), {
      extra: {
        messageData,
      },
    });
  }

  /**
   * 创建批量任务的时候，会先发送一条消息到队列， 这里负责处理这条消息：
   * 1. 检查批量任务对应的子任务是否有待处理，有的话发送到 job 队列
   * 2. 同时检查job result中的条数是否跟batch中的总条数一样，一样就说明任务完成
   *
   * 消息会循环发送，直到触发结束
   * @param messageData
   */
  async processJobMonitorMessage(messageData: JobMonitorMessagePO): Promise<BatchJobMessagePO[]> {
    this.logger.debug(`process processJobMonitorMessage with traceId=${ContextManager.current.traceId()}`);
    const { batchId, businessType, index, type } = messageData;

    //一定确保 monitorMessage的幂等性
    // const consumeHashkey = md5(`monitor_hash_consume_${businessType}_${type}_${batchId}_${index}`);

    // try {
    //   await this.redlock.acquire([consumeHashkey], 24 * 3600 * 1000);
    // } catch (e) {
    //   this.logger.warn(`processJobMonitorMessage-duplicated-consume-warn,batchId=${batchId},index=${index},error=${e.message}`, {
    //     messageData,
    //   });
    //   return;
    // }

    let startDate = messageData.startDate;
    this.logger.info(`processJobMonitorMessage start, batchId=${batchId} index=${index} type=${type} `);
    // 扫描batch_job表, 推送任务到 job queue， 开始处理
    const messageHandler = this.getProperlyMessageHandler(businessType);
    const batchEntity: BatchEntity = await this.batchRepo.findOne({ where: { batchId } });
    try {
      const messagePOList: BatchJobMessagePO[] = [];
      if (!batchEntity) {
        this.logger.info(`batchId=${batchId} is not exist or  status=${batchEntity?.status} is not waiting to process)`);
        return messagePOList;
      } else if (batchEntity.status == BatchStatusEnums.Waiting) {
        //收到第一条monitor 消息的时候，把对应的batch 标记为开始处理并读取所有job，发送消息
        const partialUpdate = Object.assign(new BatchEntity(), {
          status: BatchStatusEnums.Processing,
          endDate: null,
        });
        if (!batchEntity.startDate) {
          partialUpdate.startDate = new Date();
        }
        await this.batchRepo.update(batchId, partialUpdate);
      }
      startDate = batchEntity.startDate?.getTime() || startDate;
      // const messageHandler = this.getProperlyMessageHandler(businessType);
      switch (type) {
        case JobMonitorMessageTypeEnums.Scan: {
          // 分批扫描等待状态的job放入队列,并扫描超时的任务处理
          const lastJobId = await this.proceessWatingJobsAndOvertimeJobs(messageData, messageHandler);
          const newMessageData = Object.assign(new JobMonitorMessagePO(), messageData, {
            index: messageData.index + 1,
            monitorUUID: v4(),
          });
          if (lastJobId > 0) {
            //发现还有数据就记录下 最后处理的ID
            newMessageData.lastProceedJobId = lastJobId;
            newMessageData.type = JobMonitorMessageTypeEnums.Scan;
          } else {
            // 没有等待中的数据了，切换到Monitor模式
            newMessageData.lastProceedJobId = -1;
            newMessageData.type = JobMonitorMessageTypeEnums.Monitor;
          }

          // this.logger.info(`send-scan-message-from-scan for batchId=${batchId}, index=${newMessageData.index}, monitorUUID=${newMessageData.monitorUUID}`, {
          //   messageData: newMessageData,
          // });

          // const producerHashkey = md5(`monitor_hash_produce_${businessType}_${type}_${batchId}_${newMessageData.index}`);
          // let lock: Lock;
          // try {
          //   lock = await this.redlock.acquire([producerHashkey], 24 * 3600 * 1000);

          // } catch (e) {
          //   this.logger.warn(`send-scan-message-from-scan-duplicated-produce-warn,batchId=${batchId},index=${newMessageData.index},error=${e.message}`, {
          //     messageData: newMessageData,
          //   });
          // }
          await this.batchJobMonitorQueue.sendMessageV2(newMessageData, {
            ttl: BatchConstants.getMonitorInterval(businessType),
            retries: 1,
            breakTrace: true,
            traceTags: [
              {
                key: 'batchId',
                val: batchId + '',
                overridable: true,
              },
            ],
          });

          // 更新当前 Batch 的统计信息
          await messageHandler.refreshBatchStatus(batchEntity, false);
          break;
        }
        case JobMonitorMessageTypeEnums.Monitor: {
          // 默认会再推送一条监听monitor表的消息
          const message: JobMonitorMessagePO = Object.assign(new JobMonitorMessagePO(), messageData, {
            index: index + 1,
          });
          // 只检测任务是否都已经完成
          // 1. 如果有处理失败的，看是否需要重试
          // 2. 如果全是处理成功，标记batch完成并更新统计信息
          let shouldFinished = await this.getProperlyMessageHandler(businessType)?.getProperlyProcessor(businessType)?.shouldBatchFinished(batchEntity);
          if (shouldFinished) {
            if (batchEntity.status != BatchStatusEnums.Done && batchEntity.status != BatchStatusEnums.Error) {
              // batch里的job全部完成
              await messageHandler.onBatchSuccess(batchEntity);
            } else {
              // batch 已经标记成功或者失败，不需要处理
            }
          } else {
            const batchTimeoutHours = BatchConstants.getBatchTimeoutHours(businessType, batchEntity.recordCount);
            shouldFinished = Date.now() - startDate > batchTimeoutHours * 3600 * 1000;
            if (shouldFinished) {
              // 指定时间之后发现还未处理结束的批量任务，标记失败
              const errorMsg = `超过指定时间(${batchTimeoutHours}小时)任务还未结束`;
              await messageHandler.onBatchError(batchEntity, errorMsg);
              this.logger.error(`processJobMonitorMessage: batchId=${batchId} error=${errorMsg}`);
            } else {
              //这里执行扫描的话，应该只扫描 status=processing 的job，其他的job都应该在上面的Scan事件中已经被处理
              // 扫描处理超时任务
              await this.proceessWatingJobsAndOvertimeJobs(messageData, messageHandler);
              // job都已经全部扫描到 mq中，这里就只需要监控batch 是否已经完成
              message.lastProceedJobId = -1;
              message.type = JobMonitorMessageTypeEnums.Monitor;
              await messageHandler.refreshBatchStatus(batchEntity, false);
              await this.batchJobMonitorQueue.sendMessageV2(message, {
                ttl: BatchConstants.getMonitorInterval(businessType),
                retries: 3,
                breakTrace: true,
                traceTags: [
                  {
                    key: 'batchId',
                    val: batchId + '',
                    overridable: true,
                  },
                ],
              });
            }
          }
          break;
        }
      }
      return messagePOList;
    } catch (e) {
      await messageHandler.onBatchError(batchEntity, e.message?.substr(0, 200));
      this.logger.error(`processJobMonitorMessage: batchId=${batchId} error=${e.message}`);
      this.logger.error(e);
    }
  }

  // public async scanJobsV2(messageData: JobMonitorMessagePO, messageHandler: BatchMessageHandlerAbstract, scanFromMonitor = false) {
  //   const { batchId, batchType, businessType, operatorId, orgId, index, lastProceedJobId, type, isUpdate, source } = messageData;
  //   const fetchSize = BatchConstants.getScanJobSize(businessType);
  //   // 默认会再推送一条监听monitor表的消息
  //   const qb = this.batchJobRepo.createQueryBuilder('job');
  //   qb.where('job.batchId = :batchId', { batchId });
  //   if (lastProceedJobId > 0) {
  //     qb.andWhere('job.jobId > :lastProceedJobId', { lastProceedJobId });
  //   }
  //   qb.andWhere(
  //     new Brackets((qb1) => {
  //       qb1
  //         .where('job.status = :status1', { status1: BatchStatusEnums.Waiting })
  //         .orWhere('(job.status = :status2 and (job.startDate is null or TIMESTAMPDIFF(MINUTE, job.startDate, NOW()) > 5))', {
  //           status2: BatchStatusEnums.Processing,
  //         });
  //     }),
  //   );
  //   const jobs = await qb.take(fetchSize).getMany();
  //   this.logger.info(
  //     `processJobMonitorMessage scan jobs, batchId=${batchId} index=${index} type=${type} ,jobs.length=${jobs.length} ,checkFromMonitor=${scanFromMonitor}  `,
  //   );
  //   if (jobs.length > 0) {
  //     // 需要发送消息的任务
  //     const reSendMsgJobs = [];
  //     const noStartDateJobs = jobs.filter((x) => x.status == BatchStatusEnums.Processing && !x.startDate);
  //     if (noStartDateJobs.length > 0) {
  //       reSendMsgJobs.push(...noStartDateJobs);
  //       const jobIds = noStartDateJobs.map((j) => j.jobId);
  //       // 更新任务状态，准备重新执行
  //       this.logger.warn(`processJobMonitorMessage update noStartDateJobs status, jobIds: ${JSON.stringify(jobIds)}`);
  //       await this.batchJobRepo.update(jobIds, {
  //         status: BatchStatusEnums.Queuing,
  //         comment: scanFromMonitor ? `auto-retry at from processJobMonitorMessage() at ${new Date()}` : undefined,
  //       });
  //     }
  //     // 正常任务
  //     const waitingJobs = jobs.filter((x) => x.status == BatchStatusEnums.Waiting);
  //     if (waitingJobs.length > 0) {
  //       reSendMsgJobs.push(...waitingJobs);
  //     }
  //     // 执行超时的任务
  //     const overTimeJobs = jobs.filter((x) => x.status == BatchStatusEnums.Processing && x.startDate && Date.now() - x.startDate.getTime() > 5 * 60 * 1000);
  //     if (overTimeJobs.length > 0) {
  //       const properlyQueue = messageHandler.getTargetQueue();
  //       const msg = `processJobMonitorMessage: batchId=${batchId} ,queueName=${properlyQueue.queueName} ,overtimeJobs.length=${
  //         overTimeJobs.length
  //       }, jobIds=${overTimeJobs.join(',')}\``;
  //       this.logger.warn(msg);
  //       captureException(new Error(msg), {
  //         extra: {
  //           ...messageData,
  //         },
  //       });
  //     }
  //     await Bluebird.all([
  //       this.processOverTimeJobs(overTimeJobs, batchId),
  //       this.processSendMsgJobs(
  //         reSendMsgJobs,
  //         batchId,
  //         batchType,
  //         businessType,
  //         operatorId,
  //         orgId,
  //         scanFromMonitor,
  //         messageHandler,
  //         isUpdate,
  //         source,
  //         index,
  //         type,
  //       ),
  //     ]);
  //   }
  //   return jobs.map((j) => j.jobId);
  // }

  /**
   * 扫描等待状态的job和超时的任务
   * @param messageData
   * @param messageHandler
   * @returns
   */
  async proceessWatingJobsAndOvertimeJobs(messageData: JobMonitorMessagePO, messageHandler: BatchMessageHandlerAbstract) {
    // 扫描等待状态的job
    const lastJobId = await this.scanJobsWaiting(messageData, messageHandler);
    // 扫描超时的任务
    await this.scanJobsOvertime(messageData);
    return lastJobId;
  }

  /**
   * 只扫描等待状态的job
   * @param messageData
   * @param messageHandler
   * @param scanFromMonitor
   * @returns
   */
  public async scanJobsWaiting(messageData: JobMonitorMessagePO, messageHandler: BatchMessageHandlerAbstract) {
    const { batchId, batchType, businessType, operatorId, orgId, index, lastProceedJobId, type, isUpdate, source } = messageData;
    const fetchSize = BatchConstants.getScanJobSize(businessType);
    // 默认会再推送一条监听monitor表的消息
    const qb = this.batchJobRepo.createQueryBuilder('job');
    qb.where('job.batchId = :batchId', { batchId });
    if (lastProceedJobId > 0) {
      qb.andWhere('job.jobId > :lastProceedJobId', { lastProceedJobId });
    }
    qb.andWhere('job.status = :status', { status: BatchStatusEnums.Waiting });
    qb.orderBy('job.jobId', 'ASC');
    const jobs = await qb.take(fetchSize).getMany();
    this.logger.info(`processJobMonitorMessage scanJobsWaiting, batchId=${batchId} index=${index} type=${type} ,jobs.length=${jobs.length}`);
    let lastJobId = -1;
    if (jobs.length > 0) {
      // 需要发送消息的任务
      const jobIds = jobs.map((j) => j.jobId);
      lastJobId = jobs[jobs.length - 1].jobId;
      // 更新任务状态为队列中
      this.logger.info(`processJobMonitorMessage update noStartDateJobs status, jobIds: ${JSON.stringify(jobIds)}`);
      await this.batchJobRepo.update(jobIds, {
        status: BatchStatusEnums.Queuing,
        comment: `scanJobsFromScan().scanAt ${new Date()}`,
      });
      this.processSendMsgJobs(jobs, batchId, batchType, businessType, operatorId, orgId, false, messageHandler, isUpdate, source, index, type);
    }
    return lastJobId;
  }

  /**
   * 扫描超时的任务
   * @param messageData
   */
  async scanJobsOvertime(messageData: JobMonitorMessagePO) {
    const { batchId, businessType } = messageData;
    const fetchSize = BatchConstants.getScanJobSize(businessType);
    const qb = this.batchJobRepo.createQueryBuilder('job');
    qb.where('job.batchId = :batchId', { batchId });
    qb.andWhere(
      new Brackets((qb1) => {
        qb1
          .where('(job.status = :status2 and (job.startDate is null or TIMESTAMPDIFF(MINUTE, job.startDate, NOW()) > 5))', {
            status2: BatchStatusEnums.Processing, // 进入Processing状态，5分钟没有结束的
          })
          .orWhere('(job.status = :status3 and (TIMESTAMPDIFF(MINUTE, job.updateDate, NOW()) > 20))', {
            status3: BatchStatusEnums.Queuing, // 进入Queuing状态，20分钟没有被消费的
          });
      }),
    );
    const jobs = await qb.take(fetchSize).getMany();
    if (jobs.length > 0) {
      const processingJob = jobs.filter((j) => j.status == BatchStatusEnums.Processing);
      if (processingJob.length > 0) {
        const msg = `scanJobsFromMonitor().processTimeout, jobIds: ${JSON.stringify(processingJob.map((j) => j.jobId))}`;
        this.logger.warn(msg);
      }
      const queuingJobs = jobs.filter((j) => j.status == BatchStatusEnums.Queuing);
      if (queuingJobs.length > 0) {
        const msg = `scanJobsFromMonitor().queueTimeout, jobIds: ${JSON.stringify(queuingJobs.map((j) => j.jobId))}`;
        this.logger.warn(msg);
      }
      // 处理超时的任务
      await this.processOverTimeJobs(jobs, batchId);
    }
  }

  /**
   * 将任务信息放入队列排队
   * @param jobsToSend
   * @param batchId
   * @param batchType
   * @param businessType
   * @param operatorId
   * @param orgId
   * @param scanFromMonitor
   * @param messageHandler
   * @param isUpdate
   * @param source
   * @param index
   * @param type
   */
  async processSendMsgJobs(
    jobsToSend: any[],
    batchId: number,
    batchType: BatchTypeEnums,
    businessType: BatchBusinessTypeEnums,
    operatorId: number,
    orgId: number,
    scanFromMonitor: boolean,
    messageHandler: BatchMessageHandlerAbstract,
    isUpdate: boolean,
    source: number,
    index: number,
    type: JobMonitorMessageTypeEnums,
  ) {
    if (jobsToSend.length > 0) {
      const basePo = {
        batchId,
        batchType,
        businessType,
        startDate: Date.now(),
        operatorId,
        orgId,
      };
      const time1 = Date.now();
      await Bluebird.map(
        jobsToSend,
        async (job) => {
          const po: BatchJobMessagePO = Object.assign(new BatchJobMessagePO(), basePo, {
            items: job.jobInfo.items?.map((i: RecordBasePO) => {
              if (!i.recordHashkey) {
                i.recordHashkey = v4();
              }
              return i;
            }),
            jobId: job.jobId,
            isUpdate,
            source,
          });
          //messagePOList.push(po); TODO 避免消息内容过多，浪费内存，只在测试时候需要开启
          const properlyQueue = messageHandler.getTargetQueue();
          await properlyQueue.sendMessageV2(po, {
            breakTrace: true,
            traceTags: [
              {
                key: 'batchId',
                val: batchId + '',
                overridable: true,
              },
            ],
          });
          // @ts-ignore
          const m = `processJobMonitorMessage: {batchId=${batchId},index=${index},businessType=${businessType}, send jobs to queue=${properlyQueue.queueName},scanFromMonitor=${scanFromMonitor}`;
          this.logger.debug(m);
        },
        { concurrency: 3 },
      );
      this.logger.info(
        `processJobMonitorMessage send job message, batchId=${batchId} index=${index} type=${type} ,jobs.length=${jobsToSend.length} ,time=${
          Date.now() - time1
        }ms`,
      );
    }
  }

  /**
   * 执行超时的任务处理
   * @param overTimeJobs
   * @param batchId
   */
  async processOverTimeJobs(overTimeJobs: BatchJobEntity[], batchId: number) {
    if (overTimeJobs.length > 0) {
      const jobIds = overTimeJobs.map((j) => j.jobId);
      this.logger.info(`processJobMonitorMessage update overTimeJobs status, jobIds: ${JSON.stringify(jobIds)}`);
      // 标记任务处理超时
      await this.batchJobRepo.update(jobIds, {
        status: BatchStatusEnums.Done_Timeout,
        comment: 'job 执行超时 5 分钟',
      });
      // 处理 batch_result
      await Bluebird.map(
        overTimeJobs,
        async (job) => {
          const jobInfo = job.jobInfo;
          const itemSize = jobInfo.itemSize;
          const [jobResult, jobResultSize] = await this.batchResultRepo.findAndCount({ where: { jobId: job.jobId } });
          if (jobResultSize < itemSize) {
            const resultKeyList = jobResult?.map((x) => x.resultHashkey) || [];
            const newJobResultList = jobInfo.items
              .map((item) => {
                if (resultKeyList.includes(item.recordHashkey)) {
                  return null;
                }
                return Object.assign(new BatchResultEntity(), {
                  resultType: BatchJobResultTypeEnums.OVER_TIME, // 执行超时
                  batchId,
                  resultInfo: item,
                  jobId: job.jobId,
                  comment: 'job 执行超时 5 分钟',
                  resultHashkey: item.recordHashkey,
                });
              })
              .filter((x) => x);
            if (newJobResultList.length > 0) {
              await this.batchResultRepo.save(newJobResultList);
            }
          }
        },
        { concurrency: 3 },
      );
    }
  }

  public getProperlyMessageHandler(businessType: BatchBusinessTypeEnums): BatchMessageHandlerAbstract {
    const handler: BatchMessageHandlerAbstract = this.batchMessageHandlerDefault;
    for (const h of this.allHandlers) {
      if (h.match(businessType)) {
        return h;
      }
    }
    return handler;
    // 可优化成如下代码
    // return this.allHandlers.find((h) => h.match(businessType)) || this.batchMessageHandlerDefault;
  }

  /**
   * 单个招标排查重试
   * @param user
   * @param diligenceId
   * @returns
   */
  async tenderDiligenceRetry(user: RoverUserModel, diligenceId: number): Promise<DiligenceTenderHistoryEntity> {
    // 找到查排查失败的batch
    const batchDiligence = await this.batchTenderDiligenceRepo.findOne({ where: { id: diligenceId } });
    if (!batchDiligence?.batchId) {
      // 没有找到对应的batch,不是走batch逻辑的批量排查, 重新创建batch,发消息
      return this.biddingServiceV2.retryDiligence(user, diligenceId);
    } else {
      const { affected } = await this.retryBatch(batchDiligence.batchId, user);
      if (affected > 0) {
        return this.biddingServiceV2.retryDiligenceCheck(user, diligenceId, null);
      } else {
        throw new BadRequestException(RoverExceptions.Batch.NotNeedRetry);
      }
    }
  }

  /**
   * 重新执行指定的批量任务，会把batch 关联的失败的result找出来，重试他们关联的job，保存batch result 的时候会去检查是否已经存在，如果存在则不再保存(之前的batch中改job中对应的某些item已经成功执行过了，会自动忽略保存batch job result)
   * 1. 判断batch 是否存在并且状态是完结的(succeed or failed)
   * 2. 找到batch对应的job，目前能重试的 resultType: BatchJobResultTypeEnums.FAILED_CODE, BatchJobResultTypeEnums.FAILED_VERIFICATION, BatchJobResultTypeEnums.FAILED_BUNDLE_LIMITED
   * 3. 修改job状态为waiting
   * 4. 修改batch状态为waiting，发送monitor消息
   * 5. 后续的处理逻辑和正常的批量任务处理逻辑一样
   */
  async retryBatch(batchId: number, user: RoverUserModel) {
    // const batchEntity = await this.batchRepo.findOne(batchId);
    // if (!batchEntity) {
    //   throw new BadRequestException(`batchId=${batchId} 不存在`);
    // }
    // if (batchEntity.orgId !== user.currentOrg) {
    //   throw new BadRequestException(`batchId=${batchId} 所属组织不匹配`);
    // }
    // if (batchEntity.creatorId !== user.userId) {
    //   throw new BadRequestException(`batchId=${batchId} 创建人不匹配`);
    // }
    const batchEntity = await this.checkBatch(batchId, user);
    if (batchEntity.status !== BatchStatusEnums.Done && batchEntity.status !== BatchStatusEnums.Error) {
      throw new BadRequestException(`batchId=${batchId} 状态不是完结状态`);
    }
    if (batchEntity.canRetry !== 1) {
      throw new BadRequestException(RoverExceptions.Batch.NotNeedRetry);
    }

    await this.batchCheckService.checkBatchCount(user, batchEntity.businessType);

    const failedResults = await this.batchResultRepo.find({
      where: {
        batchId,
        resultType: In([
          BatchJobResultTypeEnums.FAILED_CODE,
          BatchJobResultTypeEnums.FAILED_VERIFICATION,
          BatchJobResultTypeEnums.FAILED_BUNDLE_LIMITED,
          BatchJobResultTypeEnums.OVER_TIME,
        ]),
      },
      select: ['jobId', 'resultInfo', 'resultId'],
    });
    const items = failedResults.map((r) => r.resultInfo);
    if (!items?.length) {
      throw new BadRequestException(RoverExceptions.Batch.NotNeedRetry);
    }
    //  批量排查和巡检会预先扣除套餐额度
    const { companyCounter, historyCounter, dailyCounter, withholdingCount, withholdingRecordCount } =
      await this.batchCreatorHelperService.checkDiligenceBundleUsage(user, { succeedItems: items }, batchEntity.businessType);

    try {
      const jobIds = failedResults.map((r) => r.jobId);
      const jobResultIds = failedResults.map((r) => r.resultId);
      await Bluebird.all([
        this.batchJobRepo.update(jobIds, {
          status: BatchStatusEnums.Waiting,
          comment: `重试, userId=${user.userId}, date=${new Date()}`,
          endDate: null,
          errorDate: null,
        }),
        this.batchRepo.update(batchId, {
          status: BatchStatusEnums.Waiting,
          startDate: new Date(),
          canRetry: 0,
          comment: `重试, userId=${user.userId}, date=${new Date()}`,
        }),
        this.batchResultRepo.delete({ resultId: In(jobResultIds) }),
      ]);

      const stockedResults = await this.batchResultRepo.find({
        where: {
          batchId,
        },
        select: ['resultHashkey'],
      });
      // 把已经处理过的result hashkey 加到redis中，避免重复处理
      const keys = stockedResults.map((r) => r.resultHashkey);
      // const batchProcessKey = getBatchProcessLockKey(batchId);
      // await RedisUtils.createSet(this.redisService.getClient(), batchProcessKey, keys, 60 * 60 * 24);
      const batchProgressLock = new BatchProgressLock(this.redisService.getClient(), batchId);
      await batchProgressLock.init(keys);

      await Bluebird.all([
        this.getProperlyMessageHandler(batchEntity.businessType).refreshBatchStatusByEntity(batchEntity.batchId, false, null, true),
        this.batchHelperService.sendBatchMonitorMessage(
          batchEntity,
          user,
          // Object.assign(new RoverUser(), {
          //   userId: batchEntity.creatorId,
          //   currentOrg: batchEntity.orgId,
          // }),
        ),
      ]);
      this.logger.info(`retry batchId=${batchId} 重试成功,请等待...`);
      return { affected: items.length };
    } catch (e) {
      if (companyCounter) {
        // 退回 公司数量额度
        await companyCounter.decrease(withholdingCount);
      }
      if (historyCounter) {
        // 退回 排查记录数量额度
        await historyCounter.decrease(withholdingRecordCount);
      }
      if (dailyCounter) {
        // 退回 每日尽调上限校验 额度
        await dailyCounter?.decrease(withholdingRecordCount);
      }
      this.logger.error('retry batchId=${batchId} 重试失败');
      this.logger.error(e);
      throw new BadRequestException({
        ...RoverExceptions.Batch.RetryFailed,
        internalMessage: e.message,
      });
    }
  }

  /**
   * 中止尚未完结的批量任务, 只支持批量排查和批量招标排查
   * 1、batch状态标记已取消；
   * 2、将batch中所有job状态是 0: 待处理, 4: 队列中排队, 的job标记为 7：已取消；
   * 3、checkJobMessage 方法调整， 对于已取消的batch, 讲当前job标记为 7：已取消； 直接返回false，不再执行job
   * 4、onBatchSuccess时refreshBatchStatus的统计数据，预扣额度退回；
   */
  async discontinue(batchId: number, user: RoverUserModel) {
    const batchEntity = await this.checkBatch(batchId, user);
    if (
      ![
        BatchBusinessTypeEnums.Diligence_File,
        BatchBusinessTypeEnums.Diligence_ID,
        BatchBusinessTypeEnums.Diligence_Customer,
        BatchBusinessTypeEnums.Bidding_Diligence_File,
      ].includes(batchEntity.businessType)
    ) {
      throw new BadRequestException(RoverExceptions.Batch.UnSupportedDiscontinue);
    }

    if (![BatchStatusEnums.Waiting, BatchStatusEnums.Processing, BatchStatusEnums.Queuing].includes(batchEntity.status)) {
      throw new BadRequestException(`batchId=${batchId} 状态已经完结状态`);
    }

    try {
      await this.batchRepo.update(batchId, { status: BatchStatusEnums.Caneled });

      // 2、将batch中所有job状态是 0、4（0-待处理、4-队列中排队）的job标记为 7-已取消；
      const jobsUpdateResult = await this.batchJobRepo.update(
        { batchId, status: In([BatchStatusEnums.Waiting, BatchStatusEnums.Queuing]) },
        { status: BatchStatusEnums.Caneled },
      );
      this.logger.info(`discontinue batchId=${batchId} 成功,请等待已执行任务结束...`);
      return { affected: jobsUpdateResult.affected };
    } catch (e) {
      this.logger.error('retry batchId=${batchId} 中止失败');
      this.logger.error(e);
      throw new BadRequestException({
        ...RoverExceptions.Batch.DiscontinueFailed,
        internalMessage: e.message,
      });
    }
  }

  /**
   * 检查batch状态
   */
  private async checkBatch(batchId: number, user: RoverUserModel): Promise<BatchEntity> {
    const batchEntity = await this.batchRepo.findOne({ where: { batchId } });
    if (!batchEntity) {
      throw new BadRequestException(`batchId=${batchId} 不存在`);
    }
    if (batchEntity.orgId !== user.currentOrg) {
      throw new BadRequestException(`batchId=${batchId} 所属组织不匹配`);
    }
    if (batchEntity.creatorId !== user.userId) {
      throw new BadRequestException(`batchId=${batchId} 创建人不匹配`);
    }
    return batchEntity;
  }

  async specificDiligenceRetry(user: RoverUserModel, diligenceId: number) {
    // 找到查排查失败的batch
    const batchDiligence = await this.batchSpecificDiligenceRepo.findOne({ where: { diligenceId } });
    if (!batchDiligence?.batchId) {
      // 没有找到对应的batch,不是走batch逻辑的批量排查, 重新创建batch,发消息
      return this.specificFacadeService.retryDiligence(user, diligenceId);
    } else {
      const { affected } = await this.retryBatch(batchDiligence.batchId, user);
      if (affected > 0) {
        return this.specificFacadeService.retryDiligenceCheck(user, diligenceId, null);
      } else {
        throw new BadRequestException(RoverExceptions.Batch.NotNeedRetry);
      }
    }
  }
}
