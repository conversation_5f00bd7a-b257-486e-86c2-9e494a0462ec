import { BadRequestException, ForbiddenException, HttpException, Injectable } from '@nestjs/common';
import { PaginationParams } from '@commons/model/common';
import { BatchBusinessTypeEnums } from '@domain/enums/batch/BatchBusinessTypeEnums';
import { BatchEntity } from '@domain/entities/BatchEntity';
import { RoverBundleCounterType, RoverBundleLimitationType, RoverBundleService } from '@kezhaozhao/saas-bundle-service';
import { CounterOperation } from '@domain/constants/common';
import { DiligenceHistoryRequest } from '@domain/model/diligence/pojo/history/DiligenceHistoryRequest';
import { SearchPersonModel } from '@domain/model/person/SearchPersonModel';
import { SearchInnerBlacklistRequest } from '@domain/dto/blacklist/SearchInnerBlacklistRequest';
import { PermissionByEnum } from '@domain/enums/PermissionScopeEnum';
import { In, MoreThan, Repository } from 'typeorm';
import { RoverExceptions } from '@commons/constants/exceptionConstants';
import { BatchTypeEnums } from '@domain/enums/batch/BatchTypeEnums';
import * as moment from 'moment';
import { SearchCustomerResponse } from '@domain/dto/customer/SearchCustomerResponse';
import { SearchCustomerModel } from '@domain/model/customer/SearchCustomerModel';
import { SearchTenderAlertProjectRequest } from '@domain/model/tenderAlert/SearchTenderAlertProjectRequest';
import { RiskDynamicsSearchRequest } from '@domain/model/monitor/request/RiskDynamicsSearchRequest';
import { SearchCustomerNewsRequest } from '@domain/dto/customer/SearchCustomerNewsRequest';
import { getExceptionDescription } from '@domain/utils/diligence/diligence.utils';
import { BatchStatusEnums } from '@domain/enums/batch/BatchStatusEnums';
import { BatchJobEntity } from '@domain/entities/BatchJobEntity';
import { BatchExportMessagePO } from '@domain/model/batch/po/message/BatchExportMessagePO';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { RabbitMQ } from '@kezhaozhao/message-queue';
import { InjectRepository } from '@nestjs/typeorm';
import { BatchResultEntity } from '@domain/entities/BatchResultEntity';
import { BatchBaseHelper } from './batch.base.helper';
import { BatchBundleService } from '../service/batch.bundle.service';
import { DiligenceHistoryService } from '@modules/risk-assessment/diligence/details/diligence.history.service';
import { PersonService } from '@modules/company-management/person/person.service';
import { DiligenceHistoryEntity } from '@domain/entities/DiligenceHistoryEntity';
import { SecurityService } from '@core/config/security.service';
import { TenderAlertService } from '@modules/relationship-investigation/tenderAlert/tenderAlert.service';
import { CustomerService } from '@modules/company-management/customer/customer.service';
import { MonitorRiskDynamicService } from '@modules/risk-assessment/monitor/risk/monitor.risk.dynamic.service';
import { MonitorSentimentDynamicService } from '@modules/risk-assessment/monitor/sentiment/monitor.sentiment.dynamic.service';
import { BlacklistInnerService } from '@modules/company-management/blacklist/blacklist.inner.service';
import { QueueService } from '@core/config/queue.service';
import { SnapshotStatus } from '@domain/model/diligence/pojo/model/SnapshotDetail';
import { AnalyzedCompanySearchedRequest } from '@domain/model/diligence/pojo/analyze/AnalyzedCompanySearchedRequest';
import { DiligenceAnalyzeService } from '@modules/risk-assessment/diligence/analyze/diligence.analyze.service';
import { ExportConditionBase } from '@domain/model/batch/export/ExportRecordModel';
import { SearchBiddingBatchResultRequest } from '@domain/dto/batch/request/SearchBiddingBatchResultRequest';
import { BatchBiddingHelper } from './batch.bidding.helper';
import { SearchDiligenceBiddingRequest } from '@domain/dto/bidding/SearchDiligenceBiddingRequest';
import { SpecificFacadeService } from '@modules/relationship-investigation/specific/specific.facade.service';
import { BatchSpecificHelper } from './batch.specific.helper';
import { SearchBatchRequest } from '@domain/dto/batch/request/SearchBatchRequest';
import { RoverUserModel } from '@domain/model/RoverUserModel';
import { ExportConditionRequest } from '@domain/dto/batch/export/ExportConditionRequest';
import { ExportBatchReportRequest } from '@domain/dto/batch/export/ExportBatchReportRequest';
import { ExportBatchBundleConsumeDetailRequest } from '@domain/dto/batch/export/ExportBatchBundleConsumeDetailRequest';
import { ExportPDFDetailRequest } from '@domain/dto/batch/export/ExportPDFDetailRequest';
import { BatchCheckService } from '../service/batch.check.service';
import { BiddingQueryService } from '@modules/relationship-investigation/bidding/service/bidding.query.service';
import { SearchCompanyRequest } from '@domain/model/monitor/request/SearchCompanyRequest';
import { MonitorCompanyService } from '@modules/risk-assessment/monitor/service/monitor.company.service';

@Injectable()
export class BatchCreatorHelperExport {
  private readonly logger = QccLogger.getLogger(BatchCreatorHelperExport.name);
  public readonly batchExportQueue: RabbitMQ;

  constructor(
    @InjectRepository(BatchResultEntity) protected readonly batchResultRepo: Repository<BatchResultEntity>,
    @InjectRepository(BatchEntity) protected readonly batchRepo: Repository<BatchEntity>,
    private readonly batchBaseHelperService: BatchBaseHelper,
    private readonly batchBiddingHelperService: BatchBiddingHelper,
    private readonly batchBundleService: BatchBundleService,
    private readonly diligenceHistoryService: DiligenceHistoryService,
    private readonly personService: PersonService,
    @InjectRepository(DiligenceHistoryEntity) private readonly diligenceHistoryRepo: Repository<DiligenceHistoryEntity>,
    private readonly securityService: SecurityService,
    private readonly tenderAlertService: TenderAlertService,
    private readonly customerService: CustomerService,
    private readonly riskDynamicService: MonitorRiskDynamicService,
    private readonly monitorCompanyService: MonitorCompanyService,
    private readonly sentimentService: MonitorSentimentDynamicService,
    private readonly blacklistInnerService: BlacklistInnerService,
    private readonly bundleService: RoverBundleService,
    @InjectRepository(BatchJobEntity) private readonly batchJobRepo: Repository<BatchJobEntity>,
    private readonly queueService: QueueService,
    private readonly diligenceAnalyzeService: DiligenceAnalyzeService,
    readonly specificFacadeService: SpecificFacadeService,
    readonly batchSpecificHelper: BatchSpecificHelper,
    private readonly batchCheckService: BatchCheckService,
    private readonly biddingQueryService: BiddingQueryService,
  ) {
    this.batchExportQueue = this.queueService.batchExportQueue;
  }

  async createExportBatchEntity<T extends PaginationParams>(user: RoverUserModel, businessType: BatchBusinessTypeEnums, condition: T) {
    await this.batchCheckService.checkBatchCount(user, businessType);
    const { recordCount, fileName, increaseExportQuantity } = await this.getBatchRecordCount(user, businessType, condition);
    return this.createBatchEntity(user, businessType, condition, recordCount, fileName, increaseExportQuantity);
  }

  async createBatchEntity<T extends PaginationParams>(
    user: RoverUserModel,
    businessType: BatchBusinessTypeEnums,
    condition: T,
    recordCount: number,
    fileName: string,
    increaseExportQuantity: boolean,
  ): Promise<BatchEntity> {
    const { currentOrg: orgId, userId, departments } = user;
    let batchEntity: BatchEntity;
    const subBatches: BatchEntity[] = [];
    try {
      if (businessType == BatchBusinessTypeEnums.Diligence_Report_Batch_Export) {
        // 一次性创建多条batchEntity

        // 创建压缩包打包任务
        batchEntity = await this.batchRepo.save({
          createDate: new Date(),
          orgId: orgId,
          depId: departments?.[0],
          creatorId: userId,
          batchType: BatchTypeEnums.Export,
          businessType,
          recordCount,
          status: BatchStatusEnums.Waiting,
          comment: '',
          fileName,
          originFileUrl: '',
          batchInfo: {
            type: BatchBusinessTypeEnums.Diligence_Report_Batch_Export,
          },
          statisticsInfo: {
            recordCount,
            duplicatedCount: 0,
          },
        });
        // 创建普通报告生成任务
        const batchIds = [];
        const msgs = [];
        for (const diligenceId of (condition as ExportBatchReportRequest).diligenceIds) {
          const subBatch = await this.batchRepo.save({
            createDate: new Date(),
            orgId: orgId,
            depId: departments?.[0],
            creatorId: userId,
            batchType: BatchTypeEnums.Export,
            businessType: BatchBusinessTypeEnums.Diligence_Report_Export,
            recordCount: 1,
            status: recordCount > 0 ? BatchStatusEnums.Waiting : BatchStatusEnums.Error,
            comment: recordCount === 0 ? '未识别到符合格式的数据' : '',
            fileName: '风险排查报告子任务_' + diligenceId,
            batchInfo: {
              isSub: true,
              parentBatchId: batchEntity.batchId,
            },
            originFileUrl: '',
            statisticsInfo: {
              recordCount: 1,
              duplicatedCount: 0,
            },
          });
          subBatches.push(subBatch);
          batchIds.push(subBatch.batchId);

          const batchJob = await this.batchJobRepo.save(
            Object.assign(new BatchJobEntity(), {
              batchId: subBatch.batchId,
              jobInfo: {
                items: [],
                itemSize: 0,
              },
              status: BatchStatusEnums.Waiting,
            }),
          );
          const msgBody: BatchExportMessagePO<ExportConditionBase> = {
            batchId: subBatch.batchId,
            batchType: subBatch.batchType,
            businessType: BatchBusinessTypeEnums.Diligence_Report_Export,
            startDate: Date.now(),
            operatorId: userId,
            orgId,
            targetId: batchEntity.batchId,
            jobId: batchJob.jobId,
            condition: Object.assign(new ExportConditionBase(), {
              batchId: subBatch.batchId,
              diligenceId,
              isSub: true,
            }),
          };

          msgs.push(msgBody);
        }
        // 生成打包任务的batchJob
        batchEntity.batchInfo.subBatchIds = subBatches.map((it) => it.batchId);
        batchEntity = await this.batchRepo.save(batchEntity);
        const batchJob = await this.batchJobRepo.save(
          Object.assign(new BatchJobEntity(), {
            batchId: batchEntity.batchId,
            jobInfo: {
              items: [],
              itemSize: 0,
            },
            status: BatchStatusEnums.Waiting,
          }),
        );
        for (const msg of msgs) {
          const messageResponse = await this.batchExportQueue.sendMessageV2(msg, { ttl: 1 * 1000, retries: 1 }); // ttl 单位为毫秒
          this.logger.info(`batch(batchId=${msg.batchId}) message sent..., response=${messageResponse}`);
        }
        const msgBody: BatchExportMessagePO<ExportConditionBase> = {
          batchId: batchEntity.batchId,
          batchType: BatchTypeEnums.Export,
          businessType,
          startDate: Date.now(),
          operatorId: userId,
          orgId,
          targetId: batchEntity.batchId,
          jobId: batchJob.jobId,
          condition: Object.assign(new ExportConditionBase(), {
            batchId: batchEntity.batchId,
            subBatchIds: batchEntity.batchInfo.subBatchIds,
            isSub: false,
          }),
        };
        const messageResponse = await this.batchExportQueue.sendMessageV2(msgBody, { ttl: 1 * 1000, retries: 1 }); // ttl 单位为毫秒
        this.logger.info(`batch(batchId=${msgBody.batchId}) message sent..., response=${messageResponse}`);
        return batchEntity;
      } else {
        // 一般单条导出任务
        batchEntity = await this.batchRepo.save(
          Object.assign(new BatchEntity(), {
            createDate: new Date(),
            orgId: orgId,
            depId: departments?.[0],
            creatorId: userId,
            batchType: BatchTypeEnums.Export,
            businessType,
            recordCount,
            status: recordCount > 0 ? BatchStatusEnums.Waiting : BatchStatusEnums.Error,
            comment: recordCount === 0 ? '未识别到符合格式的数据' : '',
            fileName,
            originFileUrl: '',
            statisticsInfo: {
              recordCount,
              duplicatedCount: 0,
            },
            source: (condition as ExportConditionRequest)?.source,
          }),
        );

        const batchJob = await this.batchJobRepo.save(
          Object.assign(new BatchJobEntity(), {
            batchId: batchEntity.batchId,
            jobInfo: {
              items: [],
              itemSize: 0,
              condition,
            },
            status: BatchStatusEnums.Waiting,
          }),
        );
        const msgBody: BatchExportMessagePO<ExportConditionBase> = {
          batchId: batchEntity.batchId,
          batchType: batchEntity.batchType,
          businessType,
          startDate: Date.now(),
          operatorId: userId,
          orgId,
          targetId: (condition as ExportConditionRequest).targetId,
          jobId: batchJob.jobId,
          condition,
        };
        const messageResponse = await this.batchExportQueue.sendMessageV2(msgBody, { ttl: 1 * 1000, retries: 1 }); // ttl 单位为毫秒
        const message = `batch(batchId=${batchEntity.batchId}) message sent..., response=${messageResponse}`;
        this.logger.info(message);
        if (!messageResponse) {
          throw new BadRequestException({
            ...RoverExceptions.Batch.CreateFailed,
            internalMessage: message,
          });
        }
      }
      return batchEntity;
    } catch (e) {
      switch (businessType) {
        case BatchBusinessTypeEnums.Diligence_Report_Export:
        case BatchBusinessTypeEnums.Tender_Report_Export:
        case BatchBusinessTypeEnums.Specific_Report_Export:
          await this.batchBundleService.bundleCounter(user, RoverBundleCounterType.DiligenceReportQuantity, CounterOperation.Decrease, 1);
          break;
        case BatchBusinessTypeEnums.Diligence_Report_Batch_Export:
          if (recordCount) {
            await this.batchBundleService.bundleCounter(user, RoverBundleCounterType.DiligenceReportQuantity, CounterOperation.Decrease, recordCount);
          }
          break;
        default:
          if (increaseExportQuantity) {
            await this.batchBundleService.useOrglimitationCounter(user, RoverBundleLimitationType.DiligenceExportDailyQuantity, CounterOperation.Decrease, 1);
          }
          break;
      }
      this.logger.error(e);
      await this.batchBaseHelperService.rollBackBatch(batchEntity, e);
      for (const subBatch of subBatches) {
        await this.batchBaseHelperService.rollBackBatch(subBatch, e);
      }
      if (e instanceof HttpException) {
        throw e;
      }
      throw new BadRequestException({
        ...RoverExceptions.Batch.CreateFailed,
        internalMessage: e.message,
      });
    }
  }

  async getBatchRecordCount<T extends PaginationParams>(
    user: RoverUserModel,
    businessType: BatchBusinessTypeEnums,
    condition: T,
  ): Promise<{ recordCount: number; fileName: string; increaseExportQuantity: boolean }> {
    const { currentOrg: orgId } = user;
    let recordCount;
    let fileName = '';
    //导出套餐项限制，最多一次5000条
    let limit = 5000;
    let increaseExportQuantity = false;
    switch (businessType) {
      case BatchBusinessTypeEnums.Diligence_Record: {
        const diligenceHistoryResponse = await this.diligenceHistoryService.search(user, Object.assign(new DiligenceHistoryRequest(), condition));
        recordCount = diligenceHistoryResponse?.total || 0;
        // 尽调结果每日导出次数卡控
        increaseExportQuantity = await this.increaseExportQuantity(user);
        break;
      }
      case BatchBusinessTypeEnums.Person_Export: {
        const searchPersonResponse = await this.personService.searchPersonList(user, Object.assign(new SearchPersonModel(), condition, true));
        recordCount = searchPersonResponse?.total || 0;
        increaseExportQuantity = await this.increaseExportQuantity(user);
        break;
      }
      case BatchBusinessTypeEnums.InnerBlacklist_Export: {
        const searchInnerBlacklistResponse = await this.blacklistInnerService.search(user, Object.assign(new SearchInnerBlacklistRequest(), condition));
        recordCount = searchInnerBlacklistResponse?.total || 0;
        increaseExportQuantity = await this.increaseExportQuantity(user);
        break;
      }
      case BatchBusinessTypeEnums.Diligence_Report_Export: {
        const pdfRequest = condition as ExportConditionRequest;
        const { by, userIds } = this.securityService.checkScope(user, 2002);
        let diligenceRecord: DiligenceHistoryEntity;
        if (by == PermissionByEnum.USER) {
          diligenceRecord = await this.diligenceHistoryRepo.findOne({
            where: {
              id: pdfRequest.diligenceId,
              orgId,
              operator: In(userIds),
            },
          });
        } else {
          diligenceRecord = await this.diligenceHistoryRepo.findOne({ where: { id: pdfRequest.diligenceId, orgId } });
        }
        if (diligenceRecord) {
          recordCount = 1;
          if (diligenceRecord.snapshotDetails.status !== SnapshotStatus.SUCCESS) {
            //快照尚未生成不让导出报告
            throw new BadRequestException(RoverExceptions.Diligence.Snapshot.NotGenerated);
          }
          fileName = `风险排查报告-${diligenceRecord.name}`;
          //   pdf报告条数上限
          await this.batchBundleService.bundleCounter(user, RoverBundleCounterType.DiligenceReportQuantity, CounterOperation.Increase, 1);
        }
        break;
      }
      case BatchBusinessTypeEnums.Diligence_Report_Batch_Export: {
        // pdf报告条数上限
        fileName = '风险排查-批量生成报告';
        const reportBatchCondition = condition as ExportBatchReportRequest;
        const { by, userIds } = this.securityService.checkScope(user, 2002);
        if (by == PermissionByEnum.USER) {
          recordCount = await this.diligenceHistoryRepo.count({
            where: {
              id: In(reportBatchCondition.diligenceIds),
              orgId,
              operator: In(userIds),
            },
          });
        } else {
          recordCount = await this.diligenceHistoryRepo.count({
            where: { id: In(reportBatchCondition.diligenceIds), orgId },
          });
        }
        if (recordCount !== reportBatchCondition.diligenceIds.length) {
          throw new BadRequestException(RoverExceptions.Batch.DataNotFount);
        }
        if (recordCount) {
          const bundleCounter = await this.bundleService.getBundleCounter(user, RoverBundleCounterType.DiligenceReportQuantity);
          // await bundleCounter.clear();
          await bundleCounter.increase(recordCount);
        }
        break;
      }
      case BatchBusinessTypeEnums.Tender_Detail_Export: {
        const tenderDetailCount = await this.batchRepo.count({
          where: {
            orgId,
            batchType: BatchTypeEnums.Export,
            businessType,
            createDate: MoreThan(moment().startOf('day').toDate()),
          },
        });
        if (tenderDetailCount > 1000) {
          throw new ForbiddenException(RoverExceptions.TenderAlert.TenderDetailExportExceedLimit);
        }
        const pdfRequest = condition as ExportPDFDetailRequest;
        const tenderDetail = await this.tenderAlertService.searchProjectDetail(pdfRequest.id);
        if (tenderDetail) {
          recordCount = 1;
        }
        fileName = tenderDetail.result.title;
        increaseExportQuantity = await this.increaseExportQuantity(user);
        break;
      }
      case BatchBusinessTypeEnums.Customer_Export: {
        //第三方列表导出
        const searchCustomerResponse: SearchCustomerResponse = await this.customerService.searchV2(user, Object.assign(new SearchCustomerModel(), condition));
        recordCount = searchCustomerResponse?.total || 0;
        increaseExportQuantity = await this.increaseExportQuantity(user);
        break;
      }
      case BatchBusinessTypeEnums.Tender_Export: {
        limit = 1000;
        const searchTenderResponse = await this.tenderAlertService.searchProject(user, Object.assign(new SearchTenderAlertProjectRequest(), condition));
        recordCount = searchTenderResponse?.Paging?.TotalRecords || 0;
        increaseExportQuantity = await this.increaseExportQuantity(user);
        break;
      }
      case BatchBusinessTypeEnums.Monitor_Company_Export: {
        // 监控企业列表导出
        const searchMonitorCompanyResponse = await this.monitorCompanyService.search2(user, Object.assign(new SearchCompanyRequest(), condition));
        recordCount = searchMonitorCompanyResponse?.total || 0;
        increaseExportQuantity = await this.increaseExportQuantity(user);
        break;
      }
      case BatchBusinessTypeEnums.Risk_Export: {
        const searchRiskResponse = await this.riskDynamicService.searchMonitorRiskDynamics(user, Object.assign(new RiskDynamicsSearchRequest(), condition));
        recordCount = searchRiskResponse?.total || 0;
        increaseExportQuantity = await this.increaseExportQuantity(user);
        break;
      }
      case BatchBusinessTypeEnums.Sentiment_Export: {
        const sentimentResponse = await this.sentimentService.searchNegativeNews(user, Object.assign(new SearchCustomerNewsRequest(), condition));
        recordCount = sentimentResponse?.total || 0;
        increaseExportQuantity = await this.increaseExportQuantity(user);
        break;
      }
      case BatchBusinessTypeEnums.Diligence_Batch_Detail:
      case BatchBusinessTypeEnums.Dimension_Detail_Export:
      case BatchBusinessTypeEnums.Analyze_Dimension_Detail:
      case BatchBusinessTypeEnums.Analyze_Record_Export: {
        const analyzeResult = await this.diligenceAnalyzeService.analyze(Object.assign(new AnalyzedCompanySearchedRequest(), condition), orgId);
        recordCount = analyzeResult?.total || 0;
        increaseExportQuantity = await this.increaseExportQuantity(user);
        break;
      }
      case BatchBusinessTypeEnums.Tender_Report_Export: {
        //   pdf报告条数上限
        const pdfRequest = condition as ExportConditionRequest;
        const diligenceRecord = await this.biddingQueryService.searchDetail(orgId, [pdfRequest.diligenceId]);
        if (diligenceRecord?.length) {
          recordCount = 1;
        }
        await this.batchBundleService.bundleCounter(user, RoverBundleCounterType.DiligenceReportQuantity, CounterOperation.Increase, 1);
        fileName = `招标排查报告-${diligenceRecord[0].projectName}（${diligenceRecord[0].projectNo}）`;
        break;
      }
      case BatchBusinessTypeEnums.Tender_Diligence_Record_Export:
      case BatchBusinessTypeEnums.Tender_Dimension_Detail_Export:
      case BatchBusinessTypeEnums.Tender_All_Dimension_Detail_Export: {
        const recordList = await this.batchBiddingHelperService.searchBiddingDetailV2(user, Object.assign(new SearchBiddingBatchResultRequest(), condition));
        recordCount = recordList?.total || 0;
        increaseExportQuantity = await this.increaseExportQuantity(user);
        break;
      }
      case BatchBusinessTypeEnums.Tender_Diligence_History_Export: {
        //招标排查历史记录导出
        const recordList = await this.biddingQueryService.search(user, Object.assign(new SearchDiligenceBiddingRequest(), condition));
        recordCount = recordList?.total || 0;
        increaseExportQuantity = await this.increaseExportQuantity(user);
        break;
      }
      case BatchBusinessTypeEnums.Specific_Batch_Export:
        const recordList = await this.batchSpecificHelper.searchSpecificDetail(user, Object.assign(new SearchBiddingBatchResultRequest(), condition));
        recordCount = recordList?.total || 0;
        increaseExportQuantity = await this.increaseExportQuantity(user);
        break;
      case BatchBusinessTypeEnums.Specific_Record_List_Export: {
        //特定利益关系排查历史记录导出
        const recordList = await this.specificFacadeService.search(user, Object.assign(new SearchDiligenceBiddingRequest(), condition));
        recordCount = recordList?.total || 0;
        increaseExportQuantity = await this.increaseExportQuantity(user);
        break;
      }
      case BatchBusinessTypeEnums.Specific_Report_Export: {
        //   pdf报告条数上限
        const pdfRequest = condition as ExportConditionRequest;
        const diligenceRecord = await this.specificFacadeService.searchDetailById(orgId, pdfRequest.diligenceId);
        if (diligenceRecord) {
          recordCount = 1;
        }
        fileName = `特定利益关系排查报告-${diligenceRecord.projectName}（${diligenceRecord.projectNo}）`;
        await this.batchBundleService.bundleCounter(user, RoverBundleCounterType.DiligenceReportQuantity, CounterOperation.Increase, 1);
        break;
      }
      case BatchBusinessTypeEnums.Bundle_Diligence_Consume_detail_Export: {
        const recordList = await this.diligenceHistoryService.search(user, Object.assign(new DiligenceHistoryRequest(), condition));
        recordCount = recordList?.total || 0;
        increaseExportQuantity = await this.increaseExportQuantity(user);
        break;
      }
      case BatchBusinessTypeEnums.Bundle_Analyze_Record_Consume_detail_Export: {
        const { ids, ...restCondition } = condition as ExportBatchBundleConsumeDetailRequest;
        const transformedCondition = { ...restCondition, batchIds: ids };
        const recordList = await this.batchBaseHelperService.searchAnalyzeRecordBatch(user, Object.assign(new SearchBatchRequest(), transformedCondition));
        increaseExportQuantity = await this.increaseExportQuantity(user);
        recordCount = recordList?.total || 0;
        break;
      }
      case BatchBusinessTypeEnums.Bundle_Bidding_Consume_detail_Export: {
        const { ids, ...restCondition } = condition as ExportBatchBundleConsumeDetailRequest;
        const transformedCondition = { ...restCondition, diligenceIds: ids };
        const recordList = await this.biddingQueryService.search(user, Object.assign(new SearchDiligenceBiddingRequest(), transformedCondition));
        recordCount = recordList?.total || 0;
        increaseExportQuantity = await this.increaseExportQuantity(user);
        break;
      }
      case BatchBusinessTypeEnums.Bundle_Special_Consume_detail_Export: {
        const { ids, ...restCondition } = condition as ExportBatchBundleConsumeDetailRequest;
        const transformedCondition = { ...restCondition, diligenceIds: ids };
        const recordList = await this.specificFacadeService.search(user, Object.assign(new SearchDiligenceBiddingRequest(), transformedCondition));
        recordCount = recordList?.total || 0;
        increaseExportQuantity = await this.increaseExportQuantity(user);
        break;
      }
    }
    if (recordCount <= 0) {
      //未查询到结果列表,不创建消息
      throw new BadRequestException(RoverExceptions.Batch.DataNotFount);
    }

    if (recordCount > limit) {
      throw new BadRequestException(
        Object.assign(RoverExceptions.Batch.ExportBatchLimited, { error: getExceptionDescription(RoverExceptions.Batch.ExportBatchLimited.error, limit) }),
      );
    }
    return { recordCount, fileName, increaseExportQuantity };
  }
  async increaseExportQuantity(user: RoverUserModel): Promise<boolean> {
    await this.batchBundleService.useOrglimitationCounter(user, RoverBundleLimitationType.DiligenceExportDailyQuantity, CounterOperation.Increase, 1);
    return true;
  }
}
