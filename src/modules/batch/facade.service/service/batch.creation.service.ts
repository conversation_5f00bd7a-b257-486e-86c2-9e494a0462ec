import { RoverUserModel } from '@domain/model/RoverUserModel';
import { BadRequestException, HttpException, Injectable } from '@nestjs/common';
import { BatchBusinessTypeEnums } from '@domain/enums/batch/BatchBusinessTypeEnums';
import { PaginationParams } from '@commons/model/common';
import { BatchCreatorHelperExport } from '../helper/batch.creator.helper.export';
import { TraceLog } from '@kezhaozhao/qcc-logger/lib/qcc/annotation/trace.annotation';
import { ParsedRecordBase } from '@domain/model/batch/po/parse/ParsedRecordBase';
import { BatchTypeEnums } from '@domain/enums/batch/BatchTypeEnums';
import { BatchInfoPO } from '@domain/model/batch/po/BatchInfoPO';
import { BatchCreatorHelperBase } from '../helper/batch.creator.helper.base';
import { BatchCheckService } from './batch.check.service';
import { FileParseResult } from '@domain/model/batch/po/parse/FileParseResult';
import { BatchConstants } from '@modules/batch/common/batch.constants';
import { RoverBundleLimitationType } from '@kezhaozhao/saas-bundle-service';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { BatchBundleService } from './batch.bundle.service';
import { BatchResultExportService } from './batch.result.export.service';
import { RoverExceptions } from '@commons/constants/exceptionConstants';
import { CounterOperation } from '@domain/constants/common';
import { v1 as uuidv1 } from 'uuid';
import { FileParserService } from './file.parser.service';
import * as Bluebird from 'bluebird';
import { BatchMatchCompanyEntity } from '@domain/entities/BatchMatchCompanyEntity';
import { InjectRepository } from '@nestjs/typeorm';
import { BatchMatchCompanyItemEntity } from '@domain/entities/BatchMatchCompanyItemEntity';
import { Repository } from 'typeorm';
import { BatchEntity } from '@domain/entities/BatchEntity';
import { OrgSettingsLogEntity } from '@domain/entities/OrgSettingsLogEntity';
import { SettingsService } from '@modules/system-management/settings/service/settings.service';
import { SettingsDistributeService } from '@modules/system-management/settings/distribute/settings.distribute.service';
import { SettingTypeEnums } from '@domain/model/settings/SettingTypeEnums';
import { error } from 'console';

@Injectable()
export class BatchCreationService {
  private readonly logger = QccLogger.getLogger(BatchCreationService.name);
  constructor(
    private readonly batchCreatorExportHelperService: BatchCreatorHelperExport,
    private readonly batchCreatorHelperService: BatchCreatorHelperBase,
    private readonly batchCheckService: BatchCheckService,
    private readonly batchBundleService: BatchBundleService,
    private readonly resultExportService: BatchResultExportService,
    private readonly fileParserService: FileParserService,
    @InjectRepository(BatchMatchCompanyEntity) private readonly batchMatchCompanyRepo: Repository<BatchMatchCompanyEntity>,
    @InjectRepository(BatchMatchCompanyItemEntity) private readonly batchMatchCompanyItemRepo: Repository<BatchMatchCompanyItemEntity>,
    private readonly settingService: SettingsService,
  ) {}

  // ****************************************************** 导入类型批量任务创建 start ******************************************************
  /**
   * 根据导入的 excel 文件创建批次
   * @param user 用户信息
   * @param filePath 文件路径
   * @param fileName 文件名称
   * @param businessType 业务类型
   * @returns 创建结果
   */
  async createBatchByFile(user: RoverUserModel, filePath: string, fileName: string, businessType: BatchBusinessTypeEnums, settingId?: number) {
    // 当前已有离线任务正在进行，请勿重复进行操作！
    await this.batchCheckService.checkBatchCount(user, businessType);
    // 校验设置授权信息
    await this.settingService.checkSettingAuth(user, businessType, settingId);
    // 获取解析结果
    const fileParseResult: FileParseResult = await this.fileParserService.getParseResult(user, filePath, businessType, settingId);
    if (fileParseResult.succeedItems.length === 0) {
      throw new BadRequestException({ error: fileParseResult.failedItems[0].errorMsg });
    }
    try {
      // 检查额度
      await this.batchCheckService.checkFileParseResult(user, fileParseResult, businessType);
      // 原始文件上传到oss
      let extension = '';
      if (fileName.lastIndexOf('.') > 0) {
        extension = fileName.substring(fileName.lastIndexOf('.'), fileName.length);
      }
      const originFileUrl = await this.resultExportService.putFileToOss(filePath, uuidv1() + extension, fileName);
      // 创建批次
      const jobItemSize = BatchConstants.getJobItemSize(businessType); //通过调整大小还控制 最终的job的数量（一个job对应一个mq message.handler）
      return this.batchCreatorHelperService.createBatchEntity(user, fileParseResult, businessType, jobItemSize, fileName, originFileUrl);
    } catch (e) {
      if (businessType == BatchBusinessTypeEnums.Diligence_File) {
        await this.batchBundleService.useOrglimitationCounter(user, RoverBundleLimitationType.BatchDiligenceDailyQuantity, CounterOperation.Decrease, 1);
      }
      if (e instanceof HttpException || e instanceof BadRequestException) {
        throw e;
      }
      this.logger.error(e);
      throw new BadRequestException({
        ...RoverExceptions.Batch.CreateFailed,
        internalMessage: e.message,
      });
    }
  }

  /**
   * 根据指定的data创建批量排查任务
   * @param user
   * @param data
   * @param businessType
   * @returns
   */
  @TraceLog({ throwError: true })
  async createBatchDiligenceTask(
    user: RoverUserModel,
    data: ParsedRecordBase[],
    businessType: BatchBusinessTypeEnums,
    batchType?: BatchTypeEnums,
    params?: BatchInfoPO,
  ) {
    return this.batchCreatorHelperService.createBatchDiligenceByData(user, data, businessType, batchType, params);
  }

  /**
   * 根据预匹配数据中转 ID 创建批量任务
   * @param user
   * @param batchId
   * @param batchBusinessType
   * @param parmas
   */
  async createBatchByBatchMatchCompanyId(
    user: RoverUserModel,
    batchMatchCompanyId: number,
    businessType: BatchBusinessTypeEnums,
    parmas?: BatchInfoPO,
  ): Promise<BatchEntity> {
    try {
      // 校验设置授权信息
      await this.settingService.checkSettingAuth(user, businessType, parmas.settingId);

      // 校验并获取预匹配数据
      const { successItems, batchMatchEntity } = await this.batchCheckService.checkBatchMatchCompanyWithData(user, batchMatchCompanyId, businessType);
      // 根据预匹配数据组装文件解析结果
      const fileParseResult = this.fileParserService.getFileParseResultByType(successItems, businessType, parmas);
      // 创建批次
      const jobItemSize = BatchConstants.getJobItemSize(businessType); //通过调整大小还控制 最终的job的数量（一个job对应一个mq message.handler）
      const batchEntity = await this.createBatchByType(user, fileParseResult, businessType, jobItemSize, batchMatchEntity, parmas);
      // 删除预匹配数据
      await Bluebird.all([
        this.batchMatchCompanyRepo.delete({ id: batchMatchCompanyId }),
        this.batchMatchCompanyItemRepo.delete({ batchId: batchMatchCompanyId }),
      ]);
      return batchEntity;
    } catch (e) {
      if (e instanceof HttpException) {
        throw e;
      }
      this.logger.error(e);
      throw new BadRequestException({
        ...RoverExceptions.Batch.CreateFailed,
        internalMessage: e.message,
      });
    }
  }

  /**
   * 根据业务创建批次
   * @param user
   * @param fileParseResult
   * @param businessType
   * @param jobItemSize
   * @param batchMatchEntity
   * @param parmas
   */
  async createBatchByType(
    user: RoverUserModel,
    fileParseResult: FileParseResult,
    businessType: BatchBusinessTypeEnums,
    jobItemSize: number,
    batchMatchEntity: BatchMatchCompanyEntity,
    parmas: BatchInfoPO,
  ) {
    let batchEntity: BatchEntity;
    switch (businessType) {
      // 尽职调查、潜在利益关系排查
      case BatchBusinessTypeEnums.Potential_Batch_Excel:
      case BatchBusinessTypeEnums.Diligence_File:
        batchEntity = await this.batchCreatorHelperService.createBatchEntity(
          user,
          fileParseResult,
          businessType,
          jobItemSize,
          batchMatchEntity.fileName,
          batchMatchEntity.originFile,
          BatchTypeEnums.Import,
          { settingId: parmas.settingId },
        );
        break;
      // 内部黑名单、第三方列表、监控 导入
      case BatchBusinessTypeEnums.Customer_File:
      case BatchBusinessTypeEnums.InnerBlacklist_File:
      case BatchBusinessTypeEnums.Monitor_File:
        batchEntity = await this.batchCreatorHelperService.createBatchEntity(
          user,
          fileParseResult,
          businessType,
          jobItemSize,
          batchMatchEntity.fileName,
          batchMatchEntity.originFile,
        );
        break;
    }
    return batchEntity;
  }

  // ****************************************************** 导入类型批量任务创建 end ******************************************************

  // ****************************************************** 导出类型批量任务创建 start ******************************************************

  /**
   * 创建批量导出任务
   * batchJob直接被启动，无须通过batch来启动
   * @param user
   * @param businessType
   * @param condition
   * @returns
   */
  async createExportBatchEntity<T extends PaginationParams>(user: RoverUserModel, businessType: BatchBusinessTypeEnums, condition: T) {
    return this.batchCreatorExportHelperService.createExportBatchEntity(user, businessType, condition);
  }
  // ****************************************************** 导出类型批量任务创建 end ******************************************************
}
