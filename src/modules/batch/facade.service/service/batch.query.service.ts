import { Query<PERSON><PERSON>erHelper } from '@commons/sql.helper';
import MyOssService from '@core/cache/basic/my-oss.service';
import { SecurityService } from '@core/config/security.service';
import { TimeInterval3Hour } from '@domain/constants/common';
import { BatchRetryRequest } from '@domain/dto/batch/request/BatchRetryRequest';
import { ChildDimensionHit } from '@domain/dto/batch/request/ChildDimensionHit';
import { SearchBatchRequest } from '@domain/dto/batch/request/SearchBatchRequest';
import { SearchBatchResultRequest } from '@domain/dto/batch/request/SearchBatchResultRequest';
import { SearchMatchCompanyRequest } from '@domain/dto/batch/request/SearchMatchCompanyRequest';
import { SearchBatchResponse } from '@domain/dto/batch/response/SearchBatchResponse';
import { SearchMatchCompanyResponse } from '@domain/dto/batch/response/SearchMatchCompanyResponse';
import { BatchDiligenceEntity } from '@domain/entities/BatchDiligenceEntity';
import { BatchEntity } from '@domain/entities/BatchEntity';
import { BatchJobEntity } from '@domain/entities/BatchJobEntity';
import { BatchMatchCompanyEntity } from '@domain/entities/BatchMatchCompanyEntity';
import { BatchMatchCompanyItemEntity } from '@domain/entities/BatchMatchCompanyItemEntity';
import { BatchResultEntity } from '@domain/entities/BatchResultEntity';
import { BatchTenderDiligenceEntity } from '@domain/entities/BatchTenderDiligenceEntity';
import { DiligenceHistoryEntity } from '@domain/entities/DiligenceHistoryEntity';
import { BatchBusinessTypeEnums } from '@domain/enums/batch/BatchBusinessTypeEnums';
import { BatchJobResultTypeEnums, GetBatchJobResultTypeEnumsName } from '@domain/enums/batch/BatchJobResultTypeEnums';
import { BatchStatusEnums } from '@domain/enums/batch/BatchStatusEnums';
import { FileParseResult } from '@domain/model/batch/po/parse/FileParseResult';
import { ParsedRecordBase } from '@domain/model/batch/po/parse/ParsedRecordBase';
import { DiligenceHistoryResponse } from '@domain/model/diligence/pojo/history/DiligenceHistoryResponse';
import { OpenapiBatchDiligenceResultRequest } from '@domain/model/openapi/diligence/OpenapiBatchDiligenceRequest';
import { RoverUserModel } from '@domain/model/RoverUserModel';
import { BatchValidationService } from '@domain/validation/BatchValidationService';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { CompanySearchService } from '@modules/company-management/company/company-search.service';
import { CompanyBusinessInfo } from '@modules/company-management/company/model/CompanyBusinessInfo';
import { EvaluationService } from '@modules/risk-assessment/diligence/evaluation/evaluation.service';
import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as Bluebird from 'bluebird';
import { chunk, compact, omit, pick, sum, uniq } from 'lodash';
import { Logger } from 'log4js';
import { Brackets, In, Repository } from 'typeorm';
import { v1 as uuidv1 } from 'uuid';
import { CustomerImportFileParser } from '../../file.parser/CustomerImportFileParser';
import { InnerBlacklistImportFileParser } from '../../file.parser/InnerBlacklistImportFileParser';
import { MonitorImportFileParser } from '../../file.parser/MonitorImportFileParser';
import { BatchBaseHelper } from '../helper/batch.base.helper';
import { BatchResultExportService } from './batch.result.export.service';

/**
 * 批次查询服务
 */
@Injectable()
export class BatchQueryService {
  private readonly logger: Logger = QccLogger.getLogger(BatchQueryService.name);

  constructor(
    @InjectRepository(BatchEntity) private readonly batchRepo: Repository<BatchEntity>,
    @InjectRepository(BatchTenderDiligenceEntity) private readonly batchTenderRepo: Repository<BatchTenderDiligenceEntity>,
    @InjectRepository(BatchJobEntity) private readonly batchJobRepo: Repository<BatchJobEntity>,
    @InjectRepository(BatchResultEntity) private readonly batchResultRepo: Repository<BatchResultEntity>,
    @InjectRepository(BatchMatchCompanyItemEntity) private readonly batchMatchCompanyItemRepo: Repository<BatchMatchCompanyItemEntity>,
    @InjectRepository(BatchMatchCompanyEntity) private readonly batchMatchCompanyRepo: Repository<BatchMatchCompanyEntity>,
    @InjectRepository(DiligenceHistoryEntity) private readonly diligenceHistoryRepo: Repository<DiligenceHistoryEntity>,
    @InjectRepository(BatchDiligenceEntity) private readonly batchDiligenceRepo: Repository<BatchDiligenceEntity>,
    private readonly resultExportService: BatchResultExportService,
    private readonly companySearchService: CompanySearchService,
    private readonly securityService: SecurityService,
    private readonly myOssService: MyOssService,
    private readonly batchBaseHelperService: BatchBaseHelper,
    private readonly innerBlacklistImportFileParser: InnerBlacklistImportFileParser,
    private readonly monitorFileParser: MonitorImportFileParser, //合作监控文件解析器
    private readonly evaluationService: EvaluationService,
    private readonly batchValidationService: BatchValidationService,
    private readonly customerFileParser: CustomerImportFileParser,
  ) {}

  async searchBatchResultError(postBody: BatchRetryRequest) {
    const { pageSize, pageIndex, batchId } = postBody;

    const qb = this.batchResultRepo
      .createQueryBuilder('result')
      .where('result.batchId = :batchId', { batchId })
      .andWhere('result.resultType IN (:...resultTypes)', {
        resultTypes: [
          //可以重试的类型
          BatchJobResultTypeEnums.FAILED_CODE,
          BatchJobResultTypeEnums.FAILED_VERIFICATION,
          BatchJobResultTypeEnums.FAILED_BUNDLE_LIMITED,
          BatchJobResultTypeEnums.OVER_TIME,
          //仅仅展示，无法重试
          BatchJobResultTypeEnums.FAILED_UNMATCHED,
          BatchJobResultTypeEnums.FAILED_UNSUPPORTED,
        ],
      })
      .select(['result.resultType', 'result.resultInfo'])
      .orderBy('result.resultType', 'ASC');

    // 应用分页
    qb.skip(pageSize * (pageIndex - 1)).take(pageSize);

    // 获取数据和总数
    const [failedResults, total] = await qb.getManyAndCount();

    // 处理查询结果，提取公司名称和错误原因
    const data = failedResults.map((item) => {
      const resultInfo = item.resultInfo as ParsedRecordBase;
      const companyId = resultInfo?.companyId;
      const companyName = resultInfo?.companyName || '未知公司';
      const errorType = item.resultType;
      const errorReason = GetBatchJobResultTypeEnumsName(item.resultType) || '未知错误';

      return {
        companyId,
        companyName,
        errorType,
        errorReason,
      };
    });

    // 返回分页结果
    return {
      pageSize,
      pageIndex,
      total,
      data,
    };
  }

  /**
   * 根据准入排查id查询pdf导出
   * @param currentUser 当前用户
   * @param diligenceId 准入排查id
   * @returns 批次
   */
  async getBatchEntityByDiligenceId(currentUser: RoverUserModel, diligenceId: number) {
    const { currentOrg: orgId } = currentUser;
    const batchDiligence = await this.batchDiligenceRepo.findOne({
      where: {
        diligenceId,
      },
    });
    if (batchDiligence) {
      const batch = await this.batchRepo.findOne({
        where: {
          batchId: batchDiligence.batchId,
          orgId,
          businessType: BatchBusinessTypeEnums.Diligence_Report_Export,
        },
      });
      if (batch) {
        this.signatureUrl(batch);
        return batch;
      }
    }
    return null;
  }

  /**
   * 根据招标排查id查询pdf导出
   * @param currentUser 当前用户
   * @param tenderId 招标排查id
   * @returns 批次
   */
  async getBatchEntityByTenderId(currentUser: RoverUserModel, tenderId: number) {
    const { currentOrg: orgId } = currentUser;
    const batchTender = await this.batchTenderRepo.findOne({
      where: {
        diligenceId: tenderId,
      },
      order: {
        id: 'DESC',
      },
    });
    if (batchTender) {
      const batch = await this.batchRepo.findOne({
        where: {
          batchId: batchTender.batchId,
          orgId,
          businessType: BatchBusinessTypeEnums.Tender_Report_Export,
        },
      });
      if (batch) {
        this.signatureUrl(batch);
        return batch;
      }
    }
    return null;
  }

  /**
   * 查询批次列表
   * @param user 用户
   * @param body 请求参数
   * @returns 批次列表
   */
  async searchBatch(user: RoverUserModel, body: SearchBatchRequest) {
    const { currentOrg: orgId } = user;
    const { pageSize, pageIndex, batchType, businessType, searchKey, status, createDate, field, order, createUsers, depIds, batchSuccess } = body;

    const qb = this.batchRepo
      .createQueryBuilder('batch')
      .leftJoinAndSelect('batch.creator', 'creator')
      .select(['batch', 'creator.name', 'creator.userId', 'creator.phone'])
      .where('batch.orgId = :orgId', { orgId })
      .andWhere('batch.batchType = :batchType', { batchType });

    const response = new SearchBatchResponse();
    Object.assign(response, { pageSize, pageIndex, total: 0, data: [] });
    if (!businessType?.length) {
      return response;
    }

    // 找到当前用户 有数据权限的 businessType，加入查询条件
    const querySqlList = compact(businessType.map((x) => this.securityService.getBatchPermissionSql(x, user)));
    if (querySqlList.length) {
      qb.andWhere(`(${querySqlList.join(' or ')})`);
    } else {
      // 没找到有数据权限的businessType，直接返回空列表
      return response;
    }
    if (batchSuccess !== undefined) {
      if (batchSuccess) {
        // 过滤成功的批次：状态为完成且不是全部都失败的情况
        qb.andWhere(
          new Brackets((qb1) => {
            qb1.where('batch.status = :doneStatus', { doneStatus: BatchStatusEnums.Done }).andWhere(
              new Brackets((qb2) => {
                // 要么没有统计信息，要么错误数量不等于总记录数量
                qb2
                  .orWhere('batch.statisticsInfo IS NULL')
                  .orWhere('JSON_EXTRACT(batch.statisticsInfo, "$.errorCount") != JSON_EXTRACT(batch.statisticsInfo, "$.recordCount")')
                  .orWhere('JSON_EXTRACT(batch.statisticsInfo, "$.recordCount") IS NULL')
                  .orWhere('JSON_EXTRACT(batch.statisticsInfo, "$.errorCount") IS NULL');
              }),
            );
          }),
        );
      } else {
        // 过滤失败的批次：状态为失败或者错误数量等于总记录数量
        qb.andWhere(
          new Brackets((qb1) => {
            qb1.orWhere('batch.status = :errorStatus', { errorStatus: BatchStatusEnums.Error }).orWhere(
              new Brackets((qb2) => {
                // 状态为完成但所有记录都失败了
                qb2
                  .where('batch.status = :doneStatus', { doneStatus: BatchStatusEnums.Done })
                  .andWhere('JSON_EXTRACT(batch.statisticsInfo, "$.errorCount") = JSON_EXTRACT(batch.statisticsInfo, "$.recordCount")')
                  .andWhere('JSON_EXTRACT(batch.statisticsInfo, "$.recordCount") > 0');
              }),
            );
          }),
        );
      }
    }

    if (searchKey) {
      qb.andWhere('batch.fileName like :name', { name: `%${searchKey}%` });
    }
    if (status?.length) {
      qb.andWhere('batch.status in (:...status)', { status });
    }
    if (createUsers?.length > 0) {
      if (createUsers.includes(-1) && createUsers.length == 1) {
        // 系统自动巡检
        qb.andWhere(`batch.batchInfo -> '$.fromSystem' = true`);
      }
      if (!createUsers.includes(-1)) {
        qb.andWhere('batch.creatorId in (:...createUsers)', { createUsers });
        // 不包含系统自动巡检
        qb.andWhere(`batch.batchInfo -> '$.fromSystem' is null`);
      }
      if (createUsers.includes(-1) && createUsers.length > 1) {
        qb.andWhere(
          new Brackets((qb1) => {
            qb1.orWhere('batch.creatorId in (:...createUsers)', { createUsers });
            qb1.orWhere(`batch.batchInfo -> '$.fromSystem' = true`);
          }),
        );
      }
    }
    if (depIds?.length) {
      qb.andWhere('batch.depId in (:...depIds)', { depIds });
    }
    // 提交时间
    QueryBuilderHelper.applyDateRangeQuery(qb, createDate, 'createDate');
    if (field && order) {
      qb.orderBy(`batch.${field}`, order);
    } else {
      qb.orderBy('batch.createDate', 'DESC');
    }
    qb.skip(pageSize * (pageIndex - 1)).take(pageSize);
    const [items, total] = await qb.getManyAndCount();
    // 对于距离任务开始时间超出 3 小时，状态为待处理状态的任务，直接失败处理
    const nowDate = new Date();
    const exceptionBatchIds = items
      .filter((item) => {
        return item.status == 0 && item.updateDate.getTime() < nowDate.getTime() - TimeInterval3Hour;
      })
      .map((item) => item.batchId);
    if (exceptionBatchIds?.length > 0) {
      this.logger.info(`batchIds: ${exceptionBatchIds.join(',')} 超过3小时，状态为待处理状态，直接失败处理`);
      await this.batchRepo.update({ batchId: In(exceptionBatchIds) }, { status: 3 });
      await this.batchJobRepo.update({ batchId: In(exceptionBatchIds) }, { status: 3 });
    }

    // 为每个批次添加needToRetry字段
    const batchIds = items.map((item) => item.batchId);
    // 查询所有批次中可重试的结果
    const retryableResults = await this.batchResultRepo.find({
      where: {
        batchId: In(batchIds),
        resultType: In([
          BatchJobResultTypeEnums.FAILED_CODE,
          BatchJobResultTypeEnums.FAILED_VERIFICATION,
          BatchJobResultTypeEnums.FAILED_BUNDLE_LIMITED,
          BatchJobResultTypeEnums.OVER_TIME,
        ]),
      },
      select: ['batchId'],
    });

    // 构建可重试批次的ID集合
    const retryableBatchIds = new Set(retryableResults.map((result) => result.batchId));

    // 处理批次数据，添加needToRetry字段
    const processedItems = items.map((item) => {
      this.signatureUrl(item);
      if (exceptionBatchIds?.includes(item.batchId)) {
        item.status = 3;
      }
      // 添加needToRetry字段：1表示需要重试，0表示不需要
      (item as any).needToRetry = retryableBatchIds.has(item.batchId) ? 1 : 0;
      return item;
    });

    response.total = total;
    response.data = processedItems;
    return response;
  }

  /**
   * 获取 batch entity
   * @param batchId
   * @param user
   */
  async getBatchEntity(user: RoverUserModel, batchId: number, withSignatureUrl = true) {
    const { currentOrg: orgId } = user;
    const batchEntity = await this.batchRepo.findOne({
      where: {
        batchId,
        orgId,
      },
    });
    if (!batchEntity) {
      return null;
    }
    if (withSignatureUrl) {
      this.signatureUrl(batchEntity);
    }
    return batchEntity;
  }

  /**
   * 签名 url
   * @param entity
   */
  private signatureUrl(entity: BatchEntity) {
    entity.resultFile = this.myOssService.signSingleUrl(entity.resultFile);
    entity.originFile = this.myOssService.signSingleUrl(entity.originFile);
    entity.detailFile = this.myOssService.signSingleUrl(entity.detailFile);
    entity.previewUrl = this.myOssService.signSingleUrl(entity.previewUrl, true);
  }

  /**
   * 签名 url with fileName
   * @param entity
   */
  private signatureUrlWithFileName(entity: BatchEntity) {
    entity.resultFile = this.myOssService.signSingleUrl(entity.resultFile);
    entity.originFile = this.myOssService.signSingleUrl(entity.originFile);
    entity.detailFile = this.myOssService.signSingleUrl(entity.detailFile);
    entity.previewUrl = this.myOssService.signSingleUrl(entity.previewUrl, true);
  }

  /**
   * 获取 batch entity 并包含创建者
   * @param user 用户
   * @param batchId 批次ID
   * @returns 批次实体
   */
  async getBatchEntityWithCreator(user: RoverUserModel, batchId: number): Promise<BatchEntity> {
    const { currentOrg: orgId } = user;
    const batchEntity = await this.batchRepo
      .createQueryBuilder('batch')
      .leftJoinAndSelect('batch.creator', 'creator')
      .where('batch.orgId = :orgId', { orgId })
      .andWhere('batch.batchId = :batchId', { batchId })
      .getOne();
    if (!batchEntity) {
      return null;
    }
    return batchEntity;
  }

  /**
   * 先根据 batchId 查询出所有的 due_diligence， batch 表，batch_diligence,due_diligence 三表联合查询
   * 然后判断 diligence表中snapshot_date 是否存在，如果存在表示 diligence 已经完成了快照，则说明 batch 已经完成了
   * 如果 diligence表中snapshot_date 不存在，则表示 diligence 没有完成快照，则说明 batch 没有完成
   * 最终返回 due_diligence 表中的 diligenceId,companyName,companyId,并根据snapshot_date 是否存在，返回 snapshotStatus 字段 0: 未生成，1: 生成中，2: 生成完成
   * @param batchId 批次ID
   * @returns 批次实体
   */
  async getBatchEntityWithSnapshot(user: RoverUserModel, params: OpenapiBatchDiligenceResultRequest) {
    const { currentOrg: orgId } = user;
    const { batchId, pageIndex, pageSize } = params;
    const totalQueryBuilder = this.diligenceHistoryRepo
      .createQueryBuilder('diligence')
      .leftJoin(BatchDiligenceEntity, 'batchDiligence', 'diligence.id = batchDiligence.diligenceId')
      .leftJoin(BatchEntity, 'batch', 'batch.batchId = batchDiligence.batchId')
      .where('diligence.orgId = :orgId', { orgId })
      .andWhere('batch.batchId = :batchId', { batchId });
    const dataQueryBuilder = totalQueryBuilder
      .clone()
      .select([
        'diligence.id as diligenceId',
        'diligence.name as companyName',
        'diligence.companyId as companyId',
        "CASE WHEN diligence.snapshot_date IS NULL THEN 0 WHEN diligence.snapshot_date IS NOT NULL AND (diligence.snapshot_id IS NULL OR diligence.snapshot_id = '') THEN 1 ELSE 2 END as snapshotStatus",
      ])
      .orderBy('diligence.id', 'DESC')
      .offset(pageSize * (pageIndex - 1))
      .limit(pageSize);
    const [total, result] = await Promise.all([totalQueryBuilder.getCount(), dataQueryBuilder.getRawMany()]);
    return {
      data: result.map((item) => ({
        batchId,
        diligenceId: Number(item.diligenceId),
        companyName: item.companyName,
        companyId: item.companyId,
        snapshotStatus: Number(item.snapshotStatus),
      })),
      total,
      pageIndex,
      pageSize,
    };
  }

  /**
   * 获取批次的最终状态（基于快照完成情况）
   * 统计一个 batch 下是否所有的 due_diligence 的 snapshotStatus 都是 2
   * 只有 batch.status === 2 并且所有 snapshotStatus 都是 2，则 batch 的状态才返回 2
   * 否则返回 batch 的原始状态
   * @param user 用户
   * @param batchId 批次ID
   * @returns batch 状态
   */
  async getBatchStatusWithSnapshotCheck(user: RoverUserModel, batchId: number): Promise<number> {
    const { currentOrg: orgId } = user;
    const batchEntity = await this.batchRepo.findOne({
      where: {
        batchId,
        orgId,
      },
      select: ['status'],
    });
    if (!batchEntity) {
      return null;
    }
    const batchStatus = batchEntity.status;
    if (batchStatus !== BatchStatusEnums.Done) {
      return batchStatus;
    }
    const diligenceList = await this.diligenceHistoryRepo
      .createQueryBuilder('diligence')
      .leftJoin(BatchDiligenceEntity, 'batchDiligence', 'diligence.id = batchDiligence.diligenceId')
      .leftJoin(BatchEntity, 'batch', 'batch.batchId = batchDiligence.batchId')
      .where('diligence.orgId = :orgId', { orgId })
      .andWhere('batch.batchId = :batchId', { batchId })
      .select([
        "CASE WHEN diligence.snapshot_date IS NULL THEN 0 WHEN diligence.snapshot_date IS NOT NULL AND (diligence.snapshot_id IS NULL OR diligence.snapshot_id = '') THEN 1 ELSE 2 END as snapshotStatus",
      ])
      .getRawMany();
    if (diligenceList.length === 0) {
      return batchStatus;
    }
    const allSnapshotStatuses = diligenceList.map((item) => Number(item.snapshotStatus));
    const allStatusIs2 = allSnapshotStatuses.every((status) => status === 2);
    if (batchStatus === BatchStatusEnums.Done && allStatusIs2) {
      return BatchStatusEnums.Done;
    }
    return batchStatus;
  }

  /**
   * 搜索用户权限下的批次列表
   * @param user 用户
   * @param body 请求参数
   * @returns 用户列表
   */
  async searchUser(user: RoverUserModel, body: SearchBatchRequest) {
    const { currentOrg: orgId } = user;
    const { batchType, businessType } = body;
    const qb = this.batchRepo
      .createQueryBuilder('batch')
      .leftJoinAndSelect('batch.creator', 'creator')
      .select(['batch', 'creator.name', 'creator.userId'])
      .where('batch.orgId = :orgId', { orgId })
      .andWhere('batch.batchType = :batchType', { batchType });

    if (!businessType?.length) {
      return [];
    }
    // 找到当前用户 有数据权限的 businessType，加入查询条件
    const querySqlList = compact(businessType.map((x) => this.securityService.getBatchPermissionSql(x, user)));
    if (querySqlList.length) {
      qb.andWhere(`(${querySqlList.join(' or ')})`);
    } else {
      // 没找到有数据权限的businessType，直接返回空列表
      return [];
    }
    qb.groupBy('batch.creatorId');
    const batch = await qb.getMany();
    return uniq(batch?.map((b) => b.creator)) || [];
  }

  /**
   * 获取批次详情
   * @param user 用户
   * @param postData 请求参数
   * @param notUsePage 是否不使用分页
   * @returns 批次详情
   */
  async getBatch(user: RoverUserModel, postData: SearchBatchResultRequest, notUsePage = false): Promise<DiligenceHistoryResponse> {
    let response: DiligenceHistoryResponse;
    const { currentOrg: orgId } = user;
    const {
      batchId,
      searchKey,
      pageSize,
      pageIndex,
      result,
      createDate,
      dimensionLevel1,
      dimensionLevel2,
      groupIds,
      labelIds,
      departments,
      province,
      existCustomer,
      diligenceIds,
      preBatchId,
      changedOnly,
    } = postData;
    const batchEntity = await this.batchRepo.findOne({
      where: {
        batchId,
      },
      relations: ['creator'],
    });
    const params = {
      batchId,
      orgId,
      searchKey,
      result,
      groupIds,
      labelIds,
      departments,
      province,
      existCustomer,
      changedOnly,
      preBatchId,
      diligenceIds,
    };
    const qb = this.batchBaseHelperService.getBatchQB(params);

    // 创建时间
    QueryBuilderHelper.applyDateRangeQuery(qb, createDate, 'createDate');
    qb.orderBy('diligence.id', 'DESC');
    //记录的是企业数，不是维度命中数
    const childDimensionHits: ChildDimensionHit[] = [];
    if (dimensionLevel1) {
      let data = await qb.getMany();
      if (data?.length) {
        this.batchBaseHelperService.generateChildDimensionHits(data, childDimensionHits, dimensionLevel1, dimensionLevel2);
        data = data.filter((d) => d?.[dimensionLevel1] > 0);
        const level1Total = data?.length;
        if (dimensionLevel2) {
          data = data.filter(
            (d) =>
              d.details.dimensionScoreDetails.find((e) => e.groupKey === dimensionLevel1)?.scoreDetails?.find((e) => e.key === dimensionLevel2)?.totalHits > 0,
          );
        }
        if (!data?.length) {
          return { pageSize, pageIndex, total: 0, data: [] };
        }
        if (childDimensionHits?.length) {
          childDimensionHits.unshift(
            Object.assign(new ChildDimensionHit(), {
              key: 'allDimension',
              totalHits: level1Total,
            }),
          );
        }
        const start = pageSize * (pageIndex - 1);
        if (notUsePage) {
          response = {
            pageSize,
            pageIndex,
            total: data.length,
            data,
            childDimensionHits,
          };
        } else {
          response = {
            pageSize: pageSize,
            pageIndex: pageIndex,
            total: data.length,
            data: data.slice(start, start + pageSize),
            childDimensionHits,
            editor: batchEntity?.creator,
            createDate: batchEntity?.updateDate,
          };
        }
      }
    } else {
      if (!notUsePage) {
        qb.skip(pageSize * (pageIndex - 1)).take(pageSize);
      }
      const [data, total] = await qb.getManyAndCount();
      this.batchBaseHelperService.generateChildDimensionHits(data, childDimensionHits, dimensionLevel1);
      if (childDimensionHits?.length) {
        childDimensionHits.unshift(
          Object.assign(new ChildDimensionHit(), {
            key: 'allDimension',
            totalHits: total,
          }),
        );
      }
      response = {
        pageSize,
        pageIndex,
        data,
        total,
        childDimensionHits,
        editor: batchEntity?.creator,
        createDate: batchEntity?.updateDate,
      };
    }
    if (response?.data?.length) {
      const companyIds = response.data.map((x) => x.companyId);
      const companyBusinessInfoMap = await this.companySearchService.getCompanyBusinessInfoMap(companyIds);
      response.data.forEach((d) => {
        d.creditcode = companyBusinessInfoMap.get(d.companyId)?.creditcode;
        this.evaluationService.postUpdateDiligence(d);
      });
    }
    if (!response.data) {
      response.data = [];
    }
    return response;
  }

  /**
   * 获取公司维度详情
   * @param orgId
   * @param postData
   * @returns
   */
  public async getCompanyDimensionDetail(orgId: number, postData: SearchBatchResultRequest) {
    const childDimensionHit: Record<string, number> = {};
    //此处统计的是纬度命中的数量
    const res: ChildDimensionHit[] = [];
    const { diligenceId, dimensionLevel1, dimensionLevel2 } = postData;
    const diligenceHis = await this.diligenceHistoryRepo.findOne({ where: { id: diligenceId, orgId } });
    if (diligenceHis) {
      diligenceHis?.details?.dimensionScoreDetails.map((e) => {
        if (dimensionLevel2) {
          e?.scoreDetails?.map((s) => {
            if (dimensionLevel2 === s?.key) {
              s?.subDimension?.map((sb) => {
                childDimensionHit[sb.key] = (childDimensionHit?.[sb.key] || 0) + sb.totalHits;
              });
            }
          });
        } else if (e.groupKey === dimensionLevel1) {
          e.scoreDetails?.map((s) => {
            childDimensionHit[s.key] = (childDimensionHit?.[s.key] || 0) + s.totalHits;
          });
        }
      });
    }
    Object?.keys(childDimensionHit)?.map((c) => {
      res.push(Object.assign(new ChildDimensionHit(), { key: c, totalHits: childDimensionHit[c] }));
    });
    return res;
  }

  /**
   * 搜索匹配公司
   * @param user
   * @param body
   * @returns
   */
  async searchMatchCompanyRequest(user: RoverUserModel, body: SearchMatchCompanyRequest): Promise<SearchMatchCompanyResponse> {
    const { pageSize, pageIndex } = body;
    const response: SearchMatchCompanyResponse = Object.assign(new SearchMatchCompanyResponse(), {
      pageIndex,
      pageSize,
    });

    const batchMatchCompanyEntity = await this.batchMatchCompanyRepo.findOne({
      where: {
        orgId: user.currentOrg,
        id: body.batchId,
      },
    });
    if (!batchMatchCompanyEntity) {
      throw new BadRequestException();
    }
    const qb = this.batchMatchCompanyItemRepo
      .createQueryBuilder('item')
      .andWhere('item.batchId = :batchId', { batchId: body.batchId })
      .addOrderBy('item.flag', 'DESC')
      .addOrderBy('item.id', 'ASC');
    qb.skip(pageSize * (pageIndex - 1)).take(pageSize);
    const [data, total] = await qb.getManyAndCount();
    const companyIds = data.filter((x) => x.companyId).map((x) => x.companyId);
    if (companyIds.length) {
      const companyInfoMap: Map<string, CompanyBusinessInfo> = await this.companySearchService.getCompanyBusinessInfoMap(companyIds);
      data
        .filter((x) => x.companyId)
        .forEach((x) => {
          const info: CompanyBusinessInfo = companyInfoMap.get(x.companyId);
          if (info) {
            Object.assign(x, omit(info, ['companyId', 'companyName']));
          }
        });
    }
    Object.assign(response, { data, total });

    const qb2 = this.batchMatchCompanyItemRepo
      .createQueryBuilder('item')
      .select(['item.flag as flag', `count(item.id) as 'count'`])
      .andWhere('item.batchId = :batchId', { batchId: body.batchId })
      .addGroupBy('item.flag');
    const groupCount = await qb2.getRawMany();
    const statistic = batchMatchCompanyEntity.statisticsInfo;
    statistic.successCount = sum(groupCount.filter((x) => x.flag == '0').map((x) => Number(x.count)));
    statistic.errorCount = sum(groupCount.filter((x) => x.flag != '0').map((x) => Number(x.count)));
    statistic.duplicatedCount = statistic.recordCount - statistic.successCount - statistic.errorCount;
    if (statistic.duplicatedCount < 0) {
      statistic.duplicatedCount = 0;
    }
    Object.assign(response, { statistic: pick(statistic, ['successCount', 'errorCount', 'duplicatedCount']) });
    return response;
  }

  /**
   * 匹配公司
   * @param user
   * @param filePath
   * @param fileName
   * @param fileParseResult
   * @returns
   */
  async getMatchCompanies(user: RoverUserModel, filePath: string, fileName: string, fileParseResult: FileParseResult) {
    const { succeedItems, failedItems } = fileParseResult;
    if (!succeedItems?.length) {
      throw new BadRequestException('未识别到符合格式的数据');
    }
    let extension = '';
    if (fileName.lastIndexOf('.') > 0) {
      extension = fileName.substring(fileName.lastIndexOf('.'), fileName.length);
    }
    const originFileUrl = await this.resultExportService.putFileToOss(filePath, uuidv1() + extension, fileName);
    const matchCompany = await this.batchMatchCompanyRepo.save(
      Object.assign(new BatchMatchCompanyEntity(), {
        createDate: new Date(),
        orgId: user.currentOrg,
        creatorId: user.userId,
        fileName,
        originFile: originFileUrl,
        statisticsInfo: { recordCount: succeedItems.length + failedItems.length },
      }),
    );
    let plusRecordCount = 0; // 通过曾用名匹配时，一个曾用名可能匹配多个公司
    const successChunks = chunk(succeedItems, 100);
    await Bluebird.map(
      successChunks,
      async (chunk) => {
        const names = chunk.map((x) => x.companyName);
        const { matchedCompanyInfos, unmatchedNames, unsupported } = await this.companySearchService.matchCompanyInfoV2(names);
        const entities: BatchMatchCompanyItemEntity[] = [];
        matchedCompanyInfos.forEach((x) => {
          let matchBy;
          if (names.includes(x.name)) {
            matchBy = 0;
          } else if (names.includes(x.creditcode)) {
            matchBy = 1;
          } else if (names.includes(x.regno)) {
            matchBy = 2;
          } else {
            matchBy = 3;
          }
          entities.push(
            Object.assign(new BatchMatchCompanyItemEntity(), {
              batchId: matchCompany.id,
              companyId: x.id,
              name: x.name,
              flag: 0,
              matchBy,
            }),
          );
        });
        unmatchedNames.forEach((x) =>
          entities.push(
            Object.assign(new BatchMatchCompanyItemEntity(), {
              batchId: matchCompany.id,
              name: x,
              flag: 2,
            }),
          ),
        );
        unsupported.forEach((x) =>
          entities.push(
            Object.assign(new BatchMatchCompanyItemEntity(), {
              batchId: matchCompany.id,
              name: x,
              flag: 1,
            }),
          ),
        );
        if (entities.length > names.length) {
          plusRecordCount += entities.length - names.length;
        }
        await this.batchMatchCompanyItemRepo.upsert(entities, ['batchId', 'name', 'companyId']);
      },
      { concurrency: 3 },
    );
    if (plusRecordCount > 0) {
      matchCompany.statisticsInfo.recordCount += plusRecordCount;
      await this.batchMatchCompanyRepo.save(matchCompany);
    }
    return matchCompany;
  }

  /**
   * 匹配公司
   * @param user
   * @param filePath
   * @param fileName
   * @param businessType
   * @returns
   */
  async matchBatchCompany(user: RoverUserModel, filePath: string, fileName: string, businessType: BatchBusinessTypeEnums) {
    let fileParseResult: FileParseResult = null;
    let supportOverSeas = true;
    let onlyMatch = true;
    switch (businessType) {
      case BatchBusinessTypeEnums.Customer_File: {
        fileParseResult = await this.customerFileParser.parseFile(user, filePath);
        break;
      }
      case BatchBusinessTypeEnums.InnerBlacklist_File: {
        fileParseResult = await this.innerBlacklistImportFileParser.parseFile(user, filePath);
        break;
      }
      case BatchBusinessTypeEnums.Monitor_File: {
        fileParseResult = await this.monitorFileParser.parseFile(user, filePath);
        supportOverSeas = false;
        onlyMatch = false;
        break;
      }
      default: {
        fileParseResult = { succeedItems: [], failedItems: [] };
        break;
      }
    }
    const { succeedItems, failedItems } = fileParseResult;
    if (!succeedItems?.length) {
      throw new BadRequestException('未识别到符合格式的数据');
    }
    let extension = '';
    if (fileName.lastIndexOf('.') > 0) {
      extension = fileName.substring(fileName.lastIndexOf('.'), fileName.length);
    }
    const originFileUrl = await this.resultExportService.putFileToOss(filePath, uuidv1() + extension, fileName);
    const batchEntity = await this.batchMatchCompanyRepo.save(
      Object.assign(new BatchMatchCompanyEntity(), {
        createDate: new Date(),
        orgId: user.currentOrg,
        creatorId: user.userId,
        fileName,
        originFile: originFileUrl,
        statisticsInfo: { recordCount: succeedItems.length + failedItems.length },
      }),
    );
    let plusRecordCount = 0; // 通过曾用名匹配时，一个曾用名可能匹配多个公司
    const successChunks = chunk(succeedItems, 100);
    //3:香港企业，4:机关单位，5：台湾企业
    await Bluebird.map(
      successChunks,
      async (chunk) => {
        const names = chunk.map((x) => x.companyName);
        const { matchedCompanyInfos, unmatchedNames, unsupported } = await this.companySearchService.matchCompanyInfoV2(
          names,
          supportOverSeas, //支持境外企业，香港企业，机关单位，台湾企业
          onlyMatch, //是否只匹配，如果为 true，则不进行标准代码的校验
        );

        const entities: BatchMatchCompanyItemEntity[] = [];

        // 获取校验结果（分组+部门）
        const validationResult = await this.batchValidationService.validate(businessType, chunk, user);

        matchedCompanyInfos.forEach((x) => {
          if (unsupported.includes(x.name)) {
            return;
          }
          let matchBy;
          if (names.includes(x.name)) {
            matchBy = 0;
          } else if (names.includes(x.creditcode)) {
            matchBy = 1;
          } else if (names.includes(x.regno)) {
            matchBy = 2;
          } else {
            matchBy = 3;
          }

          // 确定flag值
          const parsedItem = chunk.find((c) => compact([x.name, ...(x?.originalname ? x.originalname : []), x?.creditcode, x?.regno]).includes(c.companyName));
          const flag = this.batchValidationService.determineFlag(businessType, parsedItem, validationResult);

          entities.push(
            Object.assign(new BatchMatchCompanyItemEntity(), {
              batchId: batchEntity.id,
              companyId: x.id,
              name: x.name,
              flag,
              matchBy,
              parsedItem: parsedItem || {
                //originalname实际上是一个数组
                companyId: x.id,
                companyName: x.name,
              },
            }),
          );
        });
        unmatchedNames.forEach((x) => {
          entities.push(
            Object.assign(new BatchMatchCompanyItemEntity(), {
              batchId: batchEntity.id,
              name: x,
              flag: 2,
              parsedItem: chunk.find((c) => c.companyName == x) || { companyName: x },
            }),
          );
        });
        unsupported.forEach((x) =>
          entities.push(
            Object.assign(new BatchMatchCompanyItemEntity(), {
              batchId: batchEntity.id,
              name: x,
              flag: 1,
              parsedItem: chunk.find((c) => c.companyName == x) || { companyName: x },
            }),
          ),
        );
        if (entities.length > names.length) {
          plusRecordCount += entities.length - names.length;
        }
        await this.batchMatchCompanyItemRepo.upsert(entities, ['batchId', 'name', 'companyId']);
      },
      { concurrency: 2 },
    );
    if (plusRecordCount > 0) {
      batchEntity.statisticsInfo.recordCount += plusRecordCount;
      await this.batchMatchCompanyRepo.save(batchEntity);
    }
    return batchEntity;
  }
}
