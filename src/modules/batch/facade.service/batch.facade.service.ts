/**
 * @file 批量任务相关 统一管理batch和batchJob的创建
 */
import { PaginationParams } from '@commons/model/common';
import { BatchRemoveRequest } from '@domain/dto/batch/request/BatchRemoveRequest';
import { BatchRetryRequest } from '@domain/dto/batch/request/BatchRetryRequest';
import { SearchBatchRequest } from '@domain/dto/batch/request/SearchBatchRequest';
import { SearchBatchResultRequest } from '@domain/dto/batch/request/SearchBatchResultRequest';
import { SearchBiddingBatchResultRequest } from '@domain/dto/batch/request/SearchBiddingBatchResultRequest';
import { SearchMatchCompanyRequest } from '@domain/dto/batch/request/SearchMatchCompanyRequest';
import { SearchPotentialBatchResultRequest } from '@domain/dto/batch/request/SearchPotentialBatchResultRequest';
import { UpdateMatchCompanyRequest } from '@domain/dto/batch/request/UpdateMatchCompanyRequest';
import { SearchMatchCompanyResponse } from '@domain/dto/batch/response/SearchMatchCompanyResponse';
import { BatchDiligenceEntity } from '@domain/entities/BatchDiligenceEntity';
import { BatchEntity } from '@domain/entities/BatchEntity';
import { BatchJobEntity } from '@domain/entities/BatchJobEntity';
import { BatchMatchCompanyEntity } from '@domain/entities/BatchMatchCompanyEntity';
import { BatchMatchCompanyItemEntity } from '@domain/entities/BatchMatchCompanyItemEntity';
import { BatchTenderDiligenceEntity } from '@domain/entities/BatchTenderDiligenceEntity';
import { BatchBusinessTypeEnums } from '@domain/enums/batch/BatchBusinessTypeEnums';
import { BatchStatusEnums } from '@domain/enums/batch/BatchStatusEnums';
import { BatchTypeEnums } from '@domain/enums/batch/BatchTypeEnums';
import { BatchInfoPO } from '@domain/model/batch/po/BatchInfoPO';
import { FileParseResult } from '@domain/model/batch/po/parse/FileParseResult';
import { ParsedRecordBase } from '@domain/model/batch/po/parse/ParsedRecordBase';
import { DiligenceHistoryResponse } from '@domain/model/diligence/pojo/history/DiligenceHistoryResponse';
import { OpenapiBatchDiligenceResultRequest } from '@domain/model/openapi/diligence/OpenapiBatchDiligenceRequest';
import { RoverUserModel } from '@domain/model/RoverUserModel';
import { TraceLog } from '@kezhaozhao/qcc-logger/lib/qcc/annotation/trace.annotation';
import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, QueryFailedError, Repository } from 'typeorm';
import { FindOptionsWhere } from 'typeorm/find-options/FindOptionsWhere';
import { BatchCheckService } from './service/batch.check.service';
import { BatchCreationService } from './service/batch.creation.service';
import { BatchQueryService } from './service/batch.query.service';
import { BatchStatisticService } from './service/batch.statistic.service';
import { FileParserService } from './service/file.parser.service';

@Injectable()
export class BatchFacadeService {
  constructor(
    @InjectRepository(BatchMatchCompanyItemEntity) private readonly batchMatchCompanyItemRepo: Repository<BatchMatchCompanyItemEntity>,
    @InjectRepository(BatchMatchCompanyEntity) private readonly batchMatchCompanyRepo: Repository<BatchMatchCompanyEntity>,
    @InjectRepository(BatchTenderDiligenceEntity) private readonly batchTenderRepo: Repository<BatchTenderDiligenceEntity>,
    @InjectRepository(BatchJobEntity) private readonly batchJobRepo: Repository<BatchJobEntity>,
    @InjectRepository(BatchDiligenceEntity) private readonly batchDiligenceRepo: Repository<BatchDiligenceEntity>,
    private readonly batchQueryService: BatchQueryService,
    private readonly batchStatisticService: BatchStatisticService,
    private readonly batchCheckService: BatchCheckService,
    private readonly batchCreationService: BatchCreationService,
    private readonly fileParserService: FileParserService,
  ) {}

  // ******************************************************* 查询类型 start *******************************************************

  /**
   * 根据招标排查id查询pdf导出
   * @returns 批量任务结果
   * @param postBody
   */
  async searchBatchResultError(postBody: BatchRetryRequest) {
    return await this.batchQueryService.searchBatchResultError(postBody);
  }

  /**
   * 根据招标排查id查询pdf导出
   * @param currentUser 当前用户
   * @param tenderId 招标排查id
   * @returns 批次
   */
  async getBatchEntityByTenderId(currentUser: RoverUserModel, tenderId: number) {
    return await this.batchQueryService.getBatchEntityByTenderId(currentUser, tenderId);
  }

  /**
   * 根据准入排查id查询pdf导出
   * @param currentUser 当前用户
   * @param diligenceId 准入排查id
   * @returns 批次
   */
  async getBatchEntityByDiligenceId(currentUser: RoverUserModel, diligenceId: number) {
    return await this.batchQueryService.getBatchEntityByDiligenceId(currentUser, diligenceId);
  }
  /**
   * 查询批次列表
   * @param user 用户
   * @param body 请求参数
   * @returns 批次列表
   */
  async searchBatch(user: RoverUserModel, body: SearchBatchRequest) {
    return await this.batchQueryService.searchBatch(user, body);
  }

  /**
   * 获取 batch entity
   * @param batchId
   * @param user
   */
  async getBatchEntity(user: RoverUserModel, batchId: number, withSignatureUrl = true) {
    return await this.batchQueryService.getBatchEntity(user, batchId, withSignatureUrl);
  }

  /**
   * 获取 batch entity 并包含创建者
   * @param user 用户
   * @param batchId 批次ID
   * @returns 批次实体
   */
  async getBatchEntityWithCreator(user: RoverUserModel, batchId: number) {
    const batchEntity = await this.batchQueryService.getBatchEntityWithCreator(user, batchId);
    //判断 batchEntity 的 status === 2 如果 true，则需要再调用getBatchStatusWithSnapshotCheck方法
    if (batchEntity.status === BatchStatusEnums.Done) {
      const batchStatus = await this.batchQueryService.getBatchStatusWithSnapshotCheck(user, batchId);
      batchEntity.status = batchStatus;
    }
    return batchEntity;
  }

  async getBatchEntityWithSnapshot(user: RoverUserModel, params: OpenapiBatchDiligenceResultRequest) {
    return await this.batchQueryService.getBatchEntityWithSnapshot(user, params);
  }

  /**
   * 搜索用户权限下的批次列表
   * @param user 用户
   * @param body 请求参数
   * @returns 用户列表
   */
  async searchUser(user: RoverUserModel, body: SearchBatchRequest) {
    return await this.batchQueryService.searchUser(user, body);
  }

  /**
   * 获取指定批量任务详情
   * @param user
   * @param postData
   * @param notUsePage
   * @returns
   */
  async getBatch(user: RoverUserModel, postData: SearchBatchResultRequest, notUsePage = false): Promise<DiligenceHistoryResponse> {
    return await this.batchQueryService.getBatch(user, postData, notUsePage);
  }

  /**
   * 获取公司维度详情
   * @param orgId
   * @param postData
   * @returns
   */
  public async getCompanyDimensionDetail(orgId: number, postData: SearchBatchResultRequest) {
    return await this.batchQueryService.getCompanyDimensionDetail(orgId, postData);
  }

  /**
   * 搜索匹配公司
   * @param user
   * @param body
   * @returns
   */
  async searchMatchCompanyRequest(user: RoverUserModel, body: SearchMatchCompanyRequest): Promise<SearchMatchCompanyResponse> {
    return await this.batchQueryService.searchMatchCompanyRequest(user, body);
  }

  // 批量潜在利益关系排查，上传excel文件，匹配公司
  async matchBatchPotentialCompany(user: RoverUserModel, filePath: string, fileName: string) {
    const fileParseResult: FileParseResult = await this.fileParserService.getParseResult(user, filePath, BatchBusinessTypeEnums.Potential_Batch_Excel);
    return await this.batchQueryService.getMatchCompanies(user, filePath, fileName, fileParseResult);
  }

  // 批量风险排查，上传excel文件，匹配公司
  async matchBatchDiligenceCompany(user: RoverUserModel, filePath: string, fileName: string) {
    const fileParseResult: FileParseResult = await this.fileParserService.getParseResult(user, filePath, BatchBusinessTypeEnums.Diligence_File);
    return await this.batchQueryService.getMatchCompanies(user, filePath, fileName, fileParseResult);
  }

  /**
   * 匹配公司
   * @param user
   * @param filePath
   * @param fileName
   * @param businessType
   * @returns
   */
  async matchBatchCompany(user: RoverUserModel, filePath: string, fileName: string, businessType: BatchBusinessTypeEnums) {
    return await this.batchQueryService.matchBatchCompany(user, filePath, fileName, businessType);
  }

  // ******************************************************* 查询类型 end *******************************************************

  // ******************************************************* 创建导入批量任务类型 start *******************************************************

  /**
   * 根据文件创建批次
   * @param user 用户信息
   * @param filePath 文件路径
   * @param fileName 文件名称
   * @param businessType 业务类型
   * @returns 创建结果
   */
  async createBatchByFile(user: RoverUserModel, filePath: string, fileName: string, businessType: BatchBusinessTypeEnums, settingId?: number) {
    return this.batchCreationService.createBatchByFile(user, filePath, fileName, businessType, settingId);
  }

  /**
   * 根据指定的data创建批量排查任务
   * @param user
   * @param data
   * @param businessType
   * @returns
   */
  @TraceLog({ throwError: true })
  async createBatchDiligenceTask(
    user: RoverUserModel,
    data: ParsedRecordBase[],
    businessType: BatchBusinessTypeEnums,
    batchType?: BatchTypeEnums,
    params?: BatchInfoPO,
  ) {
    return this.batchCreationService.createBatchDiligenceTask(user, data, businessType, batchType, params);
  }

  /**
   * 执行已有的批量排查任务（尽职调查、潜在利益关系排查）
   * 根据预匹配数据创建批次
   * @param user
   * @param batchId
   * @param businessType BatchBusinessTypeEnums.Diligence_File,BatchBusinessTypeEnums.Potential_Batch_Excel
   * @param settingId
   * @returns
   */
  async executeBatchByBatchMatchCompanyId(user: RoverUserModel, batchMatchCompanyId: number, businessType: BatchBusinessTypeEnums, settingId?: number) {
    return this.batchCreationService.createBatchByBatchMatchCompanyId(user, batchMatchCompanyId, businessType, { settingId });
  }

  /**
   * 创建批量导入任务（内部黑名单、第三方列表、监控）
   * @param user
   * @param batchMatchCompanyId
   * @param businessType BatchBusinessTypeEnums.Customer_File,BatchBusinessTypeEnums.InnerBlacklist_File,BatchBusinessTypeEnums.Monitor_File
   * @param isUpdate 是否更新，默认 true
   */
  async executeBatchImport(user: RoverUserModel, batchMatchCompanyId: number, businessType: BatchBusinessTypeEnums, isUpdate = true): Promise<BatchEntity> {
    return this.batchCreationService.createBatchByBatchMatchCompanyId(user, batchMatchCompanyId, businessType, { isUpdate });
  }

  // ******************************************************* 创建导入批量任务类型 end *******************************************************

  // ******************************************************* 创建导出批量任务类型 start *******************************************************
  /**
   * 创建批量导出任务
   * batchJob直接被启动，无须通过batch来启动
   * @param user
   * @param businessType
   * @param condition
   * @returns
   */
  async createExportBatchEntity<T extends PaginationParams>(user: RoverUserModel, businessType: BatchBusinessTypeEnums, condition: T) {
    return this.batchCreationService.createExportBatchEntity(user, businessType, condition);
  }

  // ******************************************************* 创建导出批量任务类型 end *******************************************************

  // ******************************************************* 检查类型 start *******************************************************

  /**
   * 检查批次招标排查限制
   * @param user 用户信息
   * @param filePath 文件路径
   * @returns 检查结果
   */
  async checkBatchBiddingDiligenceLimit(user: RoverUserModel, filePath: string, settingId?: number) {
    return this.batchCheckService.checkBatchBiddingDiligenceLimit(user, filePath, settingId);
  }

  /**
   * 检查批次特定尽调限制
   * @param user 用户信息
   * @param filePath 文件路径
   * @returns 检查结果
   */
  async checkBatchSpecificDiligenceLimit(user: RoverUserModel, filePath: string) {
    return this.batchCheckService.checkBatchSpecificDiligenceLimit(user, filePath);
  }

  /**
   * 检查排查是否已生成快照
   * @param currentUser 当前用户
   * @param diligenceId 排查id
   * @returns 是否已生成快照
   */
  async isGenneratedSnapshot(currentUser: RoverUserModel, diligenceId: number) {
    return await this.batchCheckService.isGenneratedSnapshot(currentUser, diligenceId);
  }

  // ******************************************************* 检查类型 end *******************************************************

  // ******************************************************* 统计类型 start *******************************************************
  async getPotentialStatistics(user: any, data: SearchPotentialBatchResultRequest) {
    return this.batchStatisticService.getPotentialStatistics(user, data);
  }

  async getBiddingStatistics(user: RoverUserModel, body: SearchBiddingBatchResultRequest) {
    return this.batchStatisticService.getBiddingStatistics(user, body);
  }

  async searchBiddingDetail(user: RoverUserModel, body: SearchBiddingBatchResultRequest) {
    return this.batchStatisticService.searchBiddingDetail(user, body);
  }

  async getSpecificStatistics(user: RoverUserModel, body: SearchBiddingBatchResultRequest) {
    return this.batchStatisticService.getSpecificStatistics(user, body);
  }

  async searchSpecificDetail(user: RoverUserModel, body: SearchBiddingBatchResultRequest) {
    return this.batchStatisticService.searchSpecificDetail(user, body);
  }

  async searchPotentialDetail(user: any, data: SearchPotentialBatchResultRequest) {
    return this.batchStatisticService.searchPotentialDetail(user, data);
  }

  // ******************************************************* 统计类型 end *******************************************************

  // ******************************************************* 编辑类型 start *******************************************************
  /**
   * 删除匹配公司
   * @param user
   * @param data
   * @returns
   */
  async deleteMatchCompany(user: RoverUserModel, data: BatchRemoveRequest) {
    const { batchId, ids: itemIds, flag } = data;
    if ((!itemIds?.length && !flag) || (flag && !batchId && !itemIds?.length)) {
      return { affected: 0 };
    }
    const conditions: FindOptionsWhere<BatchMatchCompanyItemEntity> = {};
    if (itemIds?.length) {
      Object.assign(conditions, { id: In(itemIds) });
    }
    if (flag && batchId) {
      Object.assign(conditions, { flag: In([1, 2, 3, 4]), batchId });
    }
    const batchMatchCompanyItems = await this.batchMatchCompanyItemRepo.findBy(conditions);
    if (batchMatchCompanyItems?.length) {
      const batchMatchCompanyEntity = await this.batchMatchCompanyRepo.findOne({
        where: {
          id: batchId,
          orgId: user.currentOrg,
        },
      });
      if (batchMatchCompanyEntity) {
        batchMatchCompanyEntity.statisticsInfo.recordCount -= batchMatchCompanyItems.length;
        await this.batchMatchCompanyRepo.save(batchMatchCompanyEntity);
        return await this.batchMatchCompanyItemRepo.delete(conditions);
      }
    }
    return { affected: 0 };
  }

  /**
   * 更新匹配公司
   * @param user
   * @param itemId
   * @param body
   * @returns
   */
  async updateMatchCompany(user: RoverUserModel, itemId: number, body: UpdateMatchCompanyRequest) {
    const batchMatchCompanyItem = await this.batchMatchCompanyItemRepo.findOne({
      where: {
        id: itemId,
      },
    });
    if (!batchMatchCompanyItem) {
      throw new BadRequestException('不存在的匹配公司');
    }
    try {
      batchMatchCompanyItem.name = body.companyName;
      batchMatchCompanyItem.companyId = body.companyId;
      batchMatchCompanyItem.flag = 0;
      batchMatchCompanyItem.parsedItem = batchMatchCompanyItem.parsedItem || {};
      batchMatchCompanyItem.parsedItem['companyName'] = body.companyName;
      return await this.batchMatchCompanyItemRepo.update(itemId, batchMatchCompanyItem);
    } catch (e) {
      if (e instanceof QueryFailedError && e.message.startsWith('ER_DUP_ENTRY')) {
        return await this.batchMatchCompanyItemRepo.delete({ id: itemId });
      } else {
        throw e;
      }
    }
  }

  // ******************************************************* 编辑类型 end *******************************************************

  // ******************************************************* 结果绑定类型 start *******************************************************

  /**
   * 保存批次与招标排查的关联关系
   * @param currentUser 当前用户
   * @param batchId 批次ID
   * @param tenderId 招标排查ID
   */
  async saveBatchTenderEntity(currentUser: RoverUserModel, batchId: number, tenderId: number) {
    const batchJob = await this.batchJobRepo.findOne({
      where: {
        batchId,
      },
    });

    return await this.batchTenderRepo.save({
      batchId,
      tenderId,
      orgId: currentUser.currentOrg,
      jobId: batchJob?.jobId,
    });
  }

  /**
   * 保存批次与准入排查的关联关系
   * @param currentUser 当前用户
   * @param batchId 批次ID
   * @param diligenceId 准入排查ID
   */
  async saveBatchDiligenceEntity(currentUser: RoverUserModel, batchId: number, diligenceId: number) {
    const batchJob = await this.batchJobRepo.findOne({
      where: {
        batchId,
      },
    });
    return await this.batchDiligenceRepo.save({
      batchId,
      diligenceId,
      orgId: currentUser.currentOrg,
      jobId: batchJob?.jobId,
    });
  }

  // ******************************************************* 结果绑定类型 end *******************************************************
}
