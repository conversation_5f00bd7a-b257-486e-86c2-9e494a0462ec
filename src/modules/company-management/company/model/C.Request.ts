import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsNotEmpty, IsString, ValidateNested } from 'class-validator';
export class SupplierCustomer {
  @ApiProperty({ description: '供应商或客户关键字' })
  @IsNotEmpty()
  @IsString()
  startKey: string;

  @ApiProperty({ description: '供应商或客户关键字' })
  @IsNotEmpty()
  @IsString()
  endKey: string;
}

export class CSupplierCustomerRequest {
  @ApiProperty({ description: '供应商或客户列表' })
  @IsNotEmpty()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SupplierCustomer)
  list: SupplierCustomer[];
}
