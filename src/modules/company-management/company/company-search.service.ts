import { RoverExceptions } from '@commons/constants/exceptionConstants';
import { HttpQueryException } from '@commons/exceptions/HttpQueryException';
import { formatDateOrTimestamp, parseStringToDate } from '@commons/utils/date.utils';
import { ConfigService } from '@core/config/config.service';
import { HttpUtilsService } from '@core/config/httputils.service';
import { EconType, EnterpriseType, ForbiddenStandardCode, TreasuryType } from '@domain/constants/common';
import { CompanyEntity } from '@domain/entities/CompanyEntity';
import { CompBusiListInfoEntity } from '@domain/entities/CompBusiListInfoEntity';
import { CompanySearchRequest } from '@domain/model/company/CompanySearchRequest';
import { CompanySearchResponse } from '@domain/model/company/CompanySearchResponse';
import { CompanyListRequest } from '@domain/model/monitor/request/CompanyListRequest';
import { Client, RequestParams } from '@elastic/elasticsearch';
import { CommonListItem, CompanyClient, Contact, KysCompanyResponseDetails, KysCompanySearchRequest, SearchFilter } from '@kezhaozhao/company-search-api';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { BadParamsException } from '@kezhaozhao/qcc-exception-handler';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { DateRangeRelative, ESDetailResopne, ESResponse } from '@kezhaozhao/qcc-model';
import { createStatusCodeReverseMap } from '@modules/batch/common/file.export.template';
import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as Bluebird from 'bluebird';
import { chunk, compact, difference, find, flatten, groupBy, intersection, pick, union, uniq } from 'lodash';
import { Logger } from 'log4js';
import * as moment from 'moment/moment';
import { Cacheable, useIoRedisAdapter } from 'type-cacheable';
import { In, IsNull, MoreThanOrEqual, Not, Repository } from 'typeorm';
import { CompanyDetailService } from './company-detail.service';
import { CompanyBusinessInfo, CompanyFinanceInfoResponse, CompanyTag, Tel } from './model/CompanyBusinessInfo';
import { CompanyDetails } from './model/CompanyDetails';
import { CreditRateResult } from './model/CreditRateResult';
import { RateTrendInfo } from './model/CreditRateTrendResult';
import { CompaniesWithFreeTextRequest, SupplierCustomerWithFreeTextRequest } from './model/MultiMatchCompanyRequest';
import { SearchCertificationRequest } from './model/SearchCertificationRequest';
import { SearchCreditRequest } from './model/SearchCreditRequest';
import { SearchMultiSelectionRequest } from './model/SearchMultiSelectionRequest';
import { extractCompanyRevenue, isOrganism } from './utils';

enum CompanyDetailCommonList {
  companyScale = '32',
}

@Injectable()
export class CompanySearchService {
  private readonly logger: Logger = QccLogger.getLogger(CompanySearchService.name);
  private readonly companySearchClient: CompanyClient;
  private readonly creditFinanceClient: Client;
  private readonly creditFinanceIndexName: string;

  constructor(
    @InjectRepository(CompBusiListInfoEntity) private readonly compBusiListRepo: Repository<CompBusiListInfoEntity>,
    @InjectRepository(CompanyEntity) private readonly companyRepo: Repository<CompanyEntity>,
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly httpUtils: HttpUtilsService,
    private readonly detailService: CompanyDetailService,
  ) {
    this.companySearchClient = new CompanyClient({
      server: this.configService.kzzServer.companySearchApi,
      requestFrom: process.env.PROJECT_NAME || 'qcc-rover-service',
    });
    this.creditFinanceClient = new Client({
      nodes: configService.esConfig.finance.nodes,
      ssl: { rejectUnauthorized: false },
    });
    this.creditFinanceIndexName = configService.esConfig.finance.indexName;
    //@ts-ignore
    useIoRedisAdapter(this.redisService.getClient());
  }

  public async companyDetailsQcc(
    companyId: string,
    fields = [
      'Address',
      'CreditCode',
      'HisTelList',
      'KeyNo',
      'LatestAnnualReportAddrInfo',
      'Name',
      'SameTelAddressList',
      'Oper',
      'MultipleOper',
      'RecCap',
      'RegistCapi',
      'ShortStatus',
      'Staffs',
      'StartDate',
      'TaxNo',
      'VTList',
      'QccIndustry',
      'TermStart',
      'TeamEnd',
      'Scope',
      'Scale',
      'ContactInfo',
      'CompanyRevenue',
      'IndustryV3',
      'TagsInfoV2',
      'OriginalName',
      'CountInfo',
      'EconKind',
      'EntType',
      'Type',
      'CommonList',
      'RealCapiSource', // 实缴资本来源
      'Partners',
      'IsBranch',
      'MoreEmailList', // 更多邮箱列表
      'SameEmailList', // 相同邮箱列表
    ],
  ): Promise<CompanyDetails | null> {
    if (!companyId) {
      throw new BadParamsException(RoverExceptions.Diligence.Detail.NeedCompanyId);
    }
    try {
      if (isOrganism(companyId)) {
        //社会组织
        return this.getOrganismDetail(companyId);
      }
      const data = await this.httpUtils.getRequest(this.configService.dataServer.companyDetail, {
        keyNo: companyId,
      });
      if (data?.Status === 201) {
        //没有找到公司
        return null;
      }

      const result: CompanyDetails = Object.assign(new CompanyDetails(), pick(data.Result, fields));
      const financeData = await this.doGetCompanyFinance(companyId, data);
      if (financeData) {
        result['assetsTotal'] = financeData.assetsTotal;
        result['assetsTotalSource'] = financeData.assetsTotalSource;
        result['netProfit'] = financeData.netProfit;
        result['netProfitSource'] = financeData.netProfitSource;
        result['operIncTotal'] = financeData.operIncTotal;
        result['operIncTotalSource'] = financeData.operIncTotalSource;
      }

      result['TagsInfo'] = data.Result?.TagsInfoV2?.find((element) => element?.Type == 302);
      if (data.Result?.CommonList) {
        // 注册资本金额(外币换算人民币后的金额)
        result['Registcapiamount'] = data.Result?.CommonList?.find((element) => element?.Key == 19);
        //
        result['CommonList'] = data.Result?.CommonList?.filter((element) => element?.Key == 40);
        const standard_code = data.Result.CommonList.find((e) => e && e.Key == 52);
        if (standard_code) {
          const organizationalCode: string[] = compact(JSON.parse(standard_code.Value)?.organizational_code?.split(',')) || [];
          const esCode: string[] = compact(JSON.parse(standard_code.Value)?.es_code?.split(',')) || [];
          result.standardCode = union(organizationalCode, esCode);
        }
      }
      result.StaffHisCntInfo = data.Result?.StaffHisCntInfo;
      result.StaffHisCntPopUpInfo = data.Result?.StaffHisCntPopUpInfo;
      return result;
    } catch (e) {
      this.logger.error(`companyDetailsQcc() err:` + e.message);
      this.logger.error(e);
      throw new HttpQueryException(e);
    }
  }

  /**
   * 获取公司logo
   * @param companyId
   */
  @Cacheable({ ttlSeconds: 100, cacheKey: (args: any[]) => `cache:getLogoByKeyNos:${args[0]}` })
  public async getLogoByKeyNos(keyNos: string[]) {
    try {
      return await this.httpUtils.postRequest(this.configService.dataServer.getLogoByKeyNos, { keyNos });
    } catch (e) {
      this.logger.error(`http Post getLogoByKeyNos err:`, e);
      throw new HttpQueryException(e);
    }
  }

  /**
   * 获取社会组织详情
   * @param companyId
   */
  public async organismDetailsQcc(companyId: string) {
    return await this.detailService.organismDetailsQcc(companyId);
  }

  /**
   * 获取海外公司详情
   * @param companyId
   */
  public async overseasCompanyDetail(companyId: string) {
    return await this.detailService.overseasCompanyDetail(companyId);
  }

  public async getCompanyCertificationSummary(keyNo: string, startYear = '', searchKey = undefined) {
    try {
      const record = {
        keyNo,
        startYear,
        searchKey,
        certificateStatus: '1',
        isNew: true,
        isNewAgg: true,
      };
      const resp = await this.detailService.getCompanyCertificationSummary(record);
      if (resp?.Result?.length) {
        // 把质量管理体系认证、环境管理体系认证、安全生产许可证排在前面
        resp?.Result.sort((a, b) => {
          if (a?.CertificateCode == b?.CertificateCode) {
            return 0;
          }
          if (a?.CertificateCode == '*********') {
            return -1;
          }
          if (b?.CertificateCode == '*********') {
            return 1;
          }
          if (a?.CertificateCode == '*********') {
            return -1;
          }
          if (b?.CertificateCode == '*********') {
            return 1;
          }
          if (a?.CertificateCode == '011') {
            return -1;
          }
          if (b?.CertificateCode == '011') {
            return 1;
          }
          return 0;
        });
      }
      return resp;
    } catch (e) {
      this.logger.error(`http getCompanyCertificationSummary err:`, e);
      throw new HttpQueryException(e);
    }
  }

  public async getCompanyCertificationList(body: SearchCertificationRequest) {
    try {
      const param = {
        isNew: true,
        isNewAgg: true,
        certificateStatus: body?.certificateStatus,
      };
      if (body) {
        const { certificateCode } = body;
        // 移除判断长度大于9位的逻辑，统一使用certificateLevel参数传参
        if (certificateCode) {
          body['certificateLevel'] = certificateCode;
          body['certificateCode'] = null;
        }
        Object.assign(param, body);
      }
      const resp = await this.detailService.getCompanyCertificationList(param);
      if (resp) {
        if (!resp.Paging) {
          resp.Paging = {
            PageIndex: body.pageIndex,
            PageSize: body.pageSize,
            TotalRecords: 0,
          };
        }
        if (!resp.Result) {
          resp.Result = [];
        } else {
          const idList = resp.Result.map((item) => item.Id);
          const detailListResp = await this.detailService.getCompanyCertificationDetailList(idList);
          resp.Result.forEach((item) => {
            const detail = detailListResp.Result?.find((detail) => detail.Id == item.Id);
            if (detail) {
              item['Detail'] = detail;
            }
          });
        }
        if (!resp.GroupItems) {
          resp.GroupItems = [];
        }
      }
      return resp;
    } catch (e) {
      this.logger.error(`http getCompanyCertificationList err:`, e);
      throw new HttpQueryException(e);
    }
  }

  public async getCompanyCertificationDetail(id: string) {
    return await this.detailService.getCompanyCertificationDetail(id);
  }

  public async getCompanyCreditSummary(keyNo: string) {
    return await this.detailService.getCompanyCreditSummary(keyNo);
  }

  public async getCompanyCreditList(body: SearchCreditRequest) {
    try {
      return await this.detailService.getCompanyCreditList(body);
    } catch (e) {
      this.logger.error(`http getCompanyCreditList err:`, e);
      throw new HttpQueryException(e);
    }
  }

  /**
   * 获取社会组织详情(返回结构改为CompanyDetails)
   * @param companyId
   * @private
   */
  private async getOrganismDetail(companyId: string): Promise<CompanyDetails> {
    try {
      const [qccDetail, kysDetail] = await Bluebird.all([this.organismDetailsQcc(companyId), this.getCompanyDetails(companyId)]);
      const djInfo = qccDetail?.Result?.DJInfo;
      const result: CompanyDetails = Object.assign(new CompanyDetails(), {
        Name: djInfo?.name,
        Address: djInfo?.address,
        CreditCode: djInfo?.creditCode,
        Oper: djInfo?.Oper,
        RegistCapi: djInfo?.registCapi,
        StartDate: djInfo?.startDate,
        // 解析证书有效期为时间戳
        TermStart: djInfo?.certificatePeriod
          ? (() => {
              const [startStr] = djInfo.certificatePeriod.split(' 至 ');
              return startStr && moment(startStr, 'YYYY-MM-DD').isValid() ? moment(startStr).unix() : undefined;
            })()
          : undefined,
        TeamEnd: djInfo?.certificatePeriod
          ? (() => {
              const [, endStr] = djInfo.certificatePeriod.split(' 至 ');
              return endStr && moment(endStr, 'YYYY-MM-DD').isValid() ? moment(endStr).unix() : undefined;
            })()
          : undefined,
        VTList: qccDetail?.Result?.VTList,
        ContactInfo: qccDetail?.Result?.ContactInfo,
        CountInfo: qccDetail?.Result?.CountInfo,
        ChangeDiffInfo: qccDetail?.Result?.ChangeDiffInfo,
        ShortStatus: djInfo?.status,
        TagsInfoV2: qccDetail?.Result?.TagsInfoV2,
        Type: '0', //对社会组织特殊处理企业类型,
        standardCode: kysDetail?.Result?.standardCode || ['001005'],
      });
      // 先从qccDetail?.Result?.CommonList?.find((element) => element?.Key == 19) 取值，没有值，则从 qccDetail?.Result?.DJInfo?.registCapi（带货币单位，需要去掉货币单位） 取值，
      let registcapiAmount = qccDetail?.Result?.CommonList?.find((element) => element?.Key == 19);

      // 如果CommonList中没有值，则从registCapi中取值构建到CommonList中
      if (!registcapiAmount && qccDetail?.Result?.DJInfo?.registCapi) {
        const registCapiStr = qccDetail.Result.DJInfo.registCapi;
        registcapiAmount = { Key: 19, Value: registCapiStr };
        //需要考虑到 CommonList 有值和 null 的情况
        if (qccDetail.Result.CommonList) {
          qccDetail.Result.CommonList.push(registcapiAmount);
        } else {
          qccDetail.Result.CommonList = [registcapiAmount];
        }
      }

      result['Registcapiamount'] = registcapiAmount;
      result['CommonList'] = qccDetail?.Result?.CommonList;
      result['TagsInfo'] = qccDetail?.Result?.TagsInfoV2;
      return result;
    } catch (e) {
      this.logger.error(`http Get Organism/GetDetailV2 err:`, e);
      throw new HttpQueryException(e);
    }
  }

  /**
   * C端高级搜索接口
   * @param query
   * @returns
   */
  public async companySearchForQcc(query: SearchMultiSelectionRequest) {
    query.searchKey = query?.searchKey?.trim()?.substring(0, 100);
    if (!query?.searchKey?.trim() && !query.filter) {
      throw new BadRequestException(RoverExceptions.BadParams.Company.SearchKeyNotFound);
    }
    const { searchKey, searchIndex, pageIndex, pageSize, sortField, isSortAsc, includeFields, filter } = query;
    const requestData = {
      searchKey,
      searchIndex: 'default',
      //  0: '大陆企业', 1: '社会组织', 11: '事业单位', 12: '律师事务所',
      filter: JSON.stringify(filter),
      sortField,
      isSortAsc,
      pageIndex,
      pageSize,
    };

    if (searchIndex?.length) {
      const searchKey = {};
      query?.searchIndex?.forEach((index) => {
        if (query?.searchKey) {
          searchKey[index] = query?.searchKey;
        }
      });
      requestData.searchIndex = 'multicondition';
      requestData.searchKey = JSON.stringify(searchKey);
    }
    try {
      const data = await this.detailService.searchMultiSelection(requestData);
      if (data?.Status == 200) {
        let Result = data.Result;

        if (data?.Result?.length) {
          const statusCodeReverseMap = createStatusCodeReverseMap();
          Result = await Bluebird.map(
            data?.Result,
            (company) => {
              const hitReason = company['HitReasons'].find((r) => r.Field == '公司名称');
              if (hitReason) {
                company['HitReason'] = { Field: hitReason.Field, Value: hitReason.Value };
              }
              if (includeFields?.length) {
                return pick(company, includeFields);
              }
              const statusCode = statusCodeReverseMap.get(company?.ShortStatus);
              if (statusCode) {
                company.StatusCode = statusCode;
              }
              return company;
            },
            { concurrency: 1 },
          );
        }

        return { Status: data.Status, Paging: data.Paging, Result };
      }
      return null;
    } catch (e) {
      this.logger.error(`searchMultiSelection err:`, e);
      throw new BadRequestException(RoverExceptions.Common.RequestFailed);
    }
  }

  public async companySearchForKys(requestData: KysCompanySearchRequest): Promise<ESResponse<KysCompanyResponseDetails>> {
    return this.companySearchClient.kysSearch(requestData);
  }

  /**
   * C 端接口，批量获取企业详情
   * @param companyIds
   * @param selection
   * @returns
   */
  public async getCompanyBusinessInfoMap(companyIds: string[]): Promise<Map<string, CompanyBusinessInfo>> {
    const companyDetailMap: Map<string, CompanyBusinessInfo> = new Map<string, CompanyBusinessInfo>();
    if (!companyIds || companyIds.length === 0) {
      return companyDetailMap;
    }
    // 分批获取企业详情，因为 companyIds 可能会很大
    const companyIdChunks = chunk(companyIds, 200);
    const allCompanyListArrays = await Bluebird.map(
      companyIdChunks,
      async (idChunk) => {
        return await this.detailService.getDetailsAllInOne(idChunk);
      },
      { concurrency: 5 },
    );
    const companyList = flatten(allCompanyListArrays);
    companyList.forEach((x: any) => {
      const detail: CompanyBusinessInfo = new CompanyBusinessInfo();
      detail.companyId = x.KeyNo;
      detail.regno = x.No;
      detail.companyName = x.Name;
      detail.creditcode = x.CreditCode;
      detail.registrationStatus = x.ShortStatus;
      detail.statusCode = createStatusCodeReverseMap().get(x.ShortStatus) || '0';
      detail.scale = x.Scale;
      detail.address = x.Address;
      detail.province = x.ProvinceCode;
      detail.insuredcount = x.InsuredCount;
      detail.econkind = x.EconKind;
      detail.registcapi = x.RegistCapi;
      detail.registcapiAmt = x.RegistCapiAmt;
      detail.startDateCode = x?.StartDate ? formatDateOrTimestamp(x.StartDate) : null;
      detail.area = x?.Area && typeof x.Area === 'object' ? [x.Area.Province, x.Area.City, x.Area.County].filter((d) => d).join('/') || '-' : '-';
      detail.province = x.ProvinceCode;
      detail.opername = x.OperName || x.Oper?.Name;
      detail.originalNameWithDate = x.OriginalName;
      detail.standardCode = x.StandardCode;
      detail.operKeyNo = x.OperKeyNo || x.Oper?.KeyNo;
      detail.scope = x.Scope;
      detail.originalName = x.OriginalName?.map((item) => item.Name);
      detail.representative = this.getRepresentativeByMultipleOper(x?.MultipleOper);
      detail.recCap = x.RecCap;
      detail.Revenue = x.Revenue;
      detail.Tags = x.Tags;
      detail.industry1 = x?.IndustryV3?.IndustryCode;
      detail.industry2 = x?.IndustryV3?.SubIndustryCode;
      detail.industry3 = x?.IndustryV3?.MiddleCategoryCode;
      detail.industry4 = x?.IndustryV3?.SmallCategoryCode;
      const commonList = x.CommonList || [];
      detail.CommonList = commonList;
      const employeeCountList = commonList?.find((c) => c.Key == '75') ? JSON.parse(commonList.find((c) => c.Key == '75').Value) : [];
      detail.employeecount = employeeCountList.sort((a, b) => Number(b.Y) - Number(a.Y))[0]?.D || '-';
      const revenueList = commonList?.find((c) => c.Key == '74') ? JSON.parse(commonList.find((c) => c.Key == '74').Value) : [];
      detail.companyRevenue = revenueList.sort((a, b) => Number(b.Y) - Number(a.Y))[0]?.DV || '-';
      //疑似代记账号码
      detail.bookKeepingAgencyTels = commonList?.find((c) => c.Key == 40) ? JSON.parse(commonList.find((c) => c.Key == 40).Value).map((x) => x.k) : [];
      //疑似代记账邮箱
      detail.bookKeepingAgencyEmails = commonList?.find((c) => c.Key == 72) ? JSON.parse(commonList.find((c) => c.Key == 72).Value).map((x) => x.k) : [];
      //当前号码和历史号码的并集,并过滤掉疑似代记账号码
      detail.allTels = uniq([...(x.ContactInfo?.PhoneNumber ? [x.ContactInfo.PhoneNumber] : []), ...(x.HisTelList?.map((x) => x.Tel) || [])]).filter(
        (x) => !detail.bookKeepingAgencyTels?.includes(x),
      );
      detail.ContactInfo = x.ContactInfo;
      detail.HisTelList = x.HisTelList;
      detail.AddressList = x.AddressList;
      //过滤掉疑似代记账邮箱
      detail.Emails = x.Emails?.filter((x) => !detail.bookKeepingAgencyEmails?.includes(x));
      this.buildTelsInfo(detail);
      companyDetailMap.set(x.KeyNo, detail);
    });
    return companyDetailMap;
  }

  /**
   * 构建电话信息，为了适配前端和其他排查
   * @param company
   */
  private buildTelsInfo(company: CompanyBusinessInfo) {
    //疑似代记账号码
    const bookKeepingAgencyTels = company.bookKeepingAgencyTels;
    const tels: Tel[] = [];
    const ContactInfo = company.ContactInfo;
    if (ContactInfo?.PhoneNumber) {
      const contact: Tel = {
        t: ContactInfo.PhoneNumber, // 号码
        s: ContactInfo.TelSource || '', // 来源
        allsource: ContactInfo.TelSource, // 来源
        TelSourceDesc: ContactInfo.TelSourceDesc || '', // 来源描述
        TelArea: ContactInfo.TelArea || {}, // 地区
        TelTagsDesc: ContactInfo.TelTagsDesc || '', // 标签描述
        bat: bookKeepingAgencyTels?.length > 0 && bookKeepingAgencyTels.includes(ContactInfo.PhoneNumber) ? 1 : 0, // 是否疑似代记账 1-是 0-否,
      };
      tels.push(contact);
    }
    const HisTelList = company.HisTelList;
    if (HisTelList?.length) {
      HisTelList.forEach((x) => {
        if (!x?.Tel) {
          return;
        }
        const contact = {
          t: x.Tel, // 号码
          s: x.SourceFrom || '', // 来源
          allsource: x.SourceFrom || '', // 来源
          TelSourceDesc: x.TelSourceDesc || '', // 来源描述
          TelArea: x.TelArea || {}, // 地区
          TelTagsDesc: x.TelTagsDesc || '', // 标签描述
          bat: bookKeepingAgencyTels?.length > 0 && bookKeepingAgencyTels.includes(x.Tel) ? 1 : 0, // 是否疑似代记账 1-是 0-否,
        };
        tels.push(contact);
      });
    }
    company.tels = tels;
  }

  /**
   * 根据 companyId 批量获取企业信息
   * @param companyIds
   * @param selection
   * @returns
   */
  public async searchForCompanyList(companyIds: string[]): Promise<CompanyBusinessInfo[]> {
    if (!companyIds || companyIds.length === 0) {
      return [];
    }
    const companyMap = await this.getCompanyBusinessInfoMap(companyIds);
    return Array.from(companyMap.values());
  }

  /**
   * 分页获取企业列表
   * @param request
   * @returns
   */
  public async searchForCompanyListPage(request: CompanyListRequest) {
    const { companyIds, selection, pageIndex, pageSize } = request;
    const companyList = await this.searchForCompanyList(companyIds);
    const data = companyList?.slice((pageIndex - 1) * pageSize, pageIndex * pageSize) || [];
    //根据分页后的 data,要去获取批量调用this.getCompanyCreditSummary 并在 data 中补充获取到的TaxCredit 信息
    if (selection?.includes('TaxCredit') && data?.length > 0) {
      await Bluebird.map(
        data,
        async (item) => {
          try {
            const result = await this.getCompanyCreditSummary(item.companyId);
            item.TaxCredit = result?.Result?.TaxCredit;
          } catch (e) {
            this.logger.error(`getCompanyCreditSummary failed for companyId: ${item.companyId}`, e);
          }
        },
        { concurrency: 5 },
      );
    }
    return {
      Paging: {
        PageSize: pageSize,
        PageIndex: pageIndex,
        TotalRecords: companyList?.length || 0,
      },
      Result: data,
    };
  }

  /**
   * 获取企业详情<companyId,creditcode> Map
   * @param companyList
   */
  public async getCompanyCreditCodeMap(companyIds: string[]): Promise<Map<string, string>> {
    const companyCreditCodeMap: Map<string, string> = new Map<string, string>();
    if (!companyIds?.length) {
      return companyCreditCodeMap;
    }
    const companyBusinessInfoMap: Map<string, CompanyBusinessInfo> = await this.getCompanyBusinessInfoMap(companyIds);
    companyBusinessInfoMap.forEach((x) => companyCreditCodeMap.set(x.companyId, x.creditcode));
    return companyCreditCodeMap;
  }

  @Cacheable({ ttlSeconds: 60, cacheKey: (args: any[]) => `cache:getGlossaryInfo:${args[0]}` })
  public async getGlossaryInfo(glossaryId: string) {
    if (!glossaryId) {
      throw new BadRequestException(RoverExceptions.BadParams.Common);
    }
    const url = `${this.configService.proxyServer.userService}/extra/getGlossaryInfo`;
    try {
      return await this.httpUtils.postRequest(url, { glossaryId });
    } catch (e) {
      this.logger.error(`http Post ${url} err:`, e);
      return '';
    }
  }

  /**
   * kys 获取 company详情
   * @param companyId
   */
  @Cacheable({ ttlSeconds: 60, cacheKey: (args: any[]) => `cache:getCompanyDetails:${args[0]}` })
  public async getCompanyDetails(companyId: string): Promise<ESDetailResopne<KysCompanyResponseDetails>> {
    return this.companySearchClient.kysCompanyDetails(companyId);
  }

  /**
   * 获取公司的联系方式列表
   * @param companyId
   * @returns
   */
  @Cacheable({ ttlSeconds: 60, cacheKey: (args: any[]) => `cache:getContact:${args[0]}` })
  public async getContact(companyId: string): Promise<Contact> {
    return this.companySearchClient.kysCompanyContact(companyId);
  }

  /**
   * 获取公司信息
   * @param companyId 公司ID
   * @returns 公司信息
   */
  @Cacheable({ ttlSeconds: 60, cacheKey: (args: any[]) => `cache:getCompanyBusinessInfo:${args[0]}` })
  public async getCompanyBusinessInfo(companyId: string): Promise<CompanyBusinessInfo> {
    const companyBusinessInfoMap = await this.getCompanyBusinessInfoMap([companyId]);
    return companyBusinessInfoMap.get(companyId);
  }

  /**
   * 校验工商信息
   * @param names 公司完整企业名称name/统一社会信用代码creditcode 数组
   * @param includeFields
   * @param allowType 3:香港企业，4:机关单位，5：台湾企业
   * @param supportOverSeas
   * @returns
   */
  public async matchCompanyInfo(
    names: string[],
    includeFields = ['id', 'name', 'creditcode', 'regno'],
    allowType = ['0', '1', '11', '12'],
    supportOverSeas = false,
  ) {
    try {
      // 增加返回企业类型
      includeFields.push('t_type');
      const unsupported = [];
      const matchedNames: string[] = [];
      //调用接口匹配，并返回结果，找到names中没有匹配到的
      let unmatchedNames: string[] = names;
      // 匹配到的公司信息
      let matchedCompanyInfos: KysCompanyResponseDetails[] = [];
      if (names.length) {
        // const result = coyRes.Result;
        const result = await this.companySearchClient.kysExactMatch({
          searchKey: names,
          searchFields: ['name', 'creditcode', 'regno'],
          includeFields: includeFields,
          searchType: undefined,
        });
        if (result?.length) {
          matchedCompanyInfos = result;
          matchedCompanyInfos.forEach((e) => {
            if (names.includes(e['name_tra'])) {
              e.name = e['name_tra'];
            }
            if (allowType.includes(e.t_type)) {
              matchedNames.push(e.name);
            } else {
              unsupported.push(e.name);
            }
          });
          // 未命中完整企业名称name
          unmatchedNames = difference(names, matchedNames, unsupported);
          const matchedCreditCode = matchedCompanyInfos.map((e) => e.creditcode);
          // 未命中统一社会信用代码creditcode
          unmatchedNames = difference(unmatchedNames, matchedCreditCode);
          const matchedRegno = matchedCompanyInfos.map((e) => e.regno);
          // 未命中注册号regno
          unmatchedNames = difference(unmatchedNames, matchedRegno);
        }
        // 剩余未匹配的再进行曾用名的匹配
        if (unmatchedNames.length) {
          const resultOrginalName = await this.companySearchClient.kysExactMatch({
            searchKey: unmatchedNames,
            searchFields: ['originalname'],
            includeFields: includeFields,
            searchType: undefined,
          });
          if (resultOrginalName.length) {
            matchedCompanyInfos.push(...resultOrginalName);
            const matchedOriginalName = compact(flatten(resultOrginalName.map((e) => e.originalname)));
            // 未命中曾用名originalname
            unmatchedNames = difference(unmatchedNames, matchedOriginalName);
            matchedNames.push(...matchedOriginalName);
          }
        }
        if (unmatchedNames.length && supportOverSeas) {
          // 支持海外类型
          const overseas: KysCompanyResponseDetails[] = compact(
            await Bluebird.map(
              unmatchedNames,
              async (companyName) => {
                const query: SearchMultiSelectionRequest = Object.assign(new SearchCertificationRequest(), { searchKey: companyName });
                const data = await this.companySearchForQcc(query);
                let result;
                if (Array.isArray(data?.Result) && data?.Result?.length) {
                  result = find(data?.Result, (companyInfo) => {
                    const name = companyInfo.Name.replace(/<em>/g, '').replace(/<\/em>/g, '');
                    return companyName === name;
                  });
                }
                if (result) {
                  return {
                    id: result['KeyNo'],
                    name: companyName,
                    econkind: result['EconKind'],
                  };
                }
              },
              { concurrency: 1 },
            ),
          );
          if (overseas.length) {
            matchedCompanyInfos.push(...overseas);
            const overseasName = compact(flatten(overseas.map((e) => e.name)));
            // 未命中海外类型
            unmatchedNames = difference(unmatchedNames, overseasName);
            matchedNames.push(...overseasName);
          }
        }
      }
      if (supportOverSeas) {
        return {
          matchedCompanyInfos,
          unmatchedNames,
          matchedNames,
          unsupported,
        };
      }
      return {
        matchedCompanyInfos: matchedCompanyInfos.filter((e) => allowType.includes(e.t_type)),
        unmatchedNames,
        matchedNames,
        unsupported,
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * 校验工商信息V2（使用 C 端接口）
   * @param names
   * @param includeFields
   * @param allowType
   * @param supportOverSeas
   * @param onlyMatch 是否只匹配，如果为 true，则不进行标准代码的校验
   */
  public async matchCompanyInfoV2(names: string[], supportOverSeas = false, onlyMatch = false) {
    try {
      const text: string = names?.join(',') || '';
      if (!text.trim()) {
        return { matchedCompanyInfos: [], unmatchedNames: [], matchedNames: [], unsupported: [] };
      }
      const matchResponse = await this.getCompaniesWithFreeText({ text });
      const matchedNamesSet: Set<string> = new Set<string>();
      const matchedCompanyMap: Map<string, { name: string; id: string }> = new Map<
        string,
        {
          name: string;
          id: string;
        }
      >();
      const unsupported: string[] = [];
      if (matchResponse?.Status === 200 && Array.isArray(matchResponse?.Result)) {
        for (const item of matchResponse.Result) {
          const linkCompanies: Array<any> = item?.LinkCompany || [];
          for (const lc of linkCompanies) {
            const companyId: string = lc?.KeyNo;
            const actualName: string = lc?.ActualName;
            const oriName: string = lc?.OriName;
            const matchName: string = lc?.Name;

            if (companyId && actualName && !matchedCompanyMap.has(companyId)) {
              // 匹配 ActualName
              if (names.includes(actualName)) {
                matchedCompanyMap.set(companyId, { name: actualName, id: companyId });
                matchedNamesSet.add(actualName);
                break;
              }

              // 匹配 OriName
              if (names.includes(oriName)) {
                matchedCompanyMap.set(companyId, { name: actualName, id: companyId });
                matchedNamesSet.add(oriName);
                break;
              }
              //匹配 matchName
              if (names.includes(matchName)) {
                matchedCompanyMap.set(companyId, { name: actualName, id: companyId });
                matchedNamesSet.add(matchName);
                break;
              }
            }
          }
        }
      }
      const matchedCompanyInfos: KysCompanyResponseDetails[] = [];
      let matchedNames: string[] = Array.from(matchedNamesSet.values());
      //这里需要对 matchedCompanyMap 再次处理，需要去获取creditcode，standard_code数据,调用getCompanyBusinessInfoMapByDetailsAllInOne方法
      const companyBusinessInfoMap: Map<string, CompanyBusinessInfo> = await this.getCompanyBusinessInfoMap(Array.from(matchedCompanyMap.keys()));
      //这里需要遍历 companyBusinessInfoMap,如果CompanyBusinessInfo 的standardCode不满足条件则需要 push 到 unsupported 中
      Array.from(companyBusinessInfoMap.values()).forEach((companyInfo) => {
        if (companyInfo?.standardCode?.length && intersection(companyInfo.standardCode, ForbiddenStandardCode).length && !onlyMatch) {
          unsupported.push(companyInfo.companyName);
          //matchedNames 中要移除 companyInfo.companyName，考虑到可能是通过 creditcode 匹配的，还需要移除 companyInfo.creditcode
          matchedNames = difference(matchedNames, [companyInfo.companyName, companyInfo?.creditcode]);
        } else {
          matchedCompanyInfos.push(
            Object.assign(new KysCompanyResponseDetails(), {
              name: companyInfo.companyName,
              id: companyInfo.companyId,
              creditcode: companyInfo.creditcode,
              standardCode: companyInfo.standardCode,
              originalname: companyInfo.originalName,
            }),
          );
        }
      });

      let unmatchedNames: string[] = difference(names, [...matchedNames, ...(onlyMatch ? [] : unsupported)]);
      if (unmatchedNames.length && supportOverSeas) {
        // 支持海外类型
        const overseas: KysCompanyResponseDetails[] = compact(
          await Bluebird.map(
            unmatchedNames,
            async (companyName) => {
              const query: SearchMultiSelectionRequest = Object.assign(new SearchCertificationRequest(), { searchKey: companyName });
              const data = await this.companySearchForQcc(query);
              let result;
              if (Array.isArray(data?.Result) && data?.Result?.length) {
                result = find(data?.Result, (companyInfo) => {
                  const name = companyInfo.Name.replace(/<em>/g, '').replace(/<\/em>/g, '');
                  return companyName === name;
                });
              }
              if (result) {
                return {
                  id: result['KeyNo'],
                  name: companyName,
                  econkind: result['EconKind'],
                  creditcode: result?.['CreditCode'] ? result['CreditCode'] : '',
                  standardCode: result?.['StandardCode'] ? result['StandardCode'] : [],
                };
              }
            },
            { concurrency: 1 },
          ),
        );
        if (overseas.length) {
          matchedCompanyInfos.push(...overseas);
          const overseasName = compact(flatten(overseas.map((e) => e.name)));
          // 未命中海外类型
          unmatchedNames = difference(unmatchedNames, overseasName);
          matchedNames.push(...overseasName);
        }
      }
      return { matchedCompanyInfos, unmatchedNames, matchedNames, unsupported };
    } catch (error) {
      throw error;
    }
  }

  /**
   * 批量获取公司基础信息
   * @param names 可以是名称或者是统一社会信用编码
   * @param includeFields
   * @param allowType
   * @param supportOverSeas
   */
  public async matchCompanyListByNames(names: string[]): Promise<CompanySearchResponse[]> {
    const nameChunks = chunk(names, 50);
    const allResults = await Bluebird.map(
      nameChunks,
      async (nameChunk) => {
        return await this.getKeyNosByNames(new CompanySearchRequest(nameChunk, ['CreditCode', 'Name']));
      },
      { concurrency: 5 },
    );
    return flatten(allResults);
  }

  public async getVaildCompany(companyId: string, companyName: string): Promise<KysCompanyResponseDetails | null> {
    this.logger.info(`find inVaildCompany companyId: ${companyId} , companyName: ${companyName} `);
    // 查询同名且 isvalid = 1 的公司
    const reqData = Object.assign(new KysCompanySearchRequest(), {
      pageSize: 10,
      pageIndex: 1,
      includeInvalid: false,
      searchKey: companyName,
      includeFields: ['id', 'name', 'credit_score', 'standard_code', 'isvalid'],
    });
    try {
      const kysResponse = await this.companySearchForKys(reqData);
      const companys = kysResponse.Result.filter((c) => c.name == companyName);
      if (companys.length == 1) {
        // 如果只存在一条同名且有效的company直接替换keyno
        this.companyRepo.manager.update('CustomerEntity', { companyId }, { companyId: companys[0].id });
        this.companyRepo.manager.update('InnerBlacklistEntity', { companyId }, { companyId: companys[0].id });
        this.companyRepo.manager.update('MonitorGroupCompanyEntity', { companyId }, { companyId: companys[0].id });
        this.companyRepo.manager.update('MonitorGroupCompanyEntity', { relatedCompanyId: companyId }, { relatedCompanyId: companys[0].id });
        return companys[0];
      } else {
        this.logger.info(`find inVaildCompany  exist ${companys.length} 个  companyName: ${companyName} `);
        // 如果不存在或者有多个，直接标记第三方和内部黑名单状态无效待人工处理
        // this.companyRepo.manager.update('CustomerEntity', { companyId }, { status: BatchStatusEnums.invaild });
        // this.companyRepo.manager.update('InnerBlacklistEntity', { companyId }, { status: BatchStatusEnums.invaild });
        // this.companyRepo.manager.update('MonitorGroupCompanyEntity', { companyId }, { status: BatchStatusEnums.invaild });
        return null;
      }
    } catch (error) {
      this.logger.error(`getVaildCompany error: reqData: ${JSON.stringify(reqData)}`);
      this.logger.error(error);
      throw new HttpQueryException(error);
    }
  }

  /**
   *
   * @param day 查询近 day 天内的es有变更, 0-表示全部
   * @param updateBeforeDate 更新数据库updateDate 早于的数据
   */
  public async updateCompanyInfo(day: number, updateBeforeDate?: Date) {
    // const todayStart = updateBeforeDate || new Date();
    // todayStart.setHours(0, 0, 0, 0); // 设置为今天的0点0分0秒0毫秒

    let pageIndex = 0;
    const pageSize = 100;
    let fineshed = false;
    do {
      try {
        const [dbCompanyList, total] = await this.companyRepo.findAndCount({
          skip: pageIndex * pageSize,
          take: pageSize,
          order: { id: 'ASC' },
          // where: {},
        });
        if (!dbCompanyList.length) {
          fineshed = true;
          break;
        }
        const companyIds = dbCompanyList.map((c) => c.companyId);
        const filter = Object.assign(new SearchFilter(), {
          ids: companyIds,
        });
        if (day > 0) {
          //查询近两天内的变更
          filter.ud = Object.assign(new DateRangeRelative(), { number: day, unit: 'day', flag: 1, currently: 1 });
        }
        const reqData = Object.assign(new KysCompanySearchRequest(), {
          pageSize,
          pageIndex: 1,
          includeInvalid: true,
          filter: filter,
          includeFields: [
            'id',
            'credit_score',
            'standard_code',
            'isvalid',
            'listingstatuskw',
            'reccap',
            'reccapamount',
            'commonlist',
            'creditcode',
            'yysramount',
            'insuredcount',
          ],
        });
        const kysResponse = await this.companySearchForKys(reqData);
        const kysCompanyList = kysResponse.Result;
        //const companyList: CompanyEntity[] = await this.esService.companyFilter(companyIds);
        if (kysCompanyList?.length) {
          // const companyIdList = kysCompanyList.map((c) => c.id);
          const companyMap = groupBy(kysCompanyList, (x) => x.id);
          // const updateList = dbCompanyList.filter((c) => c.companyId in companyMap);
          const updateList = [];

          await Bluebird.map(
            dbCompanyList,
            async (c) => {
              let company = companyMap[c.companyId]?.[0];
              //
              if (company) {
                const commonList = company?.commonlist || [];

                if (company?.isvalid == '0') {
                  // 公司无效处理
                  const newC = await this.getVaildCompany(company.id, company.name);
                  if (newC) {
                    // 找到同名有效公司，替换当前companId,以及工商信息
                    company = newC;
                    c.companyId = newC.id;
                  }
                }
                let employeecount; //员工人数
                const employeecountInfo = commonList?.find((c) => c.k === '57');
                if (employeecountInfo) {
                  employeecount = JSON.parse(employeecountInfo?.v)?.D;
                }
                c.econType = '0';
                c.enterpriseType = '0';
                //统一从C端接口获取standardcodes
                let standardcodes: string[] = [];
                const dataC = await this.companySearchForQcc(
                  Object.assign(new SearchCertificationRequest(), { filter: { ids: [company.id] }, pageIndex: 1, pageSize: 1 }),
                );
                const dataResult = Array.isArray(dataC?.Result) && dataC?.Result?.find((companyInfo) => company.id === companyInfo['KeyNo']);
                if (dataResult) {
                  standardcodes = dataResult.CountInfo?.find((info) => info.k === '30')?.v?.split(',') || [];
                }

                if (standardcodes?.length) {
                  c.enterpriseType = intersection(Object.keys(EnterpriseType), standardcodes).join(',') || '0';
                  c.econType = intersection(Object.keys(EconType), standardcodes).join(',') || '0';
                }
                c.treasuryType = await this.processTreasuryType(c.companyId, standardcodes);
                c.statusCode = company?.statuscode || '0';
                c.econkind = company.econkindcode?.join(',');
                c.econkindDesc = company.econkind;
                c.province = company.province;
                c.city = company.areacode[0];
                c.district = company.areacode[1];
                c.industry1 = company.industry;
                c.industry2 = company.subind?.[0];
                c.industry3 = company.subind?.[1];
                c.industry4 = company.subind?.[2];
                c.registcapi = company.registcapi;
                c.registcapiAmount = company.registcapiamount;
                c.name = company.name;
                c.startDateCode = moment(company.startdatecode, 'YYYYMMDD').toDate();
                c.creditRate = (company as any).credit_score;
                // 是否已上市判断
                c.listStatus = this.getListStatus(company);
                c.reccap = company.reccap;
                c.reccapamount = company.reccapamount;
                //企业规模
                c.scale = commonList?.find((c) => c.k == '32')?.v;
                c.employeecount = employeecount;
                c.creditcode = company?.creditcode;
                const revenueEntry = commonList?.find((c) => c.k == '48')?.v;
                c.companyRevenue = revenueEntry ? JSON.parse(revenueEntry)?.find((f: any) => f.IsLast == 1 && f.ReportType == 4)?.Revenue ?? null : null;
                // 参保人数，如果是0则返回0，如果是空或者undefined则返回null，保存数据库类型区分，null跟0是两种情况
                const rawInsured = company?.insuredcount;
                c.insuredCount = !isNaN(Number(rawInsured)) ? Number(rawInsured) : null;
                updateList.push(c);
              }
            },
            { concurrency: 3 },
          );
          try {
            await this.companyRepo.save(updateList);
          } catch (error) {
            this.logger.error(`sync company info error for save! error: ${JSON.stringify(error.message)}`);
          }
        }

        pageIndex++;
        this.logger.info(`sync company info  total: ${total},  pageIndex: ${pageIndex} finsished: ${pageIndex * pageSize}`);
        if (pageIndex * pageSize >= total) {
          fineshed = true;
        }
      } catch (error) {
        this.logger.error(`sync company info error: ${JSON.stringify(error.message)}`);
        this.logger.error(error);
        fineshed = true;
      }
    } while (!fineshed);
  }

  /**
   * 创建公司详情信息
   * @param companyId 公司唯一标识
   * @param companyName 公司名称
   * @param update 是否更新
   * @returns
   */
  public async createCompanyInfo(companyId: string, companyName: string, update = false): Promise<CompanyEntity> {
    try {
      const company = await this.companyRepo.findOne({ where: { companyId } });
      if (company && !update) {
        return company;
      }
      const companyEntity = new CompanyEntity();

      // 统一从C端接口获取standardcodes
      let standardcodes: string[] = [];
      // 并发请求获取企业详情和搜索公司补充信息
      const [companyMap, data] = await Bluebird.all([
        this.getCompanyBusinessInfoMap([companyId]),
        this.companySearchForQcc(Object.assign(new SearchCertificationRequest(), { filter: { ids: [companyId] }, pageSize: 1, pageIndex: 1 })),
      ]);
      const dataC: CompanyBusinessInfo = companyMap.get(companyId);
      //地区 area，企查分等信息需要通过companySearchForQcc接口获取
      const result = find(data?.Result, (companyInfo) => companyId === companyInfo['KeyNo']);
      // 企业性质和机构类型（使用C端获取的standardcodes）
      let econType = '0';
      let enterpriseType = '0';

      if (dataC) {
        // commonlist key=50 注册资本金额
        const registcapiAmount = dataC.registcapiAmt;
        standardcodes = dataC.standardCode || [];
        if (standardcodes?.length) {
          enterpriseType = intersection(Object.keys(EnterpriseType), standardcodes).join(',') || '0';
          econType = intersection(Object.keys(EconType), standardcodes).join(',') || '0';
        }
        // commonlist key=52 信用评分
        const creditRate = result?.CountInfo?.find((info) => info.k == '52')?.v;
        const creditRateValue = creditRate ? JSON.parse(creditRate)?.s : undefined;
        // 员工人数
        const employeecount = result?.CountInfo?.find((c) => c.k == '57')?.v;
        const employeecountValue = employeecount ? JSON.parse(employeecount)?.D : undefined;
        //统一社会信用代码
        const creditcode = dataC.creditcode;
        //实缴资本,dataC.recCap形如554434.375332万元，需要转换为数字
        const reccapamount = dataC.recCap ? parseFloat(dataC.recCap.replace('万元', '')) : null;
        //企业规模
        const scale = dataC.scale;
        //营业收入,dataC.Revenue?.Data可能是 '755.55亿元'、'755.55万元'、'755.55元'，也可能是 null， 这里 companyRevenue 是数字，且默认单位是万元，需要转换
        const companyRevenue = extractCompanyRevenue(dataC.Revenue?.Data);
        //参保人数
        const insuredCount = dataC.insuredcount;

        Object.assign(companyEntity, {
          companyId: companyId,
          name: companyName,
          econkind: '',
          econkindDesc: dataC.econkind,
          province: dataC.province,
          city: result?.Area?.CityCode,
          district: result?.Area?.CountyCode,
          industry1: dataC.industry1,
          industry2: dataC.industry2,
          industry3: dataC.industry3,
          industry4: dataC.industry4,
          registcapi: dataC.registcapi,
          registcapiAmount,
          startDateCode: dataC?.startDateCode ? parseStringToDate(dataC.startDateCode) : null,
          shortStatus: dataC?.registrationStatus,
          statusCode: dataC?.statusCode,
          creditRate: creditRateValue,
          reccapamount,
          reccap: dataC.recCap,
          scale,
          creditcode,
          companyRevenue,
          insuredCount,
          employeecount: employeecountValue,
          listStatus: this.isStockCompany(dataC),
        });
      }
      if (standardcodes?.length) {
        enterpriseType = intersection(Object.keys(EnterpriseType), standardcodes).join(',') || '0';
        econType = intersection(Object.keys(EconType), standardcodes).join(',') || '0';
      }
      const treasuryType = await this.processTreasuryType(companyId, standardcodes);

      Object.assign(companyEntity, {
        econType,
        enterpriseType,
        treasuryType,
      });
      if (company && update) {
        await this.companyRepo.update(company.id, companyEntity);
      } else {
        const company = await this.companyRepo.findOne({ where: { companyId } });
        if (company) {
          return company;
        }
        return this.companyRepo.save(companyEntity);
      }
      return companyEntity;
    } catch (e) {
      this.logger.error(`保存company详情信息失败:${JSON.stringify(e)}`);
      throw e;
    }
  }

  private async processTreasuryType(companyId: string, standardcodes: any[]) {
    //处理司库类型，中央部委查询固定清单
    const hasCentralGov =
      (await this.compBusiListRepo.count({
        where: {
          compKeyno: companyId,
          dataStatus: 1,
          listType: In(['347', '348', '349', '350', '351', '352']),
        },
      })) > 0;
    if (hasCentralGov) {
      return 'K03';
    } else {
      // 非K03时处理映射表分类
      if (standardcodes?.length) {
        const treasuryCodes = Object.values(TreasuryType)
          .filter((type) => type.sourceCodes.some((code) => standardcodes.includes(code)))
          .map((t) => t.code);
        return treasuryCodes.join(',') || 'K06';
      }
      return 'K06';
    }
  }

  public async syncTreasuryTypeForCentralGov() {
    let pageIndex = 0;
    const pageSize = 100;
    let totalUpdated = 0;
    let finished = false;

    do {
      try {
        // 查询中央部委清单表（只查未标记为K03的）
        const centralGovList = await this.compBusiListRepo.find({
          where: { compKeyno: Not(IsNull()), dataStatus: 1, listType: In(['347', '348', '349', '350', '351', '352']) },
          skip: pageIndex * pageSize,
          take: pageSize,
          order: { id: 'ASC' },
        });

        if (!centralGovList.length) break;

        const companyIds = centralGovList.map((x) => x.compKeyno);

        // 批量更新（仅更新非K03的记录）
        const result = await this.companyRepo
          .createQueryBuilder()
          .update(CompanyEntity)
          .set({
            treasuryType: 'K03',
          })
          .where('companyId IN (:...ids)', { ids: companyIds })
          .andWhere("treasuryType != 'K03'")
          .execute();

        totalUpdated += result.affected;
        this.logger.info(`[司库类型同步] 已处理第 ${pageIndex + 1} 批，本批更新 ${result.affected} 条`);
        pageIndex++;
      } catch (error) {
        finished = true;
        this.logger.error(`[司库类型同步] 第 ${pageIndex + 1} 批处理失败${JSON.stringify(error.message)}`);
        // 失败后继续尝试下一页
        pageIndex++;
      }
    } while (!finished);

    this.logger.info(`[司库类型同步] 完成！共更新 ${totalUpdated} 家企业`);
  }

  /**
   * 查询 Company 表中 updateDate 为近 n 天的数据
   * @param days 天数，默认为 7
   * @returns Map<string, string> companyId 为 key，name 为 value
   */
  public async getCompaniesByUpdateDate(days = 7): Promise<Map<string, string>> {
    const startDate = moment().subtract(days, 'days').startOf('day').toDate();
    const companies = await this.companyRepo.find({
      where: {
        updateDate: MoreThanOrEqual(startDate),
      },
      select: ['companyId', 'name'],
    });
    const companyMap = new Map<string, string>();
    companies.forEach((company) => {
      if (company.companyId && company.name) {
        companyMap.set(company.companyId, company.name);
      }
    });
    return companyMap;
  }

  /**
   * 批量创建 company数据
   */
  public async createCompanyInfoBatch(companyMap: Map<string, string>): Promise<CompanyEntity[]> {
    const res = await Bluebird.map(
      companyMap.keys(),
      async (companyId) => {
        return this.createCompanyInfo(companyId, companyMap.get(companyId), true);
      },
      { concurrency: 5 },
    );

    return flatten(res);
  }

  public async searchAdvance(searchKey: string, pageIndex?: string, pageSize?: string, searchType?: string, searchIndex?: string, dataSource?: string) {
    const searchData = { pageIndex, pageSize, searchKey, searchType, searchIndex, dataSource };
    try {
      return this.httpUtils.postRequest(this.configService.dataServer.searchAdvance, searchData);
    } catch (e) {
      this.logger.error(`http GET ${this.configService.dataServer.searchAdvance} err:`, e);
      return '';
    }
  }

  /**
   * 相同联系方式企业列表
   * @param contactKey
   * @param pageIndex
   * @param pageSize
   * @param keyNo
   * @param dataSource
   * @returns
   */
  public async getSameContacts(contactKey: string, pageIndex?: string, pageSize?: string, keyNo?: string, dataSource?: string) {
    // return this.searchAdvance(contactKey, pageIndex, pageSize, '0,1,3,4,5,10,11,12,20', 'flag', dataSource);
    // // todo 等待 C端上线后切换
    try {
      const url = `${this.configService.proxyServer.dataService}/api/ECILocal/GetSameContacts`;
      return this.httpUtils.postRequest(url, { pageIndex, pageSize, contactKey, keyNo, dataSource });
    } catch (e) {
      this.logger.error(`http GET ${this.configService.dataServer.getSameContacts} err:`, e);
      return '';
    }
  }

  public async getCompaniesWithFreeText(body: CompaniesWithFreeTextRequest) {
    try {
      const data = await this.detailService.getCompaniesWithFreeText(body.text);
      if (data?.Status == 200) {
        const companyIds: string[] = [];
        data.Result.forEach((item) => {
          const keyNos = item?.LinkCompany?.length > 0 ? item.LinkCompany.map((m) => m.KeyNo) : item?.LinkedCompany ? [item.LinkedCompany.LinkKeyNo] : [];
          companyIds.push(...keyNos.filter((keyNo) => keyNo));
        });
        //补充 company 中的 standardCode
        const companyBusinessInfoMap: Map<string, CompanyBusinessInfo> = await this.getCompanyBusinessInfoMap(companyIds);
        data.Result.forEach((item) => {
          if (item?.LinkCompany?.length) {
            item.LinkCompany.forEach((m) => {
              m.StandardCode = companyBusinessInfoMap.get(m.KeyNo)?.standardCode;
              m.Province = companyBusinessInfoMap.get(m.KeyNo)?.province;
            });
          } else if (item?.LinkedCompany) {
            const companyBusinessInfo = companyBusinessInfoMap.get(item.LinkedCompany.LinkKeyNo);
            if (companyBusinessInfo) {
              item.LinkedCompany.StandardCode = companyBusinessInfo.standardCode;
              item.LinkedCompany.Province = companyBusinessInfo.province;
            }
          }
        });
        return { Status: data.Status, Paging: data.Paging, Result: data.Result };
      }
      return data;
    } catch (e) {
      this.logger.error(`GetCompaniesWithFreeText err:`, e);
      throw new BadRequestException(RoverExceptions.Common.RequestFailed);
    }
  }

  /**
   * 通过QCC后端数据接口，根据名称获取对应的KeyNo
   * @param names 名称列表
   * @returns KeyNo列表
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getKeyNosByNames(param: CompanySearchRequest): Promise<CompanySearchResponse[]> {
    const { names, selection, isExactlyMatch, isRepeated, isCM } = param;
    const namesStr = names.join('。');
    const sourcePath = `${this.configService.proxyServer.dataService}/api/QccSearch/List/KeyNosByNames`;
    const reqData = {
      names: namesStr,
      isExactlyMatch,
      isRepeated,
      selection,
      isCM,
    };
    const res = await this.httpUtils.postRequest(sourcePath, reqData);
    return res?.Result;
  }

  /**
   * 模糊匹配
   * @param body
   */
  public async getLinkCompaniesWithFreeText(body: CompaniesWithFreeTextRequest) {
    try {
      const data = await this.detailService.getLinkCompaniesWithFreeText(body.text);
      if (data?.Status == 200) {
        return { Status: data.Status, Paging: data.Paging, Result: data.Result };
      }
      return data;
    } catch (e) {
      this.logger.error(`getLinkCompaniesWithFreeText err:`, e);
      throw new BadRequestException(RoverExceptions.Common.RequestFailed);
    }
  }

  /**
   * 获取信用评分和信用评分趋势
   * @param keyNo
   * @returns
   */
  public async getCreditInfo(keyNo: string) {
    const [creditRate, creditRateTrend] = await Bluebird.all([this.getCreditRate(keyNo), this.getCreditRateTrend(keyNo)]);
    return {
      creditRate,
      creditRateTrend,
    };
  }

  /**
   * 查询信用评分详情
   * @param keyNo
   */
  public async getCreditRate(keyNo: string): Promise<CreditRateResult | undefined> {
    try {
      const data = await this.httpUtils.postRequest(this.configService.dataServer.getCreditRate, {
        keyNo,
      });
      if (data?.Status == 200) {
        return data.Result;
      } else {
        this.logger.info(`http POST ${this.configService.dataServer.getCreditRate} info:`, data);
      }
    } catch (e) {
      this.logger.error(`http POST ${this.configService.dataServer.getCreditRate} err:`, e);
      throw new HttpQueryException(e.Message, e.Status);
    }
  }

  /**
   * 获取企查分趋势
   * @param keyNo
   * @param date {string} 开始日期 YYYYMMDD
   */
  @Cacheable({ ttlSeconds: 600, cacheKey: (args: any[]) => `cache:getCreditRateTrend:${args[0]}:${args[1]}` })
  public async getCreditRateTrend(keyNo: string, date?: string): Promise<RateTrendInfo[] | undefined> {
    try {
      const data = await this.httpUtils.postRequest(this.configService.dataServer.getCreditRateTrend, {
        keyNo,
        date: date || moment().subtract(90, 'days').format('YYYYMMDD'), // 开始日期
        isBoss: false, // 是否为Boss平台请求
      });
      if (data?.Status == 200 || data?.Status == 201) {
        return data?.Result?.TreadList ?? [];
      } else {
        this.logger.error(`http POST ${this.configService.dataServer.getCreditRateTrend} err:`, data);
        throw new HttpQueryException(data.Message, data.Status);
      }
    } catch (e) {
      this.logger.error(`http POST ${this.configService.dataServer.getCreditRateTrend} err:`, e);
      throw new HttpQueryException(e.Message, e.Status);
    }
  }

  /**
   * 根据企业名称获取企业对应客户和供应商信息
   * @param body
   */
  public async getSupplierCustomerWithFreeText(body: SupplierCustomerWithFreeTextRequest) {
    const { pageSize, pageIndex, dataType } = body;
    const companiesWithFreeText = await this.getCompaniesWithFreeText(body);
    if (companiesWithFreeText.Status == 200) {
      const keyNo = companiesWithFreeText?.Result[0]?.LinkCompany[0]?.KeyNo;
      if (!keyNo) {
        return { Paging: { PageIndex: pageIndex, PageSize: pageSize, TotalRecords: 0 }, Result: [] };
      }
      const dataResult = await this.detailService.getSupplierCustomer(keyNo, pageIndex, pageSize, dataType);
      if (dataResult?.Status > 201) {
        this.logger.info(`getSupplierCustomer 获取供应商客户信息失败,id:${keyNo},result:${JSON.stringify(dataResult)}`);
        throw new BadRequestException(RoverExceptions.Common.RequestFailed);
      }
      return { ...pick(dataResult, ['Paging', 'Result']) };
    }
  }

  private getListStatus(company: any) {
    const listingStatusKw = company.listingstatuskw;
    if (listingStatusKw?.length > 0 && listingStatusKw.includes('F_4')) {
      // 已上市
      return 1;
    }
    // 未上市
    return 2;
  }

  /**
   * 判断是否为上市企业，只要Type 满足 1, 2, 6, 7, 11, 27, 30, 31, 35, 121, 122, 171, 502, 602 中任意一个，就算是上市 return 1,否则return 2
   * @param company CompanyBusinessInfo
   * @returns
   */
  private isStockCompany(company: CompanyBusinessInfo) {
    // Type 1-新三板
    // 2-A股上市
    // 6-港股上市
    // 7-中概股
    // 11-美股
    // 27-北交所申报
    // 30-港股VIE（JSON.parse(DataExtend2).KN表示上市主体的 keyNo）
    // 31-美股VIE（JSON.parse(DataExtend2).KN表示上市主体的 keyNo）
    // 35-主板申报（注册制）
    // 121-港股 IPO
    // 122-上市阶段
    // 171-发债企业
    // 502-科创板已申报
    // 602-创业板已申报
    const tags: CompanyTag[] = company?.Tags;
    if (!tags || tags?.length === 0) {
      return 2;
    }
    const stockCompanyTypes = [1, 2, 6, 7, 11, 27, 30, 31, 35, 121, 122, 171, 502, 602];
    const stockCompanyType = tags?.find((element) => stockCompanyTypes.includes(element?.Type));
    if (stockCompanyType) {
      // 已上市
      return 1;
    }
    // 未上市
    return 2;
  }

  // /**
  //  * 查询企业规模、利润、净利润信息
  //  * @param companyIds
  //  * @private
  //  */
  // private async getCompanyFinanceMap(companyIds: string[]) {
  //   const companyFinanceList = await Bluebird.all(
  //     companyIds.map(async (companyId) => {
  //       return await this.doGetCompanyFinance(companyId);
  //     }),
  //     { concurrency: 5 },
  //   );
  //   return groupBy(companyFinanceList, (x) => x.companyId);
  // }

  /**
   * 先查 C 端接口，没有再查 ES
   * @param companyId
   * @param data
   * @private
   */
  public async doGetCompanyFinance(companyId: string, data?: any) {
    const companyFinance = await this.detailService.getCompanyFinance(companyId);
    if (!data) {
      data = await this.httpUtils.getRequest(this.configService.dataServer.companyDetail, {
        keyNo: companyId,
      });
    }
    const companyFinanceModel = new CompanyFinanceInfoResponse();
    companyFinanceModel.companyId = companyId;

    if (data?.Status === 200 && data?.Result) {
      const topOperIncome = data?.Result?.FinancingInfo?.find((f) => f.FinanceType === 1)?.Top;
      if (topOperIncome) {
        companyFinanceModel.operIncTotal = topOperIncome?.Data;
        companyFinanceModel.operIncTotalSource = topOperIncome?.Year + '年' + topOperIncome?.TypeDesc;
      }
    }

    let hitFlag: boolean;
    // 尝试获取近一年数据
    hitFlag = this.tryGetFinanceData(companyFinance, companyFinanceModel, 1);
    if (!hitFlag) {
      // 尝试获取近三年数据
      hitFlag = this.tryGetFinanceData(companyFinance, companyFinanceModel, 3);
    }
    if (!hitFlag) {
      // 使用 ES 数据
      await this.getCompanyFinanceFromEs(companyFinanceModel);
    }

    return companyFinanceModel;
  }

  private tryGetFinanceData(companyFinance: any, companyFinanceModel: CompanyFinanceInfoResponse, yearRange: number) {
    let hitFlag = false;
    if (companyFinance?.Status === 200 && companyFinance?.Result) {
      const startDate = moment().add(`-${yearRange}`, 'years').startOf('year').unix();
      const result = companyFinance.Result;
      const assetsTotal = result.ReportFields.find((f) => f.AccountName === 'assets_total'); // 资产总计
      const operIncTotal = result.ReportFields.find((f) => f.AccountName === 'oper_inc_total'); // 营业收入总计
      const netProfit = result.ReportFields.find((f) => f.AccountName === 'net_profit'); // 净利润

      if (assetsTotal && operIncTotal && netProfit) {
        const assetsTotalField = assetsTotal.FieldList?.find((f) => f?.ReportDate && f.ReportDate >= startDate);
        if (assetsTotalField) {
          companyFinanceModel.assetsTotal = assetsTotalField.ShowValue;
          companyFinanceModel.assetsTotalSource = assetsTotalField.ReportPeriodName;
          hitFlag = true;
        }
        const operIncTotalField = operIncTotal.FieldList?.find((f) => f?.ReportDate && f.ReportDate >= startDate);
        if (operIncTotalField && !companyFinanceModel.operIncTotal) {
          companyFinanceModel.operIncTotal = operIncTotalField.ShowValue;
          companyFinanceModel.operIncTotalSource = operIncTotalField.ReportPeriodName;
          hitFlag = true;
        }
        const netProfitField = netProfit.FieldList?.find((f) => f?.ReportDate && f.ReportDate >= startDate);
        if (netProfitField) {
          companyFinanceModel.netProfit = netProfitField.ShowValue;
          companyFinanceModel.netProfitSource = netProfitField.ReportPeriodName;
          hitFlag = true;
        }
      }
    }
    return hitFlag;
  }

  public async getCompanyTelecomLicenseDetail(licenseId: string) {
    return await this.detailService.getCompanyTelecomLicenseDetail(licenseId);
  }

  /**
   *
   * @param companyFinanceModel
   * @private
   */
  private async getCompanyFinanceFromEs(companyFinanceModel: CompanyFinanceInfoResponse) {
    const companyId = companyFinanceModel.companyId;
    const { data } = await this.getCompanyFinanceById(companyId);
    if (data.length > 0) {
      //           "net_profit_hierarchy" : "P23",
      //           "name" : "苏尼特右旗农村信用合作联社桑宝力嘎分社",
      //           "id" : "b1df8de8b7c54bdc691e52135dd42fb4",
      //           "op_revenue_hierarchy" : "P23",
      //           "tot_assets_hierarchy" : "P24"
      const netProfit = data[0].net_profit_hierarchy;
      const opRevenue = data[0].op_revenue_hierarchy;
      const totAssets = data[0].tot_assets_hierarchy;
      if (!companyFinanceModel.assetsTotal) {
        companyFinanceModel.assetsTotal = this.getAssetMap().get(totAssets) || '-';
      }
      if (!companyFinanceModel.operIncTotal) {
        companyFinanceModel.operIncTotal = this.getAssetMap().get(opRevenue) || '-';
      }
      if (!companyFinanceModel.netProfit) {
        companyFinanceModel.netProfit = this.getAssetMap().get(netProfit) || '-';
      }
    }
    return companyFinanceModel;
  }

  protected searchEs(body, preference: string) {
    const searchRequest: RequestParams.Search = {
      index: this.creditFinanceIndexName,
      type: '_doc',
      body,
      preference,
    };
    return this.creditFinanceClient.search(searchRequest);
  }

  getAssetMap = () => {
    const assetMap = new Map();
    // assetMap.set('N1', '-');
    // assetMap.set('N2', '小于 -100 万元');
    // assetMap.set('N3', '-100--50 万元');
    // assetMap.set('N4', '-50--40 万元');
    // assetMap.set('N5', '-40--30 万元');
    // assetMap.set('N6', '-30--20 万元');
    // assetMap.set('N7', '-20--10 万元');
    // assetMap.set('P0', 0);
    // assetMap.set('P1', '0-1 万元');
    // assetMap.set('P2', '1-5 万元');
    // assetMap.set('P3', '5-10 万元');
    // assetMap.set('P4', '10-20 万元');
    // assetMap.set('P5', '20-30 万元');
    // assetMap.set('P6', '30-50 万元');
    // assetMap.set('P7', '50-100 万元');
    // assetMap.set('P8', '100-150 万元');
    // assetMap.set('P9', '150-200 万元');
    assetMap.set('N1', '300 万元以内');
    assetMap.set('N2', '300 万元以内');
    assetMap.set('N3', '300 万元以内');
    assetMap.set('N4', '300 万元以内');
    assetMap.set('N5', '300 万元以内');
    assetMap.set('N6', '300 万元以内');
    assetMap.set('N7', '300 万元以内');
    assetMap.set('P0', '300 万元以内');
    assetMap.set('P1', '300 万元以内');
    assetMap.set('P2', '300 万元以内');
    assetMap.set('P3', '300 万元以内');
    assetMap.set('P4', '300 万元以内');
    assetMap.set('P5', '300 万元以内');
    assetMap.set('P6', '300 万元以内');
    assetMap.set('P7', '300 万元以内');
    assetMap.set('P8', '300 万元以内');
    assetMap.set('P9', '300 万元以内');
    assetMap.set('P10', '300 万元以内');
    assetMap.set('P11', '300-500 万元');
    assetMap.set('P12', '500-1000 万元');
    assetMap.set('P13', '1000-2000 万元');
    assetMap.set('P14', '2000-5000 万元');
    assetMap.set('P15-1', '5000-8000 万元');
    assetMap.set('P15-2', '8000-10000 万元');
    assetMap.set('P16', '1-2 亿元');
    assetMap.set('P17', '2-5 亿元');
    assetMap.set('P18-1', '5-8 亿元');
    assetMap.set('P18-2', '8-10 亿元');
    assetMap.set('P19-1', '10-12 亿元');
    assetMap.set('P19-2', '12-50 亿元');
    assetMap.set('P20', '50-100 亿元');
    assetMap.set('P21', '100-200 亿元');
    assetMap.set('P22', '200-500 亿元');
    assetMap.set('P23', '500-1000 亿元');
    assetMap.set('P24', '1000 亿元以上');
    return assetMap;
  };

  private async getCompanyFinanceById(companyId: string) {
    const body = {
      query: {
        bool: {
          must: [
            {
              term: {
                id: {
                  value: companyId,
                },
              },
            },
          ],
        },
      },
      _source: {
        includes: ['tot_assets_hierarchy', 'net_profit_hierarchy', 'op_revenue_hierarchy', 'id', 'name'],
      },
    };
    const response = await this.searchEs(body, companyId);
    return {
      total: response?.body?.hits?.total?.value || 0,
      data: response?.body?.hits?.hits?.map((d) => d._source) || [],
    };
  }

  /**
   * 委派代表信息
   * @param commonList
   * @private
   */
  private getRepresentative(commonList: CommonListItem[]) {
    const operModel = commonList?.find((c) => c.k == '10') ? JSON.parse(commonList.find((c) => c.k == '10').v) : null;
    if (operModel && operModel.OperType === 2 && operModel.OperList?.[0].l) {
      return operModel.OperList[0].l;
    }
    return [];
  }

  private getRepresentativeByMultipleOper(MultipleOper: any) {
    if (MultipleOper?.OperType === 2) {
      return MultipleOper?.OperList?.length > 0 ? MultipleOper?.OperList?.map((x) => x.AssignorList).flat() : [];
    }
    return [];
  }
}
