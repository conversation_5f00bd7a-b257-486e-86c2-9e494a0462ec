# 模块文档生成标准 Prompt

## 🎯 使用说明

基于指定模块路径的代码分析，**自动识别目录类型并生成相应文档**，**不输出其他内容**。

**⚠️ 核心原则 1：基于实际代码分析，禁止臆想功能**
**⚠️ 核心原则 2：严格按照模板要求来生成文档内容，绝不添加模板外内容**
**⚠️ 核心原则 3：模块简单时优先精简或合并内容，避免强行凑内容**

### 📂 目录类型自动识别

在开始生成文档前，需要先分析目录结构，自动识别目录类型：

#### 🏢 业务域分组目录 (Domain Group)

**识别特征**：

- 只包含子目录，没有 `.ts` 或 `.js` 源码文件（README.md 除外）
- 子目录通常是具体的业务模块
- 目录名称通常表示业务领域（如 risk-assessment、company-management）

**生成策略**：只生成 **README.md**（业务域概览文档）

#### 🛠️ 业务模块目录 (Business Module)

**识别特征**：

- 包含 `.controller.ts`、`.service.ts`、`.module.ts` 等源码文件
- 可能包含 `docs/` 子目录
- 具有完整的 NestJS 模块结构
- 文件必须是在 `src/modules/` 目录下才会是业务模块，否则就是业务域分组目录，类似 `src/domains`,`src/core` 等，它们都是六层架构的其中一层，但不是业务模块

**生成策略**：

1. **必须生成**: **README.md** - 模块概览文档
2. **视复杂度决定**: **docs/** 下的 3 个详细文档（module-architecture.md、workflow.md、development-guide.md）

**⚠️ 模块复杂度判断原则**：

- **简单模块**（只有基础 CRUD 操作、单一数据源、少于 5 个 API 接口）：**仅生成 README.md**，将所有信息精简总结在概览中
- **中等复杂模块**（有一定业务逻辑、多个数据源、5-15 个 API 接口）：**生成 README.md + 1-2 个详细文档**
- **复杂模块**（复杂业务流程、多数据源整合、15 个以上 API 接口、有复杂架构设计）：**生成完整的 4 个文档**

## 📋 输出约束

根据目录类型识别结果，输出相应的文档文件：

### 🏢 业务域分组目录输出

**仅输出 1 个文件：**

- **README.md** - 业务域概览文档

### 🛠️ 业务模块目录输出

**根据模块复杂度输出相应文件：**

#### 简单模块输出（推荐）

- **README.md** - 模块概览文档（包含所有必要信息）

#### 中等复杂模块输出

- **README.md** - 模块概览文档
- **docs/module-architecture.md** - 架构设计文档（仅核心架构）
- 或 **docs/workflow.md** - 业务流程文档（仅核心流程）

#### 复杂模块输出

- **README.md** - 模块概览文档
- **docs/module-architecture.md** - 架构设计文档
- **docs/workflow.md** - 业务流程文档
- **docs/development-guide.md** - 开发指南文档

**🔗 特别注意：完成文档生成后，必须在 README.md 中添加完整的"📚 文档导航"部分，包含上级导航、模块文档、同级子模块和相关业务域的链接。**

## 🚫 严格禁止的内容

### ❌ 绝对不能包含的内容（违反将重新生成）

#### 在 module-architecture.md 中禁止：

- **安全与权限设计**（除非代码中有明确的权限控制逻辑）
- **监控与可观测性**（通用监控内容）
- **扩展性设计**（通用扩展性原则）
- **版本兼容性**（通用版本控制）
- **部署相关**（环境配置、容器化等）
- **通用性能优化建议**

#### 在 development-guide.md 中禁止：

- **环境配置**（Node.js 版本、npm 配置等）
- **通用编码规范**（变量命名、文件结构等）
- **通用开发流程**（如何创建文件、如何编写代码等）
- **测试环境搭建**
- **IDE 配置建议**
- **Git 使用规范**

#### 在 workflow.md 中禁止：

- **通用业务流程**（非该模块特有的流程）
- **系统级别的流程**（用户登录、权限验证等通用流程）

### ✅ 只能包含的内容类型

#### module-architecture.md 只能包含：

- **核心架构图**（基于实际 Controller、Service 结构）
- **数据模型关系**（基于实际 Entity/DTO）
- **关键设计决策**（基于实际代码中的特殊设计）
- **核心数据流转**（基于实际方法调用链）

#### workflow.md 只能包含：

- **该模块特有的业务流程**（基于实际 Service 方法）
- **模块内的关键业务规则**（基于实际业务逻辑）
- **模块异常处理**（基于实际异常处理代码）

#### development-guide.md 只能包含：

- **该模块特有的开发注意点**（基于实际代码分析）
- **模块内的关键开发节点**（基于实际业务流程）
- **模块特有的开发陷阱**（基于实际代码中的复杂逻辑）

## 📝 生成步骤说明

### 🏢 业务域分组目录生成步骤

**步骤 1：生成业务域 README.md**

**文件位置**: `[业务域根目录]/README.md`
**目标受众**: 需要了解业务域整体功能的开发者和产品经理
**核心目的**: 理解业务域的功能范围、子模块划分和整体架构

### 🛠️ 业务模块目录生成步骤

**步骤 1：生成模块 README.md**

**文件位置**: `[模块根目录]/README.md`
**目标受众**: 快速了解模块的新手开发者
**核心目的**: 5 分钟内掌握模块功能和基本结构

**步骤 2：生成 module-architecture.md**

**文件位置**: `[模块根目录]/docs/module-architecture.md`
**目标受众**: 需要理解技术实现的开发者
**核心目的**: 理解关键架构设计和技术要点

**步骤 3：生成 workflow.md**

**文件位置**: `[模块根目录]/docs/workflow.md`  
**目标受众**: 需要理解业务流程的开发者
**核心目的**: 掌握核心业务流程和关键节点

**步骤 4：生成 development-guide.md**

**文件位置**: `[模块根目录]/docs/development-guide.md`
**目标受众**: 需要在此模块进行开发的开发者
**核心目的**: 快速上手开发，了解关键开发注意点

---

## 📄 文件内容格式约束

### 🏢 业务域 README.md 格式约束

```markdown
# [业务域名称]业务域

## 📝 概述

[2-3 段文字描述业务域的整体功能、价值和在系统中的定位]

## 🎯 核心功能

[基于子模块分析的 4-6 个核心功能领域]

## 📁 子模块结构

[基于实际目录的子模块结构图，展示主要子模块]

## 🔗 子模块介绍

### 📊 [子模块 1 名称](./submodule1/README.md)

**核心职责**: [简述主要功能]

- [功能点 1]: [简要说明]
- [功能点 2]: [简要说明]
- [功能点 3]: [简要说明]

### 📡 [子模块 2 名称](./submodule2/README.md)

**核心职责**: [简述主要功能]

- [功能点 1]: [简要说明]
- [功能点 2]: [简要说明]

## 🔌 对外接口

### API 端点

[基于实际 Controller 的主要 API 接口列表]

### 服务导出

[基于实际 Module 导出的主要服务类]

## 📊 数据流程

[简单的业务数据流程图或文字描述]

## 🔧 技术特点

[基于实际代码分析的技术特色，3-5 个要点]

## 📚 文档导航

### ⬆️ 上级导航

- **[业务模块层总览](../README.md)** - 返回业务模块总体概览
- **[技术架构详解](../../README.md)** - 查看六层分层架构设计

### ⬇️ 子模块导航

[列出所有子模块的链接和简要说明]

### ➡️ 同级业务域

[列出同级其他业务域的链接和简要说明]

---

> [一句话总结业务域的核心价值]
```

## 📄 业务模块文件内容格式约束

### 📄 README.md - 格式约束

```markdown
# [模块名称]

## 📝 模块概述

[1-2 段文字描述模块功能和业务价值]

## 📖 文档导航

- **当前文档**: 模块概览 - 快速了解模块功能
- **[架构设计](docs/module-architecture.md)** - 理解技术实现
- **[业务流程](docs/workflow.md)** - 掌握业务流程
- **[开发指南](docs/development-guide.md)** - 快速上手开发

## 🎯 核心功能

[基于实际 Controller 接口的 3-5 个核心功能点列表]

## 📁 模块结构

[基于实际目录的简化结构图，只展示关键文件]

## 🔗 核心依赖

[基于实际 import 的主要内外部依赖，3-5 个]

## 🚀 快速示例

[基于实际 DTO 的 1 个核心 API 使用示例]

## 📚 全局文档导航

### ⬆️ 上级导航

- **[[上级业务域名称]](../README.md)** - 返回上级业务域概览
- **[业务模块层总览](../../README.md)** - 查看所有业务模块
- **[技术架构详解](../../../README.md)** - 查看六层分层架构设计

### 📋 模块文档

- **[架构设计](docs/module-architecture.md)** - 理解技术实现与架构设计
- **[业务流程](docs/workflow.md)** - 掌握核心业务流程与规则
- **[开发指南](docs/development-guide.md)** - 快速上手开发与注意事项

### ➡️ 同级子模块

[列出同级的其他子模块链接，基于实际存在的模块]

- **[[模块1名称]](../module1/README.md)** - 模块 1 功能简述
- **[[模块2名称]](../module2/README.md)** - 模块 2 功能简述

### 🔗 相关业务域

[列出相关的其他业务域链接]

- **[[业务域1名称]](../../domain1/README.md)** - 业务域 1 功能简述
- **[[业务域2名称]](../../domain2/README.md)** - 业务域 2 功能简述

---

> [一句话总结模块价值和核心能力]
```

### 🏗️ module-architecture.md - 格式约束（仅复杂模块需要）

```markdown
# [模块名称]架构设计

## 📖 文档导航

- **[模块概览](../README.md)** | **当前：架构设计** | **[业务流程](workflow.md)** | **[开发指南](development-guide.md)**

## 🏗️ 核心架构

[1-2 段文字描述整体架构风格（如分层架构、事件驱动等）]

### 架构视图

[基于实际代码的 Mermaid 架构图，支持多个视图，例如：]

1. **组件交互图** (Component Diagram): 展示 Controller, Service, Helper, Source 之间的交互
2. **类继承关系图** (Class Diagram): 展示核心类的继承与实现关系 (如 BaseEsAnalyzeService 及其子类)

## 📊 数据模型（如果有复杂 Entity 关系才包含）

[基于实际 Entity 的核心实体关系，1 个 Mermaid ER 图 + 简要说明]

## 🔧 关键设计决策

[详细列出该模块特有的关键设计决策，不限数量。每条决策应包含：]

- **设计背景**: 遇到了什么特定问题？
- **解决方案**: 采用了什么模式或策略？(基于实际代码)
- **权衡考量**: 为什么选择这个方案？有什么优缺点？

### [设计决策 1 名称]

[描述...]

### [设计决策 2 名称]

[描述...]

---

**⚠️ 注意**:

- 所有内容必须基于实际代码分析
- 重点解释"为什么" (Why) 和 "怎么做" (How)
- 避免空洞的通用描述，必须结合具体业务场景
```

### 🔄 workflow.md - 格式约束（仅有复杂业务流程的模块需要）

```markdown
# [模块名称]业务流程

## 📖 文档导航

- **[模块概览](../README.md)** | **[架构设计](module-architecture.md)** | **当前：业务流程** | **[开发指南](development-guide.md)**

## 🔄 核心业务流程

### [流程 1 名称]

[流程描述...]

[基于实际代码的 Mermaid 流程图/时序图]

### [流程 2 名称] (如有)

[流程描述...]

[基于实际代码的 Mermaid 流程图/时序图]

## 📋 关键业务规则（仅该模块特有规则）

[详细列出该模块特有的重要业务规则]

- [规则 1]: [描述]
- [规则 2]: [描述]

---

**⚠️ 注意**:

- 只包含该模块独有的业务流程
- 支持详细步骤描述和多张流程图
```

### 🚀 development-guide.md - 格式约束（仅有特殊开发注意点的模块需要）

```markdown
# [模块名称]开发指南

## 📖 文档导航

- **[模块概览](../README.md)** | **[架构设计](module-architecture.md)** | **[业务流程](workflow.md)** | **当前：开发指南**

## 🎯 开发关注点（仅该模块特有的关注点）

[基于实际代码分析的 2-3 个该模块特有的开发注意事项，不包含通用开发原则]

## ⚠️ 关键开发陷阱（基于实际代码复杂性）

[基于实际代码的 1-2 个该模块特有的开发陷阱，避免方法要具体明确]

---

**⚠️ 注意**:

- 只包含该模块特有的开发注意点
- 禁止包含通用开发指导（环境配置、编码规范、通用最佳实践等）
- 必须基于实际代码中的复杂逻辑或特殊处理
- 不需要测试指南
- 不需要开发示例(开发链路如果比较长或者很容易理解偏差的可以适当增加一点注意事项，不需要太复杂的代码示例)
```

## 🎯 生成决策流程

### 第一步：模块复杂度评估

1. **统计 API 接口数量**（Controller 中的@Get、@Post 等方法数）
2. **分析业务逻辑复杂度**（Service 中的业务方法复杂性）
3. **评估数据源复杂度**（外部 API 调用、数据库操作、缓存等）
4. **检查特殊设计模式**（是否有复杂的架构设计）

### 第二步：文档生成策略

- **简单模块**（<5 个 API，单一数据源，基础 CRUD）→ **仅 README.md**
- **中等模块**（5-15 个 API，有业务逻辑，多数据源）→ **README.md + 1 个详细文档**
- **复杂模块**（>15 个 API，复杂业务流程，多数据源整合）→ **README.md + 3 个详细文档**
- **如果符合格式要求的文档已经存在，就把最新内容更新进去即可** 可能是文档生成有一段时间了，后续又修改或者新增了代码，可以对比 生成时间 来简单判断文档的新旧程度

### 第三步：内容严格约束

- **每个文档都有字数限制**，严格控制篇幅
- **只写该模块特有的内容**，禁止通用内容
- **基于实际代码分析**，禁止臆想或凑内容
- **在头部显眼位置标注上生成文档时候的用户本地电脑当前时间（不是 AI 的时间）**，格式为：`生成时间：2025-06-12 10:00:00`
- **⚠️ 重要：必须使用用户本地电脑的实际当前时间，而不是 AI 训练时的时间**

## ❌ 生成失败标准（需重新生成）

如果生成的文档包含以下任何内容，视为生成失败：

1. **通用架构原则**（如"单一职责原则"、"开闭原则"等）
2. **环境配置指导**（Node.js 版本、npm 设置等）
3. **通用开发规范**（命名规范、代码风格等）
4. **通用性能优化**（通用缓存策略、数据库优化等）
5. **通用安全建议**（SQL 注入防护、XSS 防护等）
6. **通用监控建议**（日志记录、性能监控等）
7. **部署运维内容**（Docker 配置、CI/CD 等）
8. **测试指导**（单元测试编写、测试环境搭建等）

**⚠️ 重要说明**：

- **这不是开发教程**：不要包含如何编码、如何配置环境等通用内容
- **这是开发注意点**：专门针对在此模块进行开发时需要特别注意的要点
- **基于实际代码**：所有内容必须来自对实际代码的分析，不能编造或使用通用经验
- **宁少勿多**：内容不足时优先精简合并，不要强行凑内容

**❌ 错误示例（绝对禁止）**：
