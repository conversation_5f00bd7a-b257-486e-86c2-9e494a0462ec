# QCC Rover Service 文档体系整理 Prompt

## 🎯 整体目标

建立 QCC Rover Service 项目的完整、层次化文档体系，确保不同角色的用户能够快速理解项目架构、业务逻辑和开发规范。

## 📋 文档体系结构

### 文档层级关系

```
📁 根目录
├── README.md                           # 项目整体介绍（面向所有用户）
└── 📁 src/
    ├── README.md                       # 技术架构 + 开发指南（面向开发者）
    └── 📁 modules/
        ├── README.md                   # 业务模块总览
        ├── 📁 [业务域分组]/            # 如：risk-assessment, company-management
        │   ├── README.md               # 业务域职责和目录介绍
        │   └── 📁 [具体模块]/          # 如：diligence, monitor
        │       ├── README.md           # 模块概览（基于module-readme-template.md）
        │       └── 📁 docs/            # 详细技术文档
        │           ├── module-architecture.md
        │           ├── workflow.md
        │           └── development-guide.md
        └── 📁 [直接业务模块]/          # 如：data-processing
            ├── README.md               # 模块概览（基于module-readme-template.md）
            └── 📁 docs/                # 详细技术文档
```

## 🔧 具体任务清单

### 任务 1: 根目录 README.md

**目标用户**：项目管理者、新加入的开发者、业务人员  
**核心职责**：项目快速介绍，提供整体认知

**内容要求**：

- ✅ 项目简介和核心价值
- ✅ 技术栈概述（精简版）
- ✅ 主要业务功能列表
- ✅ 快速开始指南
- ✅ 项目结构概览（高层级）
- ✅ 文档导航（链接到 src/README.md 获取详细架构）
- ❌ 删除过于详细的技术实现细节
- ❌ 避免冗长的代码示例

### 任务 2: src/README.md

**目标用户**：开发者、架构师  
**核心职责**：技术架构详解 + 开发指南

**内容要求**：

- ✅ 保留并完善六层分层架构说明
- ✅ 详细的依赖关系规则
- ✅ 业务域概览和模块分布
- ✅ **整合开发规范章节**，包含：
  - Entity、DTO、Model、VO 的定义和使用指南（来源：`src/domain/model-dto-vo-migration-guide.md`）
  - 命名规范
  - 测试策略
  - 代码组织原则
- ✅ 路径别名配置
- ✅ 快速开始和文档导航

### 任务 3: 建立模块文档体系并确保文档导航一致性

**执行原则**：

- 根据模块层级灵活调整文档深度
- 确保文档间的导航链接正确
- 根目录的 README.md 为总的入口，一直往下延伸， 如 src/READMD.md， src/commons/README.md， src/modules/README.md , src/modules/risk-assessment/README.md , src/modules/risk-assessment/diligence/README.md , src/modules/risk-assessment/diligence/docs/\* 下面的文件 ， 这种关系你帮我灵活把控,确保他们都能正常连接起来

**导航要求**：

- 每个 README.md 都要包含"文档导航"部分
- 使用相对路径确保链接正确性
- 提供"向上"和"向下"的导航路径
- 在适当位置提供"相关文档"链接

#### ✅ 导航格式标准化

- ✅ **⬆️ 上级导航**: 所有文档都有返回上级的清晰路径
- ✅ **⬇️ 子模块导航**: 业务域文档都有子模块导航链接(有的 README.md 文件里面本身就已经有业务子模块的介绍了，这种可以继续保留子模块导航链接,但是要同时也确保子模块导航链接的正确性)
- ✅ **➡️ 同级导航**: 同层级文档间相互链接完整
- ✅ **📚 相关文档**: 技术文档和 API 文档的交叉引用

#### ✅ 路径验证

- ✅ 所有主要文档路径真实存在
- ✅ 相对路径链接格式正确
- ✅ 面包屑导航路径清晰

#### 🎯 文档导航体系特点

1. **层次化结构**: 从项目根目录到具体模块形成完整层级
2. **双向导航**: 支持向上和向下的导航路径
3. **横向连接**: 同级模块间的相互链接
4. **标准化格式**: 统一的导航格式和图标体系
5. **用户友好**: 清晰的描述和直观的导航路径

#### 🔄 持续维护建议

1. **新增模块时**: 按照标准模板添加文档导航
2. **定期检查**: 验证所有链接的有效性
3. **保持一致**: 新文档遵循已建立的导航标准
4. **用户反馈**: 根据使用反馈优化导航体验

## 📏 执行标准

### 代码分析要求

- ✅ **必须基于实际代码分析**：所有功能描述、API 接口、业务流程都要基于真实代码
- ❌ **禁止功能臆想**：不得描述代码中不存在的功能或接口
- ✅ **引用真实类名和方法**：示例代码要使用项目中实际存在的类名、方法名
- ✅ **基于实际文件结构**：目录结构图要反映真实的代码组织

### 文档质量要求

- **一致性**：使用统一的文档模板和格式规范
- **完整性**：确保每个层级都有对应的文档
- **导航性**：文档间要有清晰的导航关系
- **实用性**：内容要对开发者有实际指导价值

### 内容组织要求

- **层次化**：不同层级的文档有不同的详细程度
- **聚焦性**：每个文档都有明确的目标用户和核心职责
- **关联性**：相关文档间要有适当的交叉引用

## 🔄 执行流程

1. **分析现有代码结构**：深入了解项目的实际组织方式
2. **重构根目录文档**：创建简洁的项目介绍
3. **重构技术架构文档**：完善开发指南和规范
4. **生成模块文档**：按层级创建模块说明文档
5. **完善文档导航**：确保所有链接正确有效
6. **质量检查**：验证文档的准确性和完整性

## 📚 参考资源

- **模块文档模板**：`docs/module-readme-template.md`
- **开发规范来源**：`src/domain/model-dto-vo-migration-guide.md`
- **现有架构文档**：`src/README.md`
- **项目结构参考**：当前的 `src/modules/` 目录组织
