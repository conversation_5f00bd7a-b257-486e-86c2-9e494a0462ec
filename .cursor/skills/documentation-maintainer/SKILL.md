---
name: documentation-maintainer
description: 自动化文档维护工具。基于 docs/ 下的模版标准，自动生成/更新项目全局文档、模块文档和项目分析文档。
---

# Documentation Maintainer

This skill automates the maintenance of project documentation for DD-Platform-Service, ensuring strict adherence to the standards defined in the bundled references.

## Capabilities

### 1. Maintain Module Documentation

**Trigger**: User asks to update, generate, or refresh documentation for a specific module or directory path.

**Workflow**:

1.  **Read Template**: You MUST read the bundled reference `references/module-readme-template-v1.md` FIRST to load the latest generation rules and forbidden content constraints.
2.  **Analyze**: Analyze the target directory structure to determine:
    - **Type**: Is it a "Domain Group" (only submodules) or a "Business Module" (contains code)?
    - **Complexity**: (If Business Module) Assess API count, Service complexity, and Data sources as per the template's "Complexity Assessment" section.
3.  **Generate & Cascade Update**:
    - **Target Module**: Create or update the following files based on complexity:
      - `README.md`: Always required.
      - `docs/module-architecture.md`: For Medium/Complex modules.
      - `docs/workflow.md`: For Complex modules with specific flows.
      - `docs/development-guide.md`: For Complex modules with specific dev constraints.
    - **Cascade Check (Vital)**: If you are updating a sub-module or a specific component (e.g., `helper/`, `source/`), you **MUST** verify and update the parent module's documentation:
      - **Parent `README.md`**: Update summary, feature lists, or component descriptions to reflect the child's changes.
      - **Parent `docs/`**:
        - Update `module-architecture.md` if the change introduces new components or relationships.
        - Update `workflow.md` if the business flow logic has changed.
        - Update `development-guide.md` if new development constraints or patterns are introduced.
    - **Consistency**: Ensure terminology and descriptions are consistent between the sub-module doc and the parent doc.
4.  **Content Rules**:
    - **Strictly** follow the "Content Constraints" in the template.
    - **Prohibited**: Generic architecture principles, setup guides, or content not specific to the module.
    - **Time**: Use the current user time for "Generation Time".
5.  **Navigation**: Ensure the `README.md` contains a "Documentation Navigation" section that links UP to the parent, DOWN to submodules, and ACROSS to related modules.

### 2. Maintain Global Documentation

**Trigger**: User asks to update global docs, `src/README.md`, or the root `README.md`.

**Workflow**:

1.  **Read Standards**: Read the bundled reference `references/doc-system.md` and `src/README.md` to understand the 6-layer architecture and current global doc structure.
2.  **Scan**: Scan the `src/modules` directory to identify all current business domains and submodules.
3.  **Update `src/README.md`**:
    - Update the file tree structure if changed.
    - Update the list of modules in the "Modules" section.
    - Ensure all links to module READMEs are valid.
4.  **Update Root `README.md`**:
    - Ensure it provides a high-level overview and links correctly to `src/README.md`.

### 3. Generate Project Analysis

**Trigger**: User asks for a project analysis or overview.

**Workflow**:

1.  **Read Template**: Read the bundled reference `references/project-analysis-template.md`.
2.  **Analyze**: Perform a comprehensive scan of the codebase to gather:
    - Tech stack details.
    - Project structure.
    - Core modules and their responsibilities.
    - Key business flows (by tracing main Controllers/Services).
3.  **Generate**: Create `docs/project-analysis.md` (or user-specified path) by filling the template with actual data.

## Rules

- **Source of Truth**: The `.md` files in the `references/` directory of this skill are the absolute source of truth. Always defer to their content over these instructions if they conflict.
- **Language**: Simplified Chinese (简体中文).
- **Reality Check**: Do not hallucinate features. Only document what exists in the code.
